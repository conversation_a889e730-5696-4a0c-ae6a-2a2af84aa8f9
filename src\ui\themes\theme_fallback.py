"""
Wrapper para theme manager con fallback
"""

try:
    from .advanced_theme_manager import AdvancedThemeManager
    ThemeManagerClass = AdvancedThemeManager
except ImportError:
    from .simple.simple_theme_manager import SimpleThemeManager
    ThemeManagerClass = SimpleThemeManager


class AdvancedThemeManager(ThemeManagerClass):
    """Clase que funciona como fallback si no hay dependencias"""
    pass


# Mantener compatibilidad
__all__ = ['AdvancedThemeManager']
