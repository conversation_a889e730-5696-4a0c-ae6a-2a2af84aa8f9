"""
Diálogo para crear y editar artículos.
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLineEdit, QLabel,
    QFormLayout, QDialogButtonBox, QDoubleSpinBox, QCheckBox,
    QMessageBox, QGroupBox, QFileDialog
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QPixmap
import os
import shutil

from models.base import get_db
from models.articulo import Articulo
from models.formula_calculator import validar_codigo_articulo, generar_codigo_sugerido


class ArticuloEditarDialog(QDialog):
    """Diálogo para crear o editar un artículo."""
    
    def __init__(self, parent=None, articulo=None, duplicar=False):
        """
        Inicializa el diálogo de edición de artículo.
        
        Args:
            parent: Ventana padre
            articulo: Artículo a editar (None para crear nuevo)
            duplicar: <PERSON>, duplica el artículo en lugar de editarlo
        """
        super().__init__(parent)
        
        self.articulo = articulo
        self.duplicar = duplicar
        
        # Configurar ventana
        if articulo and not duplicar:
            self.setWindowTitle(f"Editar Artículo - {articulo.codigo}")
        elif duplicar:
            self.setWindowTitle(f"Duplicar Artículo - {articulo.codigo}")
        else:
            self.setWindowTitle("Nuevo Artículo")
        
        self.setModal(True)
        
        # ✅ CONFIGURACIÓN SIMPLE Y EFECTIVA
        from ui.utils.window_utils import setup_maximized_dialog
        setup_maximized_dialog(self, self.windowTitle())
        
        # Inicializar interfaz
        self._inicializar_ui()
        
        # Cargar datos si es edición o duplicación
        if articulo:
            self._cargar_datos_articulo()
    
    def _inicializar_ui(self):
        """Configura la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)
        
        # Título
        titulo = QLabel("Datos del Artículo")
        titulo.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout_principal.addWidget(titulo)
        
        # Formulario principal
        self._crear_formulario_principal(layout_principal)
        
        # Botones de acción
        self._crear_botones_accion(layout_principal)
    
    def _crear_formulario_principal(self, layout_principal):
        """Crea el formulario principal."""
        grupo_datos = QGroupBox("Información General")
        layout_datos = QFormLayout(grupo_datos)
        
        # Estilo común para campos de texto
        estilo_campos = """
            QLineEdit {
                font-size: 10px;
                padding: 4px 6px;
                min-height: 24px;
            }
            QLabel {
                font-size: 12px;
            }
        """
        
        # Código
        self.campo_codigo = QLineEdit()
        self.campo_codigo.setMaxLength(20)
        self.campo_codigo.textChanged.connect(self._validar_codigo)
        self.campo_codigo.setStyleSheet("""
            QLineEdit {
                font-size: 10px;
                padding: 4px 6px;
                min-height: 24px;
            }
        """)
        layout_datos.addRow("Código*:", self.campo_codigo)
        
        # Botón de generar código
        layout_codigo = QHBoxLayout()
        layout_codigo.addWidget(self.campo_codigo)
        
        boton_generar = QPushButton("Generar")
        boton_generar.clicked.connect(self._generar_codigo)
        boton_generar.setStyleSheet("font-size: 11px; padding: 3px 5px;")
        layout_codigo.addWidget(boton_generar)
        
        layout_datos.addRow("Código*:", layout_codigo)
        
        # Serie
        self.campo_serie = QLineEdit()
        self.campo_serie.setMaxLength(50)
        self.campo_serie.setPlaceholderText("Ej: Demo-2000 Practicable")
        self.campo_serie.setStyleSheet("""
            QLineEdit {
                font-size: 12px;
                padding: 4px 6px;
                min-height: 28px;
            }
        """)
        layout_datos.addRow("Serie:", self.campo_serie)
        
        # Descripción
        self.campo_descripcion = QLineEdit()
        self.campo_descripcion.setMaxLength(200)
        self.campo_descripcion.setPlaceholderText("Ej: Oscilobatiente de una hoja")
        self.campo_descripcion.setStyleSheet("""
            QLineEdit {
                font-size: 12px;
                padding: 4px 6px;
                min-height: 28px;
            }
        """)
        layout_datos.addRow("Descripción*:", self.campo_descripcion)
        
        # Tiempos
        layout_tiempos = QHBoxLayout()
        
        self.campo_tiempo_taller = QDoubleSpinBox()
        self.campo_tiempo_taller.setRange(0, 999.99)
        self.campo_tiempo_taller.setDecimals(2)
        self.campo_tiempo_taller.setSuffix(" h")
        self.campo_tiempo_taller.setStyleSheet("""
            QDoubleSpinBox {
                font-size: 12px;
                padding: 4px 6px;
                min-height: 28px;
            }
        """)
        layout_tiempos.addWidget(QLabel("Taller:"))
        layout_tiempos.addWidget(self.campo_tiempo_taller)
        
        self.campo_tiempo_obra = QDoubleSpinBox()
        self.campo_tiempo_obra.setRange(0, 999.99)
        self.campo_tiempo_obra.setDecimals(2)
        self.campo_tiempo_obra.setSuffix(" h")
        self.campo_tiempo_obra.setStyleSheet("""
            QDoubleSpinBox {
                font-size: 12px;
                padding: 4px 6px;
                min-height: 28px;
            }
        """)
        layout_tiempos.addWidget(QLabel("Obra:"))
        layout_tiempos.addWidget(self.campo_tiempo_obra)
        
        layout_datos.addRow("Tiempos:", layout_tiempos)
        
        # Dibujo
        self.campo_dibujo = QLineEdit()
        self.campo_dibujo.setPlaceholderText("Ruta al archivo de dibujo (opcional)")
        self.campo_dibujo.setStyleSheet("""
            QLineEdit {
                font-size: 12px;
                padding: 4px 6px;
                min-height: 28px;
            }
        """)
        layout_datos.addRow("Dibujo:", self.campo_dibujo)

        # Imagen
        layout_imagen = QHBoxLayout()
        self.campo_imagen = QLineEdit()
        self.campo_imagen.setPlaceholderText("Ruta al archivo de imagen (opcional)")
        self.campo_imagen.setReadOnly(True)
        self.campo_imagen.setStyleSheet("""
            QLineEdit {
                font-size: 12px;
                padding: 4px 6px;
                min-height: 28px;
            }
        """)
        layout_imagen.addWidget(self.campo_imagen)

        self.btn_seleccionar_imagen = QPushButton("📁 Seleccionar")
        self.btn_seleccionar_imagen.clicked.connect(self._seleccionar_imagen)
        layout_imagen.addWidget(self.btn_seleccionar_imagen)

        self.btn_quitar_imagen = QPushButton("🗑️ Quitar")
        self.btn_quitar_imagen.clicked.connect(self._quitar_imagen)
        self.btn_quitar_imagen.setEnabled(False)
        layout_imagen.addWidget(self.btn_quitar_imagen)

        layout_datos.addRow("Imagen:", layout_imagen)

        # Activo
        self.campo_activo = QCheckBox("Artículo activo")
        self.campo_activo.setChecked(True)
        layout_datos.addRow("", self.campo_activo)
        
        layout_principal.addWidget(grupo_datos)

        # Previsualización de imagen
        grupo_imagen = QGroupBox("Previsualización de Imagen")
        layout_preview = QVBoxLayout(grupo_imagen)

        self.label_preview_imagen = QLabel()
        self.label_preview_imagen.setFixedSize(200, 200)
        self.label_preview_imagen.setStyleSheet("""
            border: 2px solid #ccc;
            background-color: #f8f8f8;
            border-radius: 10px;
            padding: 5px;
        """)
        self.label_preview_imagen.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_preview_imagen.setText("📷\nSin imagen\nSeleccione una imagen arriba")
        self.label_preview_imagen.setWordWrap(True)
        self.label_preview_imagen.setScaledContents(True)
        layout_preview.addWidget(self.label_preview_imagen, alignment=Qt.AlignmentFlag.AlignCenter)

        layout_principal.addWidget(grupo_imagen)

        # Información adicional
        grupo_info = QGroupBox("Información Adicional")
        layout_info = QVBoxLayout(grupo_info)
        
        info_text = QLabel(
            "Después de crear el artículo, podrá agregar los componentes "
            "(perfiles, accesorios, cristales y persianas) desde la ventana principal."
        )
        info_text.setWordWrap(True)
        info_text.setStyleSheet("color: #666; font-style: italic;")
        layout_info.addWidget(info_text)
        
        layout_principal.addWidget(grupo_info)
    
    def _crear_botones_accion(self, layout_principal):
        """Crea los botones de acción."""
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        
        # Personalizar texto de botones
        if self.duplicar:
            botones.button(QDialogButtonBox.StandardButton.Ok).setText("Duplicar")
        elif self.articulo:
            botones.button(QDialogButtonBox.StandardButton.Ok).setText("Guardar")
        else:
            botones.button(QDialogButtonBox.StandardButton.Ok).setText("Crear")
        
        botones.button(QDialogButtonBox.StandardButton.Cancel).setText("Cancelar")
        
        # Conectar señales
        botones.accepted.connect(self._aceptar)
        botones.rejected.connect(self.reject)
        
        layout_principal.addWidget(botones)
    
    def _cargar_datos_articulo(self):
        """Carga los datos del artículo en el formulario."""
        if not self.articulo:
            return
        
        if self.duplicar:
            # Para duplicar, generar nuevo código
            self.campo_codigo.setText(f"{self.articulo.codigo}_COPIA")
            self.campo_descripcion.setText(f"{self.articulo.descripcion} (Copia)")
        else:
            # Para editar, cargar código original
            self.campo_codigo.setText(self.articulo.codigo)
            self.campo_descripcion.setText(self.articulo.descripcion)
        
        self.campo_serie.setText(self.articulo.serie or "")
        self.campo_tiempo_taller.setValue(self.articulo.tiempo_taller or 0.0)
        self.campo_tiempo_obra.setValue(self.articulo.tiempo_obra or 0.0)
        self.campo_dibujo.setText(self.articulo.dibujo_path or "")
        self.campo_imagen.setText(self.articulo.imagen_path or "")
        self.campo_activo.setChecked(self.articulo.activo)

        # Cargar previsualización de imagen
        self._actualizar_preview_imagen()
    
    def _validar_codigo(self):
        """Valida el código en tiempo real."""
        codigo = self.campo_codigo.text()
        es_valido, mensaje = validar_codigo_articulo(codigo)
        
        if not es_valido and codigo:
            self.campo_codigo.setStyleSheet("border: 1px solid red;")
            self.campo_codigo.setToolTip(mensaje)
        else:
            self.campo_codigo.setStyleSheet("")
            self.campo_codigo.setToolTip("")
    
    def _generar_codigo(self):
        """Genera un código sugerido."""
        serie = self.campo_serie.text()
        descripcion = self.campo_descripcion.text()
        
        codigo_sugerido = generar_codigo_sugerido(serie, descripcion)
        
        # Verificar que no exista
        db = next(get_db())
        try:
            contador = 1
            codigo_base = codigo_sugerido
            
            while True:
                existe = db.query(Articulo).filter(Articulo.codigo == codigo_sugerido).first()
                if not existe:
                    break
                
                contador += 1
                codigo_sugerido = f"{codigo_base}{contador:03d}"
                
                if contador > 999:  # Evitar bucle infinito
                    break
            
            self.campo_codigo.setText(codigo_sugerido)
            
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Error al generar código: {str(e)}")
        finally:
            db.close()

    def _seleccionar_imagen(self):
        """Selecciona una imagen para el artículo."""
        filtros = "Imágenes (*.png *.jpg *.jpeg *.bmp *.gif);;Todos los archivos (*)"

        archivo, _ = QFileDialog.getOpenFileName(
            self,
            "Seleccionar imagen para el artículo",
            "",
            filtros
        )

        if archivo:
            try:
                # Crear directorio de destino si no existe
                directorio_imagenes = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'imagenes', 'articulos')
                os.makedirs(directorio_imagenes, exist_ok=True)

                # Generar nombre único para la imagen
                nombre_archivo = os.path.basename(archivo)
                nombre_base, extension = os.path.splitext(nombre_archivo)

                # Si estamos editando, usar el código del artículo
                if self.articulo and not self.duplicar:
                    prefijo = self.articulo.codigo
                else:
                    prefijo = self.campo_codigo.text().strip() or "TEMP"

                nombre_destino = f"{prefijo}_{nombre_base}{extension}"
                ruta_destino = os.path.join(directorio_imagenes, nombre_destino)

                # Copiar archivo
                shutil.copy2(archivo, ruta_destino)

                # Actualizar campo y previsualización
                self.campo_imagen.setText(ruta_destino)
                self.btn_quitar_imagen.setEnabled(True)
                self._actualizar_preview_imagen()

                QMessageBox.information(
                    self,
                    "Imagen seleccionada",
                    f"Imagen copiada correctamente a:\n{ruta_destino}"
                )

            except Exception as e:
                QMessageBox.critical(
                    self,
                    "Error",
                    f"No se pudo copiar la imagen: {str(e)}"
                )

    def _quitar_imagen(self):
        """Quita la imagen del artículo."""
        self.campo_imagen.clear()
        self.btn_quitar_imagen.setEnabled(False)
        self._actualizar_preview_imagen()

    def _actualizar_preview_imagen(self):
        """Actualiza la previsualización de la imagen."""
        ruta_imagen = self.campo_imagen.text().strip()

        if ruta_imagen and os.path.exists(ruta_imagen):
            try:
                pixmap = QPixmap(ruta_imagen)
                if not pixmap.isNull():
                    # Escalar imagen manteniendo proporción
                    pixmap_escalado = pixmap.scaled(
                        190, 190,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    self.label_preview_imagen.setPixmap(pixmap_escalado)
                    self.label_preview_imagen.setText("")
                else:
                    self._mostrar_imagen_por_defecto()
            except Exception as e:
                self._mostrar_imagen_por_defecto()
                print(f"Error al cargar imagen: {str(e)}")
        else:
            self._mostrar_imagen_por_defecto()

    def _mostrar_imagen_por_defecto(self):
        """Muestra el texto por defecto cuando no hay imagen."""
        self.label_preview_imagen.clear()
        self.label_preview_imagen.setText("📷\nSin imagen\nSeleccione una imagen arriba")

    def _aceptar(self):
        """Valida y guarda el artículo."""
        # Validar campos obligatorios
        if not self.campo_codigo.text().strip():
            QMessageBox.warning(self, "Error", "El código es obligatorio.")
            self.campo_codigo.setFocus()
            return
        
        if not self.campo_descripcion.text().strip():
            QMessageBox.warning(self, "Error", "La descripción es obligatoria.")
            self.campo_descripcion.setFocus()
            return
        
        # Validar código
        es_valido, mensaje = validar_codigo_articulo(self.campo_codigo.text())
        if not es_valido:
            QMessageBox.warning(self, "Error", f"Código inválido: {mensaje}")
            self.campo_codigo.setFocus()
            return
        
        # Guardar artículo
        if self._guardar_articulo():
            self.accept()
    
    def _guardar_articulo(self):
        """Guarda el artículo en la base de datos."""
        db = next(get_db())
        
        try:
            # Verificar código único
            codigo = self.campo_codigo.text().strip()
            
            if self.articulo and not self.duplicar:
                # Edición: verificar que no exista otro con el mismo código
                existe = db.query(Articulo).filter(
                    Articulo.codigo == codigo,
                    Articulo.id != self.articulo.id
                ).first()
            else:
                # Creación o duplicación: verificar que no exista
                existe = db.query(Articulo).filter(Articulo.codigo == codigo).first()
            
            if existe:
                QMessageBox.warning(self, "Error", "Ya existe un artículo con este código.")
                return False
            
            # Crear o actualizar artículo
            if self.articulo and not self.duplicar:
                # Actualizar artículo existente
                articulo_db = db.query(Articulo).filter(Articulo.id == self.articulo.id).first()
                if not articulo_db:
                    QMessageBox.critical(self, "Error", "No se encontró el artículo a actualizar.")
                    return False
            else:
                # Crear nuevo artículo
                articulo_db = Articulo()
                db.add(articulo_db)
            
            # Asignar valores
            articulo_db.codigo = codigo
            articulo_db.serie = self.campo_serie.text().strip() or None
            articulo_db.descripcion = self.campo_descripcion.text().strip()
            articulo_db.tiempo_taller = self.campo_tiempo_taller.value()
            articulo_db.tiempo_obra = self.campo_tiempo_obra.value()
            articulo_db.dibujo_path = self.campo_dibujo.text().strip() or None
            articulo_db.imagen_path = self.campo_imagen.text().strip() or None
            articulo_db.activo = self.campo_activo.isChecked()
            
            # Si es duplicación, copiar componentes
            if self.duplicar and self.articulo:
                self._duplicar_componentes(db, articulo_db)
            
            db.commit()
            
            # Mensaje de éxito
            if self.duplicar:
                QMessageBox.information(
                    self,
                    "Éxito",
                    f"Artículo '{codigo}' duplicado correctamente."
                )
            elif self.articulo:
                QMessageBox.information(
                    self,
                    "Éxito",
                    f"Artículo '{codigo}' actualizado correctamente."
                )
            else:
                QMessageBox.information(
                    self,
                    "Éxito",
                    f"Artículo '{codigo}' creado correctamente."
                )
            
            return True
            
        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudo guardar el artículo: {str(e)}"
            )
            return False
        finally:
            db.close()
    
    def _duplicar_componentes(self, db, nuevo_articulo):
        """Duplica los componentes del artículo original al nuevo."""
        try:
            # Duplicar perfiles
            for perfil_comp in self.articulo.perfiles:
                from models.articulo import ArticuloPerfil
                nuevo_perfil = ArticuloPerfil(
                    articulo_id=nuevo_articulo.id,
                    perfil_id=perfil_comp.perfil_id,
                    medida_formula=perfil_comp.medida_formula,
                    condicion=perfil_comp.condicion,
                    cantidad_base=perfil_comp.cantidad_base,
                    cantidad_formula=perfil_comp.cantidad_formula,
                    orden=perfil_comp.orden
                )
                db.add(nuevo_perfil)
            
            # Duplicar accesorios
            for acc_comp in self.articulo.accesorios:
                from models.articulo import ArticuloAccesorio
                nuevo_accesorio = ArticuloAccesorio(
                    articulo_id=nuevo_articulo.id,
                    accesorio_id=acc_comp.accesorio_id,
                    condicion=acc_comp.condicion,
                    cantidad_base=acc_comp.cantidad_base,
                    cantidad_formula=acc_comp.cantidad_formula,
                    orden=acc_comp.orden
                )
                db.add(nuevo_accesorio)
            
            # Duplicar cristales
            for cristal_comp in self.articulo.cristales:
                from models.articulo import ArticuloCristal
                nuevo_cristal = ArticuloCristal(
                    articulo_id=nuevo_articulo.id,
                    cristal_id=cristal_comp.cristal_id,
                    medida_formula=cristal_comp.medida_formula,
                    condicion=cristal_comp.condicion,
                    cantidad_base=cristal_comp.cantidad_base,
                    cantidad_formula=cristal_comp.cantidad_formula,
                    orden=cristal_comp.orden
                )
                db.add(nuevo_cristal)
            
            # Duplicar persianas
            for persiana_comp in self.articulo.persianas:
                from models.articulo import ArticuloPersiana
                nueva_persiana = ArticuloPersiana(
                    articulo_id=nuevo_articulo.id,
                    persiana_id=persiana_comp.persiana_id,
                    medida_formula=persiana_comp.medida_formula,
                    condicion=persiana_comp.condicion,
                    cantidad_base=persiana_comp.cantidad_base,
                    cantidad_formula=persiana_comp.cantidad_formula,
                    orden=persiana_comp.orden
                )
                db.add(nueva_persiana)
                
        except Exception as e:
            raise Exception(f"Error al duplicar componentes: {str(e)}")
