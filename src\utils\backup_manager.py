#!/usr/bin/env python3
"""
Sistema de Backup Automático para PRO-2000
Crea copias de seguridad automáticas de la base de datos
"""

import shutil
import os
from datetime import datetime, timedelta
from pathlib import Path
import threading
import time
from typing import Optional, List

class BackupManager:
    """Gestor de backups automáticos"""
    
    def __init__(self, db_path: str = "data/pro2000.db", backup_dir: str = "data/backup"):
        self.db_path = Path(db_path)
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Configuración
        self.max_backups = 30  # Mantener 30 backups
        self.backup_interval_hours = 24  # Backup cada 24 horas
        
        # Control de hilos
        self.backup_thread = None
        self.stop_backup = False
        
    def create_backup(self, custom_name: Optional[str] = None) -> Optional[Path]:
        """
        Crea un backup manual de la base de datos
        
        Args:
            custom_name: Nombre personalizado para el backup
            
        Returns:
            Path del archivo de backup creado o None si falló
        """
        try:
            if not self.db_path.exists():
                print(f"⚠️ Base de datos no encontrada: {self.db_path}")
                return None
            
            # Generar nombre del backup
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if custom_name:
                backup_name = f"pro2000_{custom_name}_{timestamp}.db"
            else:
                backup_name = f"pro2000_backup_{timestamp}.db"
            
            backup_file = self.backup_dir / backup_name
            
            # Crear el backup
            shutil.copy2(self.db_path, backup_file)
            
            print(f"✅ Backup creado: {backup_name}")
            return backup_file
            
        except Exception as e:
            print(f"❌ Error creando backup: {e}")
            return None
    
    def start_auto_backup(self):
        """Inicia el sistema de backup automático"""
        if self.backup_thread and self.backup_thread.is_alive():
            print("⚠️ Sistema de backup ya está activo")
            return
        
        self.stop_backup = False
        self.backup_thread = threading.Thread(target=self._backup_loop, daemon=True)
        self.backup_thread.start()
        print("🔄 Sistema de backup automático iniciado")
    
    def stop_auto_backup(self):
        """Detiene el sistema de backup automático"""
        self.stop_backup = True
        if self.backup_thread:
            self.backup_thread.join(timeout=5)
        print("⏹️ Sistema de backup automático detenido")
    
    def _backup_loop(self):
        """Loop principal del backup automático"""
        while not self.stop_backup:
            try:
                # Verificar si es hora del backup
                if self._should_create_backup():
                    self.create_backup("auto")
                    self.cleanup_old_backups()
                
                # Esperar una hora antes de verificar de nuevo
                for _ in range(3600):  # 3600 segundos = 1 hora
                    if self.stop_backup:
                        break
                    time.sleep(1)
                    
            except Exception as e:
                print(f"❌ Error en loop de backup: {e}")
                time.sleep(300)  # Esperar 5 minutos antes de reintentar
    
    def _should_create_backup(self) -> bool:
        """Verifica si es hora de crear un backup"""
        # Buscar el backup más reciente
        backups = self.get_backup_list()
        if not backups:
            return True  # No hay backups, crear uno
        
        # Verificar tiempo del último backup
        latest_backup = backups[0]  # Ya está ordenado por fecha
        time_since_backup = datetime.now() - latest_backup['created']
        
        return time_since_backup >= timedelta(hours=self.backup_interval_hours)
    
    def get_backup_list(self) -> List[dict]:
        """Obtiene lista de backups disponibles ordenada por fecha"""
        backups = []
        
        for backup_file in self.backup_dir.glob("pro2000_*.db*"):
            try:
                # Extraer timestamp del nombre del archivo
                name = backup_file.stem
                if name.endswith('.db'):
                    name = name[:-3]  # Remover .db del final
                
                # Buscar patrón de timestamp
                parts = name.split('_')
                if len(parts) >= 3:
                    timestamp_str = parts[-2] + '_' + parts[-1]  # fecha_hora
                    created = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                    
                    backups.append({
                        'file': backup_file,
                        'name': backup_file.name,
                        'created': created,
                        'size': backup_file.stat().st_size,
                        'size_mb': round(backup_file.stat().st_size / (1024*1024), 2)
                    })
                    
            except Exception as e:
                print(f"⚠️ Error procesando backup {backup_file}: {e}")
        
        # Ordenar por fecha (más reciente primero)
        backups.sort(key=lambda x: x['created'], reverse=True)
        return backups
    
    def cleanup_old_backups(self):
        """Elimina backups antiguos para mantener solo los más recientes"""
        try:
            backups = self.get_backup_list()
            
            if len(backups) <= self.max_backups:
                return  # No hay suficientes backups para limpiar
            
            # Eliminar backups excedentes
            backups_to_remove = backups[self.max_backups:]
            
            for backup in backups_to_remove:
                try:
                    backup['file'].unlink()
                    print(f"🗑️ Backup antiguo eliminado: {backup['name']}")
                except Exception as e:
                    print(f"⚠️ Error eliminando backup {backup['name']}: {e}")
                    
        except Exception as e:
            print(f"❌ Error en limpieza de backups: {e}")
    
    def restore_backup(self, backup_file: Path) -> bool:
        """
        Restaura una base de datos desde un backup
        
        Args:
            backup_file: Archivo de backup a restaurar
            
        Returns:
            True si la restauración fue exitosa
        """
        try:
            if not backup_file.exists():
                print(f"❌ Archivo de backup no encontrado: {backup_file}")
                return False
            
            # Crear backup del archivo actual antes de restaurar
            current_backup = self.create_backup("pre_restore")
            if current_backup:
                print(f"✅ Backup de seguridad creado antes de restaurar")
            
            # Restaurar el archivo
            shutil.copy2(backup_file, self.db_path)
            print(f"✅ Base de datos restaurada desde: {backup_file.name}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error restaurando backup: {e}")
            return False
    
    def get_backup_info(self) -> dict:
        """Obtiene información general sobre los backups"""
        backups = self.get_backup_list()
        
        if not backups:
            return {
                'total_backups': 0,
                'total_size_mb': 0,
                'latest_backup': None,
                'oldest_backup': None
            }
        
        total_size = sum(backup['size'] for backup in backups)
        
        return {
            'total_backups': len(backups),
            'total_size_mb': round(total_size / (1024*1024), 2),
            'latest_backup': backups[0]['created'] if backups else None,
            'oldest_backup': backups[-1]['created'] if backups else None
        }

# Instancia global del gestor de backup
backup_manager = BackupManager()

def get_backup_manager() -> BackupManager:
    """Obtiene la instancia global del gestor de backup"""
    return backup_manager

if __name__ == "__main__":
    # Prueba del sistema de backup
    print("🔄 Probando sistema de backup...")
    
    manager = BackupManager()
    
    # Crear backup manual
    backup_file = manager.create_backup("manual_test")
    if backup_file:
        print(f"✅ Backup de prueba creado: {backup_file}")
    
    # Mostrar información de backups
    info = manager.get_backup_info()
    print(f"📊 Información de backups:")
    print(f"   Total: {info['total_backups']} archivos")
    print(f"   Tamaño total: {info['total_size_mb']} MB")
    if info['latest_backup']:
        print(f"   Último backup: {info['latest_backup']}")
    
    # Listar backups
    backups = manager.get_backup_list()
    if backups:
        print(f"\n📋 Backups disponibles:")
        for backup in backups[:5]:  # Mostrar solo los 5 más recientes
            print(f"   - {backup['name']} ({backup['size_mb']} MB)")
