"""
Gestor de iconos para PRO-2000.
Proporciona iconos consistentes en toda la aplicación.
"""

from PyQt6.QtGui import QIcon, QPixmap, QPainter, QColor, QFont
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtWidgets import QApplication
import os


class IconManager:
    """Gestor de iconos de la aplicación."""
    
    def __init__(self):
        self.icon_cache = {}
        self.icon_size = QSize(24, 24)
        self.colors = {
            'primary': '#0d6efd',
            'secondary': '#6c757d',
            'success': '#198754',
            'danger': '#dc3545',
            'warning': '#ffc107',
            'info': '#0dcaf0',
            'light': '#f8f9fa',
            'dark': '#212529'
        }
    
    def get_icon(self, icon_name, color='primary', size=None):
        """
        Obtiene un icono por nombre.
        
        Args:
            icon_name: Nombre del icono
            color: Color del icono (primary, secondary, etc.)
            size: Tama<PERSON> del icono (QSize)
        
        Returns:
            QIcon: Icono generado
        """
        if size is None:
            size = self.icon_size
        
        cache_key = f"{icon_name}_{color}_{size.width()}x{size.height()}"
        
        if cache_key not in self.icon_cache:
            self.icon_cache[cache_key] = self._create_icon(icon_name, color, size)
        
        return self.icon_cache[cache_key]
    
    def _create_icon(self, icon_name, color, size):
        """Crea un icono."""
        # Primero intentar cargar desde archivo
        icon_path = os.path.join(os.path.dirname(__file__), 'assets', f'{icon_name}.png')
        if os.path.exists(icon_path):
            return QIcon(icon_path)
        
        # Si no existe, crear icono programáticamente
        return self._create_programmatic_icon(icon_name, color, size)
    
    def _create_programmatic_icon(self, icon_name, color, size):
        """Crea un icono programáticamente."""
        pixmap = QPixmap(size)
        pixmap.fill(Qt.GlobalColor.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Obtener color
        color_hex = self.colors.get(color, color)
        if isinstance(color_hex, str) and color_hex.startswith('#'):
            qcolor = QColor(color_hex)
        else:
            qcolor = QColor(color_hex)
        
        painter.setPen(qcolor)
        painter.setBrush(qcolor)
        
        # Dibujar según el tipo de icono
        self._draw_icon_shape(painter, icon_name, size)
        
        painter.end()
        
        return QIcon(pixmap)
    
    def _draw_icon_shape(self, painter, icon_name, size):
        """Dibuja la forma del icono."""
        w, h = size.width(), size.height()
        margin = 2
        
        if icon_name == 'add' or icon_name == 'plus':
            # Icono de agregar (+)
            painter.drawLine(w//2, margin, w//2, h-margin)
            painter.drawLine(margin, h//2, w-margin, h//2)
            
        elif icon_name == 'edit' or icon_name == 'pencil':
            # Icono de editar (lápiz)
            painter.drawLine(margin, h-margin, w-margin-4, margin+4)
            painter.drawRect(w-margin-4, margin, 4, 4)
            
        elif icon_name == 'delete' or icon_name == 'trash':
            # Icono de eliminar (papelera)
            painter.drawRect(margin+4, margin+6, w-8, h-10)
            painter.drawLine(margin+2, margin+6, w-margin-2, margin+6)
            painter.drawLine(w//2-2, margin+2, w//2+2, margin+2)
            
        elif icon_name == 'save' or icon_name == 'disk':
            # Icono de guardar (diskette)
            painter.drawRect(margin, margin, w-2*margin, h-2*margin)
            painter.drawRect(margin+2, margin, w-2*margin-4, margin+6)
            
        elif icon_name == 'search' or icon_name == 'find':
            # Icono de buscar (lupa)
            center_x, center_y = w//2-2, h//2-2
            radius = min(w, h)//3
            painter.drawEllipse(center_x-radius, center_y-radius, 2*radius, 2*radius)
            painter.drawLine(center_x+radius-2, center_y+radius-2, w-margin, h-margin)
            
        elif icon_name == 'settings' or icon_name == 'gear':
            # Icono de configuración (engranaje)
            center_x, center_y = w//2, h//2
            radius = min(w, h)//3
            painter.drawEllipse(center_x-radius, center_y-radius, 2*radius, 2*radius)
            painter.drawEllipse(center_x-radius//2, center_y-radius//2, radius, radius)
            
        elif icon_name == 'home' or icon_name == 'house':
            # Icono de inicio (casa)
            painter.drawLine(w//2, margin, margin, h//2)
            painter.drawLine(w//2, margin, w-margin, h//2)
            painter.drawRect(margin+4, h//2, w-8, h//2-margin)
            
        elif icon_name == 'user' or icon_name == 'person':
            # Icono de usuario (persona)
            head_radius = w//6
            painter.drawEllipse(w//2-head_radius, margin+2, 2*head_radius, 2*head_radius)
            painter.drawEllipse(margin+2, h//2+2, w-4, h//2-margin-2)
            
        elif icon_name == 'folder':
            # Icono de carpeta
            painter.drawRect(margin, margin+4, w-2*margin, h-margin-4)
            painter.drawRect(margin, margin+4, w//3, 4)
            
        elif icon_name == 'file' or icon_name == 'document':
            # Icono de archivo
            painter.drawRect(margin+2, margin, w-2*margin-6, h-2*margin)
            painter.drawLine(w-margin-6, margin, w-margin-6, margin+6)
            painter.drawLine(w-margin-6, margin+6, w-margin, margin+6)
            
        elif icon_name == 'print' or icon_name == 'printer':
            # Icono de imprimir
            painter.drawRect(margin+2, margin+4, w-4, h//2)
            painter.drawRect(margin, margin+8, w-2*margin, h//3)
            painter.drawRect(margin+4, h-margin-4, w-8, 4)
            
        elif icon_name == 'export' or icon_name == 'download':
            # Icono de exportar (flecha hacia abajo)
            painter.drawLine(w//2, margin, w//2, h-margin-6)
            painter.drawLine(w//2, h-margin-6, margin+4, h-margin-10)
            painter.drawLine(w//2, h-margin-6, w-margin-4, h-margin-10)
            painter.drawLine(margin, h-margin, w-margin, h-margin)
            
        elif icon_name == 'import' or icon_name == 'upload':
            # Icono de importar (flecha hacia arriba)
            painter.drawLine(w//2, h-margin, w//2, margin+6)
            painter.drawLine(w//2, margin+6, margin+4, margin+10)
            painter.drawLine(w//2, margin+6, w-margin-4, margin+10)
            painter.drawLine(margin, margin, w-margin, margin)
            
        elif icon_name == 'refresh' or icon_name == 'reload':
            # Icono de actualizar (flecha circular)
            center_x, center_y = w//2, h//2
            radius = min(w, h)//3
            painter.drawArc(center_x-radius, center_y-radius, 2*radius, 2*radius, 0, 270*16)
            painter.drawLine(center_x+radius-2, center_y-radius+2, center_x+radius+2, center_y-radius-2)
            painter.drawLine(center_x+radius-2, center_y-radius+2, center_x+radius-6, center_y-radius+2)
            
        elif icon_name == 'close' or icon_name == 'x':
            # Icono de cerrar (X)
            painter.drawLine(margin+2, margin+2, w-margin-2, h-margin-2)
            painter.drawLine(w-margin-2, margin+2, margin+2, h-margin-2)
            
        elif icon_name == 'check' or icon_name == 'ok':
            # Icono de check (✓)
            painter.drawLine(margin+2, h//2, w//2-2, h-margin-4)
            painter.drawLine(w//2-2, h-margin-4, w-margin-2, margin+4)
            
        elif icon_name == 'warning' or icon_name == 'alert':
            # Icono de advertencia (triángulo)
            painter.drawLine(w//2, margin, margin, h-margin)
            painter.drawLine(w//2, margin, w-margin, h-margin)
            painter.drawLine(margin, h-margin, w-margin, h-margin)
            painter.drawEllipse(w//2-2, h-margin-8, 4, 4)
            
        elif icon_name == 'info' or icon_name == 'information':
            # Icono de información (i)
            painter.drawEllipse(w//2-2, margin+2, 4, 4)
            painter.drawLine(w//2, margin+8, w//2, h-margin-2)
            
        elif icon_name == 'chart' or icon_name == 'graph':
            # Icono de gráfico
            painter.drawRect(margin, margin, w-2*margin, h-2*margin)
            painter.drawRect(margin+4, h//2, 4, h//2-margin)
            painter.drawRect(margin+10, h//3, 4, 2*h//3-margin)
            painter.drawRect(margin+16, h//4, 4, 3*h//4-margin)
            
        else:
            # Icono por defecto (cuadrado)
            painter.drawRect(margin+4, margin+4, w-8, h-8)
    
    def get_standard_icons(self):
        """Obtiene un diccionario con iconos estándar."""
        return {
            'add': self.get_icon('add', 'success'),
            'edit': self.get_icon('edit', 'primary'),
            'delete': self.get_icon('delete', 'danger'),
            'save': self.get_icon('save', 'primary'),
            'search': self.get_icon('search', 'info'),
            'settings': self.get_icon('settings', 'secondary'),
            'home': self.get_icon('home', 'primary'),
            'user': self.get_icon('user', 'secondary'),
            'folder': self.get_icon('folder', 'warning'),
            'file': self.get_icon('file', 'info'),
            'print': self.get_icon('print', 'secondary'),
            'export': self.get_icon('export', 'success'),
            'import': self.get_icon('import', 'info'),
            'refresh': self.get_icon('refresh', 'primary'),
            'close': self.get_icon('close', 'danger'),
            'check': self.get_icon('check', 'success'),
            'warning': self.get_icon('warning', 'warning'),
            'info': self.get_icon('info', 'info'),
            'chart': self.get_icon('chart', 'primary')
        }
    
    def set_icon_size(self, size):
        """Establece el tamaño por defecto de los iconos."""
        self.icon_size = size
        self.icon_cache.clear()  # Limpiar cache para regenerar con nuevo tamaño
    
    def clear_cache(self):
        """Limpia el cache de iconos."""
        self.icon_cache.clear()


# Instancia global del gestor de iconos
icon_manager = IconManager()
