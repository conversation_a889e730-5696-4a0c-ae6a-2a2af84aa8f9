"""
<PERSON><PERSON><PERSON><PERSON> que define el diálogo para gestionar cristales.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QLineEdit, QTableWidget, QTableWidgetItem, QMessageBox, QHeaderView,
    QDialogButtonBox, QFormLayout, QDoubleSpinBox, QCheckBox, QComboBox, QSpinBox
)
from PyQt6.QtCore import Qt, pyqtSlot
from PyQt6.QtGui import QIcon

from models.base import get_db
from models.cristal import Cristal

class CristalDialog(QDialog):
    """Diálogo para gestionar cristales."""
    
    def __init__(self, parent=None):
        """
        Inicializa el diálogo de gestión de cristales.
        
        Args:
            parent: Widget padre
        """
        super().__init__(parent)
        self.setWindowTitle("Gestión de Cristales")
        self.setMinimumSize(900, 600)
        
        # Variables
        self.cristal_actual = None
        
        # Configurar interfaz
        self._configurar_ui()
        
        # Cargar datos
        self._cargar_cristales()
    
    def _configurar_ui(self):
        """Configura la interfaz de usuario del diálogo."""
        # Layout principal
        layout_principal = QVBoxLayout(self)
        
        # Barra de herramientas
        layout_botones = QHBoxLayout()
        
        self.boton_nuevo = QPushButton("Nuevo")
        self.boton_editar = QPushButton("Editar")
        self.boton_eliminar = QPushButton("Eliminar")
        self.boton_actualizar = QPushButton("Actualizar")
        
        # Deshabilitar botones hasta que se seleccione un cristal
        self.boton_editar.setEnabled(False)
        self.boton_eliminar.setEnabled(False)
        
        layout_botones.addWidget(self.boton_nuevo)
        layout_botones.addWidget(self.boton_editar)
        layout_botones.addWidget(self.boton_eliminar)
        layout_botones.addStretch()
        layout_botones.addWidget(self.boton_actualizar)
        
        # Tabla de cristales
        self.tabla_cristales = QTableWidget()
        self.tabla_cristales.setColumnCount(7)
        self.tabla_cristales.setHorizontalHeaderLabels([
            "Código", "Descripción", "Espesor (mm)", "Tipo", "Precio/m² (€)", "Stock", "Activo"
        ])
        
        # Ajustar el ancho de las columnas
        header = self.tabla_cristales.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # Código
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Descripción
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Espesor
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Tipo
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Precio
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # Stock
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # Activo
        
        # Conectar señales
        self.boton_nuevo.clicked.connect(self._on_nuevo_cristal)
        self.boton_editar.clicked.connect(self._on_editar_cristal)
        self.boton_eliminar.clicked.connect(self._on_eliminar_cristal)
        self.boton_actualizar.clicked.connect(self._cargar_cristales)
        self.tabla_cristales.itemSelectionChanged.connect(self._on_seleccion_cambiada)
        self.tabla_cristales.doubleClicked.connect(self._on_editar_cristal)
        
        # Añadir widgets al layout principal
        layout_principal.addLayout(layout_botones)
        layout_principal.addWidget(self.tabla_cristales)
        
        # Botones de diálogo
        botones_dialogo = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        botones_dialogo.rejected.connect(self.reject)
        layout_principal.addWidget(botones_dialogo)
    
    def _cargar_cristales(self):
        """Carga la lista de cristales desde la base de datos."""
        db = next(get_db())
        
        try:
            # Obtener todos los cristales
            cristales = db.query(Cristal).order_by(Cristal.codigo).all()
            
            # Configurar la tabla
            self.tabla_cristales.setRowCount(len(cristales))
            
            for fila, cristal in enumerate(cristales):
                # Código
                self.tabla_cristales.setItem(fila, 0, QTableWidgetItem(cristal.codigo))
                
                # Descripción
                self.tabla_cristales.setItem(fila, 1, QTableWidgetItem(cristal.descripcion))
                
                # Espesor
                espesor_item = QTableWidgetItem(f"{cristal.espesor:.1f}")
                espesor_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.tabla_cristales.setItem(fila, 2, espesor_item)
                
                # Tipo
                self.tabla_cristales.setItem(fila, 3, QTableWidgetItem(cristal.tipo or ""))
                
                # Precio
                precio_item = QTableWidgetItem(f"{cristal.precio_metro_cuadrado:.2f}")
                precio_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.tabla_cristales.setItem(fila, 4, precio_item)
                
                # Stock
                stock_item = QTableWidgetItem(str(cristal.stock))
                stock_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.tabla_cristales.setItem(fila, 5, stock_item)
                
                # Activo
                activo_item = QTableWidgetItem()
                activo_item.setFlags(Qt.ItemFlag.ItemIsUserCheckable | Qt.ItemFlag.ItemIsEnabled)
                activo_item.setCheckState(
                    Qt.CheckState.Checked if cristal.activo else Qt.CheckState.Unchecked
                )
                activo_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.tabla_cristales.setItem(fila, 6, activo_item)
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudieron cargar los cristales: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        finally:
            db.close()
    
    def _on_seleccion_cambiada(self):
        """Maneja el evento de cambio de selección en la tabla."""
        seleccion = self.tabla_cristales.selectedItems()
        habilitar = len(seleccion) > 0
        
        self.boton_editar.setEnabled(habilitar)
        self.boton_eliminar.setEnabled(habilitar)
    
    def _on_nuevo_cristal(self):
        """Maneja el evento de crear un nuevo cristal."""
        dialogo = CristalEditarDialog(self)
        if dialogo.exec() == QDialog.DialogCode.Accepted:
            # Guardar el nuevo cristal
            datos = dialogo.get_datos()
            db = next(get_db())

            try:
                nuevo_cristal = Cristal(**datos)
                db.add(nuevo_cristal)
                db.commit()

                QMessageBox.information(
                    self,
                    "Éxito",
                    f"Cristal '{datos['codigo']}' creado correctamente.",
                    QMessageBox.StandardButton.Ok
                )

                self._cargar_cristales()

            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self,
                    "Error",
                    f"No se pudo crear el cristal: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )
            finally:
                db.close()
    
    def _on_editar_cristal(self):
        """Maneja el evento de editar un cristal existente."""
        fila = self.tabla_cristales.currentRow()
        if fila < 0:
            return

        codigo = self.tabla_cristales.item(fila, 0).text()
        db = next(get_db())

        try:
            cristal = db.query(Cristal).filter(Cristal.codigo == codigo).first()
            if not cristal:
                raise ValueError("Cristal no encontrado")

            dialogo = CristalEditarDialog(self, cristal)
            if dialogo.exec() == QDialog.DialogCode.Accepted:
                # Actualizar el cristal existente
                datos = dialogo.get_datos()

                cristal.descripcion = datos['descripcion']
                cristal.espesor = datos['espesor']
                cristal.tipo = datos['tipo']
                cristal.precio_metro_cuadrado = datos['precio_metro_cuadrado']
                cristal.stock = datos['stock']
                cristal.activo = datos['activo']

                db.commit()

                QMessageBox.information(
                    self,
                    "Éxito",
                    f"Cristal '{cristal.codigo}' actualizado correctamente.",
                    QMessageBox.StandardButton.Ok
                )

                self._cargar_cristales()

        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudo actualizar el cristal: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        finally:
            db.close()
    
    def _on_eliminar_cristal(self):
        """Maneja el evento de eliminar un cristal."""
        fila = self.tabla_cristales.currentRow()
        if fila < 0:
            return
        
        codigo = self.tabla_cristales.item(fila, 0).text()
        descripcion = self.tabla_cristales.item(fila, 1).text()
        
        respuesta = QMessageBox.question(
            self,
            "Confirmar eliminación",
            f"¿Está seguro de que desea eliminar el cristal '{codigo} - {descripcion}'?\n"
            "Esta acción no se puede deshacer.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if respuesta == QMessageBox.StandardButton.Yes:
            db = next(get_db())
            try:
                cristal = db.query(Cristal).filter(Cristal.codigo == codigo).first()
                if cristal:
                    db.delete(cristal)
                    db.commit()
                    self._cargar_cristales()
            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self,
                    "Error",
                    f"No se pudo eliminar el cristal: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )
            finally:
                db.close()


class CristalEditarDialog(QDialog):
    """Diálogo para editar o crear un cristal."""
    
    TIPOS_CRISTAL = [
        "Claro", "Templado", "Laminado", "Reflectivo", 
        "Mateado", "Serigrafiado", "Doble Acristalamiento"
    ]
    
    def __init__(self, parent=None, cristal=None):
        """
        Inicializa el diálogo de edición de cristal.
        
        Args:
            parent: Widget padre
            cristal: Instancia de Cristal a editar (None para nuevo)
        """
        super().__init__(parent)
        
        self.cristal = cristal
        self.setWindowTitle("Editar Cristal" if cristal else "Nuevo Cristal")
        self.setMinimumWidth(450)
        
        # Configurar interfaz
        self._configurar_ui()
        
        # Si se está editando un cristal, cargar sus datos
        if cristal:
            self._cargar_datos_cristal()
    
    def _configurar_ui(self):
        """Configura la interfaz de usuario del diálogo."""
        # Layout principal
        layout_principal = QVBoxLayout(self)
        
        # Formulario
        layout_formulario = QFormLayout()
        layout_formulario.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.AllNonFixedFieldsGrow)
        
        # Código
        self.codigo = QLineEdit()
        self.codigo.setMaxLength(10)
        layout_formulario.addRow("Código*:", self.codigo)
        
        # Descripción
        self.descripcion = QLineEdit()
        self.descripcion.setMaxLength(100)
        layout_formulario.addRow("Descripción*:", self.descripcion)
        
        # Espesor
        self.espesor = QDoubleSpinBox()
        self.espesor.setRange(0.1, 50.0)
        self.espesor.setDecimals(1)
        self.espesor.setSuffix(" mm")
        self.espesor.setValue(4.0)
        layout_formulario.addRow("Espesor* (mm):", self.espesor)
        
        # Tipo
        self.tipo = QComboBox()
        self.tipo.addItem("", "")  # Opción vacía
        self.tipo.addItems(self.TIPOS_CRISTAL)
        layout_formulario.addRow("Tipo:", self.tipo)
        
        # Precio por metro cuadrado
        self.precio_metro2 = QDoubleSpinBox()
        self.precio_metro2.setRange(0, 10000)
        self.precio_metro2.setDecimals(2)
        self.precio_metro2.setPrefix("€ ")
        self.precio_metro2.setValue(0)
        layout_formulario.addRow("Precio por m²*:", self.precio_metro2)
        
        # Stock
        self.stock = QSpinBox()
        self.stock.setRange(0, 1000000)
        self.stock.setValue(0)
        layout_formulario.addRow("Stock:", self.stock)
        
        # Activo
        self.activo = QCheckBox("Cristal activo")
        self.activo.setChecked(True)
        layout_formulario.addRow("", self.activo)
        
        # Validación
        self.etiqueta_error = QLabel()
        self.etiqueta_error.setStyleSheet("color: red;")
        self.etiqueta_error.setVisible(False)
        
        # Botones
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel
        )
        botones.accepted.connect(self._validar_y_aceptar)
        botones.rejected.connect(self.reject)
        
        # Añadir widgets al layout principal
        layout_principal.addLayout(layout_formulario)
        layout_principal.addWidget(self.etiqueta_error)
        layout_principal.addWidget(botones)
        
        # Conectar señales para validación en tiempo real
        self.codigo.textChanged.connect(self._validar_formulario)
        self.descripcion.textChanged.connect(self._validar_formulario)
    
    def _cargar_datos_cristal(self):
        """Carga los datos del cristal en el formulario."""
        if not self.cristal:
            return
            
        self.codigo.setText(self.cristal.codigo)
        self.codigo.setReadOnly(True)  # No permitir editar el código
        self.descripcion.setText(self.cristal.descripcion)
        self.espesor.setValue(float(self.cristal.espesor))
        
        # Establecer el tipo de cristal
        index = self.tipo.findText(self.cristal.tipo or "")
        if index >= 0:
            self.tipo.setCurrentIndex(index)
        
        self.precio_metro2.setValue(float(self.cristal.precio_metro_cuadrado))
        self.stock.setValue(int(self.cristal.stock or 0))
        self.activo.setChecked(bool(self.cristal.activo))
    
    def _validar_formulario(self):
        """Valida los campos del formulario."""
        codigo = self.codigo.text().strip()
        descripcion = self.descripcion.text().strip()
        
        if not codigo:
            self.mostrar_error("El código es obligatorio")
            return False
            
        if not descripcion:
            self.mostrar_error("La descripción es obligatoria")
            return False
            
        if self.espesor.value() <= 0:
            self.mostrar_error("El espesor debe ser mayor que cero")
            return False
            
        if self.precio_metro2.value() < 0:
            self.mostrar_error("El precio no puede ser negativo")
            return False
            
        self.ocultar_error()
        return True
    
    def mostrar_error(self, mensaje):
        """Muestra un mensaje de error en el formulario."""
        self.etiqueta_error.setText(mensaje)
        self.etiqueta_error.setVisible(True)
    
    def ocultar_error(self):
        """Oculta el mensaje de error."""
        self.etiqueta_error.setVisible(False)
    
    def _validar_y_aceptar(self):
        """Valida el formulario y acepta el diálogo si es válido."""
        if not self._validar_formulario():
            return
            
        # Validar que el código no esté duplicado (solo para nuevos cristales)
        if not self.cristal:
            db = next(get_db())
            try:
                existe = db.query(Cristal).filter(Cristal.codigo == self.codigo.text().strip()).first()
                if existe:
                    self.mostrar_error("Ya existe un cristal con este código")
                    return
            finally:
                db.close()
        
        self.accept()
    
    def get_datos(self):
        """
        Devuelve un diccionario con los datos del formulario.
        
        Returns:
            dict: Datos del cristal
        """
        return {
            'codigo': self.codigo.text().strip(),
            'descripcion': self.descripcion.text().strip(),
            'espesor': float(self.espesor.value()),
            'tipo': self.tipo.currentText() or None,
            'precio_metro_cuadrado': float(self.precio_metro2.value()),
            'stock': int(self.stock.value() or 0),
            'activo': self.activo.isChecked()
        }
