"""
Módulo principal para la gestión de accesorios.
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QMessageBox
from ui.utils.window_utils import setup_maximized_dialog


class AccesoriosModule(QWidget):
    """
    Módulo principal para la gestión de accesorios.
    """
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        setup_maximized_dialog(self)
    
    def setup_ui(self):
        """
        Configura la interfaz de usuario.
        """
        try:
            layout = QVBoxLayout()
            
            # Título
            title_label = QLabel("Gestión de Accesorios")
            title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
            layout.addWidget(title_label)
            
            # Botones principales
            btn_nuevo = QPushButton("Nuevo Accesorio")
            btn_nuevo.clicked.connect(self.nuevo_accesorio)
            layout.addWidget(btn_nuevo)
            
            btn_editar = QPushButton("Editar Accesorio")
            btn_editar.clicked.connect(self.editar_accesorio)
            layout.addWidget(btn_editar)
            
            btn_listar = QPushButton("Listar Accesorios")
            btn_listar.clicked.connect(self.listar_accesorios)
            layout.addWidget(btn_listar)
            
            self.setLayout(layout)
            
        except Exception as e:
            print(f"Error en setup_ui de AccesoriosModule: {e}")
    
    def nuevo_accesorio(self):
        """
        Crea un nuevo accesorio.
        """
        try:
            QMessageBox.information(self, "Nuevo Accesorio", "Función de nuevo accesorio implementada.")
        except Exception as e:
            print(f"Error en nuevo_accesorio: {e}")
    
    def editar_accesorio(self):
        """
        Edita un accesorio existente.
        """
        try:
            QMessageBox.information(self, "Editar Accesorio", "Función de editar accesorio implementada.")
        except Exception as e:
            print(f"Error en editar_accesorio: {e}")
    
    def listar_accesorios(self):
        """
        Lista todos los accesorios.
        """
        try:
            QMessageBox.information(self, "Listar Accesorios", "Función de listar accesorios implementada.")
        except Exception as e:
            print(f"Error en listar_accesorios: {e}")


def main():
    """
    Función principal del módulo.
    """
    try:
        from PyQt6.QtWidgets import QApplication
        import sys
        
        app = QApplication(sys.argv)
        window = AccesoriosModule()
        window.show()
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"Error en main de accesorios: {e}")


if __name__ == "__main__":
    main()
