"""
Sistema Profesional de Diseño de Artículos V2
Interfaz completamente rediseñada, simple y funcional.
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QSpinBox, QDoubleSpinBox, QComboBox, QGroupBox, QFrame,
    QScrollArea, QTextEdit, QSplitter, QFormLayout
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPalette


class SistemaProfesionalV2(QWidget):
    """Sistema profesional completamente rediseñado."""
    
    articulo_creado = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.datos_articulo = {}
        self._crear_interfaz()
    
    def _crear_interfaz(self):
        """Crea la interfaz principal."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Splitter principal
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # Panel de configuración (izquierda)
        self._crear_panel_configuracion(splitter)
        
        # Panel de vista previa (derecha)
        self._crear_panel_vista_previa(splitter)
        
        # Configurar proporciones
        splitter.setSizes([400, 600])
    
    def _crear_panel_configuracion(self, splitter):
        """Crea el panel de configuración del artículo."""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: white;
                border-right: 1px solid #e0e0e0;
            }
        """)
        
        # Scroll area para el contenido
        scroll = QScrollArea()
        scroll.setWidget(panel)
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Título
        titulo = QLabel("Configuración del Artículo")
        titulo.setFont(QFont("Segoe UI", 18, QFont.Weight.Bold))
        titulo.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(titulo)
        
        # Sección: Dimensiones
        self._crear_seccion_dimensiones(layout)
        
        # Sección: Tipo de Apertura
        self._crear_seccion_apertura(layout)
        
        # Sección: Perfiles
        self._crear_seccion_perfiles(layout)
        
        # Sección: Cristales
        self._crear_seccion_cristales(layout)
        
        # Sección: Herrajes
        self._crear_seccion_herrajes(layout)
        
        # Botones de acción
        self._crear_botones_accion(layout)
        
        # Espaciador
        layout.addStretch()
        
        splitter.addWidget(scroll)
    
    def _crear_seccion_dimensiones(self, layout):
        """Crea la sección de dimensiones."""
        grupo = QGroupBox("📏 Dimensiones")
        grupo.setStyleSheet(self._estilo_grupo())
        form_layout = QFormLayout(grupo)
        
        # Ancho
        self.spin_ancho = QSpinBox()
        self.spin_ancho.setRange(300, 3000)
        self.spin_ancho.setValue(1200)
        self.spin_ancho.setSuffix(" mm")
        self.spin_ancho.setStyleSheet(self._estilo_input())
        form_layout.addRow("Ancho:", self.spin_ancho)
        
        # Alto
        self.spin_alto = QSpinBox()
        self.spin_alto.setRange(300, 3000)
        self.spin_alto.setValue(1500)
        self.spin_alto.setSuffix(" mm")
        self.spin_alto.setStyleSheet(self._estilo_input())
        form_layout.addRow("Alto:", self.spin_alto)
        
        # Grosor del marco
        self.spin_grosor = QSpinBox()
        self.spin_grosor.setRange(50, 200)
        self.spin_grosor.setValue(70)
        self.spin_grosor.setSuffix(" mm")
        self.spin_grosor.setStyleSheet(self._estilo_input())
        form_layout.addRow("Grosor Marco:", self.spin_grosor)
        
        layout.addWidget(grupo)
    
    def _crear_seccion_apertura(self, layout):
        """Crea la sección de tipo de apertura."""
        grupo = QGroupBox("🚪 Tipo de Apertura")
        grupo.setStyleSheet(self._estilo_grupo())
        form_layout = QFormLayout(grupo)
        
        # Tipo de apertura
        self.combo_apertura = QComboBox()
        self.combo_apertura.addItems([
            "Fijo",
            "Practicable",
            "Oscilobatiente", 
            "Corredera",
            "Plegable"
        ])
        self.combo_apertura.setCurrentText("Practicable")
        self.combo_apertura.setStyleSheet(self._estilo_input())
        form_layout.addRow("Tipo:", self.combo_apertura)
        
        # Número de hojas
        self.spin_hojas = QSpinBox()
        self.spin_hojas.setRange(1, 4)
        self.spin_hojas.setValue(2)
        self.spin_hojas.setStyleSheet(self._estilo_input())
        form_layout.addRow("Número de Hojas:", self.spin_hojas)
        
        layout.addWidget(grupo)
    
    def _crear_seccion_perfiles(self, layout):
        """Crea la sección de perfiles."""
        grupo = QGroupBox("🔧 Perfiles")
        grupo.setStyleSheet(self._estilo_grupo())
        form_layout = QFormLayout(grupo)
        
        # Material
        self.combo_material = QComboBox()
        self.combo_material.addItems(["Aluminio", "PVC", "Madera"])
        self.combo_material.setStyleSheet(self._estilo_input())
        form_layout.addRow("Material:", self.combo_material)
        
        # Serie
        self.combo_serie = QComboBox()
        self.combo_serie.addItems(["Serie 70", "Serie 80", "Serie 90"])
        self.combo_serie.setStyleSheet(self._estilo_input())
        form_layout.addRow("Serie:", self.combo_serie)
        
        # Color
        self.combo_color = QComboBox()
        self.combo_color.addItems([
            "Blanco", "Gris Antracita", "Marrón", 
            "Negro", "Imitación Madera"
        ])
        self.combo_color.setStyleSheet(self._estilo_input())
        form_layout.addRow("Color:", self.combo_color)
        
        layout.addWidget(grupo)
    
    def _crear_seccion_cristales(self, layout):
        """Crea la sección de cristales."""
        grupo = QGroupBox("🔷 Cristales")
        grupo.setStyleSheet(self._estilo_grupo())
        form_layout = QFormLayout(grupo)
        
        # Tipo de cristal
        self.combo_cristal = QComboBox()
        self.combo_cristal.addItems([
            "Simple 4mm",
            "Doble 4+12+4",
            "Doble 6+12+6", 
            "Triple 4+12+4+12+4",
            "Bajo Emisivo"
        ])
        self.combo_cristal.setCurrentText("Doble 4+12+4")
        self.combo_cristal.setStyleSheet(self._estilo_input())
        form_layout.addRow("Tipo:", self.combo_cristal)
        
        layout.addWidget(grupo)
    
    def _crear_seccion_herrajes(self, layout):
        """Crea la sección de herrajes."""
        grupo = QGroupBox("🔩 Herrajes")
        grupo.setStyleSheet(self._estilo_grupo())
        form_layout = QFormLayout(grupo)
        
        # Tipo de herraje
        self.combo_herraje = QComboBox()
        self.combo_herraje.addItems([
            "Estándar",
            "Microventilación",
            "Seguridad Reforzada",
            "Motorizado"
        ])
        self.combo_herraje.setStyleSheet(self._estilo_input())
        form_layout.addRow("Tipo:", self.combo_herraje)
        
        layout.addWidget(grupo)
    
    def _crear_botones_accion(self, layout):
        """Crea los botones de acción."""
        frame_botones = QFrame()
        layout_botones = QHBoxLayout(frame_botones)
        
        # Botón Generar
        btn_generar = QPushButton("🏗️ Generar Artículo")
        btn_generar.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        btn_generar.clicked.connect(self._generar_articulo)
        layout_botones.addWidget(btn_generar)
        
        # Botón Limpiar
        btn_limpiar = QPushButton("🗑️ Limpiar")
        btn_limpiar.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        btn_limpiar.clicked.connect(self._limpiar_formulario)
        layout_botones.addWidget(btn_limpiar)
        
        layout.addWidget(frame_botones)
    
    def _crear_panel_vista_previa(self, splitter):
        """Crea el panel de vista previa."""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #e0e0e0;
            }
        """)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Título
        titulo = QLabel("Vista Previa del Artículo")
        titulo.setFont(QFont("Segoe UI", 16, QFont.Weight.Bold))
        titulo.setStyleSheet("color: #2c3e50; margin-bottom: 15px;")
        layout.addWidget(titulo)
        
        # Área de vista previa
        self.area_vista_previa = QTextEdit()
        self.area_vista_previa.setReadOnly(True)
        self.area_vista_previa.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 15px;
                font-family: 'Courier New';
                font-size: 12px;
            }
        """)
        self.area_vista_previa.setPlainText("Configura los parámetros del artículo para ver la vista previa...")
        layout.addWidget(self.area_vista_previa)
        
        splitter.addWidget(panel)
    
    def _estilo_grupo(self):
        """Retorna el estilo para grupos."""
        return """
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
            }
        """
    
    def _estilo_input(self):
        """Retorna el estilo para inputs."""
        return """
            QSpinBox, QDoubleSpinBox, QComboBox {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
                border-color: #3498db;
            }
        """
    
    def _generar_articulo(self):
        """Genera el artículo con los parámetros configurados."""
        # Recopilar datos
        datos = {
            'ancho': self.spin_ancho.value(),
            'alto': self.spin_alto.value(),
            'grosor_marco': self.spin_grosor.value(),
            'tipo_apertura': self.combo_apertura.currentText(),
            'num_hojas': self.spin_hojas.value(),
            'material': self.combo_material.currentText(),
            'serie': self.combo_serie.currentText(),
            'color': self.combo_color.currentText(),
            'cristal': self.combo_cristal.currentText(),
            'herraje': self.combo_herraje.currentText()
        }
        
        # Actualizar vista previa
        self._actualizar_vista_previa(datos)
        
        # Emitir señal
        self.articulo_creado.emit(datos)
    
    def _actualizar_vista_previa(self, datos):
        """Actualiza la vista previa con los datos del artículo."""
        texto = f"""
ARTÍCULO GENERADO
================

DIMENSIONES:
- Ancho: {datos['ancho']} mm
- Alto: {datos['alto']} mm
- Grosor Marco: {datos['grosor_marco']} mm

CONFIGURACIÓN:
- Tipo de Apertura: {datos['tipo_apertura']}
- Número de Hojas: {datos['num_hojas']}
- Material: {datos['material']}
- Serie: {datos['serie']}
- Color: {datos['color']}

CRISTALES:
- Tipo: {datos['cristal']}

HERRAJES:
- Tipo: {datos['herraje']}

RESUMEN:
Artículo de {datos['material']} {datos['serie']} de {datos['ancho']}x{datos['alto']}mm
con apertura {datos['tipo_apertura']} de {datos['num_hojas']} hoja(s).
Cristal {datos['cristal']} y herrajes {datos['herraje']}.
Color: {datos['color']}.
        """
        
        self.area_vista_previa.setPlainText(texto.strip())
    
    def _limpiar_formulario(self):
        """Limpia el formulario."""
        self.spin_ancho.setValue(1200)
        self.spin_alto.setValue(1500)
        self.spin_grosor.setValue(70)
        self.combo_apertura.setCurrentIndex(0)
        self.spin_hojas.setValue(2)
        self.combo_material.setCurrentIndex(0)
        self.combo_serie.setCurrentIndex(0)
        self.combo_color.setCurrentIndex(0)
        self.combo_cristal.setCurrentIndex(1)
        self.combo_herraje.setCurrentIndex(0)
        
        self.area_vista_previa.setPlainText("Configura los parámetros del artículo para ver la vista previa...")
