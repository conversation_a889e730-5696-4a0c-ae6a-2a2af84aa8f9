"""
Migración para actualizar el módulo de perfiles a la versión 2.0
Agrega las nuevas tablas y columnas para distribuidores, series y tipos de perfiles
"""

import sqlite3
import os
from pathlib import Path

def get_db_path():
    """Obtiene la ruta de la base de datos."""
    # Buscar el archivo de base de datos
    possible_paths = [
        "data/pro2000.db",
        "../data/pro2000.db",
        "pro2000.db"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    # Si no existe, crear en data/
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    return "data/pro2000.db"

def ejecutar_migracion():
    """Ejecuta la migración de la base de datos."""
    db_path = get_db_path()
    print(f"Conectando a la base de datos: {db_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("Iniciando migración de perfiles v2.0...")
        
        # 1. Crear tabla de distribuidores
        print("Creando tabla distribuidores...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS distribuidores (
                id INTEGER PRIMARY KEY,
                codigo VARCHAR(10) UNIQUE NOT NULL,
                nombre VARCHAR(100) NOT NULL,
                contacto VARCHAR(100),
                telefono VARCHAR(20),
                email VARCHAR(100),
                direccion TEXT,
                activo BOOLEAN DEFAULT 1,
                fecha_creacion DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 2. Crear tabla de series de perfiles
        print("Creando tabla series_perfiles...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS series_perfiles (
                id INTEGER PRIMARY KEY,
                codigo VARCHAR(20) UNIQUE NOT NULL,
                nombre VARCHAR(100) NOT NULL,
                descripcion TEXT,
                activo BOOLEAN DEFAULT 1,
                fecha_creacion DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 3. Crear tabla de tipos de perfiles
        print("Creando tabla tipos_perfiles...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tipos_perfiles (
                id INTEGER PRIMARY KEY,
                codigo VARCHAR(20) UNIQUE NOT NULL,
                nombre VARCHAR(100) NOT NULL,
                descripcion TEXT,
                categoria VARCHAR(50),
                activo BOOLEAN DEFAULT 1,
                fecha_creacion DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 4. Verificar si las columnas ya existen en la tabla perfiles
        cursor.execute("PRAGMA table_info(perfiles)")
        columnas_existentes = [row[1] for row in cursor.fetchall()]
        
        # 5. Agregar nuevas columnas a la tabla perfiles si no existen
        nuevas_columnas = [
            ("distribuidor_id", "INTEGER"),
            ("serie_id", "INTEGER"),
            ("tipo_id", "INTEGER"),
            ("material", "VARCHAR(20) DEFAULT 'Aluminio'"),
            ("color", "VARCHAR(50)"),
            ("acabado", "VARCHAR(50)"),
            ("ancho", "FLOAT"),
            ("alto", "FLOAT"),
            ("espesor", "FLOAT"),
            ("peso_metro", "FLOAT"),
            ("precio_compra", "FLOAT DEFAULT 0.0"),
            ("margen_beneficio", "FLOAT DEFAULT 0.0"),
            ("observaciones", "TEXT"),
            ("codigo_barras", "VARCHAR(50)"),
            ("stock_minimo", "INTEGER DEFAULT 0"),
            ("unidad_medida", "VARCHAR(10) DEFAULT 'm'"),
            ("fecha_creacion", "DATETIME"),
            ("fecha_modificacion", "DATETIME")
        ]
        
        for columna, tipo in nuevas_columnas:
            if columna not in columnas_existentes:
                print(f"Agregando columna {columna}...")
                cursor.execute(f"ALTER TABLE perfiles ADD COLUMN {columna} {tipo}")
        
        # 6. Actualizar la longitud de la columna descripcion si es necesario
        print("Verificando longitud de columna descripcion...")
        # SQLite no permite modificar el tipo de columna directamente, pero 200 caracteres debería ser suficiente
        
        # 7. Insertar datos de ejemplo para distribuidores
        print("Insertando distribuidores de ejemplo...")
        distribuidores_ejemplo = [
            ("CORTIZO", "Cortizo", "Departamento Comercial", "981234567", "<EMAIL>", "Padrón, A Coruña"),
            ("TECHNAL", "Technal", "Servicio Técnico", "914567890", "<EMAIL>", "Madrid"),
            ("REYNAERS", "Reynaers", "Atención al Cliente", "932345678", "<EMAIL>", "Barcelona"),
            ("KOEMMERLING", "Kömmerling", "Soporte Técnico", "963456789", "<EMAIL>", "Valencia"),
            ("VEKA", "Veka", "Departamento Técnico", "954567890", "<EMAIL>", "Sevilla")
        ]
        
        for codigo, nombre, contacto, telefono, email, direccion in distribuidores_ejemplo:
            cursor.execute("""
                INSERT OR IGNORE INTO distribuidores (codigo, nombre, contacto, telefono, email, direccion)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (codigo, nombre, contacto, telefono, email, direccion))
        
        # 8. Insertar series de ejemplo
        print("Insertando series de ejemplo...")
        series_ejemplo = [
            ("COR-70", "Cortizo COR-70", "Serie de ventanas y puertas de 70mm"),
            ("COR-80", "Cortizo COR-80", "Serie de ventanas y puertas de 80mm"),
            ("SOLEAL", "Technal Soleal", "Serie de ventanas de altas prestaciones"),
            ("LUMEAL", "Technal Lumeal", "Serie de ventanas con rotura de puente térmico"),
            ("MASTERLINE", "Reynaers MasterLine", "Serie premium de ventanas y puertas"),
            ("CONCEPT", "Reynaers ConCept", "Serie de sistemas de fachadas"),
            ("76MD", "Kömmerling 76MD", "Serie de ventanas de PVC de 76mm"),
            ("88MD", "Kömmerling 88MD", "Serie de ventanas de PVC de 88mm")
        ]
        
        for codigo, nombre, descripcion in series_ejemplo:
            cursor.execute("""
                INSERT OR IGNORE INTO series_perfiles (codigo, nombre, descripcion)
                VALUES (?, ?, ?)
            """, (codigo, nombre, descripcion))
        
        # 9. Insertar tipos de ejemplo
        print("Insertando tipos de ejemplo...")
        tipos_ejemplo = [
            ("MARCO", "Marco", "Perfil principal del marco", "marco"),
            ("HOJA", "Hoja", "Perfil de la hoja móvil", "hoja"),
            ("INVERSOR", "Inversor", "Perfil inversor para cambio de dirección", "inversor"),
            ("TRAVESANO", "Travesaño", "Perfil horizontal divisorio", "travesaño"),
            ("JUNQUILLO", "Junquillo", "Perfil para sujeción de vidrio", "junquillo"),
            ("ALARGADERA", "Alargadera", "Perfil de extensión", "alargadera"),
            ("GUIA", "Guía", "Perfil guía para corredera", "guia"),
            ("UNION", "Unión", "Perfil de unión entre elementos", "union"),
            ("JAMBA", "Jamba", "Perfil lateral del marco", "jamba"),
            ("DINTEL", "Dintel", "Perfil superior del marco", "dintel")
        ]
        
        for codigo, nombre, descripcion, categoria in tipos_ejemplo:
            cursor.execute("""
                INSERT OR IGNORE INTO tipos_perfiles (codigo, nombre, descripcion, categoria)
                VALUES (?, ?, ?, ?)
            """, (codigo, nombre, descripcion, categoria))
        
        # 10. Actualizar perfiles existentes con valores por defecto
        print("Actualizando perfiles existentes...")
        cursor.execute("""
            UPDATE perfiles
            SET material = 'Aluminio',
                unidad_medida = 'm',
                fecha_creacion = CURRENT_TIMESTAMP,
                fecha_modificacion = CURRENT_TIMESTAMP
            WHERE material IS NULL
        """)

        # 11. Asegurar que los valores booleanos sean correctos
        print("Corrigiendo valores booleanos...")
        cursor.execute("UPDATE distribuidores SET activo = 1 WHERE activo IS NULL")
        cursor.execute("UPDATE series_perfiles SET activo = 1 WHERE activo IS NULL")
        cursor.execute("UPDATE tipos_perfiles SET activo = 1 WHERE activo IS NULL")
        cursor.execute("UPDATE perfiles SET activo = 1 WHERE activo IS NULL")
        
        # Confirmar cambios
        conn.commit()
        print("✅ Migración completada exitosamente!")
        
        # Mostrar estadísticas
        cursor.execute("SELECT COUNT(*) FROM distribuidores")
        count_dist = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM series_perfiles")
        count_series = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM tipos_perfiles")
        count_tipos = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM perfiles")
        count_perfiles = cursor.fetchone()[0]
        
        print(f"\n📊 Estadísticas de la base de datos:")
        print(f"   • Distribuidores: {count_dist}")
        print(f"   • Series: {count_series}")
        print(f"   • Tipos: {count_tipos}")
        print(f"   • Perfiles: {count_perfiles}")
        
    except Exception as e:
        print(f"❌ Error durante la migración: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    print("🔄 Iniciando migración de perfiles v2.0...")
    ejecutar_migracion()
    print("🎉 Migración completada!")
