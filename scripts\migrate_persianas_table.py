"""
Script para migrar la estructura de la tabla 'persianas' al nuevo esquema.
"""
import os
import sys
import sqlite3
from sqlite3 import Error

def create_connection(db_file):
    """Crear una conexión a la base de datos SQLite."""
    conn = None
    try:
        conn = sqlite3.connect(db_file)
        conn.row_factory = sqlite3.Row  # Para acceder a las columnas por nombre
        print(f"Conexión exitosa a SQLite versión {sqlite3.version}")
        return conn
    except Error as e:
        print(f"Error al conectar a la base de datos: {e}")
        return None

def backup_table(conn, table_name, backup_name):
    """Crear una copia de seguridad de una tabla."""
    try:
        cursor = conn.cursor()
        
        # Verificar si ya existe la tabla de respaldo
        cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name=?", (backup_name,))
        if cursor.fetchone():
            print(f"La tabla de respaldo {backup_name} ya existe. No se realizará la copia de seguridad.")
            return False
            
        # Crear tabla de respaldo con los mismos datos
        cursor.execute(f"CREATE TABLE {backup_name} AS SELECT * FROM {table_name}")
        conn.commit()
        print(f"Copia de seguridad creada: {backup_name}")
        return True
    except Error as e:
        print(f"Error al crear copia de seguridad: {e}")
        conn.rollback()
        return False

def migrate_persianas_table(conn):
    """Migrar la tabla persianas al nuevo esquema."""
    try:
        cursor = conn.cursor()
        
        # 1. Crear tabla temporal con la estructura antigua
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS persianas_old (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            codigo VARCHAR(20) NOT NULL,
            descripcion VARCHAR(200) NOT NULL,
            ancho_mm INTEGER,
            alto_mm INTEGER,
            tipo VARCHAR(50),
            orientacion VARCHAR(20),
            color VARCHAR(50),
            precio FLOAT NOT NULL,
            stock INTEGER,
            activo BOOLEAN,
            UNIQUE (codigo)
        )
        """)
        
        # 2. Copiar datos de la tabla original a la temporal
        cursor.execute("""
        INSERT INTO persianas_old 
        SELECT id, codigo, descripcion, ancho_mm, alto_mm, tipo, 
               orientacion, color, precio, stock, activo 
        FROM persianas
        """)
        
        # 3. Eliminar tabla original
        cursor.execute("DROP TABLE persianas")
        
        # 4. Crear nueva tabla con la estructura actualizada
        cursor.execute("""
        CREATE TABLE persianas (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            referencia TEXT NOT NULL UNIQUE,
            nombre TEXT NOT NULL,
            altura_cajon INTEGER NOT NULL,
            tipo_cajon TEXT NOT NULL,
            tipo_operacion TEXT NOT NULL,
            posicion_mando TEXT,
            color TEXT NOT NULL,
            activo BOOLEAN DEFAULT 1,
            creado_en TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            actualizado_en TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 5. Migrar datos de la tabla temporal a la nueva tabla
        cursor.execute("""
        INSERT INTO persianas (id, referencia, nombre, color, activo, 
                             altura_cajon, tipo_cajon, tipo_operacion)
        SELECT 
            id, 
            codigo, 
            descripcion, 
            COALESCE(color, 'Blanco'), 
            COALESCE(activo, 1),
            200,  -- Valor por defecto para altura_cajon
            'normal',  -- Valor por defecto para tipo_cajon
            'recogedor'  -- Valor por defecto para tipo_operacion
        FROM persianas_old
        """)
        
        # 6. Crear índices
        cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_persianas_referencia ON persianas (referencia);
        """)
        
        cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_persianas_activo ON persianas (activo);
        """)
        
        # 7. Eliminar tabla temporal
        cursor.execute("DROP TABLE persianas_old")
        
        conn.commit()
        print("Migración completada exitosamente.")
        return True
        
    except Error as e:
        print(f"Error durante la migración: {e}")
        conn.rollback()
        return False

def main():
    # Ruta a la base de datos
    db_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'data', 'pro2000.db'))
    
    # Crear conexión
    conn = create_connection(db_path)
    if conn is not None:
        try:
            # Crear copia de seguridad
            backup_table(conn, 'persianas', 'persianas_backup')
            
            # Realizar migración
            if migrate_persianas_table(conn):
                print("La tabla 'persianas' ha sido migrada al nuevo esquema.")
                
                # Verificar migración
                cursor = conn.cursor()
                cursor.execute("PRAGMA table_info(persianas)")
                columns = cursor.fetchall()
                print("\nEstructura de la tabla 'persianas' después de la migración:")
                for col in columns:
                    print(f"- {col[1]}: {col[2]}")
                
        except Error as e:
            print(f"Error durante el proceso de migración: {e}")
        finally:
            conn.close()
            print("Conexión cerrada.")
    else:
        print("No se pudo establecer la conexión a la base de datos.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
