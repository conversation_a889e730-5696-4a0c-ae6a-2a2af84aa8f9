"""
Lienzo profesional para diseño de artículos de carpintería.
Sistema completo de selección de perfiles individuales y configuración profesional.
"""

import math
from PyQt6.QtWidgets import QWidget, QMessageBox
from PyQt6.QtCore import Qt, pyqtSignal, QRectF, QPointF
from PyQt6.QtGui import QPainter, QPen, QBrush, QColor, QFont


class ElementoPerfil:
    """Representa un perfil individual seleccionable en el lienzo."""
    
    def __init__(self, tipo, rect, propiedades=None):
        self.tipo = tipo  # 'marco_superior', 'marco_inferior', 'marco_izquierdo', 'marco_derecho', 'division_v', 'division_h', 'hoja'
        self.rect = rect  # QRectF con la posición y tamaño
        self.propiedades = propiedades or {}
        self.seleccionado = False
        self.hover = False
        
        # Propiedades por defecto según el tipo
        self._establecer_propiedades_defecto()
    
    def _establecer_propiedades_defecto(self):
        """Establece propiedades por defecto según el tipo de perfil."""
        defaults = {
            'marco_superior': {
                'tipo_perfil': 'marco',
                'categoria': 'Marco superior',
                'grosor': 70,
                'material': 'PVC',
                'color': 'Blanco',
                'medida': 1200
            },
            'marco_inferior': {
                'tipo_perfil': 'marco',
                'categoria': 'Marco inferior',
                'grosor': 70,
                'material': 'PVC',
                'color': 'Blanco',
                'medida': 1200
            },
            'marco_izquierdo': {
                'tipo_perfil': 'marco',
                'categoria': 'Marco lateral izquierdo',
                'grosor': 70,
                'material': 'PVC',
                'color': 'Blanco',
                'medida': 1400
            },
            'marco_derecho': {
                'tipo_perfil': 'marco',
                'categoria': 'Marco lateral derecho',
                'grosor': 70,
                'material': 'PVC',
                'color': 'Blanco',
                'medida': 1400
            },
            'division_v': {
                'tipo_perfil': 'travesaño',
                'categoria': 'División vertical',
                'grosor': 50,
                'material': 'PVC',
                'color': 'Blanco',
                'medida': 1400
            },
            'division_h': {
                'tipo_perfil': 'travesaño',
                'categoria': 'División horizontal',
                'grosor': 50,
                'material': 'PVC',
                'color': 'Blanco',
                'medida': 1200
            },
            'hoja': {
                'tipo_perfil': 'hoja',
                'categoria': 'Hoja practicable',
                'grosor': 60,
                'material': 'PVC',
                'color': 'Blanco',
                'tipo_apertura': 'practicable_derecha'
            }
        }
        
        if self.tipo in defaults:
            for key, value in defaults[self.tipo].items():
                if key not in self.propiedades:
                    self.propiedades[key] = value
    
    def contiene_punto(self, punto, tolerancia=5):
        """Verifica si un punto está dentro del elemento con tolerancia."""
        rect_expandido = self.rect.adjusted(-tolerancia, -tolerancia, tolerancia, tolerancia)
        return rect_expandido.contains(punto)


class LienzoArticuloProfesional(QWidget):
    """
    Lienzo profesional para diseño de artículos de carpintería.
    Permite seleccionar y configurar cada perfil individualmente.
    """
    
    # Señales
    elemento_seleccionado = pyqtSignal(object)  # Emite el elemento seleccionado
    articulo_modificado = pyqtSignal()  # Emite cuando se modifica el artículo
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Configuración del widget
        self.setMinimumSize(800, 600)
        self.setMouseTracking(True)
        
        # Propiedades del artículo
        self.ancho_total = 1200  # mm
        self.alto_total = 1400   # mm
        self.grosor_marco = 70   # mm
        
        # Escala de visualización
        self.escala = 0.3
        self.offset_x = 50
        self.offset_y = 50
        
        # Elementos del artículo
        self.elementos = []
        self.elemento_seleccionado_actual = None
        self.elemento_hover = None
        
        # Divisiones
        self.divisiones_verticales = []    # Posiciones X de divisiones verticales
        self.divisiones_horizontales = []  # Posiciones Y de divisiones horizontales
        
        # Hojas
        self.hojas = []  # Lista de hojas configuradas
        
        # Crear elementos iniciales (marco básico)
        self._crear_marco_inicial()
    
    def _crear_marco_inicial(self):
        """Crea los elementos del marco inicial."""
        self.elementos.clear()
        
        # Calcular posiciones en pantalla
        x = self.offset_x
        y = self.offset_y
        ancho_pantalla = self.ancho_total * self.escala
        alto_pantalla = self.alto_total * self.escala
        grosor_pantalla = self.grosor_marco * self.escala
        
        # Marco superior
        rect_superior = QRectF(x, y, ancho_pantalla, grosor_pantalla)
        self.elementos.append(ElementoPerfil('marco_superior', rect_superior))
        
        # Marco inferior
        rect_inferior = QRectF(x, y + alto_pantalla - grosor_pantalla, ancho_pantalla, grosor_pantalla)
        self.elementos.append(ElementoPerfil('marco_inferior', rect_inferior))
        
        # Marco izquierdo
        rect_izquierdo = QRectF(x, y, grosor_pantalla, alto_pantalla)
        self.elementos.append(ElementoPerfil('marco_izquierdo', rect_izquierdo))
        
        # Marco derecho
        rect_derecho = QRectF(x + ancho_pantalla - grosor_pantalla, y, grosor_pantalla, alto_pantalla)
        self.elementos.append(ElementoPerfil('marco_derecho', rect_derecho))
    
    def mousePressEvent(self, event):
        """Maneja los clics del mouse para seleccionar elementos."""
        if event.button() == Qt.MouseButton.LeftButton:
            punto = QPointF(event.position())
            elemento_clickeado = self._encontrar_elemento_en_punto(punto)
            
            if elemento_clickeado:
                self._seleccionar_elemento(elemento_clickeado)
                self._abrir_dialogo_configuracion(elemento_clickeado)
            else:
                self._deseleccionar_todo()
            
            self.update()
    
    def mouseMoveEvent(self, event):
        """Maneja el movimiento del mouse para efectos hover."""
        punto = QPointF(event.position())
        elemento_hover = self._encontrar_elemento_en_punto(punto)
        
        if elemento_hover != self.elemento_hover:
            # Limpiar hover anterior
            if self.elemento_hover:
                self.elemento_hover.hover = False
            
            # Establecer nuevo hover
            self.elemento_hover = elemento_hover
            if self.elemento_hover:
                self.elemento_hover.hover = True
            
            self.update()
    
    def _encontrar_elemento_en_punto(self, punto):
        """Encuentra el elemento que contiene el punto dado."""
        for elemento in reversed(self.elementos):  # Revisar desde arriba hacia abajo
            if elemento.contiene_punto(punto):
                return elemento
        return None
    
    def _seleccionar_elemento(self, elemento):
        """Selecciona un elemento."""
        # Deseleccionar elemento anterior
        if self.elemento_seleccionado_actual:
            self.elemento_seleccionado_actual.seleccionado = False
        
        # Seleccionar nuevo elemento
        self.elemento_seleccionado_actual = elemento
        elemento.seleccionado = True
        
        # Emitir señal
        self.elemento_seleccionado.emit(elemento)
    
    def _deseleccionar_todo(self):
        """Deselecciona todos los elementos."""
        if self.elemento_seleccionado_actual:
            self.elemento_seleccionado_actual.seleccionado = False
            self.elemento_seleccionado_actual = None
    
    def _abrir_dialogo_configuracion(self, elemento):
        """Abre el diálogo de configuración profesional para el elemento."""
        # Importación dinámica para evitar ciclos de importación
        from .dialogo_configuracion import DialogoConfiguracionProfesional

        dialogo = DialogoConfiguracionProfesional(elemento, self)
        if dialogo.exec() == dialogo.DialogCode.Accepted:
            # Aplicar cambios al elemento
            nuevas_propiedades = dialogo.obtener_propiedades()
            elemento.propiedades.update(nuevas_propiedades)
            
            # Emitir señal de modificación
            self.articulo_modificado.emit()
            self.update()
    
    def paintEvent(self, event):
        """Dibuja el lienzo profesional minimalista."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Fondo limpio y profesional
        painter.fillRect(self.rect(), QColor(255, 255, 255))

        # Solo mostrar mensaje de bienvenida si no hay elementos
        if not self.elementos:
            self._dibujar_area_trabajo_vacia(painter)

        # Dibujar elementos del usuario
        for elemento in self.elementos:
            self._dibujar_elemento(painter, elemento)

        # Dibujar información del elemento seleccionado
        if self.elemento_seleccionado_actual:
            self._dibujar_info_elemento(painter, self.elemento_seleccionado_actual)

    def _dibujar_area_trabajo_vacia(self, painter):
        """Dibuja el área de trabajo cuando está vacía."""
        # Texto de bienvenida centrado
        painter.setPen(QPen(QColor(150, 150, 150)))
        painter.setFont(QFont("Segoe UI", 16))

        texto_principal = "Área de Diseño"
        rect_texto = painter.fontMetrics().boundingRect(texto_principal)
        x = (self.width() - rect_texto.width()) // 2
        y = (self.height() - rect_texto.height()) // 2 - 20

        painter.drawText(x, y, texto_principal)

        # Subtexto
        painter.setFont(QFont("Segoe UI", 10))
        subtexto = "Selecciona herramientas del panel izquierdo para comenzar"
        rect_subtexto = painter.fontMetrics().boundingRect(subtexto)
        x_sub = (self.width() - rect_subtexto.width()) // 2
        y_sub = y + 40

        painter.setPen(QPen(QColor(180, 180, 180)))
        painter.drawText(x_sub, y_sub, subtexto)

        # Línea decorativa sutil
        painter.setPen(QPen(QColor(220, 220, 220), 1))
        linea_y = y + 20
        painter.drawLine(
            self.width() // 2 - 100, linea_y,
            self.width() // 2 + 100, linea_y
        )



    def _dibujar_elemento(self, painter, elemento):
        """Dibuja un elemento individual."""
        # Colores según estado
        if elemento.seleccionado:
            color_borde = QColor("#0078d4")
            color_relleno = QColor("#e3f2fd")
            grosor_borde = 3
        elif elemento.hover:
            color_borde = QColor("#106ebe")
            color_relleno = QColor("#f0f8ff")
            grosor_borde = 2
        else:
            color_borde = QColor("#666666")
            color_relleno = QColor("#ffffff")
            grosor_borde = 1
        
        # Dibujar elemento
        painter.setPen(QPen(color_borde, grosor_borde))
        painter.setBrush(QBrush(color_relleno))
        painter.drawRect(elemento.rect)
        
        # Etiqueta del elemento
        painter.setPen(QPen(QColor("#333333")))
        painter.setFont(QFont("Arial", 8))
        
        texto = elemento.propiedades.get('categoria', elemento.tipo)
        rect_texto = elemento.rect.adjusted(2, 2, -2, -2)
        painter.drawText(rect_texto, Qt.AlignmentFlag.AlignCenter, texto)
    
    def _dibujar_info_elemento(self, painter, elemento):
        """Dibuja información del elemento seleccionado."""
        info_lines = [
            f"Tipo: {elemento.propiedades.get('categoria', 'N/A')}",
            f"Material: {elemento.propiedades.get('material', 'N/A')}",
            f"Grosor: {elemento.propiedades.get('grosor', 'N/A')} mm",
            f"Medida: {elemento.propiedades.get('medida', 'N/A')} mm"
        ]
        
        # Fondo para la información
        x_info = 10
        y_info = self.height() - 100
        ancho_info = 250
        alto_info = 80
        
        painter.setPen(QPen(QColor("#cccccc")))
        painter.setBrush(QBrush(QColor("#ffffff")))
        painter.drawRect(x_info, y_info, ancho_info, alto_info)
        
        # Texto de información
        painter.setPen(QPen(QColor("#333333")))
        painter.setFont(QFont("Arial", 9))
        
        for i, line in enumerate(info_lines):
            painter.drawText(x_info + 10, y_info + 20 + i * 15, line)
