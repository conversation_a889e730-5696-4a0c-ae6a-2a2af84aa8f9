"""
Diálogos para agregar/editar componentes de artículos.
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLineEdit, QLabel,
    QFormLayout, QDialogButtonBox, QComboBox, QDoubleSpinBox, QSpinBox,
    QMessageBox, QGroupBox, QTextEdit
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

from models.base import get_db
from models.perfil import Perfil
from models.cristal import Cristal
from models.accesorio import Accesorio
from models.persiana import Persiana
from models.articulo import ArticuloPerfil, ArticuloAccesorio, ArticuloCristal


class ComponentePerfilDialog(QDialog):
    """Diálogo para agregar/editar un perfil en un artículo."""
    
    def __init__(self, parent=None, articulo=None, componente=None):
        super().__init__(parent)
        self.articulo = articulo
        self.componente = componente  # Para edición
        
        self.setWindowTitle("Agregar Perfil al Artículo" if not componente else "Editar Perfil")
        self.setModal(True)
        
        # ✅ CONFIGURACIÓN SIMPLE Y EFECTIVA
        from ui.utils.window_utils import setup_maximized_dialog
        force_dialog_maximized(self, self.windowTitle())
        
        self._inicializar_ui()
        self._cargar_perfiles()
        
        if componente:
            self._cargar_datos_componente()
    
    def _inicializar_ui(self):
        """Configura la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)
        
        # Título
        titulo = QLabel("Configuración del Perfil")
        titulo.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout_principal.addWidget(titulo)
        
        # Formulario
        grupo_datos = QGroupBox("Datos del Componente")
        layout_datos = QFormLayout(grupo_datos)
        
        # Perfil
        self.combo_perfil = QComboBox()
        layout_datos.addRow("Perfil*:", self.combo_perfil)
        
        # Medida (fórmula)
        self.campo_medida = QLineEdit()
        self.campo_medida.setPlaceholderText("Ej: H-25, A+10, (H+A)/2")
        layout_datos.addRow("Fórmula de Medida:", self.campo_medida)
        
        # Condición
        self.campo_condicion = QLineEdit()
        self.campo_condicion.setPlaceholderText("Ej: H>1000, tipo='practicable'")
        layout_datos.addRow("Condición:", self.campo_condicion)
        
        # Cantidad base
        self.campo_cantidad_base = QDoubleSpinBox()
        self.campo_cantidad_base.setRange(0, 999.99)
        self.campo_cantidad_base.setDecimals(2)
        self.campo_cantidad_base.setValue(1.0)
        layout_datos.addRow("Cantidad Base:", self.campo_cantidad_base)
        
        # Fórmula de cantidad
        self.campo_cantidad_formula = QLineEdit()
        self.campo_cantidad_formula.setPlaceholderText("Ej: 1, 2, IF(H>1500,3,2)")
        layout_datos.addRow("Fórmula Cantidad:", self.campo_cantidad_formula)
        
        # Orden
        self.campo_orden = QSpinBox()
        self.campo_orden.setRange(0, 999)
        self.campo_orden.setValue(1)
        layout_datos.addRow("Orden:", self.campo_orden)
        
        layout_principal.addWidget(grupo_datos)
        
        # Ayuda
        grupo_ayuda = QGroupBox("Ayuda - Variables Disponibles")
        layout_ayuda = QVBoxLayout(grupo_ayuda)
        
        ayuda_text = QLabel(
            "Variables: H (altura), A (anchura), L (largo), P (perímetro), S (superficie)\n"
            "Operadores: +, -, *, /, (), >, <, >=, <=, ==, !=, AND, OR\n"
            "Funciones: IF(cond,si,no), MIN(a,b), MAX(a,b), ROUND(x), ABS(x)\n"
            "Ejemplos: H-25, A+10, (H-50)/2, IF(H>1000,3,2)"
        )
        ayuda_text.setWordWrap(True)
        ayuda_text.setStyleSheet("color: #666; font-size: 10px;")
        layout_ayuda.addWidget(ayuda_text)
        
        layout_principal.addWidget(grupo_ayuda)
        
        # Botones
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        botones.accepted.connect(self._aceptar)
        botones.rejected.connect(self.reject)
        layout_principal.addWidget(botones)
    
    def _cargar_perfiles(self):
        """Carga los perfiles disponibles."""
        db = next(get_db())
        try:
            perfiles = db.query(Perfil).filter(Perfil.activo == True).order_by(Perfil.codigo).all()
            
            self.combo_perfil.clear()
            for perfil in perfiles:
                self.combo_perfil.addItem(f"{perfil.codigo} - {perfil.descripcion}", perfil.id)
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al cargar perfiles: {str(e)}")
        finally:
            db.close()
    
    def _cargar_datos_componente(self):
        """Carga los datos del componente para edición."""
        if not self.componente:
            return
        
        # Buscar el perfil en el combo
        for i in range(self.combo_perfil.count()):
            if self.combo_perfil.itemData(i) == self.componente.perfil_id:
                self.combo_perfil.setCurrentIndex(i)
                break
        
        self.campo_medida.setText(self.componente.medida_formula or "")
        self.campo_condicion.setText(self.componente.condicion or "")
        self.campo_cantidad_base.setValue(self.componente.cantidad_base or 1.0)
        self.campo_cantidad_formula.setText(self.componente.cantidad_formula or "")
        self.campo_orden.setValue(self.componente.orden or 1)
    
    def _aceptar(self):
        """Valida y guarda el componente."""
        if self.combo_perfil.currentData() is None:
            QMessageBox.warning(self, "Error", "Debe seleccionar un perfil.")
            return
        
        if self._guardar_componente():
            self.accept()
    
    def _guardar_componente(self):
        """Guarda el componente en la base de datos."""
        db = next(get_db())
        
        try:
            if self.componente:
                # Editar componente existente
                componente_db = db.query(ArticuloPerfil).filter(
                    ArticuloPerfil.id == self.componente.id
                ).first()
                if not componente_db:
                    QMessageBox.critical(self, "Error", "No se encontró el componente a editar.")
                    return False
            else:
                # Crear nuevo componente
                componente_db = ArticuloPerfil()
                componente_db.articulo_id = self.articulo.id
                db.add(componente_db)
            
            # Asignar valores
            componente_db.perfil_id = self.combo_perfil.currentData()
            componente_db.medida_formula = self.campo_medida.text().strip() or None
            componente_db.condicion = self.campo_condicion.text().strip() or None
            componente_db.cantidad_base = self.campo_cantidad_base.value()
            componente_db.cantidad_formula = self.campo_cantidad_formula.text().strip() or None
            componente_db.orden = self.campo_orden.value()
            
            db.commit()
            
            QMessageBox.information(
                self,
                "Éxito",
                "Componente guardado correctamente."
            )
            
            return True
            
        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudo guardar el componente: {str(e)}"
            )
            return False
        finally:
            db.close()


class ComponenteAccesorioDialog(QDialog):
    """Diálogo para agregar/editar un accesorio en un artículo."""

    def __init__(self, parent=None, articulo=None, componente=None):
        super().__init__(parent)
        self.articulo = articulo
        self.componente = componente

        self.setWindowTitle("Agregar Accesorio al Artículo" if not componente else "Editar Accesorio")
        self.setModal(True)
        
        # ✅ CONFIGURACIÓN SIMPLE Y EFECTIVA
        from ui.utils.window_utils import setup_maximized_dialog
        force_dialog_maximized(self, self.windowTitle())

        self._inicializar_ui()
        self._cargar_accesorios()

        if componente:
            self._cargar_datos_componente()

    def _inicializar_ui(self):
        """Configura la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)

        # Título
        titulo = QLabel("Configuración del Accesorio")
        titulo.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout_principal.addWidget(titulo)

        # Formulario
        grupo_datos = QGroupBox("Datos del Componente")
        layout_datos = QFormLayout(grupo_datos)

        # Accesorio
        self.combo_accesorio = QComboBox()
        layout_datos.addRow("Accesorio*:", self.combo_accesorio)

        # Condición
        self.campo_condicion = QLineEdit()
        self.campo_condicion.setPlaceholderText("Ej: H>1000, tipo='oscilobatiente'")
        layout_datos.addRow("Condición:", self.campo_condicion)

        # Cantidad base
        self.campo_cantidad_base = QDoubleSpinBox()
        self.campo_cantidad_base.setRange(0, 999.99)
        self.campo_cantidad_base.setDecimals(2)
        self.campo_cantidad_base.setValue(1.0)
        layout_datos.addRow("Cantidad Base:", self.campo_cantidad_base)

        # Fórmula de cantidad
        self.campo_cantidad_formula = QLineEdit()
        self.campo_cantidad_formula.setPlaceholderText("Ej: 1, 2, IF(H>1500,3,2)")
        layout_datos.addRow("Fórmula Cantidad:", self.campo_cantidad_formula)

        # Orden
        self.campo_orden = QSpinBox()
        self.campo_orden.setRange(0, 999)
        self.campo_orden.setValue(1)
        layout_datos.addRow("Orden:", self.campo_orden)

        layout_principal.addWidget(grupo_datos)

        # Ayuda
        grupo_ayuda = QGroupBox("Ayuda - Variables Disponibles")
        layout_ayuda = QVBoxLayout(grupo_ayuda)

        ayuda_text = QLabel(
            "Variables: H (altura), A (anchura), L (largo), P (perímetro), S (superficie)\n"
            "Funciones: IF(cond,si,no), MIN(a,b), MAX(a,b), ROUND(x)\n"
            "Ejemplos: IF(H>1500,2,1), ROUND(P/500), MIN(H,A)/100"
        )
        ayuda_text.setWordWrap(True)
        ayuda_text.setStyleSheet("color: #666; font-size: 10px;")
        layout_ayuda.addWidget(ayuda_text)

        layout_principal.addWidget(grupo_ayuda)

        # Botones
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        botones.accepted.connect(self._aceptar)
        botones.rejected.connect(self.reject)
        layout_principal.addWidget(botones)

    def _cargar_accesorios(self):
        """Carga los accesorios disponibles."""
        db = next(get_db())
        try:
            accesorios = db.query(Accesorio).filter(Accesorio.activo == True).order_by(Accesorio.codigo).all()

            self.combo_accesorio.clear()
            for accesorio in accesorios:
                self.combo_accesorio.addItem(f"{accesorio.codigo} - {accesorio.descripcion}", accesorio.id)

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al cargar accesorios: {str(e)}")
        finally:
            db.close()

    def _cargar_datos_componente(self):
        """Carga los datos del componente para edición."""
        if not self.componente:
            return

        # Buscar el accesorio en el combo
        for i in range(self.combo_accesorio.count()):
            if self.combo_accesorio.itemData(i) == self.componente.accesorio_id:
                self.combo_accesorio.setCurrentIndex(i)
                break

        self.campo_condicion.setText(self.componente.condicion or "")
        self.campo_cantidad_base.setValue(self.componente.cantidad_base or 1.0)
        self.campo_cantidad_formula.setText(self.componente.cantidad_formula or "")
        self.campo_orden.setValue(self.componente.orden or 1)

    def _aceptar(self):
        """Valida y guarda el componente."""
        if self.combo_accesorio.currentData() is None:
            QMessageBox.warning(self, "Error", "Debe seleccionar un accesorio.")
            return

        if self._guardar_componente():
            self.accept()

    def _guardar_componente(self):
        """Guarda el componente en la base de datos."""
        db = next(get_db())

        try:
            if self.componente:
                # Editar componente existente
                componente_db = db.query(ArticuloAccesorio).filter(
                    ArticuloAccesorio.id == self.componente.id
                ).first()
                if not componente_db:
                    QMessageBox.critical(self, "Error", "No se encontró el componente a editar.")
                    return False
            else:
                # Crear nuevo componente
                componente_db = ArticuloAccesorio()
                componente_db.articulo_id = self.articulo.id
                db.add(componente_db)

            # Asignar valores
            componente_db.accesorio_id = self.combo_accesorio.currentData()
            componente_db.condicion = self.campo_condicion.text().strip() or None
            componente_db.cantidad_base = self.campo_cantidad_base.value()
            componente_db.cantidad_formula = self.campo_cantidad_formula.text().strip() or None
            componente_db.orden = self.campo_orden.value()

            db.commit()

            QMessageBox.information(self, "Éxito", "Componente guardado correctamente.")
            return True

        except Exception as e:
            db.rollback()
            QMessageBox.critical(self, "Error", f"No se pudo guardar el componente: {str(e)}")
            return False
        finally:
            db.close()


class ComponenteCristalDialog(QDialog):
    """Diálogo para agregar/editar un cristal en un artículo."""

    def __init__(self, parent=None, articulo=None, componente=None):
        super().__init__(parent)
        self.articulo = articulo
        self.componente = componente

        self.setWindowTitle("Agregar Cristal al Artículo" if not componente else "Editar Cristal")
        self.setModal(True)
        
        # ✅ CONFIGURACIÓN SIMPLE Y EFECTIVA
        from ui.utils.window_utils import setup_maximized_dialog
        force_dialog_maximized(self, self.windowTitle())

        self._inicializar_ui()
        self._cargar_cristales()

        if componente:
            self._cargar_datos_componente()

    def _inicializar_ui(self):
        """Configura la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)

        # Título
        titulo = QLabel("Configuración del Cristal")
        titulo.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout_principal.addWidget(titulo)

        # Formulario
        grupo_datos = QGroupBox("Datos del Componente")
        layout_datos = QFormLayout(grupo_datos)

        # Cristal
        self.combo_cristal = QComboBox()
        layout_datos.addRow("Cristal*:", self.combo_cristal)

        # Medida (fórmula)
        self.campo_medida = QLineEdit()
        self.campo_medida.setPlaceholderText("Ej: (H-30)*(A-30), H*A, (H-50)*(A-25)")
        layout_datos.addRow("Fórmula de Superficie:", self.campo_medida)

        # Condición
        self.campo_condicion = QLineEdit()
        self.campo_condicion.setPlaceholderText("Ej: cristal_incluido=1, H>500")
        layout_datos.addRow("Condición:", self.campo_condicion)

        # Cantidad base
        self.campo_cantidad_base = QDoubleSpinBox()
        self.campo_cantidad_base.setRange(0, 999.99)
        self.campo_cantidad_base.setDecimals(2)
        self.campo_cantidad_base.setValue(1.0)
        layout_datos.addRow("Cantidad Base:", self.campo_cantidad_base)

        # Fórmula de cantidad
        self.campo_cantidad_formula = QLineEdit()
        self.campo_cantidad_formula.setPlaceholderText("Ej: 1, 2, IF(tipo='doble',2,1)")
        layout_datos.addRow("Fórmula Cantidad:", self.campo_cantidad_formula)

        # Orden
        self.campo_orden = QSpinBox()
        self.campo_orden.setRange(0, 999)
        self.campo_orden.setValue(1)
        layout_datos.addRow("Orden:", self.campo_orden)

        layout_principal.addWidget(grupo_datos)

        # Ayuda
        grupo_ayuda = QGroupBox("Ayuda - Fórmulas de Superficie")
        layout_ayuda = QVBoxLayout(grupo_ayuda)

        ayuda_text = QLabel(
            "Variables: H (altura), A (anchura), L (largo), P (perímetro), S (superficie)\n"
            "Para cristales, la fórmula de medida calcula la superficie en mm²\n"
            "Ejemplos: (H-30)*(A-30) = superficie menos marcos de 30mm\n"
            "          H*A = superficie total\n"
            "          (H-50)*(A-25) = superficie con descuentos específicos"
        )
        ayuda_text.setWordWrap(True)
        ayuda_text.setStyleSheet("color: #666; font-size: 10px;")
        layout_ayuda.addWidget(ayuda_text)

        layout_principal.addWidget(grupo_ayuda)

        # Botones
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        botones.accepted.connect(self._aceptar)
        botones.rejected.connect(self.reject)
        layout_principal.addWidget(botones)

    def _cargar_cristales(self):
        """Carga los cristales disponibles."""
        db = next(get_db())
        try:
            cristales = db.query(Cristal).filter(Cristal.activo == True).order_by(Cristal.codigo).all()

            self.combo_cristal.clear()
            for cristal in cristales:
                self.combo_cristal.addItem(f"{cristal.codigo} - {cristal.descripcion}", cristal.id)

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al cargar cristales: {str(e)}")
        finally:
            db.close()

    def _cargar_datos_componente(self):
        """Carga los datos del componente para edición."""
        if not self.componente:
            return

        # Buscar el cristal en el combo
        for i in range(self.combo_cristal.count()):
            if self.combo_cristal.itemData(i) == self.componente.cristal_id:
                self.combo_cristal.setCurrentIndex(i)
                break

        self.campo_medida.setText(self.componente.medida_formula or "")
        self.campo_condicion.setText(self.componente.condicion or "")
        self.campo_cantidad_base.setValue(self.componente.cantidad_base or 1.0)
        self.campo_cantidad_formula.setText(self.componente.cantidad_formula or "")
        self.campo_orden.setValue(self.componente.orden or 1)

    def _aceptar(self):
        """Valida y guarda el componente."""
        if self.combo_cristal.currentData() is None:
            QMessageBox.warning(self, "Error", "Debe seleccionar un cristal.")
            return

        if self._guardar_componente():
            self.accept()

    def _guardar_componente(self):
        """Guarda el componente en la base de datos."""
        db = next(get_db())

        try:
            if self.componente:
                # Editar componente existente
                componente_db = db.query(ArticuloCristal).filter(
                    ArticuloCristal.id == self.componente.id
                ).first()
                if not componente_db:
                    QMessageBox.critical(self, "Error", "No se encontró el componente a editar.")
                    return False
            else:
                # Crear nuevo componente
                componente_db = ArticuloCristal()
                componente_db.articulo_id = self.articulo.id
                db.add(componente_db)

            # Asignar valores
            componente_db.cristal_id = self.combo_cristal.currentData()
            componente_db.medida_formula = self.campo_medida.text().strip() or None
            componente_db.condicion = self.campo_condicion.text().strip() or None
            componente_db.cantidad_base = self.campo_cantidad_base.value()
            componente_db.cantidad_formula = self.campo_cantidad_formula.text().strip() or None
            componente_db.orden = self.campo_orden.value()

            db.commit()

            QMessageBox.information(self, "Éxito", "Componente guardado correctamente.")
            return True

        except Exception as e:
            db.rollback()
            QMessageBox.critical(self, "Error", f"No se pudo guardar el componente: {str(e)}")
            return False
        finally:
            db.close()
