#!/usr/bin/env python3
"""
Demo final del Sistema Profesional de Diseño de Artículos.
Sistema completamente funcional y limpio.
"""

import sys
import os
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, 
    QPushButton, QHBoxLayout, QMessageBox, QFrame
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

# Añadir el directorio src al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Importar el nuevo sistema profesional
from ui.widgets.lienzo_profesional.integracion_principal import IntegracionSistemaProfesional


class VentanaDemoProfesional(QMainWindow):
    """
    Ventana de demostración del sistema profesional completamente limpio.
    """
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🏗️ Sistema Profesional de Diseño de Artículos - Demo Final")
        self.setGeometry(100, 100, 1200, 800)
        
        # Widget central
        widget_central = QWidget()
        self.setCentralWidget(widget_central)
        
        # Layout principal
        layout = QVBoxLayout(widget_central)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Título principal
        titulo = QLabel("🏗️ Sistema Profesional de Diseño de Artículos")
        titulo.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        titulo.setStyleSheet("""
            QLabel {
                color: #0078d4; 
                padding: 15px; 
                text-align: center;
                background-color: #f8f9fa;
                border-radius: 10px;
                border: 2px solid #0078d4;
            }
        """)
        titulo.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(titulo)
        
        # Información del sistema
        info_sistema = QLabel(
            "✅ Sistema completamente migrado y funcional\n"
            "🔧 Selección individual de perfiles (marcos, divisiones, hojas)\n"
            "📋 Configuración técnica completa con base de datos real\n"
            "💰 Cálculo automático de costes y generación de artículos\n"
            "🎯 100% profesional - Compatible con software comercial"
        )
        info_sistema.setStyleSheet("""
            QLabel {
                background-color: #e8f5e8; 
                color: #2d5a2d; 
                padding: 20px; 
                border-radius: 8px;
                font-size: 14px;
                line-height: 1.5;
            }
        """)
        info_sistema.setWordWrap(True)
        layout.addWidget(info_sistema)
        
        # Barra de acciones
        barra_acciones = self._crear_barra_acciones()
        layout.addWidget(barra_acciones)
        
        # Separador
        separador = QFrame()
        separador.setFrameShape(QFrame.Shape.HLine)
        separador.setFrameShadow(QFrame.Shadow.Sunken)
        separador.setStyleSheet("color: #ddd;")
        layout.addWidget(separador)
        
        # Sistema profesional integrado
        try:
            self.sistema_profesional = IntegracionSistemaProfesional()
            layout.addWidget(self.sistema_profesional)
            
            # Conectar señales del sistema
            if hasattr(self.sistema_profesional, 'lienzo_profesional'):
                lienzo = self.sistema_profesional.lienzo_profesional
                if hasattr(lienzo, 'elemento_seleccionado'):
                    lienzo.elemento_seleccionado.connect(self._elemento_seleccionado)
                if hasattr(lienzo, 'elemento_configurado'):
                    lienzo.elemento_configurado.connect(self._elemento_configurado)
                    
        except Exception as e:
            error_widget = QLabel(f"❌ Error al cargar el sistema profesional:\n{e}")
            error_widget.setStyleSheet("""
                QLabel {
                    color: #d32f2f; 
                    background-color: #ffebee; 
                    padding: 20px; 
                    border-radius: 8px;
                    border: 2px solid #f44336;
                    font-size: 14px;
                }
            """)
            layout.addWidget(error_widget)
            self.sistema_profesional = None
        
        # Barra de estado
        self.label_estado = QLabel("✅ Sistema profesional cargado - Listo para diseñar artículos")
        self.label_estado.setStyleSheet("""
            QLabel {
                background-color: #e3f2fd; 
                color: #1565c0; 
                padding: 10px; 
                border-radius: 5px;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.label_estado)
    
    def _crear_barra_acciones(self):
        """Crea la barra de acciones principales."""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa; 
                border-radius: 8px; 
                padding: 10px;
                border: 1px solid #dee2e6;
            }
        """)
        layout = QHBoxLayout(frame)
        
        # Botón nuevo artículo
        btn_nuevo = QPushButton("📄 Nuevo Artículo")
        btn_nuevo.setToolTip("Crear un nuevo artículo desde cero")
        btn_nuevo.clicked.connect(self._nuevo_articulo)
        layout.addWidget(btn_nuevo)
        
        # Botón cargar plantilla
        btn_plantilla = QPushButton("📋 Cargar Plantilla")
        btn_plantilla.setToolTip("Cargar una plantilla de ventana predefinida")
        btn_plantilla.clicked.connect(self._cargar_plantilla)
        layout.addWidget(btn_plantilla)
        
        # Espaciador
        layout.addStretch()
        
        # Botón información
        btn_info = QPushButton("ℹ️ Información")
        btn_info.setToolTip("Información sobre el sistema profesional")
        btn_info.clicked.connect(self._mostrar_informacion)
        layout.addWidget(btn_info)
        
        return frame
    
    def _elemento_seleccionado(self, elemento):
        """Maneja la selección de un elemento."""
        if elemento:
            categoria = elemento.propiedades.get('categoria', 'Elemento')
            self.label_estado.setText(f"🔧 Configurando: {categoria}")
            self.label_estado.setStyleSheet("""
                QLabel {
                    background-color: #fff3cd; 
                    color: #856404; 
                    padding: 10px; 
                    border-radius: 5px;
                    font-weight: bold;
                }
            """)
        else:
            self.label_estado.setText("✅ Listo para seleccionar elementos")
            self.label_estado.setStyleSheet("""
                QLabel {
                    background-color: #e8f5e8; 
                    color: #2d5a2d; 
                    padding: 10px; 
                    border-radius: 5px;
                    font-weight: bold;
                }
            """)
    
    def _elemento_configurado(self, elemento):
        """Maneja la configuración de un elemento."""
        categoria = elemento.propiedades.get('categoria', 'Elemento')
        self.label_estado.setText(f"✅ {categoria} configurado correctamente")
        self.label_estado.setStyleSheet("""
            QLabel {
                background-color: #e8f5e8; 
                color: #2d5a2d; 
                padding: 10px; 
                border-radius: 5px;
                font-weight: bold;
            }
        """)
    
    def _nuevo_articulo(self):
        """Crea un nuevo artículo."""
        if self.sistema_profesional and hasattr(self.sistema_profesional, 'lienzo_profesional'):
            respuesta = QMessageBox.question(
                self,
                "Nuevo Artículo",
                "¿Desea crear un nuevo artículo?\nSe limpiarán todos los elementos actuales.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if respuesta == QMessageBox.StandardButton.Yes:
                self.sistema_profesional.lienzo_profesional.limpiar_lienzo()
                self.label_estado.setText("📄 Nuevo artículo creado - Configure los elementos")
    
    def _cargar_plantilla(self):
        """Carga una plantilla predefinida."""
        if self.sistema_profesional and hasattr(self.sistema_profesional, 'lienzo_profesional'):
            self.sistema_profesional.lienzo_profesional.cargar_plantilla_ventana()
            self.label_estado.setText("📋 Plantilla cargada - Configure cada elemento")
    
    def _mostrar_informacion(self):
        """Muestra información del sistema."""
        QMessageBox.information(
            self,
            "🏗️ Sistema Profesional",
            "<h2>🏗️ Sistema Profesional de Diseño de Artículos</h2>"
            "<h3>✨ Características principales:</h3>"
            "<ul>"
            "<li><b>🔧 Selección individual:</b> Cada perfil es seleccionable (marcos, divisiones, hojas)</li>"
            "<li><b>📋 Base de datos integrada:</b> Perfiles reales con precios y especificaciones</li>"
            "<li><b>⚙️ Configuración técnica:</b> Medidas, ángulos, materiales, colores</li>"
            "<li><b>💰 Cálculo automático:</b> Costes, tiempos, materiales</li>"
            "<li><b>📄 Generación de artículos:</b> Artículos completos para obras</li>"
            "</ul>"
            "<h3>🚀 Cómo usar:</h3>"
            "<ol>"
            "<li>Haga clic en cualquier elemento del lienzo</li>"
            "<li>Configure el perfil desde la base de datos</li>"
            "<li>Establezca medidas y parámetros técnicos</li>"
            "<li>Genere el artículo completo</li>"
            "</ol>"
            "<p><i>🎯 Sistema 100% profesional para carpintería de PVC y aluminio</i></p>"
        )


def main():
    """Función principal."""
    # Crear aplicación
    app = QApplication(sys.argv)
    app.setApplicationName("Sistema Profesional Demo")
    
    # Configurar estilo profesional
    app.setStyleSheet("""
        QPushButton {
            background-color: #0078d4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
            min-width: 120px;
        }
        
        QPushButton:hover {
            background-color: #106ebe;
        }
        
        QPushButton:pressed {
            background-color: #005a9e;
        }
        
        QMainWindow {
            background-color: #ffffff;
        }
        
        QFrame {
            border: none;
        }
    """)
    
    try:
        # Crear y mostrar ventana
        ventana = VentanaDemoProfesional()
        ventana.show()
        
        # Mensaje de bienvenida
        QMessageBox.information(
            ventana,
            "🎉 Sistema Profesional Cargado",
            "✅ <b>Sistema Profesional de Diseño completamente funcional</b><br><br>"
            "🔧 <b>Funcionalidades disponibles:</b><br>"
            "• Selección individual de perfiles<br>"
            "• Configuración técnica completa<br>"
            "• Base de datos integrada<br>"
            "• Generación de artículos profesionales<br><br>"
            "🎯 <b>Haga clic en cualquier elemento del lienzo para empezar</b>"
        )
        
        # Ejecutar aplicación
        return app.exec()
        
    except Exception as e:
        QMessageBox.critical(
            None,
            "Error",
            f"Error al inicializar la aplicación:\n\n{str(e)}"
        )
        return 1


if __name__ == "__main__":
    sys.exit(main())
