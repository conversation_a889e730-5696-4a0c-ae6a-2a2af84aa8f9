from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.sql import func
from .base import Base

class Usuario(Base):
    """
    Modelo que representa a un usuario en el sistema.
    Corresponde a la tabla 'usuario' en la base de datos.
    """
    __tablename__ = 'usuario'
    
    # Columnas
    id = Column(Integer, primary_key=True, autoincrement=True)
    nombre = Column(String(100), nullable=False, comment='Nombre del usuario')
    tipo = Column(Integer, nullable=False, comment='Tipo de usuario (1=Administrador, 2=Usuario)')
    clave = Column(String(50), nullable=True, comment='Contraseña del usuario (debería estar hasheada)')
    activo = Column(Integer, nullable=False, default=1, comment='Estado del usuario (1=Activo, 0=Inactivo)')
    fecha_creacion = Column(DateTime(timezone=True), server_default=func.now())
    fecha_actualizacion = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relaciones (se agregarán según sea necesario)
    
    def __repr__(self):
        return f"<Usuario(id={self.id}, nombre='{self.nombre}', tipo={self.tipo})>"
    
    def es_administrador(self):
        """Determina si el usuario es administrador"""
        return self.tipo == 1
    
    @classmethod
    def buscar_por_nombre(cls, db, nombre):
        """Busca un usuario por su nombre"""
        return db.query(cls).filter(cls.nombre == nombre).first()
    
    @classmethod
    def autenticar(cls, db, nombre, clave):
        """Autentica un usuario por nombre y clave"""
        # NOTA: En producción, la clave debería estar hasheada
        usuario = cls.buscar_por_nombre(db, nombre)
        if usuario and usuario.clave == clave:
            return usuario
        return None
