"""
Generador de Reportes Profesional para PRO-2000
Genera PDFs profesionales con dibujos técnicos, medidas y optimización de materiales
"""

import os
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

try:
    from reportlab.lib.pagesizes import A4, landscape
    from reportlab.lib.units import mm, cm
    from reportlab.lib.colors import Color, black, blue, red, green
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import (
        SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle,
        PageBreak, Image, Frame, PageTemplate, BaseDocTemplate
    )
    from reportlab.graphics.shapes import Drawing, Line, Rect, Circle, String
    from reportlab.graphics import renderPDF
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("⚠️ ReportLab no disponible. Instale con: pip install reportlab")

from PyQt6.QtWidgets import QMessageBox, QFileDialog
from PyQt6.QtCore import QObject

try:
    from models.base import get_db
    from models.obra import Obra
    from models.articulo import Articulo, ObraArticulo
    from models.perfil import Perfil
    from models.accesorio import Accesorio
    from models.cristal import Cristal
except ImportError as e:
    print(f"⚠️ Error importando modelos: {e}")


class GeneradorReportesProfesional(QObject):
    """Generador de reportes profesionales con dibujos técnicos"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        
        if not REPORTLAB_AVAILABLE:
            QMessageBox.warning(
                parent,
                "ReportLab no disponible",
                "Para generar PDFs profesionales, instale ReportLab:\npip install reportlab"
            )
    
    def generar_informe_taller_completo(self, obra_id: int, opciones: Dict[str, Any] = None) -> str:
        """
        Genera un informe de taller completo con dibujos técnicos
        
        Args:
            obra_id: ID de la obra
            opciones: Diccionario con opciones de generación
            
        Returns:
            str: Ruta del archivo generado
        """
        if not REPORTLAB_AVAILABLE:
            return ""
        
        try:
            # Obtener datos de la obra
            db = next(get_db())
            obra = db.query(Obra).filter(Obra.id == obra_id).first()
            if not obra:
                raise ValueError(f"No se encontró la obra con ID {obra_id}")
            
            # Obtener artículos de la obra
            obra_articulos = db.query(ObraArticulo).filter(
                ObraArticulo.obra_id == obra_id
            ).all()
            
            if not obra_articulos:
                QMessageBox.warning(
                    self.parent,
                    "Sin artículos",
                    "La obra no tiene artículos para generar el informe."
                )
                return ""
            
            # Seleccionar archivo de destino
            nombre_archivo = f"Informe_Taller_{obra.codigo}_{datetime.now().strftime('%Y%m%d_%H%M')}.pdf"
            archivo, _ = QFileDialog.getSaveFileName(
                self.parent,
                "Guardar Informe de Taller",
                nombre_archivo,
                "Archivos PDF (*.pdf)"
            )
            
            if not archivo:
                return ""
            
            # Generar el PDF
            self._generar_pdf_taller(archivo, obra, obra_articulos, opciones or {})
            
            db.close()
            return archivo
            
        except Exception as e:
            QMessageBox.critical(
                self.parent,
                "Error",
                f"Error generando informe de taller: {str(e)}"
            )
            return ""
    
    def _generar_pdf_taller(self, archivo: str, obra, obra_articulos: List, opciones: Dict):
        """Genera el PDF del informe de taller"""
        
        # Configurar documento
        doc = SimpleDocTemplate(
            archivo,
            pagesize=A4,
            leftMargin=15*mm,
            rightMargin=15*mm,
            topMargin=20*mm,
            bottomMargin=20*mm
        )
        
        elementos = []
        estilos = getSampleStyleSheet()
        
        # Estilo personalizado para títulos
        estilo_titulo = ParagraphStyle(
            'TituloPersonalizado',
            parent=estilos['Heading1'],
            fontSize=18,
            spaceAfter=20,
            textColor=blue,
            alignment=TA_CENTER
        )
        
        # Estilo para subtítulos
        estilo_subtitulo = ParagraphStyle(
            'SubtituloPersonalizado',
            parent=estilos['Heading2'],
            fontSize=14,
            spaceAfter=12,
            textColor=black,
            alignment=TA_LEFT
        )
        
        # Portada
        self._agregar_portada_taller(elementos, obra, estilos, estilo_titulo)
        elementos.append(PageBreak())
        
        # Resumen ejecutivo
        self._agregar_resumen_ejecutivo(elementos, obra, obra_articulos, estilos, estilo_subtitulo)
        elementos.append(PageBreak())
        
        # Generar una página por artículo
        for i, obra_articulo in enumerate(obra_articulos):
            self._agregar_pagina_articulo(elementos, obra_articulo, i+1, estilos, estilo_subtitulo)
            if i < len(obra_articulos) - 1:  # No agregar PageBreak en el último
                elementos.append(PageBreak())
        
        # Construir documento
        doc.build(elementos)
    
    def _agregar_portada_taller(self, elementos: List, obra, estilos, estilo_titulo):
        """Agrega la portada del informe de taller"""
        
        # Título principal
        elementos.append(Paragraph("INFORME DE TALLER", estilo_titulo))
        elementos.append(Spacer(1, 20))
        
        # Logo o imagen de empresa (si existe)
        # elementos.append(Image("logo.png", width=100, height=50))  # Opcional
        
        # Información de la obra
        info_obra = f"""
        <b>Obra:</b> {obra.codigo}<br/>
        <b>Nombre:</b> {obra.nombre}<br/>
        <b>Cliente:</b> {obra.cliente.nombre if obra.cliente else 'N/A'}<br/>
        <b>Fecha de generación:</b> {datetime.now().strftime('%d/%m/%Y %H:%M')}<br/>
        <b>Estado:</b> {obra.estado}<br/>
        """
        
        elementos.append(Paragraph(info_obra, estilos['Normal']))
        elementos.append(Spacer(1, 30))
        
        # Descripción del informe
        descripcion = """
        <b>Descripción del Informe:</b><br/>
        Este informe contiene las especificaciones técnicas detalladas para la fabricación 
        de todos los artículos incluidos en la obra. Cada artículo incluye:
        <br/><br/>
        • Dibujo técnico con medidas<br/>
        • Lista detallada de perfiles necesarios<br/>
        • Accesorios requeridos<br/>
        • Cristales especificados<br/>
        • Medidas in-situ colocadas<br/>
        """
        
        elementos.append(Paragraph(descripcion, estilos['Normal']))
    
    def _agregar_resumen_ejecutivo(self, elementos: List, obra, obra_articulos: List, estilos, estilo_subtitulo):
        """Agrega el resumen ejecutivo"""
        
        elementos.append(Paragraph("RESUMEN EJECUTIVO", estilo_subtitulo))
        elementos.append(Spacer(1, 12))
        
        # Estadísticas generales
        total_articulos = len(obra_articulos)
        total_unidades = sum(oa.cantidad for oa in obra_articulos)
        
        resumen = f"""
        <b>Total de artículos diferentes:</b> {total_articulos}<br/>
        <b>Total de unidades a fabricar:</b> {total_unidades}<br/>
        <b>Fecha de inicio estimada:</b> {obra.fecha_inicio.strftime('%d/%m/%Y') if obra.fecha_inicio else 'No definida'}<br/>
        <b>Fecha de entrega estimada:</b> {obra.fecha_fin.strftime('%d/%m/%Y') if obra.fecha_fin else 'No definida'}<br/>
        """
        
        elementos.append(Paragraph(resumen, estilos['Normal']))
        elementos.append(Spacer(1, 20))
        
        # Tabla resumen de artículos
        datos_tabla = [['Artículo', 'Descripción', 'Cantidad', 'Medidas (mm)']]
        
        for oa in obra_articulos:
            datos_tabla.append([
                oa.articulo.codigo,
                oa.articulo.descripcion[:40] + "..." if len(oa.articulo.descripcion) > 40 else oa.articulo.descripcion,
                str(oa.cantidad),
                f"{oa.altura:.0f} x {oa.anchura:.0f}"
            ])
        
        tabla = Table(datos_tabla, colWidths=[40*mm, 80*mm, 25*mm, 35*mm])
        tabla.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), Color(0.8, 0.8, 0.8)),
            ('TEXTCOLOR', (0, 0), (-1, 0), black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), Color(0.95, 0.95, 0.95)),
            ('GRID', (0, 0), (-1, -1), 1, black)
        ]))
        
        elementos.append(tabla)
    
    def _agregar_pagina_articulo(self, elementos: List, obra_articulo, numero_pagina: int, estilos, estilo_subtitulo):
        """Agrega una página completa para un artículo"""
        
        # Título del artículo
        titulo = f"ARTÍCULO {numero_pagina}: {obra_articulo.articulo.codigo}"
        elementos.append(Paragraph(titulo, estilo_subtitulo))
        elementos.append(Spacer(1, 12))
        
        # Información básica del artículo
        info_basica = f"""
        <b>Código:</b> {obra_articulo.articulo.codigo}<br/>
        <b>Descripción:</b> {obra_articulo.articulo.descripcion}<br/>
        <b>Serie:</b> {obra_articulo.articulo.serie or 'N/A'}<br/>
        <b>Cantidad a fabricar:</b> {obra_articulo.cantidad} unidades<br/>
        <b>Medidas:</b> {obra_articulo.altura:.0f} mm (alto) x {obra_articulo.anchura:.0f} mm (ancho)<br/>
        """
        
        elementos.append(Paragraph(info_basica, estilos['Normal']))
        elementos.append(Spacer(1, 15))
        
        # Dibujo técnico con medidas (si hay imagen)
        if obra_articulo.articulo.imagen_path and os.path.exists(obra_articulo.articulo.imagen_path):
            self._agregar_dibujo_tecnico(elementos, obra_articulo)
        else:
            # Dibujo esquemático simple
            self._agregar_dibujo_esquematico(elementos, obra_articulo)
        
        elementos.append(Spacer(1, 15))
        
        # Listas de materiales
        self._agregar_listas_materiales(elementos, obra_articulo, estilos)
    
    def _agregar_dibujo_tecnico(self, elementos: List, obra_articulo):
        """Agrega el dibujo técnico con medidas"""
        try:
            # Cargar imagen del artículo
            imagen = Image(obra_articulo.articulo.imagen_path, width=120*mm, height=80*mm)
            elementos.append(imagen)
            
            # Agregar medidas in-situ si existen
            if obra_articulo.notas:
                try:
                    medidas = json.loads(obra_articulo.notas)
                    if medidas:
                        medidas_texto = "Medidas in-situ colocadas: "
                        for i, medida in enumerate(medidas):
                            medidas_texto += f"{medida.get('texto', '')} ({medida.get('tipo', '')})"
                            if i < len(medidas) - 1:
                                medidas_texto += ", "
                        
                        from reportlab.lib.styles import getSampleStyleSheet
                        estilos = getSampleStyleSheet()
                        elementos.append(Spacer(1, 5))
                        elementos.append(Paragraph(medidas_texto, estilos['Italic']))
                except:
                    pass
                    
        except Exception as e:
            print(f"Error cargando imagen: {e}")
            self._agregar_dibujo_esquematico(elementos, obra_articulo)
    
    def _agregar_dibujo_esquematico(self, elementos: List, obra_articulo):
        """Agrega un dibujo esquemático simple"""
        
        # Crear dibujo vectorial simple
        drawing = Drawing(120*mm, 80*mm)
        
        # Rectángulo principal (marco)
        ancho_dibujo = 100*mm
        alto_dibujo = 60*mm
        x_inicio = 10*mm
        y_inicio = 10*mm
        
        # Marco exterior
        drawing.add(Rect(x_inicio, y_inicio, ancho_dibujo, alto_dibujo, 
                        strokeColor=black, strokeWidth=2, fillColor=None))
        
        # Medidas en el dibujo
        # Medida horizontal (anchura)
        drawing.add(Line(x_inicio, y_inicio-5*mm, x_inicio+ancho_dibujo, y_inicio-5*mm, strokeColor=blue))
        drawing.add(String(x_inicio+ancho_dibujo/2, y_inicio-8*mm, f"{obra_articulo.anchura:.0f} mm", 
                          fontSize=10, textAnchor='middle', fillColor=blue))
        
        # Medida vertical (altura)
        drawing.add(Line(x_inicio-5*mm, y_inicio, x_inicio-5*mm, y_inicio+alto_dibujo, strokeColor=blue))
        drawing.add(String(x_inicio-8*mm, y_inicio+alto_dibujo/2, f"{obra_articulo.altura:.0f} mm", 
                          fontSize=10, textAnchor='middle', fillColor=blue))
        
        elementos.append(drawing)
    
    def _agregar_listas_materiales(self, elementos: List, obra_articulo, estilos):
        """Agrega las listas de materiales necesarios"""
        
        # Obtener componentes del artículo
        db = next(get_db())
        
        try:
            # Perfiles
            perfiles = db.query(Perfil).join(
                'articulo_perfiles'
            ).filter_by(articulo_id=obra_articulo.articulo_id).all()
            
            if perfiles:
                elementos.append(Paragraph("<b>PERFILES NECESARIOS:</b>", estilos['Heading3']))
                
                # Tabla de perfiles
                datos_perfiles = [['Código', 'Descripción', 'Material', 'Longitud', 'Cantidad']]
                
                for perfil in perfiles:
                    # Calcular longitud según el tipo de perfil
                    if 'marco' in perfil.codigo.lower():
                        longitud = obra_articulo.altura if 'vertical' in perfil.descripcion.lower() else obra_articulo.anchura
                    else:
                        longitud = max(obra_articulo.altura, obra_articulo.anchura)  # Estimación
                    
                    datos_perfiles.append([
                        perfil.codigo,
                        perfil.descripcion[:30] + "..." if len(perfil.descripcion) > 30 else perfil.descripcion,
                        perfil.material or 'N/A',
                        f"{longitud:.0f} mm",
                        str(obra_articulo.cantidad * 2)  # Estimación
                    ])
                
                tabla_perfiles = Table(datos_perfiles, colWidths=[25*mm, 60*mm, 25*mm, 25*mm, 20*mm])
                tabla_perfiles.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), Color(0.7, 0.9, 0.7)),
                    ('TEXTCOLOR', (0, 0), (-1, 0), black),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, -1), 8),
                    ('GRID', (0, 0), (-1, -1), 1, black)
                ]))
                
                elementos.append(tabla_perfiles)
                elementos.append(Spacer(1, 10))
            
            # Accesorios
            accesorios = db.query(Accesorio).join(
                'articulo_accesorios'
            ).filter_by(articulo_id=obra_articulo.articulo_id).all()
            
            if accesorios:
                elementos.append(Paragraph("<b>ACCESORIOS NECESARIOS:</b>", estilos['Heading3']))
                
                datos_accesorios = [['Código', 'Descripción', 'Cantidad']]
                
                for accesorio in accesorios:
                    datos_accesorios.append([
                        accesorio.codigo,
                        accesorio.descripcion[:50] + "..." if len(accesorio.descripcion) > 50 else accesorio.descripcion,
                        str(obra_articulo.cantidad)
                    ])
                
                tabla_accesorios = Table(datos_accesorios, colWidths=[30*mm, 80*mm, 25*mm])
                tabla_accesorios.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), Color(0.9, 0.7, 0.7)),
                    ('TEXTCOLOR', (0, 0), (-1, 0), black),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, -1), 8),
                    ('GRID', (0, 0), (-1, -1), 1, black)
                ]))
                
                elementos.append(tabla_accesorios)
                elementos.append(Spacer(1, 10))
            
            # Cristales
            cristales = db.query(Cristal).join(
                'articulo_cristales'
            ).filter_by(articulo_id=obra_articulo.articulo_id).all()
            
            if cristales:
                elementos.append(Paragraph("<b>CRISTALES NECESARIOS:</b>", estilos['Heading3']))
                
                datos_cristales = [['Código', 'Descripción', 'Espesor', 'Medidas']]
                
                for cristal in cristales:
                    # Calcular medidas del cristal (reducir un poco para el marco)
                    ancho_cristal = obra_articulo.anchura - 50  # 50mm menos para el marco
                    alto_cristal = obra_articulo.altura - 50
                    
                    datos_cristales.append([
                        cristal.codigo,
                        cristal.descripcion[:40] + "..." if len(cristal.descripcion) > 40 else cristal.descripcion,
                        f"{cristal.espesor} mm",
                        f"{ancho_cristal:.0f} x {alto_cristal:.0f} mm"
                    ])
                
                tabla_cristales = Table(datos_cristales, colWidths=[25*mm, 60*mm, 20*mm, 30*mm])
                tabla_cristales.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), Color(0.7, 0.7, 0.9)),
                    ('TEXTCOLOR', (0, 0), (-1, 0), black),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, -1), 8),
                    ('GRID', (0, 0), (-1, -1), 1, black)
                ]))
                
                elementos.append(tabla_cristales)
        
        finally:
            db.close()
