"""
Modelos de datos para el sistema de artículos.
"""
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey, Text
import json
from sqlalchemy.orm import relationship
from datetime import datetime
from .base import Base


class Articulo(Base):
    """Modelo para representar un artículo (modelo de ventana/puerta)."""
    
    __tablename__ = 'articulos'
    
    id = Column(Integer, primary_key=True)
    codigo = Column(String(20), unique=True, nullable=False)
    serie = Column(String(50))
    descripcion = Column(String(200), nullable=False)
    tiempo_taller = Column(Float, default=0.0)  # Tiempo en horas
    tiempo_obra = Column(Float, default=0.0)    # Tiempo en horas
    dibujo_path = Column(String(255))           # Ruta al archivo de dibujo
    imagen_path = Column(String(500))           # Ruta al archivo de imagen
    activo = Column(Boolean, default=True)
    fecha_creacion = Column(DateTime, default=datetime.now)
    
    # Relaciones con componentes
    perfiles = relationship("ArticuloPerfil", back_populates="articulo", cascade="all, delete-orphan")
    accesorios = relationship("ArticuloAccesorio", back_populates="articulo", cascade="all, delete-orphan")
    cristales = relationship("ArticuloCristal", back_populates="articulo", cascade="all, delete-orphan")
    # persianas = relationship("ArticuloPersiana", back_populates="articulo", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Articulo(codigo='{self.codigo}', descripcion='{self.descripcion}')>"
    
    def calcular_materiales(self, altura, anchura, altura_1=None, altura_2=None, altura_3=None, altura_4=None,
                           anchura_1=None, anchura_2=None, anchura_3=None, anchura_4=None, diseno_data=None):
        """
        Calcula los materiales necesarios para las medidas dadas.

        Args:
            altura (float): Altura principal (H) en mm
            anchura (float): Anchura principal (A) en mm
            altura_1 (float, optional): Segunda altura (H1) en mm
            altura_2 (float, optional): Tercera altura (H2) en mm
            altura_3 (float, optional): Cuarta altura (H3) en mm
            altura_4 (float, optional): Quinta altura (H4) en mm
            anchura_1 (float, optional): Segunda anchura (A1) en mm
            anchura_2 (float, optional): Tercera anchura (A2) en mm
            anchura_3 (float, optional): Cuarta anchura (A3) en mm
            anchura_4 (float, optional): Quinta anchura (A4) en mm

        Returns:
            dict: Diccionario con los materiales calculados
        """
        from .formula_calculator import FormulaCalculator
        from .base import get_db

        # Crear diccionario con todas las variables de medidas
        variables_medidas = {
            'H': altura,
            'A': anchura,
            'H1': altura_1 or 0,
            'H2': altura_2 or 0,
            'H3': altura_3 or 0,
            'H4': altura_4 or 0,
            'A1': anchura_1 or 0,
            'A2': anchura_2 or 0,
            'A3': anchura_3 or 0,
            'A4': anchura_4 or 0
        }

        calculator = FormulaCalculator(altura, anchura, variables_medidas, diseno_data=diseno_data)
        materiales = {
            'perfiles': [],
            'accesorios': [],
            'cristales': [],
            'persianas': []
        }

        # Obtener una nueva sesión para asegurar que las relaciones estén disponibles
        db = next(get_db())

        try:
            # Recargar el artículo con todas sus relaciones
            articulo_db = db.query(Articulo).filter(Articulo.id == self.id).first()
            if not articulo_db:
                return materiales

            # Calcular perfiles
            for perfil_comp in articulo_db.perfiles:
                cantidad = calculator.evaluar(perfil_comp.cantidad_formula or str(perfil_comp.cantidad_base))
                medida = calculator.evaluar(perfil_comp.medida_formula) if perfil_comp.medida_formula else None

                if calculator.evaluar_condicion(perfil_comp.condicion):
                    metros_totales = (cantidad * medida / 1000) if medida else 0

                    materiales['perfiles'].append({
                        'perfil': perfil_comp.perfil,
                        'cantidad': cantidad,
                        'medida': medida,
                        'metros_totales': metros_totales
                    })

            # Calcular accesorios
            for acc_comp in articulo_db.accesorios:
                cantidad = calculator.evaluar(acc_comp.cantidad_formula or str(acc_comp.cantidad_base))

                if calculator.evaluar_condicion(acc_comp.condicion):
                    materiales['accesorios'].append({
                        'accesorio': acc_comp.accesorio,
                        'cantidad': cantidad
                    })

            # Calcular cristales
            for cristal_comp in articulo_db.cristales:
                cantidad = calculator.evaluar(cristal_comp.cantidad_formula or str(cristal_comp.cantidad_base))
                medida = calculator.evaluar(cristal_comp.medida_formula) if cristal_comp.medida_formula else None

                if calculator.evaluar_condicion(cristal_comp.condicion):
                    metros_cuadrados = (cantidad * medida / 1000000) if medida else 0

                    materiales['cristales'].append({
                        'cristal': cristal_comp.cristal,
                        'cantidad': cantidad,
                        'medida': medida,
                        'metros_cuadrados': metros_cuadrados
                    })

            # Calcular persianas (temporalmente deshabilitado)
            # for persiana_comp in articulo_db.persianas:
            #     cantidad = calculator.evaluar(persiana_comp.cantidad_formula or str(persiana_comp.cantidad_base))
            #     medida = calculator.evaluar(persiana_comp.medida_formula) if persiana_comp.medida_formula else None
            #
            #     if calculator.evaluar_condicion(persiana_comp.condicion):
            #         materiales['persianas'].append({
            #             'persiana': persiana_comp.persiana,
            #             'cantidad': cantidad,
            #             'medida': medida
            #         })

        except Exception as e:
            print(f"Error al calcular materiales: {str(e)}")
        finally:
            db.close()

        return materiales


class ArticuloPerfil(Base):
    """Relación entre artículo y perfil con fórmulas."""
    
    __tablename__ = 'articulo_perfiles'
    
    id = Column(Integer, primary_key=True)
    articulo_id = Column(Integer, ForeignKey('articulos.id'), nullable=False)
    perfil_id = Column(Integer, ForeignKey('perfiles.id'), nullable=False)
    medida_formula = Column(String(100))        # Ej: "H-25", "A+10"
    condicion = Column(String(100))             # Ej: "H>1500", "A<1000"
    cantidad_base = Column(Float, default=1.0)
    cantidad_formula = Column(String(50))       # Ej: "2", "H/500"
    orden = Column(Integer, default=0)

    # Nuevos campos profesionales
    tipo_perfil = Column(String(50), default='marco')  # marco, hoja, inversor, travesaño, junquillo, alargadera, guia, union, jamba
    angulo_izquierdo = Column(Float, default=90.0)     # Ángulo de corte izquierdo en grados
    angulo_derecho = Column(Float, default=90.0)       # Ángulo de corte derecho en grados
    variable_medida = Column(String(10), default='H')  # H, H1, H2, H3, H4, A, A1, A2, A3, A4
    descripcion_posicion = Column(String(100))         # Descripción de la posición del perfil

    # Relaciones
    articulo = relationship("Articulo", back_populates="perfiles")
    perfil = relationship("Perfil")


class ArticuloAccesorio(Base):
    """Relación entre artículo y accesorio con fórmulas."""
    
    __tablename__ = 'articulo_accesorios'
    
    id = Column(Integer, primary_key=True)
    articulo_id = Column(Integer, ForeignKey('articulos.id'), nullable=False)
    accesorio_id = Column(Integer, ForeignKey('accesorios.id'), nullable=False)
    condicion = Column(String(100))             # Ej: "H>1500", "A<1000"
    cantidad_base = Column(Float, default=1.0)
    cantidad_formula = Column(String(50))       # Ej: "2", "IF(H>1500,3,2)"
    orden = Column(Integer, default=0)
    
    # Relaciones
    articulo = relationship("Articulo", back_populates="accesorios")
    accesorio = relationship("Accesorio")


class ArticuloCristal(Base):
    """Relación entre artículo y cristal con fórmulas."""
    
    __tablename__ = 'articulo_cristales'
    
    id = Column(Integer, primary_key=True)
    articulo_id = Column(Integer, ForeignKey('articulos.id'), nullable=False)
    cristal_id = Column(Integer, ForeignKey('cristales.id'), nullable=False)
    medida_formula = Column(String(100))        # Ej: "(H-30)*(A-30)"
    condicion = Column(String(100))             # Ej: "H>500 AND A>500"
    cantidad_base = Column(Float, default=1.0)
    cantidad_formula = Column(String(50))       # Ej: "1", "2"
    orden = Column(Integer, default=0)
    
    # Relaciones
    articulo = relationship("Articulo", back_populates="cristales")
    cristal = relationship("Cristal")


# class ArticuloPersiana(Base):
#     """Relación entre artículo y persiana con fórmulas."""
#
#     __tablename__ = 'articulo_persianas'
#
#     id = Column(Integer, primary_key=True)
#     articulo_id = Column(Integer, ForeignKey('articulos.id'), nullable=False)
#     persiana_id = Column(Integer, ForeignKey('persianas.id'), nullable=False)
#     medida_formula = Column(String(100))        # Ej: "H*A"
#     condicion = Column(String(100))             # Ej: "persiana_incluida=1"
#     cantidad_base = Column(Float, default=1.0)
#     cantidad_formula = Column(String(50))       # Ej: "1"
#     orden = Column(Integer, default=0)
#
#     # Relaciones
#     articulo = relationship("Articulo", back_populates="persianas")
#     persiana = relationship("models.persiana.Persiana", lazy="select")


class FormulaVariable(Base):
    """Variables disponibles para usar en fórmulas."""
    
    __tablename__ = 'formula_variables'
    
    id = Column(Integer, primary_key=True)
    codigo = Column(String(10), unique=True, nullable=False)  # H, A, L, P, S
    descripcion = Column(String(100), nullable=False)
    tipo = Column(String(20), default='medida')  # medida, calculada, condicional
    formula = Column(String(100))  # Para variables calculadas como P=2*H+2*A
    
    def __repr__(self):
        return f"<FormulaVariable(codigo='{self.codigo}', descripcion='{self.descripcion}')>"


class ObraArticulo(Base):
    """Relación entre obra y artículo con medidas específicas."""
    
    __tablename__ = 'obra_articulos'
    
    id = Column(Integer, primary_key=True)
    obra_id = Column(Integer, ForeignKey('obras.id'), nullable=False)
    articulo_id = Column(Integer, ForeignKey('articulos.id'), nullable=False)

    # Medidas principales
    altura = Column(Float, nullable=False)      # Altura principal (H)
    anchura = Column(Float, nullable=False)     # Anchura principal (A)

    # Medidas adicionales para artículos complejos
    altura_1 = Column(Float)                    # H1 - Segunda altura
    altura_2 = Column(Float)                    # H2 - Tercera altura
    altura_3 = Column(Float)                    # H3 - Cuarta altura
    altura_4 = Column(Float)                    # H4 - Quinta altura
    anchura_1 = Column(Float)                   # A1 - Segunda anchura
    anchura_2 = Column(Float)                   # A2 - Tercera anchura
    anchura_3 = Column(Float)                   # A3 - Cuarta anchura
    anchura_4 = Column(Float)                   # A4 - Quinta anchura

    cantidad = Column(Integer, default=1)       # Número de unidades
    precio_unitario = Column(Float)             # Precio calculado por unidad
    precio_total = Column(Float)                # Precio total (unitario * cantidad)
    notas = Column(Text)
    
    # Relaciones
    obra = relationship("Obra")
    articulo = relationship("Articulo")
    
    def calcular_precio(self):
        """
        Calcula el precio basado en los materiales del artículo,
        considerando medidas adicionales y datos de diseño si existen.
        """
        if not self.articulo:
            return 0.0

        # Intentar cargar datos de diseño desde las notas
        diseno_data = None
        if self.notas:
            try:
                # Usamos json.loads para convertir el string JSON a un dict
                datos_guardados = json.loads(self.notas)
                # Verificamos que sea un diccionario y contenga claves esperadas del diseño
                if isinstance(datos_guardados, dict) and 'celdas' in datos_guardados:
                    diseno_data = datos_guardados
            except (json.JSONDecodeError, TypeError):
                # Si las notas no son un JSON válido o no tienen el formato esperado, se ignora.
                pass

        # Llamar a calcular_materiales con todas las medidas y los datos de diseño
        materiales = self.articulo.calcular_materiales(
            self.altura, self.anchura,
            self.altura_1, self.altura_2, self.altura_3, self.altura_4,
            self.anchura_1, self.anchura_2, self.anchura_3, self.anchura_4,
            diseno_data=diseno_data
        )
        
        precio_total = 0.0
        
        # Sumar costes de perfiles
        for material in materiales['perfiles']:
            if material['perfil'] and material['perfil'].precio_metro:
                precio_total += material['metros_totales'] * material['perfil'].precio_metro
        
        # Sumar costes de accesorios
        for material in materiales['accesorios']:
            if material['accesorio'] and material['accesorio'].precio:
                precio_total += material['cantidad'] * material['accesorio'].precio
        
        # Sumar costes de cristales
        for material in materiales['cristales']:
            if material['cristal'] and material['cristal'].precio_metro_cuadrado:
                precio_total += material['metros_cuadrados'] * material['cristal'].precio_metro_cuadrado
        
        # Sumar costes de persianas
        for material in materiales['persianas']:
            if material['persiana'] and material['persiana'].precio:
                precio_total += material['cantidad'] * material['persiana'].precio
        
        self.precio_unitario = precio_total
        self.precio_total = precio_total * self.cantidad
        
        return self.precio_total
