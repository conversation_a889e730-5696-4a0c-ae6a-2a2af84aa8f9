"""
Módulo para la generación de informes en diferentes formatos.
"""
import os
import csv
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Union

from PyQt6.QtWidgets import QFileDialog, QMessageBox
from PyQt6.QtPrintSupport import QPrinter, QPrintDialog
from PyQt6.QtGui import QTextDocument, QTextCursor, QTextCharFormat, QTextBlockFormat, QTextLength, QPageSize, QPageLayout, QPageLayout
from PyQt6.QtCore import Qt, QMarginsF

from models.base import get_db
from models.informe import Informe, LineaInforme

class ReportGenerator:
    """Clase para generar informes en diferentes formatos."""
    
    def __init__(self, parent=None):
        """Inicializa el generador de informes."""
        self.parent = parent
        self.db = next(get_db())
    
    def generate_pdf(self, informe_id: int, output_path: Optional[str] = None) -> str:
        """
        Genera un informe en formato PDF.
        
        Args:
            informe_id: ID del informe a generar
            output_path: Ruta donde guardar el PDF. Si es None, se abrirá un diálogo.
            
        Returns:
            str: Ruta del archivo generado o cadena vacía si se canceló.
        """
        try:
            # Obtener el informe de la base de datos
            informe = self.db.query(Informe).filter(Informe.id == informe_id).first()
            if not informe:
                self._show_error("Error", "No se encontró el informe especificado.")
                return ""
            
            # Si no se proporcionó una ruta, abrir diálogo para guardar
            if not output_path:
                default_name = f"informe_{informe.nombre.lower().replace(' ', '_')}_{datetime.now().strftime('%Y%m%d')}.pdf"
                output_path, _ = QFileDialog.getSaveFileName(
                    self.parent,
                    "Guardar informe como PDF",
                    str(Path.home() / default_name),
                    "Archivos PDF (*.pdf)"
                )
                
                if not output_path:  # Usuario canceló
                    return ""
            
            # Crear documento
            doc = QTextDocument()
            
            # Configurar el documento
            cursor = QTextCursor(doc)
            
            # Formato para el título
            title_format = QTextCharFormat()
            title_format.setFontPointSize(16)
            title_format.setFontWeight(700)  # Negrita
            title_format.setForeground(Qt.GlobalColor.darkBlue)
            
            # Formato para los encabezados de sección
            header_format = QTextCharFormat()
            header_format.setFontPointSize(12)
            header_format.setFontWeight(600)
            header_format.setForeground(Qt.GlobalColor.darkBlue)
            
            # Formato para el texto normal
            normal_format = QTextCharFormat()
            normal_format.setFontPointSize(10)
            
            # Título del informe
            cursor.insertText(f"Informe: {informe.nombre}\n", title_format)
            cursor.insertText(f"Generado el: {datetime.now().strftime('%d/%m/%Y %H:%M')}\n\n", normal_format)
            
            # Descripción
            if informe.descripcion:
                cursor.insertText("Descripción: ", header_format)
                cursor.insertText(f"{informe.descripcion}\n\n", normal_format)
            
            # Configurar impresora
            printer = QPrinter(QPrinter.PrinterMode.HighResolution)
            printer.setOutputFormat(QPrinter.OutputFormat.PdfFormat)
            printer.setOutputFileName(output_path)
            
            # Configurar tamaño de página y márgenes
            page_layout = QPageLayout()
            page_layout.setPageSize(QPageSize(QPageSize.PageSizeId.A4))
            page_layout.setOrientation(QPageLayout.Orientation.Portrait)
            page_layout.setMargins(QMarginsF(20, 20, 20, 20))  # 20mm de margen
            printer.setPageLayout(page_layout)
            
            # Generar el documento
            doc.setPageSize(printer.pageRect(QPrinter.Unit.Point).size())
            doc.print_(printer)
            
            return output_path
            
        except Exception as e:
            self._show_error("Error al generar PDF", f"Ocurrió un error al generar el PDF: {str(e)}")
            return ""
    
    def generate_csv(self, informe_id: int, output_path: Optional[str] = None) -> str:
        """
        Genera un informe en formato CSV.
        
        Args:
            informe_id: ID del informe a generar
            output_path: Ruta donde guardar el CSV. Si es None, se abrirá un diálogo.
            
        Returns:
            str: Ruta del archivo generado o cadena vacía si se canceló.
        """
        try:
            # Obtener el informe y sus líneas
            informe = self.db.query(Informe).filter(Informe.id == informe_id).first()
            if not informe:
                self._show_error("Error", "No se encontró el informe especificado.")
                return ""
                
            lineas = self.db.query(LineaInforme).filter(
                LineaInforme.informe_id == informe_id
            ).order_by(LineaInforme.orden).all()
            
            # Si no se proporcionó una ruta, abrir diálogo para guardar
            if not output_path:
                default_name = f"informe_{informe.nombre.lower().replace(' ', '_')}_{datetime.now().strftime('%Y%m%d')}.csv"
                output_path, _ = QFileDialog.getSaveFileName(
                    self.parent,
                    "Guardar informe como CSV",
                    str(Path.home() / default_name),
                    "Archivos CSV (*.csv)"
                )
                
                if not output_path:  # Usuario canceló
                    return ""
            
            # Escribir el archivo CSV
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile, delimiter=';', quotechar='"', quoting=csv.QUOTE_MINIMAL)
                
                # Escribir encabezado
                writer.writerow(["Orden", "Tipo", "Contenido"])
                
                # Escribir líneas
                for linea in lineas:
                    writer.writerow([linea.orden, linea.tipo, linea.contenido])
            
            return output_path
            
        except Exception as e:
            self._show_error("Error al generar CSV", f"Ocurrió un error al generar el CSV: {str(e)}")
            return ""
    
    def _show_error(self, titulo: str, mensaje: str):
        """Muestra un mensaje de error."""
        if self.parent:
            QMessageBox.critical(self.parent, titulo, mensaje)
        else:
            print(f"[ERROR] {titulo}: {mensaje}")
    
    def __del__(self):
        """Cierra la conexión a la base de datos al destruir el generador."""
        if hasattr(self, 'db'):
            self.db.close()
