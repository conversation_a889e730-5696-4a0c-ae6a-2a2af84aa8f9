"""
Calculador de fórmulas para el sistema de artículos.
"""
import re
import math
from typing import Dict, Any, Union


class FormulaCalculator:
    """Calculador de fórmulas con variables H, A, L, P, S."""
    
    def __init__(self, altura: float, anchura: float, variables_adicionales: dict = None, diseno_data: dict = None):
        """
        Inicializa el calculador con las medidas base.

        Args:
            altura (float): Altura principal en mm
            anchura (float): Anchura principal en mm
            variables_adicionales (dict): Variables adicionales como H1, H2, A1, A2, etc.
        """
        self.altura = altura
        self.anchura = anchura

        # Variables disponibles
        self.variables = {
            'H': altura,                    # Altura principal
            'A': anchura,                   # Anchura principal
            'L': max(altura, anchura),      # Largo (mayor de las dos medidas)
            'P': 2 * altura + 2 * anchura, # Perímetro
            'S': altura * anchura           # Superficie
        }

        # Agregar variables adicionales si se proporcionan
        if variables_adicionales:
            self.variables.update(variables_adicionales)
        
        # Funciones disponibles
        self.funciones = {
            'MIN': min,
            'MAX': max,
            'ROUND': round,
            'CEIL': math.ceil,
            'FLOOR': math.floor,
            'ABS': abs,
            'SQRT': math.sqrt,
            'IF': self._funcion_if
        }

        # Agregar funciones al namespace de evaluación
        self.variables.update(self.funciones)

        # Procesar datos de diseño si se proporcionan
        if diseno_data:
            self._procesar_diseno(diseno_data)


        # Agregar operadores lógicos
        self.variables['NOT'] = lambda x: not x

    def _procesar_diseno(self, diseno_data: dict):
        """Procesa los datos de un diseño gráfico y crea nuevas variables."""
        # Constantes de diseño (podrían ser configurables en el futuro)
        GROSOR_MARCO = 50

        altura = self.altura
        anchura = self.anchura

        divisiones_v = diseno_data.get('divisiones_v', [])
        divisiones_h = diseno_data.get('divisiones_h', [])
        celdas = diseno_data.get('celdas', {})

        num_div_v = len(divisiones_v)
        num_div_h = len(divisiones_h)

        # Variables de recuento
        self.variables['NUM_DIV_V'] = num_div_v
        self.variables['NUM_DIV_H'] = num_div_h
        self.variables['NUM_HUECOS'] = (num_div_v + 1) * (num_div_h + 1)
        self.variables['NUM_HOJAS'] = sum(1 for v in celdas.values() if 'Hoja' in v)
        self.variables['NUM_FIJOS'] = sum(1 for v in celdas.values() if 'Fijo' in v)

        # Variables de cálculo
        altura_hueco = max(0, altura - 2 * GROSOR_MARCO)
        anchura_hueco = max(0, anchura - 2 * GROSOR_MARCO)
        
        self.variables['S_HUECO'] = altura_hueco * anchura_hueco
        self.variables['LONG_DIVS'] = (num_div_v * altura_hueco) + (num_div_h * anchura_hueco)

    def _funcion_if(self, condicion: bool, valor_si: float, valor_no: float) -> float:
        """Función IF condicional."""
        return valor_si if condicion else valor_no
    
    def _limpiar_formula(self, formula: str) -> str:
        """Limpia y prepara la fórmula para evaluación."""
        if not formula:
            return "0"

        # Convertir a string si no lo es
        formula = str(formula).strip()

        # Reemplazar todas las variables numéricas disponibles
        variables_numericas = {}
        for var, valor in self.variables.items():
            if isinstance(valor, (int, float)) and not callable(valor):
                variables_numericas[var] = valor

        for var, valor in variables_numericas.items():
            formula = re.sub(r'\b' + var + r'\b', str(valor), formula)

        # Reemplazar operadores lógicos
        formula = formula.replace(' AND ', ' and ')
        formula = formula.replace(' OR ', ' or ')
        formula = formula.replace(' NOT ', ' not ')

        return formula
    
    def evaluar(self, formula: str) -> float:
        """
        Evalúa una fórmula matemática.
        
        Args:
            formula (str): Fórmula a evaluar (ej: "H-25", "A+10", "H*A/1000")
            
        Returns:
            float: Resultado de la evaluación
        """
        try:
            if not formula:
                return 0.0
            
            # Limpiar la fórmula
            formula_limpia = self._limpiar_formula(formula)
            
            # Manejar funciones especiales
            formula_limpia = self._procesar_funciones(formula_limpia)
            
            # Evaluar la expresión
            namespace = {"__builtins__": {}}
            namespace.update(self.funciones)
            resultado = eval(formula_limpia, namespace)

            return float(resultado)
            
        except Exception as e:
            print(f"Error evaluando fórmula '{formula}': {e}")
            return 0.0
    
    def _procesar_funciones(self, formula: str) -> str:
        """Procesa funciones especiales en la fórmula."""
        # Procesar función IF
        patron_if = r'IF\s*\(\s*([^,]+)\s*,\s*([^,]+)\s*,\s*([^)]+)\s*\)'
        
        def reemplazar_if(match):
            condicion = match.group(1).strip()
            valor_si = match.group(2).strip()
            valor_no = match.group(3).strip()
            
            try:
                # Evaluar la condición
                namespace = {"__builtins__": {}}
                namespace.update(self.variables)
                cond_resultado = eval(condicion, namespace)
                return valor_si if cond_resultado else valor_no
            except:
                return valor_no
        
        formula = re.sub(patron_if, reemplazar_if, formula)
        
        return formula
    
    def evaluar_condicion(self, condicion: str) -> bool:
        """
        Evalúa una condición lógica.
        
        Args:
            condicion (str): Condición a evaluar (ej: "H>1500", "A<1000 AND H>500")
            
        Returns:
            bool: Resultado de la evaluación
        """
        try:
            if not condicion:
                return True  # Sin condición = siempre verdadero
            
            # Limpiar la condición
            condicion_limpia = self._limpiar_formula(condicion)
            
            # Evaluar la condición
            namespace = {"__builtins__": {}}
            namespace.update(self.variables)
            resultado = eval(condicion_limpia, namespace)

            return bool(resultado)
            
        except Exception as e:
            print(f"Error evaluando condición '{condicion}': {e}")
            return True  # En caso de error, asumir verdadero
    
    def validar_formula(self, formula: str) -> tuple[bool, str]:
        """
        Valida si una fórmula es sintácticamente correcta.
        
        Args:
            formula (str): Fórmula a validar
            
        Returns:
            tuple[bool, str]: (es_valida, mensaje_error)
        """
        try:
            if not formula:
                return True, ""
            
            # Verificar caracteres permitidos
            caracteres_permitidos = r'^[HALPS0-9+\-*/().,\s<>=!&|ANDORNOTIFMINMAXROUNDCEILFLOORABS]+$'
            if not re.match(caracteres_permitidos, formula.upper()):
                return False, "La fórmula contiene caracteres no permitidos"
            
            # Verificar paréntesis balanceados
            if formula.count('(') != formula.count(')'):
                return False, "Paréntesis no balanceados"
            
            # Intentar evaluar con valores de prueba
            calculador_prueba = FormulaCalculator(1000, 800)
            calculador_prueba.evaluar(formula)
            
            return True, ""
            
        except Exception as e:
            return False, f"Error de sintaxis: {str(e)}"
    
    def obtener_variables_disponibles(self) -> Dict[str, str]:
        """
        Obtiene la lista de variables disponibles con sus descripciones.

        Returns:
            Dict[str, str]: Diccionario con código y descripción de variables
        """
        variables_desc = {
            'H': 'Altura principal en mm',
            'A': 'Anchura principal en mm',
            'L': 'Largo (mayor de H y A) en mm',
            'P': 'Perímetro (2*H + 2*A) en mm',
            'S': 'Superficie (H*A) en mm²',
            'H1': 'Segunda altura en mm',
            'H2': 'Tercera altura en mm',
            'H3': 'Cuarta altura en mm',
            'H4': 'Quinta altura en mm',
            'A1': 'Segunda anchura en mm',
            'A2': 'Tercera anchura en mm',
            'A3': 'Cuarta anchura en mm',
            'A4': 'Quinta anchura en mm'
        }

        # Solo devolver las variables que están disponibles
        resultado = {}
        for var, desc in variables_desc.items():
            if var in self.variables and isinstance(self.variables[var], (int, float)):
                resultado[var] = desc

        return resultado
    
    def obtener_funciones_disponibles(self) -> Dict[str, str]:
        """
        Obtiene la lista de funciones disponibles con sus descripciones.
        
        Returns:
            Dict[str, str]: Diccionario con función y descripción
        """
        return {
            'MIN(a,b)': 'Devuelve el menor de dos valores',
            'MAX(a,b)': 'Devuelve el mayor de dos valores',
            'ROUND(x)': 'Redondea al entero más cercano',
            'CEIL(x)': 'Redondea hacia arriba',
            'FLOOR(x)': 'Redondea hacia abajo',
            'ABS(x)': 'Valor absoluto',
            'SQRT(x)': 'Raíz cuadrada',
            'IF(cond,si,no)': 'Condicional: si cond es verdadero devuelve si, sino no'
        }
    
    def obtener_ejemplos_formulas(self) -> Dict[str, str]:
        """
        Obtiene ejemplos de fórmulas comunes.
        
        Returns:
            Dict[str, str]: Diccionario con fórmula y descripción
        """
        return {
            'H-25': 'Altura menos 25mm (para marcos)',
            'A+10': 'Anchura más 10mm (para holguras)',
            '(H-50)/2': 'Mitad de la altura menos 50mm',
            'H*A/1000000': 'Superficie en metros cuadrados',
            'P/100': 'Un elemento cada 100mm de perímetro',
            'IF(H>1500,3,2)': '3 elementos si altura > 1500mm, sino 2',
            'MIN(H,A)-30': 'La menor medida menos 30mm',
            'ROUND(H/100)*100': 'Altura redondeada a centímetros',
            '2*H+A': 'Dos veces la altura más la anchura'
        }


# Funciones de utilidad para validación
def validar_codigo_articulo(codigo: str) -> tuple[bool, str]:
    """
    Valida el formato del código de artículo.
    
    Args:
        codigo (str): Código a validar
        
    Returns:
        tuple[bool, str]: (es_valido, mensaje_error)
    """
    if not codigo:
        return False, "El código es obligatorio"
    
    if len(codigo) > 20:
        return False, "El código no puede tener más de 20 caracteres"
    
    # Verificar que solo contenga letras, números y algunos símbolos
    if not re.match(r'^[A-Za-z0-9_-]+$', codigo):
        return False, "El código solo puede contener letras, números, guiones y guiones bajos"
    
    return True, ""


def generar_codigo_sugerido(serie: str, tipo: str) -> str:
    """
    Genera un código sugerido basado en la serie y tipo.
    
    Args:
        serie (str): Serie del artículo
        tipo (str): Tipo de artículo
        
    Returns:
        str: Código sugerido
    """
    prefijo = ""
    
    # Prefijo basado en tipo
    if "ventana" in tipo.lower():
        prefijo = "V"
    elif "puerta" in tipo.lower():
        prefijo = "P"
    elif "corredera" in tipo.lower():
        prefijo = "C"
    elif "oscilobatiente" in tipo.lower():
        prefijo = "O"
    else:
        prefijo = "A"  # Artículo genérico
    
    # Número basado en serie
    numero = "001"
    if serie:
        # Extraer números de la serie
        numeros = re.findall(r'\d+', serie)
        if numeros:
            numero = numeros[0].zfill(3)
    
    return f"{prefijo}{numero}"
