"""
Módulo mejorado para gestionar perfiles de aluminio/PVC con distribuidor, serie y tipo.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QLineEdit, QTableWidget, QTableWidgetItem, QMessageBox, QHeaderView,
    QDialogButtonBox, QFormLayout, QDoubleSpinBox, QCheckBox, QComboBox,
    QTabWidget, QWidget, QTextEdit, QSpinBox, QGroupBox, QGridLayout,
    QSplitter, QFrame, QScrollArea
)
from PyQt6.QtCore import Qt, pyqtSlot, QTimer
from PyQt6.QtGui import QIcon, QFont, QColor
from datetime import datetime

from models.base import get_db
from models.perfil import Perfil, Distribuidor, SeriePerfil, TipoPerfil
from .distribuidor_dialog import DistribuidorDialog
from .serie_dialog import SerieDialog
from .tipo_dialog import TipoDialog
from ui.utils.window_utils import smart_dialog_setup, force_dialog_maximized
from ui.themes.theme_fallback import AdvancedThemeManager

class PerfilDialog(QDialog):
    """Diálogo mejorado para gestionar perfiles de aluminio/PVC con distribuidor, serie y tipo."""

    def __init__(self, parent=None):
        """
        Inicializa el diálogo de gestión de perfiles.

        Args:
            parent: Widget padre
        """
        super().__init__(parent)
        self.setWindowTitle("Gestión de Perfiles")
        self.setModal(True)  # Hacer el diálogo modal

        # Variables
        self.perfil_actual = None
        self.filtros_activos = {}

        # ✅ FORZAR MAXIMIZACIÓN COMPLETA
        force_dialog_maximized(self, "Gestión de Perfiles")

        # Aplicar tema profesional
        self.theme_manager = AdvancedThemeManager()
        self.theme_manager.apply_professional_theme_to_widget(self)

        # Configurar interfaz
        self._configurar_ui()

        # Cargar datos iniciales
        self._cargar_datos_iniciales()

        # Timer para búsqueda en tiempo real
        self.timer_busqueda = QTimer()
        self.timer_busqueda.setSingleShot(True)
        self.timer_busqueda.timeout.connect(self._aplicar_filtros)
    
    def _configurar_ui(self):
        """Configura la interfaz de usuario mejorada del diálogo."""
        # Layout principal
        layout_principal = QVBoxLayout(self)
        layout_principal.setContentsMargins(12, 12, 12, 12)
        layout_principal.setSpacing(12)



        # Panel superior - Filtros y acciones (horizontal)
        panel_superior = self._crear_panel_superior()
        layout_principal.addWidget(panel_superior)

        # Panel inferior - Tabla de perfiles
        panel_tabla = self._crear_panel_tabla()
        layout_principal.addWidget(panel_tabla)

        # Barra de estado
        self.label_estado = QLabel("Listo")
        self.label_estado.setStyleSheet("color: #7f8c8d; font-style: italic;")
        layout_principal.addWidget(self.label_estado)

        # Botones del diálogo
        botones = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        botones.rejected.connect(self.reject)
        layout_principal.addWidget(botones)

    def _crear_panel_superior(self):
        """Crea el panel superior con filtros y acciones en horizontal."""
        panel = QFrame()
        panel.setFrameStyle(QFrame.Shape.StyledPanel)
        panel.setMaximumHeight(160)  # Altura más compacta sin título duplicado

        layout = QHBoxLayout(panel)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(16)

        # Sección de filtros (lado izquierdo)
        seccion_filtros = self._crear_seccion_filtros()
        layout.addWidget(seccion_filtros, 2)  # Más espacio para filtros

        # Separador visual
        separador = QFrame()
        separador.setFrameShape(QFrame.Shape.VLine)
        separador.setFrameShadow(QFrame.Shadow.Sunken)
        separador.setStyleSheet("color: #bdc3c7;")
        layout.addWidget(separador)

        # Sección de acciones (lado derecho)
        seccion_acciones = self._crear_seccion_acciones()
        layout.addWidget(seccion_acciones, 1)  # Menos espacio para acciones

        return panel

    def _crear_seccion_filtros(self):
        """Crea la sección de filtros."""
        seccion = QGroupBox("🔍 Filtros y Búsqueda")
        layout = QVBoxLayout(seccion)
        layout.setSpacing(8)

        # Primera fila - Búsqueda
        layout_busqueda = QHBoxLayout()

        label_busqueda = QLabel("Buscar:")
        label_busqueda.setMinimumWidth(60)
        label_busqueda.setStyleSheet("font-size: 12px; font-weight: bold; color: #34495e;")
        layout_busqueda.addWidget(label_busqueda)

        self.campo_busqueda = QLineEdit()
        self.campo_busqueda.setPlaceholderText("Código, descripción, referencia...")
        self.campo_busqueda.setStyleSheet("""
            QLineEdit {
                font-size: 12px;
                padding: 6px 8px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
                min-height: 20px;
                max-height: 28px;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """)
        self.campo_busqueda.textChanged.connect(self._on_busqueda_cambio)
        layout_busqueda.addWidget(self.campo_busqueda)

        layout.addLayout(layout_busqueda)

        # Segunda fila - Filtros principales
        layout_filtros1 = QHBoxLayout()

        # Distribuidor
        label_dist = QLabel("Distribuidor:")
        label_dist.setStyleSheet("font-size: 12px; font-weight: bold; color: #34495e;")
        layout_filtros1.addWidget(label_dist)
        self.combo_distribuidor = QComboBox()
        self.combo_distribuidor.addItem("Todos", None)
        self.combo_distribuidor.currentTextChanged.connect(self._on_filtro_cambio)
        self._aplicar_estilo_combo(self.combo_distribuidor)
        layout_filtros1.addWidget(self.combo_distribuidor)

        # Serie
        label_serie = QLabel("Serie:")
        label_serie.setStyleSheet("font-size: 12px; font-weight: bold; color: #34495e;")
        layout_filtros1.addWidget(label_serie)
        self.combo_serie = QComboBox()
        self.combo_serie.addItem("Todas", None)
        self.combo_serie.currentTextChanged.connect(self._on_filtro_cambio)
        self._aplicar_estilo_combo(self.combo_serie)
        layout_filtros1.addWidget(self.combo_serie)

        # Tipo
        label_tipo = QLabel("Tipo:")
        label_tipo.setStyleSheet("font-size: 12px; font-weight: bold; color: #34495e;")
        layout_filtros1.addWidget(label_tipo)
        self.combo_tipo = QComboBox()
        self.combo_tipo.addItem("Todos", None)
        self.combo_tipo.currentTextChanged.connect(self._on_filtro_cambio)
        self._aplicar_estilo_combo(self.combo_tipo)
        layout_filtros1.addWidget(self.combo_tipo)

        layout.addLayout(layout_filtros1)

        # Tercera fila - Filtros secundarios
        layout_filtros2 = QHBoxLayout()

        # Material
        label_material = QLabel("Material:")
        label_material.setStyleSheet("font-size: 12px; font-weight: bold; color: #34495e;")
        layout_filtros2.addWidget(label_material)
        self.combo_material = QComboBox()
        self.combo_material.addItems(["Todos", "Aluminio", "PVC", "Acero", "Madera"])
        self.combo_material.currentTextChanged.connect(self._on_filtro_cambio)
        self._aplicar_estilo_combo(self.combo_material)
        layout_filtros2.addWidget(self.combo_material)

        # Estado
        label_estado = QLabel("Estado:")
        label_estado.setStyleSheet("font-size: 12px; font-weight: bold; color: #34495e;")
        layout_filtros2.addWidget(label_estado)
        self.combo_activo = QComboBox()
        self.combo_activo.addItems(["Todos", "Activos", "Inactivos"])
        self.combo_activo.currentTextChanged.connect(self._on_filtro_cambio)
        self._aplicar_estilo_combo(self.combo_activo)
        layout_filtros2.addWidget(self.combo_activo)

        # Botón limpiar filtros
        self.boton_limpiar = QPushButton("🧹 Limpiar")
        self.boton_limpiar.setStyleSheet(self._get_button_style("#95a5a6"))
        self.boton_limpiar.clicked.connect(self._limpiar_filtros)
        layout_filtros2.addWidget(self.boton_limpiar)

        # Espaciador
        layout_filtros2.addStretch()

        layout.addLayout(layout_filtros2)

        return seccion

    def _crear_seccion_acciones(self):
        """Crea la sección de acciones."""
        seccion = QGroupBox("⚡ Acciones")
        layout = QVBoxLayout(seccion)
        layout.setSpacing(6)

        # Primera fila - Acciones principales
        layout_acciones1 = QHBoxLayout()

        self.boton_nuevo = QPushButton("➕ Nuevo")
        self.boton_nuevo.setStyleSheet(self._get_button_style("#27ae60"))
        self.boton_nuevo.clicked.connect(self._on_nuevo_perfil)
        layout_acciones1.addWidget(self.boton_nuevo)

        self.boton_editar = QPushButton("✏️ Editar")
        self.boton_editar.setStyleSheet(self._get_button_style("#3498db"))
        self.boton_editar.setEnabled(False)
        self.boton_editar.clicked.connect(self._on_editar_perfil)
        layout_acciones1.addWidget(self.boton_editar)

        self.boton_duplicar = QPushButton("📋 Duplicar")
        self.boton_duplicar.setStyleSheet(self._get_button_style("#f39c12"))
        self.boton_duplicar.setEnabled(False)
        self.boton_duplicar.clicked.connect(self._on_duplicar_perfil)
        layout_acciones1.addWidget(self.boton_duplicar)

        self.boton_eliminar = QPushButton("🗑️ Eliminar")
        self.boton_eliminar.setStyleSheet(self._get_button_style("#e74c3c"))
        self.boton_eliminar.setEnabled(False)
        self.boton_eliminar.clicked.connect(self._on_eliminar_perfil)
        layout_acciones1.addWidget(self.boton_eliminar)

        layout.addLayout(layout_acciones1)

        # Segunda fila - Gestión de catálogos
        layout_acciones2 = QHBoxLayout()

        self.boton_distribuidores = QPushButton("🏢 Distribuidores")
        self.boton_distribuidores.setStyleSheet(self._get_button_style("#9b59b6"))
        self.boton_distribuidores.clicked.connect(self._gestionar_distribuidores)
        layout_acciones2.addWidget(self.boton_distribuidores)

        self.boton_series = QPushButton("📚 Series")
        self.boton_series.setStyleSheet(self._get_button_style("#9b59b6"))
        self.boton_series.clicked.connect(self._gestionar_series)
        layout_acciones2.addWidget(self.boton_series)

        self.boton_tipos = QPushButton("🏷️ Tipos")
        self.boton_tipos.setStyleSheet(self._get_button_style("#9b59b6"))
        self.boton_tipos.clicked.connect(self._gestionar_tipos)
        layout_acciones2.addWidget(self.boton_tipos)

        self.boton_actualizar = QPushButton("🔄 Actualizar")
        self.boton_actualizar.setStyleSheet(self._get_button_style("#34495e"))
        self.boton_actualizar.clicked.connect(self._cargar_perfiles)
        layout_acciones2.addWidget(self.boton_actualizar)

        layout.addLayout(layout_acciones2)

        return seccion

    def _aplicar_estilo_combo(self, combo):
        """Aplica estilo consistente a los combos."""
        combo.setStyleSheet("""
            QComboBox {
                font-size: 12px;
                padding: 4px 8px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
                min-height: 20px;
                max-height: 28px;
                min-width: 80px;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #7f8c8d;
                margin-right: 4px;
            }
        """)

    def _limpiar_filtros(self):
        """Limpia todos los filtros y recarga la tabla."""
        self.campo_busqueda.clear()
        self.combo_distribuidor.setCurrentIndex(0)
        self.combo_serie.setCurrentIndex(0)
        self.combo_tipo.setCurrentIndex(0)
        self.combo_material.setCurrentIndex(0)
        self.combo_activo.setCurrentIndex(0)
        self._cargar_perfiles()



    def _crear_panel_tabla(self):
        """Crea el panel de la tabla de perfiles."""
        panel = QGroupBox("📋 Lista de Perfiles")
        panel.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #34495e;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)

        # Información de resultados
        self.label_resultados = QLabel("0 perfiles encontrados")
        self.label_resultados.setStyleSheet("color: #7f8c8d; font-size: 12px; font-weight: normal;")
        layout.addWidget(self.label_resultados)

        # Tabla de perfiles mejorada
        self.tabla_perfiles = QTableWidget()
        self.tabla_perfiles.setColumnCount(10)
        self.tabla_perfiles.setHorizontalHeaderLabels([
            "Código", "Descripción", "Distribuidor", "Serie", "Tipo",
            "Material", "Precio/m (€)", "Stock Mín.", "Estado", "Acciones"
        ])

        # Configurar tabla
        self.tabla_perfiles.setAlternatingRowColors(False)  # Desactivar filas alternadas
        self.tabla_perfiles.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.tabla_perfiles.setSortingEnabled(True)

        # Ajustar el ancho de las columnas
        header = self.tabla_perfiles.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # Código
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Descripción
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Distribuidor
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Serie
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Tipo
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # Material
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # Precio
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)  # Stock
        header.setSectionResizeMode(8, QHeaderView.ResizeMode.ResizeToContents)  # Estado
        header.setSectionResizeMode(9, QHeaderView.ResizeMode.Fixed)  # Acciones

        # Establecer ancho fijo para la columna de acciones
        self.tabla_perfiles.setColumnWidth(9, 60)  # Ancho para un solo botón

        # Conectar señales
        self.tabla_perfiles.itemSelectionChanged.connect(self._on_seleccion_cambiada)
        self.tabla_perfiles.doubleClicked.connect(self._on_editar_perfil)

        layout.addWidget(self.tabla_perfiles)

        return panel

    def _get_button_style(self, color):
        """Obtiene el estilo para botones del panel de acciones."""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: 1px solid {color};
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-height: 32px;
                max-height: 36px;
                text-align: center;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
                border-color: {color}dd;
                border-width: 2px;
            }}
            QPushButton:pressed {{
                background-color: {color}aa;
                border-color: {color}aa;
                border-width: 1px;
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                border-color: #95a5a6;
                color: #7f8c8d;
            }}
        """

    def _cargar_datos_iniciales(self):
        """Carga los datos iniciales para los combos y la tabla."""
        self._cargar_distribuidores()
        self._cargar_series()
        self._cargar_tipos()
        self._cargar_perfiles()

    def _cargar_distribuidores(self):
        """Carga los distribuidores en el combo."""
        db = next(get_db())
        try:
            distribuidores = db.query(Distribuidor).filter(Distribuidor.activo == True).order_by(Distribuidor.nombre).all()

            # Limpiar combo
            self.combo_distribuidor.clear()
            self.combo_distribuidor.addItem("Todos", None)

            for distribuidor in distribuidores:
                self.combo_distribuidor.addItem(distribuidor.nombre, distribuidor.id)

        except Exception as e:
            print(f"Error cargando distribuidores: {e}")
        finally:
            db.close()

    def _cargar_series(self):
        """Carga las series en el combo."""
        db = next(get_db())
        try:
            series = db.query(SeriePerfil).filter(SeriePerfil.activo == True).order_by(SeriePerfil.nombre).all()

            # Limpiar combo
            self.combo_serie.clear()
            self.combo_serie.addItem("Todas", None)

            for serie in series:
                self.combo_serie.addItem(serie.nombre, serie.id)

        except Exception as e:
            print(f"Error cargando series: {e}")
        finally:
            db.close()

    def _cargar_tipos(self):
        """Carga los tipos en el combo."""
        db = next(get_db())
        try:
            tipos = db.query(TipoPerfil).filter(TipoPerfil.activo == True).order_by(TipoPerfil.nombre).all()

            # Limpiar combo
            self.combo_tipo.clear()
            self.combo_tipo.addItem("Todos", None)

            for tipo in tipos:
                self.combo_tipo.addItem(f"{tipo.nombre} ({tipo.categoria})", tipo.id)

        except Exception as e:
            print(f"Error cargando tipos: {e}")
        finally:
            db.close()

    def _on_busqueda_cambio(self):
        """Maneja el cambio en el campo de búsqueda."""
        self.timer_busqueda.stop()
        self.timer_busqueda.start(300)  # Esperar 300ms antes de buscar

    def _on_filtro_cambio(self):
        """Maneja el cambio en los filtros."""
        self._aplicar_filtros()

    def _aplicar_filtros(self):
        """Aplica los filtros y actualiza la tabla."""
        self._cargar_perfiles()
    
    def _cargar_perfiles(self):
        """Carga la lista de perfiles desde la base de datos con filtros aplicados."""
        db = next(get_db())

        try:
            # Construir query base
            query = db.query(Perfil).join(Distribuidor, Perfil.distribuidor_id == Distribuidor.id, isouter=True)\
                                   .join(SeriePerfil, Perfil.serie_id == SeriePerfil.id, isouter=True)\
                                   .join(TipoPerfil, Perfil.tipo_id == TipoPerfil.id, isouter=True)

            # Aplicar filtros
            texto_busqueda = self.campo_busqueda.text().strip()
            if texto_busqueda:
                query = query.filter(
                    (Perfil.codigo.ilike(f"%{texto_busqueda}%")) |
                    (Perfil.descripcion.ilike(f"%{texto_busqueda}%")) |
                    (Perfil.referencia.ilike(f"%{texto_busqueda}%"))
                )

            # Filtro por distribuidor
            distribuidor_id = self.combo_distribuidor.currentData()
            if distribuidor_id:
                query = query.filter(Perfil.distribuidor_id == distribuidor_id)

            # Filtro por serie
            serie_id = self.combo_serie.currentData()
            if serie_id:
                query = query.filter(Perfil.serie_id == serie_id)

            # Filtro por tipo
            tipo_id = self.combo_tipo.currentData()
            if tipo_id:
                query = query.filter(Perfil.tipo_id == tipo_id)

            # Filtro por material
            material = self.combo_material.currentText()
            if material != "Todos":
                query = query.filter(Perfil.material == material)

            # Filtro por estado
            estado = self.combo_activo.currentText()
            if estado == "Activos":
                query = query.filter(Perfil.activo == True)
            elif estado == "Inactivos":
                query = query.filter(Perfil.activo == False)

            # Obtener perfiles
            perfiles = query.order_by(Perfil.codigo).all()

            # Actualizar label de resultados
            self.label_resultados.setText(f"{len(perfiles)} perfiles encontrados")

            # Configurar la tabla
            self.tabla_perfiles.setRowCount(len(perfiles))

            for fila, perfil in enumerate(perfiles):
                # Código
                self.tabla_perfiles.setItem(fila, 0, QTableWidgetItem(perfil.codigo))

                # Descripción
                self.tabla_perfiles.setItem(fila, 1, QTableWidgetItem(perfil.descripcion))

                # Distribuidor
                distribuidor_nombre = perfil.distribuidor.nombre if perfil.distribuidor else "Sin distribuidor"
                self.tabla_perfiles.setItem(fila, 2, QTableWidgetItem(distribuidor_nombre))

                # Serie
                serie_nombre = perfil.serie.nombre if perfil.serie else "Sin serie"
                self.tabla_perfiles.setItem(fila, 3, QTableWidgetItem(serie_nombre))

                # Tipo
                tipo_nombre = perfil.tipo.nombre if perfil.tipo else "Sin tipo"
                self.tabla_perfiles.setItem(fila, 4, QTableWidgetItem(tipo_nombre))

                # Material
                material = perfil.material or "No especificado"
                self.tabla_perfiles.setItem(fila, 5, QTableWidgetItem(material))

                # Precio
                precio_item = QTableWidgetItem(f"{perfil.precio_metro:.2f}")
                precio_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.tabla_perfiles.setItem(fila, 6, precio_item)

                # Stock mínimo
                stock_item = QTableWidgetItem(str(perfil.stock_minimo or 0))
                stock_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.tabla_perfiles.setItem(fila, 7, stock_item)

                # Estado
                estado_item = QTableWidgetItem("✅ Activo" if perfil.activo else "❌ Inactivo")
                estado_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                if perfil.activo:
                    estado_item.setBackground(QColor("#d4edda"))  # Verde claro
                    estado_item.setForeground(QColor("#155724"))  # Verde oscuro
                else:
                    estado_item.setBackground(QColor("#f8d7da"))  # Rojo claro
                    estado_item.setForeground(QColor("#721c24"))  # Rojo oscuro
                self.tabla_perfiles.setItem(fila, 8, estado_item)

                # Acciones (solo botón de editar)
                widget_acciones = QWidget()
                layout_acciones = QHBoxLayout(widget_acciones)
                layout_acciones.setContentsMargins(4, 4, 4, 4)
                layout_acciones.setSpacing(0)

                btn_editar = QPushButton("✏️")
                btn_editar.setToolTip("Editar perfil")
                btn_editar.setFixedSize(36, 36)  # Botón único más grande
                btn_editar.setStyleSheet("""
                    QPushButton {
                        border: 1px solid #f39c12;
                        border-radius: 8px;
                        background-color: #f39c12;
                        color: white;
                        font-size: 16px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #e67e22;
                        border-width: 2px;
                    }
                    QPushButton:pressed {
                        background-color: #d35400;
                    }
                """)
                btn_editar.clicked.connect(lambda checked=False, p=perfil: self._editar_perfil_directo(p))
                layout_acciones.addWidget(btn_editar)

                # Centrar el botón
                layout_acciones.setAlignment(Qt.AlignmentFlag.AlignCenter)

                self.tabla_perfiles.setCellWidget(fila, 9, widget_acciones)

            # Actualizar estado
            self.label_estado.setText(f"Última actualización: {datetime.now().strftime('%H:%M:%S')}")

        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudieron cargar los perfiles: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
            self.label_estado.setText("Error al cargar perfiles")
        finally:
            db.close()

    def _on_seleccion_cambiada(self):
        """Maneja el evento de cambio de selección en la tabla."""
        seleccion = self.tabla_perfiles.selectedItems()
        habilitar = len(seleccion) > 0

        self.boton_editar.setEnabled(habilitar)
        self.boton_duplicar.setEnabled(habilitar)
        self.boton_eliminar.setEnabled(habilitar)

    def _ver_detalles_perfil(self, perfil):
        """Muestra los detalles de un perfil."""
        QMessageBox.information(
            self,
            f"Detalles del Perfil: {perfil.codigo}",
            f"<b>Código:</b> {perfil.codigo}<br>"
            f"<b>Descripción:</b> {perfil.descripcion}<br>"
            f"<b>Distribuidor:</b> {perfil.distribuidor.nombre if perfil.distribuidor else 'Sin distribuidor'}<br>"
            f"<b>Serie:</b> {perfil.serie.nombre if perfil.serie else 'Sin serie'}<br>"
            f"<b>Tipo:</b> {perfil.tipo.nombre if perfil.tipo else 'Sin tipo'}<br>"
            f"<b>Material:</b> {perfil.material or 'No especificado'}<br>"
            f"<b>Precio/metro:</b> €{perfil.precio_metro:.2f}<br>"
            f"<b>Estado:</b> {'Activo' if perfil.activo else 'Inactivo'}"
        )

    def _editar_perfil_directo(self, perfil):
        """Edita un perfil directamente."""
        dialogo = PerfilEditarDialog(self, perfil)
        if dialogo.exec() == QDialog.DialogCode.Accepted:
            self._cargar_perfiles()

    def _on_nuevo_perfil(self):
        """Maneja el evento de crear un nuevo perfil."""
        dialogo = PerfilEditarDialog(self)
        if dialogo.exec() == QDialog.DialogCode.Accepted:
            self._cargar_perfiles()
            # Recargar combos por si se crearon nuevos distribuidores/series/tipos
            self._cargar_datos_iniciales()

    def _on_editar_perfil(self):
        """Maneja el evento de editar un perfil existente."""
        fila = self.tabla_perfiles.currentRow()
        if fila < 0:
            QMessageBox.warning(self, "Advertencia", "Seleccione un perfil para editar.")
            return

        codigo = self.tabla_perfiles.item(fila, 0).text()
        db = next(get_db())

        try:
            perfil = db.query(Perfil).filter(Perfil.codigo == codigo).first()
            if not perfil:
                raise ValueError("Perfil no encontrado")

            dialogo = PerfilEditarDialog(self, perfil)
            if dialogo.exec() == QDialog.DialogCode.Accepted:
                self._cargar_perfiles()

        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudo cargar el perfil: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        finally:
            db.close()

    def _on_duplicar_perfil(self):
        """Duplica un perfil existente."""
        fila = self.tabla_perfiles.currentRow()
        if fila < 0:
            QMessageBox.warning(self, "Advertencia", "Seleccione un perfil para duplicar.")
            return

        codigo = self.tabla_perfiles.item(fila, 0).text()
        db = next(get_db())

        try:
            perfil_original = db.query(Perfil).filter(Perfil.codigo == codigo).first()
            if not perfil_original:
                raise ValueError("Perfil no encontrado")

            # Crear copia del perfil
            dialogo = PerfilEditarDialog(self, perfil_original, duplicar=True)
            if dialogo.exec() == QDialog.DialogCode.Accepted:
                self._cargar_perfiles()

        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudo duplicar el perfil: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        finally:
            db.close()

    def _on_eliminar_perfil(self):
        """Maneja el evento de eliminar un perfil."""
        fila = self.tabla_perfiles.currentRow()
        if fila < 0:
            QMessageBox.warning(self, "Advertencia", "Seleccione un perfil para eliminar.")
            return

        codigo = self.tabla_perfiles.item(fila, 0).text()
        descripcion = self.tabla_perfiles.item(fila, 1).text()

        respuesta = QMessageBox.question(
            self,
            "Confirmar eliminación",
            f"¿Está seguro de que desea eliminar el perfil '{codigo} - {descripcion}'?\n"
            "Esta acción no se puede deshacer.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if respuesta == QMessageBox.StandardButton.Yes:
            db = next(get_db())
            try:
                perfil = db.query(Perfil).filter(Perfil.codigo == codigo).first()
                if perfil:
                    db.delete(perfil)
                    db.commit()
                    self._cargar_perfiles()
                    QMessageBox.information(self, "Éxito", "Perfil eliminado correctamente.")
            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self,
                    "Error",
                    f"No se pudo eliminar el perfil: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )
            finally:
                db.close()

    def _gestionar_distribuidores(self):
        """Abre el diálogo de gestión de distribuidores."""
        dialogo = DistribuidorDialog(self)
        dialogo.exec()
        # Recargar combos y tabla siempre (el usuario pudo haber hecho cambios)
        self._cargar_distribuidores()
        self._cargar_perfiles()

    def _gestionar_series(self):
        """Abre el diálogo de gestión de series."""
        dialogo = SerieDialog(self)
        dialogo.exec()
        # Recargar combos y tabla siempre (el usuario pudo haber hecho cambios)
        self._cargar_series()
        self._cargar_perfiles()

    def _gestionar_tipos(self):
        """Abre el diálogo de gestión de tipos."""
        dialogo = TipoDialog(self)
        dialogo.exec()
        # Recargar combos y tabla siempre (el usuario pudo haber hecho cambios)
        self._cargar_tipos()
        self._cargar_perfiles()
    
    def _on_seleccion_cambiada(self):
        """Maneja el evento de cambio de selección en la tabla."""
        seleccion = self.tabla_perfiles.selectedItems()
        habilitar = len(seleccion) > 0
        
        self.boton_editar.setEnabled(habilitar)
        self.boton_eliminar.setEnabled(habilitar)
    
    def _on_nuevo_perfil(self):
        """Maneja el evento de crear un nuevo perfil."""
        dialogo = PerfilEditarDialog(self)
        if dialogo.exec() == QDialog.DialogCode.Accepted:
            # Solo recargar la tabla, el guardado ya se hace en el diálogo de edición
            self._cargar_perfiles()

    
    def _on_editar_perfil(self):
        """Maneja el evento de editar un perfil existente."""
        fila = self.tabla_perfiles.currentRow()
        if fila < 0:
            return

        codigo = self.tabla_perfiles.item(fila, 0).text()
        db = next(get_db())

        try:
            perfil = db.query(Perfil).filter(Perfil.codigo == codigo).first()
            if not perfil:
                raise ValueError("Perfil no encontrado")

            dialogo = PerfilEditarDialog(self, perfil)
            if dialogo.exec() == QDialog.DialogCode.Accepted:
                # Actualizar el perfil existente
                datos = dialogo.get_datos()

                perfil.descripcion = datos['descripcion']
                perfil.referencia = datos['referencia']
                perfil.precio_metro = datos['precio_metro']
                perfil.activo = datos['activo']

                db.commit()

                QMessageBox.information(
                    self,
                    "Éxito",
                    f"Perfil '{perfil.codigo}' actualizado correctamente.",
                    QMessageBox.StandardButton.Ok
                )

                self._cargar_perfiles()

        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudo actualizar el perfil: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        finally:
            db.close()
    
    def _on_eliminar_perfil(self):
        """Maneja el evento de eliminar un perfil."""
        fila = self.tabla_perfiles.currentRow()
        if fila < 0:
            return
        
        codigo = self.tabla_perfiles.item(fila, 0).text()
        descripcion = self.tabla_perfiles.item(fila, 1).text()
        
        respuesta = QMessageBox.question(
            self,
            "Confirmar eliminación",
            f"¿Está seguro de que desea eliminar el perfil '{codigo} - {descripcion}'?\n"
            "Esta acción no se puede deshacer.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if respuesta == QMessageBox.StandardButton.Yes:
            db = next(get_db())
            try:
                perfil = db.query(Perfil).filter(Perfil.codigo == codigo).first()
                if perfil:
                    db.delete(perfil)
                    db.commit()
                    self._cargar_perfiles()
            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self,
                    "Error",
                    f"No se pudo eliminar el perfil: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )
            finally:
                db.close()


class PerfilEditarDialog(QDialog):
    """Diálogo mejorado para editar o crear un perfil con todas las características."""

    def __init__(self, parent=None, perfil=None, duplicar=False):
        """
        Inicializa el diálogo de edición de perfil.

        Args:
            parent: Widget padre
            perfil: Instancia de Perfil a editar (None para nuevo)
            duplicar: Si True, duplica el perfil (genera nuevo código)
        """
        super().__init__(parent)

        self.perfil = perfil
        self.duplicar = duplicar

        if duplicar:
            self.setWindowTitle(f"Duplicar Perfil: {perfil.codigo}")
        elif perfil:
            self.setWindowTitle(f"Editar Perfil: {perfil.codigo}")
        else:
            self.setWindowTitle("Nuevo Perfil")

        # Configurar interfaz ANTES de la geometría
        self._configurar_ui()

        # Cargar datos de combos
        self._cargar_combos()

        # Si se está editando un perfil, cargar sus datos
        if perfil:
            self._cargar_datos_perfil()

        # Configuración inteligente del diálogo
        from ui.utils.window_utils import smart_dialog_setup
        smart_dialog_setup(self, "management", self.windowTitle(), maximize=True)

    def _configurar_ui(self):
        """Configura la interfaz de usuario mejorada del diálogo."""
        # Layout principal
        layout_principal = QVBoxLayout(self)
        layout_principal.setContentsMargins(16, 16, 16, 16)
        layout_principal.setSpacing(16)

        # Título
        titulo = QLabel("📝 Información del Perfil")
        titulo.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        titulo.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout_principal.addWidget(titulo)

        # Crear pestañas
        self.tabs = QTabWidget()

        # Pestaña 1: Información básica
        tab_basica = self._crear_tab_informacion_basica()
        self.tabs.addTab(tab_basica, "📋 Información Básica")

        # Pestaña 2: Características técnicas
        tab_tecnica = self._crear_tab_caracteristicas_tecnicas()
        self.tabs.addTab(tab_tecnica, "🔧 Características Técnicas")

        # Pestaña 3: Precios y stock
        tab_precios = self._crear_tab_precios_stock()
        self.tabs.addTab(tab_precios, "💰 Precios y Stock")

        # Pestaña 4: Información adicional
        tab_adicional = self._crear_tab_informacion_adicional()
        self.tabs.addTab(tab_adicional, "📝 Información Adicional")

        layout_principal.addWidget(self.tabs)

        # Validación
        self.etiqueta_error = QLabel()
        self.etiqueta_error.setStyleSheet("color: #e74c3c; font-weight: bold; padding: 8px;")
        self.etiqueta_error.setVisible(False)
        layout_principal.addWidget(self.etiqueta_error)

        # Botones
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel
        )
        botones.button(QDialogButtonBox.StandardButton.Ok).setText("💾 Guardar")
        botones.button(QDialogButtonBox.StandardButton.Cancel).setText("❌ Cancelar")
        botones.accepted.connect(self._validar_y_aceptar)
        botones.rejected.connect(self.reject)

        layout_principal.addWidget(botones)

        # Conectar señales para validación en tiempo real
        self.codigo.textChanged.connect(self._validar_formulario)
        self.descripcion.textChanged.connect(self._validar_formulario)

    def _crear_tab_informacion_basica(self):
        """Crea la pestaña de información básica."""
        tab = QWidget()
        layout = QFormLayout(tab)
        layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.AllNonFixedFieldsGrow)
        layout.setSpacing(12)

        # Código
        self.codigo = QLineEdit()
        self.codigo.setMaxLength(20)
        self.codigo.setPlaceholderText("Ej: ALU001, PVC002...")
        layout.addRow("Código*:", self.codigo)

        # Descripción
        self.descripcion = QLineEdit()
        self.descripcion.setMaxLength(200)
        self.descripcion.setPlaceholderText("Descripción detallada del perfil...")
        layout.addRow("Descripción*:", self.descripcion)

        # Referencia
        self.referencia = QLineEdit()
        self.referencia.setMaxLength(50)
        self.referencia.setPlaceholderText("Referencia del fabricante...")
        layout.addRow("Referencia:", self.referencia)

        # Distribuidor con botón de gestión
        distribuidor_layout = QHBoxLayout()
        self.combo_distribuidor = QComboBox()
        distribuidor_layout.addWidget(self.combo_distribuidor)

        btn_gestionar_dist = QPushButton("⚙️")
        btn_gestionar_dist.setToolTip("Gestionar distribuidores")
        btn_gestionar_dist.setMaximumWidth(30)
        btn_gestionar_dist.clicked.connect(self._gestionar_distribuidores_desde_edicion)
        distribuidor_layout.addWidget(btn_gestionar_dist)

        layout.addRow("Distribuidor:", distribuidor_layout)

        # Serie con botón de gestión
        serie_layout = QHBoxLayout()
        self.combo_serie = QComboBox()
        serie_layout.addWidget(self.combo_serie)

        btn_gestionar_serie = QPushButton("⚙️")
        btn_gestionar_serie.setToolTip("Gestionar series")
        btn_gestionar_serie.setMaximumWidth(30)
        btn_gestionar_serie.clicked.connect(self._gestionar_series_desde_edicion)
        serie_layout.addWidget(btn_gestionar_serie)

        layout.addRow("Serie:", serie_layout)

        # Tipo con botón de gestión
        tipo_layout = QHBoxLayout()
        self.combo_tipo = QComboBox()
        tipo_layout.addWidget(self.combo_tipo)

        btn_gestionar_tipo = QPushButton("⚙️")
        btn_gestionar_tipo.setToolTip("Gestionar tipos")
        btn_gestionar_tipo.setMaximumWidth(30)
        btn_gestionar_tipo.clicked.connect(self._gestionar_tipos_desde_edicion)
        tipo_layout.addWidget(btn_gestionar_tipo)

        layout.addRow("Tipo:", tipo_layout)

        # Material
        self.combo_material = QComboBox()
        self.combo_material.addItems(["Aluminio", "PVC", "Acero", "Madera", "Otro"])
        self.combo_material.setCurrentText("Aluminio")
        layout.addRow("Material*:", self.combo_material)

        # Color
        self.color = QLineEdit()
        self.color.setMaxLength(50)
        self.color.setPlaceholderText("Ej: Blanco, Negro, Anodizado...")
        layout.addRow("Color:", self.color)

        # Acabado
        self.acabado = QLineEdit()
        self.acabado.setMaxLength(50)
        self.acabado.setPlaceholderText("Ej: Anodizado, Lacado, Texturizado...")
        layout.addRow("Acabado:", self.acabado)

        # Activo
        self.activo = QCheckBox("Perfil activo")
        self.activo.setChecked(True)
        layout.addRow("Estado:", self.activo)

        return tab

    def _crear_tab_caracteristicas_tecnicas(self):
        """Crea la pestaña de características técnicas."""
        tab = QWidget()
        layout = QFormLayout(tab)
        layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.AllNonFixedFieldsGrow)
        layout.setSpacing(12)

        # Dimensiones
        dimensiones_group = QGroupBox("Dimensiones")
        dimensiones_layout = QFormLayout(dimensiones_group)

        self.ancho = QDoubleSpinBox()
        self.ancho.setRange(0, 1000)
        self.ancho.setDecimals(2)
        self.ancho.setSuffix(" mm")
        dimensiones_layout.addRow("Ancho:", self.ancho)

        self.alto = QDoubleSpinBox()
        self.alto.setRange(0, 1000)
        self.alto.setDecimals(2)
        self.alto.setSuffix(" mm")
        dimensiones_layout.addRow("Alto:", self.alto)

        self.espesor = QDoubleSpinBox()
        self.espesor.setRange(0, 100)
        self.espesor.setDecimals(2)
        self.espesor.setSuffix(" mm")
        dimensiones_layout.addRow("Espesor:", self.espesor)

        layout.addRow(dimensiones_group)

        # Peso
        self.peso_metro = QDoubleSpinBox()
        self.peso_metro.setRange(0, 100)
        self.peso_metro.setDecimals(3)
        self.peso_metro.setSuffix(" kg/m")
        layout.addRow("Peso por metro:", self.peso_metro)

        # Unidad de medida
        self.unidad_medida = QComboBox()
        self.unidad_medida.addItems(["m", "mm", "pza", "kg"])
        self.unidad_medida.setCurrentText("m")
        layout.addRow("Unidad de medida:", self.unidad_medida)

        # Código de barras
        self.codigo_barras = QLineEdit()
        self.codigo_barras.setMaxLength(50)
        self.codigo_barras.setPlaceholderText("Código de barras del producto...")
        layout.addRow("Código de barras:", self.codigo_barras)

        return tab

    def _crear_tab_precios_stock(self):
        """Crea la pestaña de precios y stock."""
        tab = QWidget()
        layout = QFormLayout(tab)
        layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.AllNonFixedFieldsGrow)
        layout.setSpacing(12)

        # Precios
        precios_group = QGroupBox("Gestión de Precios")
        precios_layout = QFormLayout(precios_group)

        self.precio_compra = QDoubleSpinBox()
        self.precio_compra.setRange(0, 10000)
        self.precio_compra.setDecimals(2)
        self.precio_compra.setPrefix("€ ")
        precios_layout.addRow("Precio de compra:", self.precio_compra)

        self.margen_beneficio = QDoubleSpinBox()
        self.margen_beneficio.setRange(0, 1000)
        self.margen_beneficio.setDecimals(2)
        self.margen_beneficio.setSuffix(" %")
        precios_layout.addRow("Margen de beneficio:", self.margen_beneficio)

        self.precio_metro = QDoubleSpinBox()
        self.precio_metro.setRange(0, 10000)
        self.precio_metro.setDecimals(2)
        self.precio_metro.setPrefix("€ ")
        precios_layout.addRow("Precio de venta/metro*:", self.precio_metro)

        layout.addRow(precios_group)

        # Stock
        stock_group = QGroupBox("Control de Stock")
        stock_layout = QFormLayout(stock_group)

        self.stock_minimo = QSpinBox()
        self.stock_minimo.setRange(0, 10000)
        self.stock_minimo.setSuffix(" unidades")
        stock_layout.addRow("Stock mínimo:", self.stock_minimo)

        layout.addRow(stock_group)

        # Conectar señales para cálculo automático
        self.precio_compra.valueChanged.connect(self._calcular_precio_venta)
        self.margen_beneficio.valueChanged.connect(self._calcular_precio_venta)

        return tab

    def _crear_tab_informacion_adicional(self):
        """Crea la pestaña de información adicional."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(12)

        # Observaciones
        observaciones_group = QGroupBox("Observaciones y Notas")
        observaciones_layout = QVBoxLayout(observaciones_group)

        self.observaciones = QTextEdit()
        self.observaciones.setMaximumHeight(150)
        self.observaciones.setPlaceholderText("Notas adicionales, instrucciones especiales, etc...")
        observaciones_layout.addWidget(self.observaciones)

        layout.addWidget(observaciones_group)

        # Información de fechas (solo lectura)
        if self.perfil and not self.duplicar:
            fechas_group = QGroupBox("Información de Fechas")
            fechas_layout = QFormLayout(fechas_group)

            fecha_creacion = QLabel(self.perfil.fecha_creacion.strftime("%d/%m/%Y %H:%M") if self.perfil.fecha_creacion else "No disponible")
            fechas_layout.addRow("Fecha de creación:", fecha_creacion)

            fecha_modificacion = QLabel(self.perfil.fecha_modificacion.strftime("%d/%m/%Y %H:%M") if self.perfil.fecha_modificacion else "No disponible")
            fechas_layout.addRow("Última modificación:", fecha_modificacion)

            layout.addWidget(fechas_group)

        layout.addStretch()
        return tab

    def _calcular_precio_venta(self):
        """Calcula automáticamente el precio de venta basado en precio de compra y margen."""
        precio_compra = self.precio_compra.value()
        margen = self.margen_beneficio.value()

        if precio_compra > 0 and margen > 0:
            precio_venta = precio_compra * (1 + margen / 100)
            self.precio_metro.setValue(precio_venta)

    def _cargar_combos(self):
        """Carga los datos de los combos."""
        db = next(get_db())
        try:
            # Cargar distribuidores
            distribuidores = db.query(Distribuidor).filter(Distribuidor.activo == True).order_by(Distribuidor.nombre).all()
            self.combo_distribuidor.addItem("Seleccionar distribuidor...", None)
            for distribuidor in distribuidores:
                self.combo_distribuidor.addItem(distribuidor.nombre, distribuidor.id)

            # Cargar series
            series = db.query(SeriePerfil).filter(SeriePerfil.activo == True).order_by(SeriePerfil.nombre).all()
            self.combo_serie.addItem("Seleccionar serie...", None)
            for serie in series:
                self.combo_serie.addItem(serie.nombre, serie.id)

            # Cargar tipos
            tipos = db.query(TipoPerfil).filter(TipoPerfil.activo == True).order_by(TipoPerfil.nombre).all()
            self.combo_tipo.addItem("Seleccionar tipo...", None)
            for tipo in tipos:
                self.combo_tipo.addItem(f"{tipo.nombre} ({tipo.categoria})", tipo.id)

        except Exception as e:
            print(f"Error cargando combos: {e}")
        finally:
            db.close()
    
    def _cargar_datos_perfil(self):
        """Carga los datos del perfil en el formulario."""
        if not self.perfil:
            return

        # Información básica
        if self.duplicar:
            self.codigo.setText(f"{self.perfil.codigo}_COPY")
            self.codigo.setReadOnly(False)
        else:
            self.codigo.setText(self.perfil.codigo)
            self.codigo.setReadOnly(True)  # No permitir editar el código

        self.descripcion.setText(self.perfil.descripcion)
        self.referencia.setText(self.perfil.referencia or "")

        # Seleccionar distribuidor
        if self.perfil.distribuidor_id:
            for i in range(self.combo_distribuidor.count()):
                if self.combo_distribuidor.itemData(i) == self.perfil.distribuidor_id:
                    self.combo_distribuidor.setCurrentIndex(i)
                    break

        # Seleccionar serie
        if self.perfil.serie_id:
            for i in range(self.combo_serie.count()):
                if self.combo_serie.itemData(i) == self.perfil.serie_id:
                    self.combo_serie.setCurrentIndex(i)
                    break

        # Seleccionar tipo
        if self.perfil.tipo_id:
            for i in range(self.combo_tipo.count()):
                if self.combo_tipo.itemData(i) == self.perfil.tipo_id:
                    self.combo_tipo.setCurrentIndex(i)
                    break

        # Material, color, acabado
        if self.perfil.material:
            self.combo_material.setCurrentText(self.perfil.material)
        self.color.setText(self.perfil.color or "")
        self.acabado.setText(self.perfil.acabado or "")

        # Características técnicas
        self.ancho.setValue(float(self.perfil.ancho or 0))
        self.alto.setValue(float(self.perfil.alto or 0))
        self.espesor.setValue(float(self.perfil.espesor or 0))
        self.peso_metro.setValue(float(self.perfil.peso_metro or 0))
        self.unidad_medida.setCurrentText(self.perfil.unidad_medida or "m")
        self.codigo_barras.setText(self.perfil.codigo_barras or "")

        # Precios y stock
        self.precio_compra.setValue(float(self.perfil.precio_compra or 0))
        self.margen_beneficio.setValue(float(self.perfil.margen_beneficio or 0))
        self.precio_metro.setValue(float(self.perfil.precio_metro))
        self.stock_minimo.setValue(int(self.perfil.stock_minimo or 0))

        # Información adicional
        self.observaciones.setPlainText(self.perfil.observaciones or "")

        # Estado
        self.activo.setChecked(bool(self.perfil.activo) if self.perfil.activo is not None else True)
    
    def _validar_formulario(self):
        """Valida los campos del formulario."""
        codigo = self.codigo.text().strip()
        descripcion = self.descripcion.text().strip()
        
        if not codigo:
            self.mostrar_error("El código es obligatorio")
            return False
            
        if not descripcion:
            self.mostrar_error("La descripción es obligatoria")
            return False
            
        self.ocultar_error()
        return True
    
    def mostrar_error(self, mensaje):
        """Muestra un mensaje de error en el formulario."""
        self.etiqueta_error.setText(mensaje)
        self.etiqueta_error.setVisible(True)
    
    def ocultar_error(self):
        """Oculta el mensaje de error."""
        self.etiqueta_error.setVisible(False)
    

    
    def get_datos(self):
        """
        Devuelve un diccionario con los datos del formulario.

        Returns:
            dict: Datos del perfil
        """
        return {
            'codigo': self.codigo.text().strip(),
            'descripcion': self.descripcion.text().strip(),
            'referencia': self.referencia.text().strip() or None,
            'distribuidor_id': self.combo_distribuidor.currentData(),
            'serie_id': self.combo_serie.currentData(),
            'tipo_id': self.combo_tipo.currentData(),
            'material': self.combo_material.currentText(),
            'color': self.color.text().strip() or None,
            'acabado': self.acabado.text().strip() or None,
            'ancho': float(self.ancho.value()) if self.ancho.value() > 0 else None,
            'alto': float(self.alto.value()) if self.alto.value() > 0 else None,
            'espesor': float(self.espesor.value()) if self.espesor.value() > 0 else None,
            'peso_metro': float(self.peso_metro.value()) if self.peso_metro.value() > 0 else None,
            'precio_metro': float(self.precio_metro.value()),
            'precio_compra': float(self.precio_compra.value()) if self.precio_compra.value() > 0 else None,
            'margen_beneficio': float(self.margen_beneficio.value()) if self.margen_beneficio.value() > 0 else None,
            'observaciones': self.observaciones.toPlainText().strip() or None,
            'codigo_barras': self.codigo_barras.text().strip() or None,
            'stock_minimo': int(self.stock_minimo.value()),
            'unidad_medida': self.unidad_medida.currentText(),
            'activo': self.activo.isChecked()
        }

    def _validar_y_aceptar(self):
        """Valida el formulario y acepta el diálogo si es válido."""
        if not self._validar_formulario():
            return

        # Validar que el código no esté duplicado (solo para nuevos perfiles o duplicados)
        if not self.perfil or self.duplicar:
            db = next(get_db())
            try:
                existe = db.query(Perfil).filter(Perfil.codigo == self.codigo.text().strip()).first()
                if existe:
                    self.mostrar_error("Ya existe un perfil con este código")
                    return
            finally:
                db.close()

        # Guardar el perfil
        self._guardar_perfil()

    def _guardar_perfil(self):
        """Guarda el perfil en la base de datos."""
        db = next(get_db())
        try:
            datos = self.get_datos()

            if self.perfil and not self.duplicar:
                # Actualizar perfil existente
                for key, value in datos.items():
                    if key != 'codigo':  # No cambiar el código
                        setattr(self.perfil, key, value)

                self.perfil.fecha_modificacion = datetime.now()
                db.commit()

                QMessageBox.information(
                    self,
                    "Éxito",
                    f"Perfil '{self.perfil.codigo}' actualizado correctamente.",
                    QMessageBox.StandardButton.Ok
                )
            else:
                # Crear nuevo perfil
                nuevo_perfil = Perfil(**datos)
                nuevo_perfil.fecha_creacion = datetime.now()
                nuevo_perfil.fecha_modificacion = datetime.now()

                db.add(nuevo_perfil)
                db.commit()

                QMessageBox.information(
                    self,
                    "Éxito",
                    f"Perfil '{datos['codigo']}' creado correctamente.",
                    QMessageBox.StandardButton.Ok
                )

            self.accept()

        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudo guardar el perfil: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        finally:
            db.close()

    def _gestionar_distribuidores_desde_edicion(self):
        """Gestiona distribuidores desde el diálogo de edición."""
        dialogo = DistribuidorDialog(self)
        dialogo.exec()
        self._cargar_combos()

    def _gestionar_series_desde_edicion(self):
        """Gestiona series desde el diálogo de edición."""
        dialogo = SerieDialog(self)
        dialogo.exec()
        self._cargar_combos()

    def _gestionar_tipos_desde_edicion(self):
        """Gestiona tipos desde el diálogo de edición."""
        dialogo = TipoDialog(self)
        dialogo.exec()
        self._cargar_combos()
