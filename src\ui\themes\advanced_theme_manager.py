"""
Gestor de temas avanzado para PRO-2000
Maneja la aplicación de temas modernos y estilos profesionales
"""

import os
import json
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QSettings, pyqtSignal, QObject
from PyQt6.QtGui import QFont, QPalette, QColor
from .modern_theme import ModernTheme
from .revolutionary_theme import RevolutionaryTheme
try:
    from ..effects.visual_effects import VisualEffectsManager, ThemeTransition
    EFFECTS_AVAILABLE = True
except ImportError:
    EFFECTS_AVAILABLE = False

try:
    import qdarkstyle
    QDARKSTYLE_AVAILABLE = True
except ImportError:
    QDARKSTYLE_AVAILABLE = False

try:
    import qtawesome as qta
    QTAWESOME_AVAILABLE = True
except ImportError:
    QTAWESOME_AVAILABLE = False


class AdvancedThemeManager(QObject):
    """Gestor avanzado de temas de la aplicación"""
    
    theme_changed = pyqtSignal(str)  # Señal cuando cambia el tema
    
    AVAILABLE_THEMES = {
        "revolutionary": "🚀 Revolucionario (NUEVO)",
        "professional": "Tema Profesional",
        "dark": "Tema Oscuro",
        "light": "Tema Claro",
        "modern_dark": "Moderno Oscuro",
        "modern_light": "Moderno Claro"
    }
    
    def __init__(self):
        super().__init__()
        self.settings = QSettings("PRO-2000", "ThemeSettings")
        self.current_theme = self.settings.value("current_theme", "revolutionary")
        
    def apply_theme(self, theme_name="revolutionary"):
        """Aplica un tema específico con efectos avanzados"""
        app = QApplication.instance()
        if not app:
            return

        try:
            print(f"🎨 Aplicando tema: {theme_name}")
            if theme_name == "revolutionary":
                print("🚀 Cargando tema revolucionario...")
                stylesheet = RevolutionaryTheme.get_complete_stylesheet()
                print(f"📝 Stylesheet generado: {len(stylesheet)} caracteres")
                self._setup_revolutionary_icons()
                self._setup_revolutionary_font(app)
            elif theme_name == "professional":
                stylesheet = ModernTheme.get_professional_stylesheet()
                self._setup_professional_icons()
            elif theme_name == "dark":
                stylesheet = ModernTheme.get_dark_theme_css()
                self._setup_dark_icons()
            elif theme_name == "modern_dark":
                if QDARKSTYLE_AVAILABLE:
                    stylesheet = qdarkstyle.load_stylesheet_pyqt6()
                else:
                    stylesheet = ModernTheme.get_dark_theme_css()
                self._setup_dark_icons()
            elif theme_name == "light":
                stylesheet = ModernTheme.get_light_theme_css()
                self._setup_light_icons()
            elif theme_name == "modern_light":
                stylesheet = self._get_modern_light_stylesheet()
                self._setup_light_icons()
            else:
                stylesheet = RevolutionaryTheme.get_complete_stylesheet()
                self._setup_revolutionary_icons()

            # Aplicar transición suave si es posible
            if EFFECTS_AVAILABLE and hasattr(app, 'activeWindow') and app.activeWindow():
                try:
                    ThemeTransition.fade_transition(app.activeWindow(), stylesheet, 300)
                except:
                    app.setStyleSheet(stylesheet)
            else:
                app.setStyleSheet(stylesheet)
                print("✅ Stylesheet aplicado directamente")

            if theme_name != "revolutionary":
                self._setup_modern_font(app)

            self.current_theme = theme_name
            self.settings.setValue("current_theme", theme_name)
            self.theme_changed.emit(theme_name)

        except Exception as e:
            print(f"Error aplicando tema {theme_name}: {e}")
            # Fallback al tema revolucionario
            app.setStyleSheet(RevolutionaryTheme.get_complete_stylesheet())
        
    def _setup_revolutionary_icons(self):
        """Configura iconos para el tema revolucionario"""
        if QTAWESOME_AVAILABLE:
            try:
                qta.set_defaults(
                    color=RevolutionaryTheme.COLORS['text_primary'],
                    color_disabled=RevolutionaryTheme.COLORS['text_muted']
                )
            except:
                pass

    def _setup_professional_icons(self):
        """Configura iconos para el tema profesional"""
        if QTAWESOME_AVAILABLE:
            try:
                qta.set_defaults(color=ModernTheme.PRIMARY_BLUE, color_disabled='#94a3b8')
            except:
                pass
        
    def _setup_dark_icons(self):
        """Configura iconos para temas oscuros"""
        if QTAWESOME_AVAILABLE:
            try:
                qta.set_defaults(color='#f1f5f9', color_disabled='#64748b')
            except:
                pass

    def _setup_light_icons(self):
        """Configura iconos para temas claros"""
        if QTAWESOME_AVAILABLE:
            try:
                qta.set_defaults(color='#334155', color_disabled='#94a3b8')
            except:
                pass
        
    def _setup_revolutionary_font(self, app):
        """Configura fuentes para el tema revolucionario"""
        font = QFont("Inter", 11)
        if not font.exactMatch():
            font = QFont("SF Pro Display", 11)
            if not font.exactMatch():
                font = QFont("Segoe UI", 11)
        font.setStyleHint(QFont.StyleHint.System)
        font.setWeight(QFont.Weight.Normal)
        app.setFont(font)

    def _setup_modern_font(self, app):
        """Configura fuentes modernas"""
        font = QFont("Segoe UI", 10)
        font.setStyleHint(QFont.StyleHint.System)
        app.setFont(font)
        
    def _get_modern_light_stylesheet(self):
        """Retorna stylesheet moderno claro"""
        return f"""
        QMainWindow {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f8fafc);
            color: #0f172a;
            font-family: 'Segoe UI', system-ui, sans-serif;
        }}
        
        QPushButton {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3b82f6, stop:1 #2563eb);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
        }}
        
        QPushButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #60a5fa, stop:1 #3b82f6);
        }}
        
        QLineEdit {{
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 14px;
        }}
        
        QLineEdit:focus {{
            border-color: #3b82f6;
        }}
        
        QTableWidget {{
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            gridline-color: #f1f5f9;
        }}
        
        QHeaderView::section {{
            background: #f8fafc;
            border: none;
            border-bottom: 1px solid #e2e8f0;
            padding: 12px 8px;
            font-weight: 600;
        }}
        """
        
    def toggle_theme(self):
        """Alterna entre temas principales"""
        if self.current_theme == "revolutionary":
            self.apply_theme("professional")
        elif self.current_theme == "professional":
            self.apply_theme("dark")
        elif self.current_theme == "dark":
            self.apply_theme("revolutionary")
        else:
            self.apply_theme("revolutionary")
        
    def get_current_theme(self):
        """Retorna el tema actual"""
        return self.current_theme
        
    def get_available_themes(self):
        """Retorna lista de temas disponibles"""
        return self.AVAILABLE_THEMES
        
    def load_saved_theme(self):
        """Carga el tema guardado"""
        saved_theme = self.settings.value("current_theme", "revolutionary")
        self.apply_theme(saved_theme)
        
    def get_theme_icon(self, icon_name, size=16):
        """Obtiene un icono apropiado para el tema actual"""
        if QTAWESOME_AVAILABLE:
            try:
                if self.current_theme in ["dark", "modern_dark"]:
                    color = '#f1f5f9'
                else:
                    color = ModernTheme.PRIMARY_BLUE

                return qta.icon(icon_name, color=color, scale_factor=size/16)
            except:
                return None
        return None

    def apply_revolutionary_theme_to_widget(self, widget):
        """Aplica el tema revolucionario a un widget específico con efectos"""
        widget.setStyleSheet(RevolutionaryTheme.get_complete_stylesheet())

        # Añadir efectos visuales si están disponibles
        if EFFECTS_AVAILABLE:
            try:
                VisualEffectsManager.add_glow_effect(widget, RevolutionaryTheme.COLORS['primary'])
                VisualEffectsManager.add_fade_in_animation(widget, 400)
            except:
                pass

    def apply_professional_theme_to_widget(self, widget):
        """Aplica el tema profesional a un widget específico"""
        widget.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8fafc);
                color: {ModernTheme.TEXT_PRIMARY_LIGHT};
                font-family: 'Segoe UI', system-ui, sans-serif;
            }}

            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {ModernTheme.PRIMARY_BLUE}, stop:1 {ModernTheme.PRIMARY_DARK});
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: 600;
                font-size: 14px;
            }}

            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {ModernTheme.SECONDARY_BLUE}, stop:1 {ModernTheme.PRIMARY_BLUE});
            }}

            QLineEdit {{
                background: white;
                border: 2px solid {ModernTheme.BORDER_LIGHT};
                border-radius: 8px;
                padding: 10px 12px;
                font-size: 14px;
            }}

            QLineEdit:focus {{
                border-color: {ModernTheme.PRIMARY_BLUE};
            }}

            QComboBox {{
                background: white;
                border: 2px solid {ModernTheme.BORDER_LIGHT};
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
            }}

            QComboBox:focus {{
                border-color: {ModernTheme.PRIMARY_BLUE};
            }}

            QTableWidget {{
                background: white;
                border: 1px solid {ModernTheme.BORDER_LIGHT};
                border-radius: 8px;
                gridline-color: {ModernTheme.SURFACE_LIGHT};
            }}

            QHeaderView::section {{
                background: {ModernTheme.BG_LIGHT_SECONDARY};
                border: none;
                border-bottom: 1px solid {ModernTheme.BORDER_LIGHT};
                padding: 12px 8px;
                font-weight: 600;
                color: {ModernTheme.TEXT_PRIMARY_LIGHT};
            }}

            QGroupBox {{
                font-weight: 600;
                border: 2px solid {ModernTheme.BORDER_LIGHT};
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 8px;
                color: {ModernTheme.TEXT_PRIMARY_LIGHT};
            }}

            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                background: white;
                color: {ModernTheme.TEXT_PRIMARY_LIGHT};
            }}
        """)
