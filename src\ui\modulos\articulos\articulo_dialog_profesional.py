            f"📝 <b>{self.articulo_actual.codigo}</b> - {self.articulo_actual.descripcion}<br>"
            f"<span style='color: #64748b;'>Serie: {self.articulo_actual.serie or 'Sin serie'}</span>"
            )
            
            # Cargar datos en formularios
            self.campo_codigo.setText(self.articulo_actual.codigo)
            self.campo_serie.setText(self.articulo_actual.serie or "")
            self.campo_descripcion.setText(self.articulo_actual.descripcion)
            self.campo_tiempo_taller.setValue(self.articulo_actual.tiempo_taller or 0.0)
            self.campo_tiempo_obra.setValue(self.articulo_actual.tiempo_obra or 0.0)
            self.campo_dibujo.setText(self.articulo_actual.dibujo_path or "")
            self.campo_imagen.setText(self.articulo_actual.imagen_path or "")
            self.campo_activo.setChecked(self.articulo_actual.activo)
            
            # Actualizar vista previa de imagen
            self._actualizar_preview_imagen(self.articulo_actual.imagen_path)
            
            # Cargar componentes
            self._cargar_perfiles_articulo()
            self._cargar_accesorios_articulo()
            self._cargar_cristales_articulo()
            
        except Exception as e:
            self.status_bar.set_status(f"❌ Error cargando detalles: {str(e)}", "error")
    
    # Métodos de validación
    def _validar_codigo(self):
        """Valida el código en tiempo real"""
        codigo = self.campo_codigo.text().strip().upper()
        
        if not codigo:
            self.label_validacion.setText("")
            return
        
        # Validar formato
        import re
        if not re.match(r'^[A-Z0-9]+$', codigo):
            self.label_validacion.setText("❌ Solo letras y números")
            self.label_validacion.setStyleSheet("color: #dc2626; font-size: 12px;")
            return
        
        # Verificar duplicados
        if self._codigo_existe(codigo):
            self.label_validacion.setText("❌ Código ya existe")
            self.label_validacion.setStyleSheet("color: #dc2626; font-size: 12px;")
        else:
            self.label_validacion.setText("✅ Código válido")
            self.label_validacion.setStyleSheet("color: #059669; font-size: 12px;")
    
    def _codigo_existe(self, codigo):
        """Verifica si un código ya existe"""
        if not codigo:
            return False
        
        articulo_actual_id = self.articulo_actual.id if self.articulo_actual else None
        
        for articulo in self.articulos:
            if (articulo.codigo.upper() == codigo.upper() and 
                articulo.id != articulo_actual_id):
                return True
        return False
    
    def _actualizar_contador(self):
        """Actualiza el contador de caracteres"""
        texto = self.campo_descripcion.text()
        longitud = len(texto)
        max_longitud = 200
        
        self.label_contador.setText(f"{longitud}/{max_longitud} caracteres")
        
        if longitud > max_longitud * 0.9:
            self.label_contador.setStyleSheet("color: #dc2626; font-size: 11px; font-weight: bold;")
        elif longitud > max_longitud * 0.7:
            self.label_contador.setStyleSheet("color: #f59e0b; font-size: 11px;")
        else:
            self.label_contador.setStyleSheet("color: #64748b; font-size: 11px;")
    
    # Métodos de archivos
    def _seleccionar_dibujo(self):
        """Selecciona archivo de dibujo"""
        archivo, _ = QFileDialog.getOpenFileName(
            self,
            "Seleccionar Archivo de Dibujo",
            "",
            "Archivos de dibujo (*.dwg *.dxf *.pdf);;Todos los archivos (*)"
        )
        if archivo:
            self.campo_dibujo.setText(archivo)
    
    def _seleccionar_imagen(self):
        """Selecciona imagen del artículo"""
        archivo, _ = QFileDialog.getOpenFileName(
            self,
            "Seleccionar Imagen",
            "",
            "Imágenes (*.png *.jpg *.jpeg *.bmp *.gif);;Todos los archivos (*)"
        )
        if archivo:
            self.campo_imagen.setText(archivo)
            self._actualizar_preview_imagen(archivo)
    
    def _actualizar_preview_imagen(self, ruta_imagen):
        """Actualiza la vista previa de imagen"""
        try:
            if ruta_imagen and os.path.exists(ruta_imagen):
                pixmap = QPixmap(ruta_imagen)
                if not pixmap.isNull():
                    pixmap_escalado = pixmap.scaled(
                        120, 120,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    self.label_preview.setPixmap(pixmap_escalado)
                    self.label_preview.setText("")
                else:
                    self.label_preview.clear()
                    self.label_preview.setText("❌ Error al cargar imagen")
            else:
                self.label_preview.clear()
                self.label_preview.setText("Sin imagen seleccionada")
        except Exception:
            self.label_preview.clear()
            self.label_preview.setText("❌ Error al cargar imagen")
    
    # Métodos de filtrado
    def _filtrar_articulos(self, texto_busqueda):
        """Filtra artículos según texto de búsqueda"""
        texto = texto_busqueda.lower()
        
        for fila in range(self.tabla_articulos.rowCount()):
            codigo = self.tabla_articulos.item(fila, 0).text().lower()
            descripcion = self.tabla_articulos.item(fila, 1).text().lower()
            serie = self.tabla_articulos.item(fila, 2).text().lower()
            
            visible = (texto in codigo or texto in descripcion or texto in serie)
            self.tabla_articulos.setRowHidden(fila, not visible)
    
    # Métodos de acciones principales
    def _nuevo_articulo(self):
        """Crea un nuevo artículo"""
        # Aquí iría la lógica para abrir el diálogo de nuevo artículo
        QMessageBox.information(self, "Nuevo Artículo", "Funcionalidad en desarrollo")
    
    def _duplicar_articulo(self):
        """Duplica el artículo seleccionado"""
        if not self.articulo_actual:
            return
        
        respuesta = create_professional_message_box(
            "Duplicar Artículo",
            f"¿Desea duplicar el artículo '{self.articulo_actual.codigo}'?",
            "question",
            self
        )
        
        if respuesta.exec() == QMessageBox.StandardButton.Yes:
            # Lógica de duplicación
            QMessageBox.information(self, "Duplicar", "Funcionalidad en desarrollo")
    
    def _eliminar_articulo(self):
        """Elimina el artículo seleccionado"""
        if not self.articulo_actual:
            return
        
        respuesta = create_professional_message_box(
            "Eliminar Artículo",
            f"¿Está seguro de eliminar el artículo '{self.articulo_actual.codigo}'?\n"
            "Esta acción no se puede deshacer.",
            "question",
            self
        )
        
        if respuesta.exec() == QMessageBox.StandardButton.Yes:
            try:
                db = next(get_db())
                try:
                    articulo_db = db.query(Articulo).filter(
                        Articulo.id == self.articulo_actual.id
                    ).first()
                    
                    if articulo_db:
                        db.delete(articulo_db)
                        db.commit()
                        
                        self.status_bar.set_status("✅ Artículo eliminado", "success")
                        self._cargar_articulos()
                        self._limpiar_seleccion()
                        
                        create_professional_message_box(
                            "Éxito", "Artículo eliminado correctamente", "info", self
                        ).exec()
                        
                except Exception as e:
                    db.rollback()
                    raise e
                finally:
                    db.close()
                    
            except Exception as e:
                self.status_bar.set_status(f"❌ Error eliminando: {str(e)}", "error")
                create_professional_message_box(
                    "Error", f"Error al eliminar artículo: {str(e)}", "error", self
                ).exec()
    
    def _exportar_articulos(self):
        """Exporta la lista de artículos"""
        try:
            import csv
            from datetime import datetime
            
            archivo, _ = QFileDialog.getSaveFileName(
                self,
                "Exportar Artículos",
                f"articulos_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "Archivos CSV (*.csv)"
            )
            
            if not archivo:
                return
            
            with open(archivo, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                
                # Encabezados
                writer.writerow([
                    'Código', 'Serie', 'Descripción', 'Tiempo Taller',
                    'Tiempo Obra', 'Activo', 'Fecha Creación'
                ])
                
                # Datos
                for articulo in self.articulos:
                    writer.writerow([
                        articulo.codigo,
                        articulo.serie or '',
                        articulo.descripcion,
                        articulo.tiempo_taller or 0,
                        articulo.tiempo_obra or 0,
                        'Sí' if articulo.activo else 'No',
                        articulo.fecha_creacion.strftime('%Y-%m-%d %H:%M:%S') 
                        if articulo.fecha_creacion else ''
                    ])
            
            self.status_bar.set_status("✅ Exportación completada", "success")
            create_professional_message_box(
                "Exportación Exitosa",
                f"Se exportaron {len(self.articulos)} artículos a:\n{archivo}",
                "info", self
            ).exec()
            
        except Exception as e:
            self.status_bar.set_status(f"❌ Error exportando: {str(e)}", "error")
            create_professional_message_box(
                "Error", f"Error al exportar: {str(e)}", "error", self
            ).exec()
    
    def _guardar_datos_generales(self):
        """Guarda los datos generales del artículo"""
        if not self.articulo_actual:
            return
        
        # Validar datos requeridos
        if not self.campo_codigo.text().strip():
            create_professional_message_box(
                "Error", "El código es obligatorio", "error", self
            ).exec()
            return
        
        if not self.campo_descripcion.text().strip():
            create_professional_message_box(
                "Error", "La descripción es obligatoria", "error", self
            ).exec()
            return
        
        try:
            db = next(get_db())
            try:
                # Verificar código único
                codigo_nuevo = self.campo_codigo.text().strip()
                if codigo_nuevo != self.articulo_actual.codigo:
                    existe = db.query(Articulo).filter(
                        Articulo.codigo == codigo_nuevo,
                        Articulo.id != self.articulo_actual.id
                    ).first()
                    
                    if existe:
                        create_professional_message_box(
                            "Error", "Ya existe un artículo con este código", "error", self
                        ).exec()
                        return
                
                # Actualizar artículo
                articulo_db = db.query(Articulo).filter(
                    Articulo.id == self.articulo_actual.id
                ).first()
                
                if articulo_db:
                    articulo_db.codigo = codigo_nuevo
                    articulo_db.serie = self.campo_serie.text().strip() or None
                    articulo_db.descripcion = self.campo_descripcion.text().strip()
                    articulo_db.tiempo_taller = self.campo_tiempo_taller.value()
                    articulo_db.tiempo_obra = self.campo_tiempo_obra.value()
                    articulo_db.dibujo_path = self.campo_dibujo.text().strip() or None
                    articulo_db.imagen_path = self.campo_imagen.text().strip() or None
                    articulo_db.activo = self.campo_activo.isChecked()
                    
                    db.commit()
                    
                    # Actualizar objeto local
                    self.articulo_actual.codigo = articulo_db.codigo
                    self.articulo_actual.serie = articulo_db.serie
                    self.articulo_actual.descripcion = articulo_db.descripcion
                    self.articulo_actual.tiempo_taller = articulo_db.tiempo_taller
                    self.articulo_actual.tiempo_obra = articulo_db.tiempo_obra
                    self.articulo_actual.dibujo_path = articulo_db.dibujo_path
                    self.articulo_actual.imagen_path = articulo_db.imagen_path
                    self.articulo_actual.activo = articulo_db.activo
                    
                    self.status_bar.set_status("✅ Datos guardados correctamente", "success")
                    self._cargar_articulos()  # Recargar lista
                    
                    create_professional_message_box(
                        "Éxito", "Datos guardados correctamente", "info", self
                    ).exec()
                    
            finally:
                db.close()
                
        except Exception as e:
            self.status_bar.set_status(f"❌ Error guardando: {str(e)}", "error")
            create_professional_message_box(
                "Error", f"Error al guardar: {str(e)}", "error", self
            ).exec()
    
    # Métodos para cargar componentes
    def _cargar_perfiles_articulo(self):
        """Carga los perfiles del artículo"""
        if not self.articulo_actual:
            return
        
        try:
            db = next(get_db())
            try:
                perfiles = db.query(ArticuloPerfil).filter(
                    ArticuloPerfil.articulo_id == self.articulo_actual.id
                ).order_by(ArticuloPerfil.orden).all()
                
                self.tabla_perfiles.setRowCount(len(perfiles))
                
                for fila, perfil_comp in enumerate(perfiles):
                    # Perfil
                    item_perfil = QTableWidgetItem(
                        perfil_comp.perfil.descripcion if perfil_comp.perfil else ""
                    )
                    item_perfil.setData(Qt.ItemDataRole.UserRole, perfil_comp.id)
                    self.tabla_perfiles.setItem(fila, 0, item_perfil)
                    
                    # Resto de columnas
                    tipo_perfil = getattr(perfil_comp, 'tipo_perfil', 'marco') or 'marco'
                    self.tabla_perfiles.setItem(fila, 1, QTableWidgetItem(tipo_perfil.title()))
                    
                    self.tabla_perfiles.setItem(fila, 2, QTableWidgetItem(perfil_comp.medida_formula or ""))
                    
                    variable = getattr(perfil_comp, 'variable_medida', 'H') or 'H'
                    self.tabla_perfiles.setItem(fila, 3, QTableWidgetItem(variable))
                    
                    angulo_izq = getattr(perfil_comp, 'angulo_izquierdo', 90.0) or 90.0
                    angulo_der = getattr(perfil_comp, 'angulo_derecho', 90.0) or 90.0
                    self.tabla_perfiles.setItem(fila, 4, QTableWidgetItem(f"{angulo_izq:.0f}°/{angulo_der:.0f}°"))
                    
                    self.tabla_perfiles.setItem(fila, 5, QTableWidgetItem(perfil_comp.condicion or ""))
                    self.tabla_perfiles.setItem(fila, 6, QTableWidgetItem(str(perfil_comp.cantidad_base)))
                    self.tabla_perfiles.setItem(fila, 7, QTableWidgetItem(perfil_comp.cantidad_formula or ""))
                    self.tabla_perfiles.setItem(fila, 8, QTableWidgetItem(str(perfil_comp.orden)))
                
            finally:
                db.close()
                
        except Exception as e:
            self.status_bar.set_status(f"❌ Error cargando perfiles: {str(e)}", "error")
    
    def _cargar_accesorios_articulo(self):
        """Carga los accesorios del artículo"""
        if not self.articulo_actual:
            return
        
        try:
            db = next(get_db())
            try:
                accesorios = db.query(ArticuloAccesorio).filter(
                    ArticuloAccesorio.articulo_id == self.articulo_actual.id
                ).order_by(ArticuloAccesorio.orden).all()
                
                self.tabla_accesorios.setRowCount(len(accesorios))
                
                for fila, acc_comp in enumerate(accesorios):
                    # Accesorio
                    item_acc = QTableWidgetItem(
                        acc_comp.accesorio.descripcion if acc_comp.accesorio else ""
                    )
                    item_acc.setData(Qt.ItemDataRole.UserRole, acc_comp.id)
                    self.tabla_accesorios.setItem(fila, 0, item_acc)
                    
                    # Resto de columnas
                    self.tabla_accesorios.setItem(fila, 1, QTableWidgetItem(acc_comp.condicion or ""))
                    self.tabla_accesorios.setItem(fila, 2, QTableWidgetItem(str(acc_comp.cantidad_base)))
                    self.tabla_accesorios.setItem(fila, 3, QTableWidgetItem(acc_comp.cantidad_formula or ""))
                    self.tabla_accesorios.setItem(fila, 4, QTableWidgetItem(str(acc_comp.orden)))
                
            finally:
                db.close()
                
        except Exception as e:
            self.status_bar.set_status(f"❌ Error cargando accesorios: {str(e)}", "error")
    
    def _cargar_cristales_articulo(self):
        """Carga los cristales del artículo"""
        if not self.articulo_actual:
            return
        
        try:
            db = next(get_db())
            try:
                cristales = db.query(ArticuloCristal).filter(
                    ArticuloCristal.articulo_id == self.articulo_actual.id
                ).order_by(ArticuloCristal.orden).all()
                
                self.tabla_cristales.setRowCount(len(cristales))
                
                for fila, cris_comp in enumerate(cristales):
                    # Cristal
                    item_cris = QTableWidgetItem(
                        cris_comp.cristal.descripcion if cris_comp.cristal else ""
                    )
                    item_cris.setData(Qt.ItemDataRole.UserRole, cris_comp.id)
                    self.tabla_cristales.setItem(fila, 0, item_cris)
                    
                    # Resto de columnas
                    self.tabla_cristales.setItem(fila, 1, QTableWidgetItem(cris_comp.medida_formula or ""))
                    self.tabla_cristales.setItem(fila, 2, QTableWidgetItem(cris_comp.condicion or ""))
                    self.tabla_cristales.setItem(fila, 3, QTableWidgetItem(str(cris_comp.cantidad_base)))
                    self.tabla_cristales.setItem(fila, 4, QTableWidgetItem(cris_comp.cantidad_formula or ""))
                    self.tabla_cristales.setItem(fila, 5, QTableWidgetItem(str(cris_comp.orden)))
                
            finally:
                db.close()
                
        except Exception as e:
            self.status_bar.set_status(f"❌ Error cargando cristales: {str(e)}", "error")
    
    # Métodos de gestión de componentes (placeholders)
    def _agregar_perfil(self):
        """Agrega un perfil al artículo"""
        QMessageBox.information(self, "Agregar Perfil", "Funcionalidad en desarrollo")
    
    def _editar_perfil(self):
        """Edita un perfil del artículo"""
        QMessageBox.information(self, "Editar Perfil", "Funcionalidad en desarrollo")
    
    def _eliminar_perfil(self):
        """Elimina un perfil del artículo"""
        QMessageBox.information(self, "Eliminar Perfil", "Funcionalidad en desarrollo")
    
    def _ordenar_perfiles(self):
        """Reordena los perfiles del artículo"""
        QMessageBox.information(self, "Ordenar Perfiles", "Funcionalidad en desarrollo")
    
    def _agregar_accesorio(self):
        """Agrega un accesorio al artículo"""
        QMessageBox.information(self, "Agregar Accesorio", "Funcionalidad en desarrollo")
    
    def _eliminar_accesorio(self):
        """Elimina un accesorio del artículo"""
        QMessageBox.information(self, "Eliminar Accesorio", "Funcionalidad en desarrollo")
    
    def _ordenar_accesorios(self):
        """Reordena los accesorios del artículo"""
        QMessageBox.information(self, "Ordenar Accesorios", "Funcionalidad en desarrollo")
    
    def _agregar_cristal(self):
        """Agrega un cristal al artículo"""
        QMessageBox.information(self, "Agregar Cristal", "Funcionalidad en desarrollo")
    
    def _eliminar_cristal(self):
        """Elimina un cristal del artículo"""
        QMessageBox.information(self, "Eliminar Cristal", "Funcionalidad en desarrollo")
    
    def _ordenar_cristales(self):
        """Reordena los cristales del artículo"""
        QMessageBox.information(self, "Ordenar Cristales", "Funcionalidad en desarrollo")
    
    def _calcular_materiales(self):
        """Calcula los materiales necesarios"""
        if not self.articulo_actual:
            self.texto_resultados.setText("❌ No hay artículo seleccionado")
            return
        
        altura = self.campo_altura_sim.value()
        anchura = self.campo_anchura_sim.value()
        
        try:
            # Simular cálculo de materiales
            resultado = f"""
🧮 CÁLCULO DE MATERIALES
{'='*50}
📐 Artículo: {self.articulo_actual.codigo} - {self.articulo_actual.descripcion}
📏 Medidas: {altura} x {anchura} mm
📊 Variables: H={altura}, A={anchura}, L={max(altura, anchura)}, P={2*altura + 2*anchura}, S={altura*anchura}

🔍 ANÁLISIS:
{'='*50}
⚠️  Esta funcionalidad está en desarrollo.
📋 En la versión final calculará automáticamente:
   • Cantidad de perfiles necesarios
   • Metros lineales totales
   • Superficies de cristal
   • Cantidad de accesorios
   • Costes estimados

💡 Para implementar esta funcionalidad, se necesita:
   • Fórmulas de cálculo configuradas en cada componente
   • Sistema de evaluación de expresiones matemáticas
   • Base de datos de precios actualizada
            """
            
            self.texto_resultados.setText(resultado)
            
        except Exception as e:
            self.texto_resultados.setText(f"❌ Error en el cálculo: {str(e)}")


# Reemplazar el diálogo original con la versión profesional
ArticuloDialog = ArticuloDialogProfesional
