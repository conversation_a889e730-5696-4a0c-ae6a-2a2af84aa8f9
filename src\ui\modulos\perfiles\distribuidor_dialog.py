"""
Diálogo para gestionar distribuidores de perfiles
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QLineEdit, QTableWidget, QTableWidgetItem, QMessageBox, QHeaderView,
    QDialogButtonBox, QFormLayout, QTextEdit, QCheckBox, QWidget
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from datetime import datetime

from models.base import get_db
from models.perfil import Distribuidor
from ui.utils.window_utils import smart_dialog_setup


class DistribuidorDialog(QDialog):
    """Diálogo para gestionar distribuidores."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Gestión de Distribuidores")

        # Configurar geometría inteligente
        smart_dialog_setup(self, "management", "Gestión de Distribuidores")

        self.distribuidor_actual = None
        self._configurar_ui()
        self._cargar_distribuidores()
        
    def _configurar_ui(self):
        """Configura la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)
        layout_principal.setContentsMargins(16, 16, 16, 16)
        layout_principal.setSpacing(16)
        
        # Título
        titulo = QLabel("🏢 Gestión de Distribuidores")
        titulo.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        titulo.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout_principal.addWidget(titulo)
        
        # Layout horizontal para tabla y formulario
        layout_horizontal = QHBoxLayout()
        
        # Panel izquierdo - Lista de distribuidores
        panel_izquierdo = self._crear_panel_lista()
        layout_horizontal.addWidget(panel_izquierdo, 2)
        
        # Panel derecho - Formulario
        panel_derecho = self._crear_panel_formulario()
        layout_horizontal.addWidget(panel_derecho, 1)
        
        layout_principal.addLayout(layout_horizontal)
        
        # Botones de diálogo
        botones = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        botones.rejected.connect(self.accept)
        layout_principal.addWidget(botones)
        
    def _crear_panel_lista(self):
        """Crea el panel de lista de distribuidores."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(0, 0, 8, 0)
        
        # Título y botones
        header_layout = QHBoxLayout()
        
        titulo_lista = QLabel("Lista de Distribuidores")
        titulo_lista.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        header_layout.addWidget(titulo_lista)
        
        header_layout.addStretch()
        
        self.btn_actualizar = QPushButton("🔄 Actualizar")
        self.btn_actualizar.clicked.connect(self._cargar_distribuidores)
        header_layout.addWidget(self.btn_actualizar)
        
        layout.addLayout(header_layout)
        
        # Tabla
        self.tabla_distribuidores = QTableWidget()
        self.tabla_distribuidores.setColumnCount(5)
        self.tabla_distribuidores.setHorizontalHeaderLabels([
            "Código", "Nombre", "Contacto", "Teléfono", "Estado"
        ])
        
        # Configurar tabla
        self.tabla_distribuidores.setAlternatingRowColors(False)  # Desactivar filas alternadas
        self.tabla_distribuidores.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        header = self.tabla_distribuidores.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        
        self.tabla_distribuidores.itemSelectionChanged.connect(self._on_seleccion_cambiada)
        
        layout.addWidget(self.tabla_distribuidores)
        
        return panel
        
    def _crear_panel_formulario(self):
        """Crea el panel del formulario."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(8, 0, 0, 0)
        
        # Título
        titulo_form = QLabel("Datos del Distribuidor")
        titulo_form.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(titulo_form)
        
        # Formulario
        form_layout = QFormLayout()
        form_layout.setSpacing(12)
        
        # Código
        self.codigo = QLineEdit()
        self.codigo.setMaxLength(10)
        self.codigo.setPlaceholderText("Ej: CORTIZO")
        form_layout.addRow("Código*:", self.codigo)
        
        # Nombre
        self.nombre = QLineEdit()
        self.nombre.setMaxLength(100)
        self.nombre.setPlaceholderText("Nombre de la empresa")
        form_layout.addRow("Nombre*:", self.nombre)
        
        # Contacto
        self.contacto = QLineEdit()
        self.contacto.setMaxLength(100)
        self.contacto.setPlaceholderText("Persona de contacto")
        form_layout.addRow("Contacto:", self.contacto)
        
        # Teléfono
        self.telefono = QLineEdit()
        self.telefono.setMaxLength(20)
        self.telefono.setPlaceholderText("Número de teléfono")
        form_layout.addRow("Teléfono:", self.telefono)
        
        # Email
        self.email = QLineEdit()
        self.email.setMaxLength(100)
        self.email.setPlaceholderText("<EMAIL>")
        form_layout.addRow("Email:", self.email)
        
        # Dirección
        self.direccion = QTextEdit()
        self.direccion.setMaximumHeight(80)
        self.direccion.setPlaceholderText("Dirección completa")
        form_layout.addRow("Dirección:", self.direccion)
        
        # Activo
        self.activo = QCheckBox("Distribuidor activo")
        self.activo.setChecked(True)
        form_layout.addRow("Estado:", self.activo)
        
        layout.addLayout(form_layout)
        
        # Mensaje de error
        self.label_error = QLabel()
        self.label_error.setStyleSheet("color: #e74c3c; font-weight: bold;")
        self.label_error.setVisible(False)
        layout.addWidget(self.label_error)
        
        # Botones de acción
        botones_layout = QVBoxLayout()
        botones_layout.setSpacing(8)
        
        self.btn_nuevo = QPushButton("➕ Nuevo")
        self.btn_nuevo.setStyleSheet(self._get_button_style("#27ae60"))
        self.btn_nuevo.clicked.connect(self._nuevo_distribuidor)
        botones_layout.addWidget(self.btn_nuevo)
        
        self.btn_guardar = QPushButton("💾 Guardar")
        self.btn_guardar.setStyleSheet(self._get_button_style("#3498db"))
        self.btn_guardar.setEnabled(False)
        self.btn_guardar.clicked.connect(self._guardar_distribuidor)
        botones_layout.addWidget(self.btn_guardar)
        
        self.btn_eliminar = QPushButton("🗑️ Eliminar")
        self.btn_eliminar.setStyleSheet(self._get_button_style("#e74c3c"))
        self.btn_eliminar.setEnabled(False)
        self.btn_eliminar.clicked.connect(self._eliminar_distribuidor)
        botones_layout.addWidget(self.btn_eliminar)
        
        layout.addLayout(botones_layout)
        layout.addStretch()
        
        return panel
    
    def _get_button_style(self, color):
        """Obtiene el estilo para botones."""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 10px 16px;
                border-radius: 6px;
                font-weight: bold;
                min-height: 35px;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
            }}
            QPushButton:pressed {{
                background-color: {color}aa;
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
        """
    
    def _cargar_distribuidores(self):
        """Carga la lista de distribuidores."""
        db = next(get_db())
        try:
            distribuidores = db.query(Distribuidor).order_by(Distribuidor.nombre).all()
            
            self.tabla_distribuidores.setRowCount(len(distribuidores))
            
            for fila, distribuidor in enumerate(distribuidores):
                # Código
                self.tabla_distribuidores.setItem(fila, 0, QTableWidgetItem(distribuidor.codigo))
                
                # Nombre
                self.tabla_distribuidores.setItem(fila, 1, QTableWidgetItem(distribuidor.nombre))
                
                # Contacto
                contacto = distribuidor.contacto or ""
                self.tabla_distribuidores.setItem(fila, 2, QTableWidgetItem(contacto))
                
                # Teléfono
                telefono = distribuidor.telefono or ""
                self.tabla_distribuidores.setItem(fila, 3, QTableWidgetItem(telefono))
                
                # Estado
                estado = "✅ Activo" if distribuidor.activo else "❌ Inactivo"
                estado_item = QTableWidgetItem(estado)
                estado_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.tabla_distribuidores.setItem(fila, 4, estado_item)
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error cargando distribuidores: {str(e)}")
        finally:
            db.close()
    
    def _on_seleccion_cambiada(self):
        """Maneja el cambio de selección."""
        fila = self.tabla_distribuidores.currentRow()
        if fila >= 0:
            codigo = self.tabla_distribuidores.item(fila, 0).text()
            self._cargar_distribuidor(codigo)
            self.btn_guardar.setEnabled(True)
            self.btn_eliminar.setEnabled(True)
        else:
            self._limpiar_formulario()
            self.btn_guardar.setEnabled(False)
            self.btn_eliminar.setEnabled(False)
    
    def _cargar_distribuidor(self, codigo):
        """Carga un distribuidor en el formulario."""
        db = next(get_db())
        try:
            distribuidor = db.query(Distribuidor).filter(Distribuidor.codigo == codigo).first()
            if distribuidor:
                self.distribuidor_actual = distribuidor
                self.codigo.setText(distribuidor.codigo)
                self.codigo.setReadOnly(True)
                self.nombre.setText(distribuidor.nombre)
                self.contacto.setText(distribuidor.contacto or "")
                self.telefono.setText(distribuidor.telefono or "")
                self.email.setText(distribuidor.email or "")
                self.direccion.setPlainText(distribuidor.direccion or "")
                self.activo.setChecked(bool(distribuidor.activo) if distribuidor.activo is not None else True)
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error cargando distribuidor: {str(e)}")
        finally:
            db.close()
    
    def _nuevo_distribuidor(self):
        """Prepara el formulario para un nuevo distribuidor."""
        self._limpiar_formulario()
        self.distribuidor_actual = None
        self.codigo.setReadOnly(False)
        self.btn_guardar.setEnabled(True)
        self.btn_eliminar.setEnabled(False)
        self.codigo.setFocus()
    
    def _limpiar_formulario(self):
        """Limpia el formulario."""
        self.codigo.clear()
        self.nombre.clear()
        self.contacto.clear()
        self.telefono.clear()
        self.email.clear()
        self.direccion.clear()
        self.activo.setChecked(True)
        self.label_error.setVisible(False)
    
    def _validar_formulario(self):
        """Valida el formulario."""
        if not self.codigo.text().strip():
            self._mostrar_error("El código es obligatorio")
            return False
        
        if not self.nombre.text().strip():
            self._mostrar_error("El nombre es obligatorio")
            return False
        
        self.label_error.setVisible(False)
        return True
    
    def _mostrar_error(self, mensaje):
        """Muestra un mensaje de error."""
        self.label_error.setText(mensaje)
        self.label_error.setVisible(True)
    
    def _guardar_distribuidor(self):
        """Guarda el distribuidor."""
        if not self._validar_formulario():
            return
        
        db = next(get_db())
        try:
            if self.distribuidor_actual:
                # Actualizar existente
                self.distribuidor_actual.nombre = self.nombre.text().strip()
                self.distribuidor_actual.contacto = self.contacto.text().strip() or None
                self.distribuidor_actual.telefono = self.telefono.text().strip() or None
                self.distribuidor_actual.email = self.email.text().strip() or None
                self.distribuidor_actual.direccion = self.direccion.toPlainText().strip() or None
                self.distribuidor_actual.activo = self.activo.isChecked()
            else:
                # Crear nuevo
                # Verificar que no exista el código
                existe = db.query(Distribuidor).filter(Distribuidor.codigo == self.codigo.text().strip()).first()
                if existe:
                    self._mostrar_error("Ya existe un distribuidor con este código")
                    return
                
                nuevo_distribuidor = Distribuidor(
                    codigo=self.codigo.text().strip(),
                    nombre=self.nombre.text().strip(),
                    contacto=self.contacto.text().strip() or None,
                    telefono=self.telefono.text().strip() or None,
                    email=self.email.text().strip() or None,
                    direccion=self.direccion.toPlainText().strip() or None,
                    activo=self.activo.isChecked(),
                    fecha_creacion=datetime.now()
                )
                db.add(nuevo_distribuidor)
            
            db.commit()
            QMessageBox.information(self, "Éxito", "Distribuidor guardado correctamente")
            self._cargar_distribuidores()
            
        except Exception as e:
            db.rollback()
            QMessageBox.critical(self, "Error", f"Error guardando distribuidor: {str(e)}")
        finally:
            db.close()
    
    def _eliminar_distribuidor(self):
        """Elimina el distribuidor actual."""
        if not self.distribuidor_actual:
            return
        
        respuesta = QMessageBox.question(
            self,
            "Confirmar eliminación",
            f"¿Está seguro de eliminar el distribuidor '{self.distribuidor_actual.nombre}'?\n"
            "Esta acción no se puede deshacer.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if respuesta == QMessageBox.StandardButton.Yes:
            db = next(get_db())
            try:
                db.delete(self.distribuidor_actual)
                db.commit()
                QMessageBox.information(self, "Éxito", "Distribuidor eliminado correctamente")
                self._cargar_distribuidores()
                self._limpiar_formulario()
                self.distribuidor_actual = None
                self.btn_guardar.setEnabled(False)
                self.btn_eliminar.setEnabled(False)
                
            except Exception as e:
                db.rollback()
                QMessageBox.critical(self, "Error", f"Error eliminando distribuidor: {str(e)}")
            finally:
                db.close()
