from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey
from sqlalchemy.orm import relationship
from .base import Base

class Accesorio(Base):
    """
    Modelo que representa un accesorio en el sistema.
    """
    __tablename__ = 'accesorios'
    
    id = Column(Integer, primary_key=True)
    codigo = Column(String(20), nullable=False)
    descripcion = Column(String(100), nullable=False)
    tipo = Column(String(50))  # Ej: manivela, cerradura, etc. (DEPRECATED - usar tipo_accesorio_id)
    tipo_accesorio_id = Column(Integer, ForeignKey('tipos_accesorios.id'), nullable=True)
    unidad = Column(String(20), default='Unidad')  # Unidad de medida
    precio = Column(Float, nullable=False, default=0.0)
    stock = Column(Integer, default=0)
    stock_minimo = Column(Integer, default=0)
    distribuidor_id = Column(Integer, ForeignKey('distribuidores.id'), nullable=True)
    activo = Column(Boolean, default=True)

    # Relaciones
    distribuidor = relationship("Distribuidor", back_populates="accesorios")
    tipo_accesorio = relationship("TipoAccesorio")
    
    def __repr__(self):
        return f"<Accesorio(codigo='{self.codigo}', descripcion='{self.descripcion}')>"
    
    def to_dict(self):
        """Convierte el objeto a un diccionario."""
        return {
            'id': self.id,
            'codigo': self.codigo,
            'descripcion': self.descripcion,
            'tipo': self.tipo,  # Campo legacy
            'tipo_accesorio_id': self.tipo_accesorio_id,
            'tipo_accesorio': self.tipo_accesorio.nombre if self.tipo_accesorio else None,
            'unidad': self.unidad,
            'precio': self.precio,
            'stock': self.stock,
            'stock_minimo': self.stock_minimo,
            'activo': self.activo
        }
