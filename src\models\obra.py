from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON>an, Foreign<PERSON>ey, DateTime, Text
from sqlalchemy.orm import relationship
from datetime import datetime
from .base import Base

class Obra(Base):
    """
    Modelo que representa una obra o proyecto en el sistema.
    """
    __tablename__ = 'obras'
    
    id = Column(Integer, primary_key=True)
    codigo = Column(String(20), unique=True, nullable=False)
    nombre = Column(String(200), nullable=False)
    descripcion = Column(Text)
    direccion = Column(String(200))
    fecha_inicio = Column(DateTime, default=datetime.utcnow)
    fecha_fin = Column(DateTime)
    estado = Column(String(20), default='Pendiente')  # Pendiente, En curso, Finalizada, Facturada
    presupuesto = Column(Float, default=0.0)
    coste_actual = Column(Float, default=0.0)
    notas = Column(Text)
    
    # Claves foráneas
    cliente_id = Column(Integer, Foreign<PERSON>ey('clientes.id'))
    perfil_id = Column(Integer, ForeignKey('perfiles.id'))
    cristal_id = Column(Integer, ForeignKey('cristales.id'))
    
    # Relaciones
    cliente = relationship("Cliente", back_populates="obras")
    perfil = relationship("Perfil", back_populates="obras")
    cristal = relationship("Cristal", back_populates="obras")
    articulos = relationship("ObraArticulo", back_populates="obra", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Obra(codigo='{self.codigo}', nombre='{self.nombre}')>"
    
    def calcular_coste_total(self):
        """Calcula el coste total de la obra basado en sus artículos."""
        coste_total = 0.0

        for obra_articulo in self.articulos:
            # Calcular precio del artículo si no está calculado
            if obra_articulo.precio_total is None:
                obra_articulo.calcular_precio()

            coste_total += obra_articulo.precio_total or 0.0

        return coste_total

    def to_dict(self):
        """Convierte el objeto a un diccionario."""
        return {
            'id': self.id,
            'codigo': self.codigo,
            'nombre': self.nombre,
            'descripcion': self.descripcion,
            'direccion': self.direccion,
            'fecha_inicio': self.fecha_inicio.isoformat() if self.fecha_inicio else None,
            'fecha_fin': self.fecha_fin.isoformat() if self.fecha_fin else None,
            'estado': self.estado,
            'presupuesto': self.presupuesto,
            'coste_actual': self.coste_actual,
            'cliente_id': self.cliente_id,
            'perfil_id': self.perfil_id,
            'cristal_id': self.cristal_id
        }
