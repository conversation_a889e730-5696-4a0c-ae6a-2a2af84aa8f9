"""
Diálogo de Reportes Profesionales para PRO-2000
Permite generar diferentes tipos de reportes con opciones avanzadas
"""

import sys
import os
from pathlib import Path

# Agregar el directorio raíz al path
root_dir = str(Path(__file__).parent.parent.parent.parent.absolute())
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QFrame,
    QGridLayout, QCheckBox, QComboBox, QGroupBox, QTextEdit,
    QTabWidget, QWidget, QMessageBox, QProgressBar, QListWidget,
    QListWidgetItem, QSplitter, QScrollArea
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

try:
    from models.base import get_db
    from models.obra import Obra
    from models.articulo import ObraArticulo
    from ui.utils.window_utils import setup_maximized_dialog
    from utils.generador_reportes_profesional import GeneradorReportesProfesional
except ImportError as e:
    print(f"⚠️ Error importando módulos: {e}")


class GeneradorReportesThread(QThread):
    """Thread para generar reportes en segundo plano"""
    
    progreso_actualizado = pyqtSignal(int, str)
    reporte_completado = pyqtSignal(str, str)  # tipo_reporte, archivo_generado
    error_ocurrido = pyqtSignal(str)
    
    def __init__(self, obra_id, tipo_reporte, opciones):
        super().__init__()
        self.obra_id = obra_id
        self.tipo_reporte = tipo_reporte
        self.opciones = opciones
        self.generador = GeneradorReportesProfesional()
    
    def run(self):
        """Ejecuta la generación del reporte"""
        try:
            self.progreso_actualizado.emit(10, "Iniciando generación...")
            
            if self.tipo_reporte == "taller":
                self.progreso_actualizado.emit(30, "Generando informe de taller...")
                archivo = self.generador.generar_informe_taller_completo(self.obra_id, self.opciones)
                
            elif self.tipo_reporte == "pedido":
                self.progreso_actualizado.emit(30, "Generando pedido de materiales...")
                archivo = self.generador.generar_pedido_materiales(self.obra_id, self.opciones)
                
            elif self.tipo_reporte == "optimizacion":
                self.progreso_actualizado.emit(30, "Optimizando cortes de perfiles...")
                archivo = self.generador.generar_optimizacion_perfiles(self.obra_id, self.opciones)
                
            elif self.tipo_reporte == "presupuesto":
                self.progreso_actualizado.emit(30, "Generando presupuesto...")
                archivo = self.generador.generar_presupuesto(self.obra_id, self.opciones)
                
            elif self.tipo_reporte == "factura":
                self.progreso_actualizado.emit(30, "Generando factura...")
                archivo = self.generador.generar_factura(self.obra_id, self.opciones)
            
            else:
                raise ValueError(f"Tipo de reporte no soportado: {self.tipo_reporte}")
            
            self.progreso_actualizado.emit(90, "Finalizando...")
            
            if archivo:
                self.progreso_actualizado.emit(100, "Completado")
                self.reporte_completado.emit(self.tipo_reporte, archivo)
            else:
                self.error_ocurrido.emit("No se pudo generar el archivo")
                
        except Exception as e:
            self.error_ocurrido.emit(str(e))


class ReportesProfesionalesDialog(QDialog):
    """Diálogo principal para generar reportes profesionales"""
    
    def __init__(self, parent=None, obra=None):
        super().__init__(parent)
        self.obra = obra
        self.generador_thread = None
        
        if not obra:
            QMessageBox.critical(self, "Error", "No se ha especificado una obra.")
            self.reject()
            return
        
        self.setWindowTitle(f"Reportes Profesionales - {obra.codigo}")
        setup_maximized_dialog(self, f"Reportes Profesionales - {obra.codigo}")
        
        self.init_ui()
        self.cargar_datos_obra()
    
    def init_ui(self):
        """Inicializa la interfaz de usuario"""
        layout_principal = QVBoxLayout(self)
        layout_principal.setContentsMargins(8, 8, 8, 8)
        layout_principal.setSpacing(8)
        
        # Header
        header = self._crear_header()
        layout_principal.addWidget(header)
        
        # Contenido principal con pestañas
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f5f5f5;
                color: #333;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QTabBar::tab:selected {
                background-color: #2196F3;
                color: white;
            }
            QTabBar::tab:hover:!selected {
                background-color: #e3f2fd;
            }
        """)
        
        # Crear pestañas
        self._crear_tab_taller()
        self._crear_tab_pedidos()
        self._crear_tab_optimizacion()
        self._crear_tab_comercial()
        
        layout_principal.addWidget(self.tabs)
        
        # Barra de progreso
        self.progress_frame = self._crear_barra_progreso()
        layout_principal.addWidget(self.progress_frame)
        
        # Botones de acción
        botones = self._crear_botones_accion()
        layout_principal.addWidget(botones)
    
    def _crear_header(self):
        """Crea el header con información de la obra"""
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FF9800, stop:1 #F57C00);
                border-radius: 8px;
                color: white;
                padding: 12px;
            }
        """)
        header.setFixedHeight(80)
        
        layout = QVBoxLayout(header)
        
        # Título
        titulo = QLabel("📊 REPORTES PROFESIONALES")
        titulo.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")
        layout.addWidget(titulo)
        
        # Información de la obra
        info_obra = QLabel(f"🏗️ {self.obra.codigo} - {self.obra.nombre}")
        info_obra.setStyleSheet("font-size: 14px; color: white;")
        layout.addWidget(info_obra)
        
        return header
    
    def _crear_tab_taller(self):
        """Crea la pestaña de informes de taller"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)
        
        # Descripción
        descripcion = QLabel("""
        <b>📋 Informe de Taller Profesional</b><br/>
        Genera un informe completo para el taller con:
        • Una página por cada artículo con dibujo técnico<br/>
        • Medidas colocadas sobre el dibujo<br/>
        • Listas detalladas de perfiles, accesorios y cristales<br/>
        • Especificaciones técnicas completas
        """)
        descripcion.setStyleSheet("""
            QLabel {
                background-color: #e3f2fd;
                border: 2px solid #2196F3;
                border-radius: 8px;
                padding: 12px;
                color: #1976D2;
            }
        """)
        descripcion.setWordWrap(True)
        layout.addWidget(descripcion)
        
        # Opciones de configuración
        grupo_opciones = QGroupBox("⚙️ Opciones de Generación")
        grupo_opciones.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                color: #1976D2;
                border: 2px solid #e3f2fd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        opciones_layout = QGridLayout(grupo_opciones)
        
        # Checkboxes de opciones
        self.check_incluir_dibujos = QCheckBox("Incluir dibujos técnicos")
        self.check_incluir_dibujos.setChecked(True)
        opciones_layout.addWidget(self.check_incluir_dibujos, 0, 0)
        
        self.check_incluir_medidas = QCheckBox("Incluir medidas in-situ")
        self.check_incluir_medidas.setChecked(True)
        opciones_layout.addWidget(self.check_incluir_medidas, 0, 1)
        
        self.check_incluir_perfiles = QCheckBox("Incluir lista de perfiles")
        self.check_incluir_perfiles.setChecked(True)
        opciones_layout.addWidget(self.check_incluir_perfiles, 1, 0)
        
        self.check_incluir_accesorios = QCheckBox("Incluir lista de accesorios")
        self.check_incluir_accesorios.setChecked(True)
        opciones_layout.addWidget(self.check_incluir_accesorios, 1, 1)
        
        self.check_incluir_cristales = QCheckBox("Incluir lista de cristales")
        self.check_incluir_cristales.setChecked(True)
        opciones_layout.addWidget(self.check_incluir_cristales, 2, 0)
        
        self.check_pagina_por_articulo = QCheckBox("Una página por artículo")
        self.check_pagina_por_articulo.setChecked(True)
        opciones_layout.addWidget(self.check_pagina_por_articulo, 2, 1)
        
        layout.addWidget(grupo_opciones)
        
        # Botón de generación
        self.btn_generar_taller = QPushButton("🔧 Generar Informe de Taller")
        self.btn_generar_taller.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 16px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)
        self.btn_generar_taller.clicked.connect(lambda: self.generar_reporte("taller"))
        layout.addWidget(self.btn_generar_taller)
        
        layout.addStretch()
        
        self.tabs.addTab(tab, "🔧 Taller")
    
    def _crear_tab_pedidos(self):
        """Crea la pestaña de pedidos de materiales"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)
        
        # Descripción
        descripcion = QLabel("""
        <b>📦 Pedidos de Materiales</b><br/>
        Genera pedidos individuales por tipo de material:
        • Pedido de perfiles con longitudes optimizadas<br/>
        • Pedido de accesorios con cantidades exactas<br/>
        • Pedido de cristales con medidas precisas<br/>
        • Selección de qué materiales incluir
        """)
        descripcion.setStyleSheet("""
            QLabel {
                background-color: #e8f5e8;
                border: 2px solid #4CAF50;
                border-radius: 8px;
                padding: 12px;
                color: #2E7D32;
            }
        """)
        descripcion.setWordWrap(True)
        layout.addWidget(descripcion)
        
        # Selección de materiales
        grupo_materiales = QGroupBox("📋 Seleccionar Materiales a Incluir")
        grupo_materiales.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                color: #2E7D32;
                border: 2px solid #e8f5e8;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        materiales_layout = QVBoxLayout(grupo_materiales)
        
        self.check_pedido_perfiles = QCheckBox("📏 Perfiles (con optimización de cortes)")
        self.check_pedido_perfiles.setChecked(True)
        materiales_layout.addWidget(self.check_pedido_perfiles)
        
        self.check_pedido_accesorios = QCheckBox("🔧 Accesorios")
        self.check_pedido_accesorios.setChecked(True)
        materiales_layout.addWidget(self.check_pedido_accesorios)
        
        self.check_pedido_cristales = QCheckBox("💎 Cristales")
        self.check_pedido_cristales.setChecked(True)
        materiales_layout.addWidget(self.check_pedido_cristales)
        
        layout.addWidget(grupo_materiales)
        
        # Botón de generación
        self.btn_generar_pedido = QPushButton("📦 Generar Pedido de Materiales")
        self.btn_generar_pedido.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 16px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #388E3C;
            }
            QPushButton:pressed {
                background-color: #2E7D32;
            }
        """)
        self.btn_generar_pedido.clicked.connect(lambda: self.generar_reporte("pedido"))
        layout.addWidget(self.btn_generar_pedido)
        
        layout.addStretch()
        
        self.tabs.addTab(tab, "📦 Pedidos")
    
    def _crear_tab_optimizacion(self):
        """Crea la pestaña de optimización de perfiles"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)
        
        # Descripción
        descripcion = QLabel("""
        <b>⚡ Optimización de Perfiles</b><br/>
        Genera un informe profesional de optimización como los programas especializados:
        • Dibujos de barras con cortes marcados<br/>
        • Cálculo de desperdicios minimizados<br/>
        • Estadísticas de eficiencia<br/>
        • Listado optimizado para compra
        """)
        descripcion.setStyleSheet("""
            QLabel {
                background-color: #fff3e0;
                border: 2px solid #FF9800;
                border-radius: 8px;
                padding: 12px;
                color: #F57C00;
            }
        """)
        descripcion.setWordWrap(True)
        layout.addWidget(descripcion)
        
        # Configuración de optimización
        grupo_config = QGroupBox("⚙️ Configuración de Optimización")
        grupo_config.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                color: #F57C00;
                border: 2px solid #fff3e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        config_layout = QGridLayout(grupo_config)
        
        # Longitud de barra estándar
        config_layout.addWidget(QLabel("Longitud de barra estándar:"), 0, 0)
        self.combo_longitud_barra = QComboBox()
        self.combo_longitud_barra.addItems(["6000 mm", "5000 mm", "4000 mm", "3000 mm"])
        config_layout.addWidget(self.combo_longitud_barra, 0, 1)
        
        # Margen de corte
        config_layout.addWidget(QLabel("Margen de corte:"), 1, 0)
        self.combo_margen_corte = QComboBox()
        self.combo_margen_corte.addItems(["3 mm", "5 mm", "10 mm"])
        config_layout.addWidget(self.combo_margen_corte, 1, 1)
        
        # Opciones adicionales
        self.check_mostrar_desperdicios = QCheckBox("Mostrar desperdicios en dibujos")
        self.check_mostrar_desperdicios.setChecked(True)
        config_layout.addWidget(self.check_mostrar_desperdicios, 2, 0, 1, 2)
        
        self.check_incluir_estadisticas = QCheckBox("Incluir estadísticas de eficiencia")
        self.check_incluir_estadisticas.setChecked(True)
        config_layout.addWidget(self.check_incluir_estadisticas, 3, 0, 1, 2)
        
        layout.addWidget(grupo_config)
        
        # Botón de generación
        self.btn_generar_optimizacion = QPushButton("⚡ Generar Optimización de Perfiles")
        self.btn_generar_optimizacion.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 16px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #EF6C00;
            }
        """)
        self.btn_generar_optimizacion.clicked.connect(lambda: self.generar_reporte("optimizacion"))
        layout.addWidget(self.btn_generar_optimizacion)
        
        layout.addStretch()
        
        self.tabs.addTab(tab, "⚡ Optimización")
    
    def _crear_tab_comercial(self):
        """Crea la pestaña de documentos comerciales"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)
        
        # Descripción
        descripcion = QLabel("""
        <b>💼 Documentos Comerciales</b><br/>
        Genera presupuestos y facturas profesionales:
        • Presupuestos detallados con precios<br/>
        • Facturas con numeración automática<br/>
        • Formato profesional con logo de empresa<br/>
        • Cálculos automáticos de IVA y totales
        """)
        descripcion.setStyleSheet("""
            QLabel {
                background-color: #f3e5f5;
                border: 2px solid #9C27B0;
                border-radius: 8px;
                padding: 12px;
                color: #7B1FA2;
            }
        """)
        descripcion.setWordWrap(True)
        layout.addWidget(descripcion)
        
        # Botones de documentos comerciales
        botones_layout = QVBoxLayout()
        botones_layout.setSpacing(12)
        
        # Presupuesto
        self.btn_generar_presupuesto = QPushButton("💰 Generar Presupuesto")
        self.btn_generar_presupuesto.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 16px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
            QPushButton:pressed {
                background-color: #6A1B9A;
            }
        """)
        self.btn_generar_presupuesto.clicked.connect(lambda: self.generar_reporte("presupuesto"))
        botones_layout.addWidget(self.btn_generar_presupuesto)
        
        # Factura
        self.btn_generar_factura = QPushButton("🧾 Generar Factura")
        self.btn_generar_factura.setStyleSheet("""
            QPushButton {
                background-color: #673AB7;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 16px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #5E35B1;
            }
            QPushButton:pressed {
                background-color: #512DA8;
            }
        """)
        self.btn_generar_factura.clicked.connect(lambda: self.generar_reporte("factura"))
        botones_layout.addWidget(self.btn_generar_factura)
        
        layout.addLayout(botones_layout)
        layout.addStretch()
        
        self.tabs.addTab(tab, "💼 Comercial")

    def _crear_barra_progreso(self):
        """Crea la barra de progreso"""
        frame = QFrame()
        frame.setFixedHeight(60)
        frame.setStyleSheet("""
            QFrame {
                background-color: #f5f5f5;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
            }
        """)
        frame.setVisible(False)  # Oculta por defecto

        layout = QVBoxLayout(frame)
        layout.setContentsMargins(16, 8, 16, 8)

        self.label_progreso = QLabel("Generando reporte...")
        self.label_progreso.setStyleSheet("font-weight: bold; color: #1976D2;")
        layout.addWidget(self.label_progreso)

        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                text-align: center;
                background-color: white;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2196F3, stop:1 #1976D2);
                border-radius: 6px;
            }
        """)
        layout.addWidget(self.progress_bar)

        return frame

    def _crear_botones_accion(self):
        """Crea los botones de acción"""
        frame = QFrame()
        frame.setFixedHeight(60)
        frame.setStyleSheet("""
            QFrame {
                background-color: #f5f5f5;
                border-radius: 8px;
            }
        """)

        layout = QHBoxLayout(frame)
        layout.setContentsMargins(16, 12, 16, 12)

        # Botón ver archivos generados
        self.btn_ver_archivos = QPushButton("📁 Ver Archivos Generados")
        self.btn_ver_archivos.setStyleSheet("""
            QPushButton {
                background-color: #607D8B;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #546E7A;
            }
        """)
        self.btn_ver_archivos.clicked.connect(self.ver_archivos_generados)
        layout.addWidget(self.btn_ver_archivos)

        layout.addStretch()

        # Botón cerrar
        self.btn_cerrar = QPushButton("❌ Cerrar")
        self.btn_cerrar.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        self.btn_cerrar.clicked.connect(self.accept)
        layout.addWidget(self.btn_cerrar)

        return frame

    def cargar_datos_obra(self):
        """Carga los datos de la obra"""
        try:
            db = next(get_db())
            obra_articulos = db.query(ObraArticulo).filter(
                ObraArticulo.obra_id == self.obra.id
            ).count()

            if obra_articulos == 0:
                QMessageBox.warning(
                    self,
                    "Sin artículos",
                    "La obra no tiene artículos. Agregue artículos antes de generar reportes."
                )

            db.close()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error cargando datos de la obra: {e}")

    def generar_reporte(self, tipo_reporte):
        """Genera un reporte del tipo especificado"""

        # Recopilar opciones según el tipo de reporte
        opciones = self._recopilar_opciones(tipo_reporte)

        # Mostrar barra de progreso
        self.progress_frame.setVisible(True)
        self.progress_bar.setValue(0)
        self.label_progreso.setText(f"Preparando generación de {tipo_reporte}...")

        # Deshabilitar botones
        self._habilitar_botones(False)

        # Crear y ejecutar thread
        self.generador_thread = GeneradorReportesThread(self.obra.id, tipo_reporte, opciones)
        self.generador_thread.progreso_actualizado.connect(self._actualizar_progreso)
        self.generador_thread.reporte_completado.connect(self._reporte_completado)
        self.generador_thread.error_ocurrido.connect(self._error_generacion)
        self.generador_thread.start()

    def _recopilar_opciones(self, tipo_reporte):
        """Recopila las opciones según el tipo de reporte"""
        opciones = {}

        if tipo_reporte == "taller":
            opciones = {
                'incluir_dibujos': self.check_incluir_dibujos.isChecked(),
                'incluir_medidas': self.check_incluir_medidas.isChecked(),
                'incluir_perfiles': self.check_incluir_perfiles.isChecked(),
                'incluir_accesorios': self.check_incluir_accesorios.isChecked(),
                'incluir_cristales': self.check_incluir_cristales.isChecked(),
                'pagina_por_articulo': self.check_pagina_por_articulo.isChecked()
            }

        elif tipo_reporte == "pedido":
            opciones = {
                'incluir_perfiles': self.check_pedido_perfiles.isChecked(),
                'incluir_accesorios': self.check_pedido_accesorios.isChecked(),
                'incluir_cristales': self.check_pedido_cristales.isChecked()
            }

        elif tipo_reporte == "optimizacion":
            longitud_texto = self.combo_longitud_barra.currentText()
            longitud = int(longitud_texto.split()[0])  # Extraer número

            margen_texto = self.combo_margen_corte.currentText()
            margen = int(margen_texto.split()[0])  # Extraer número

            opciones = {
                'longitud_barra': longitud,
                'margen_corte': margen,
                'mostrar_desperdicios': self.check_mostrar_desperdicios.isChecked(),
                'incluir_estadisticas': self.check_incluir_estadisticas.isChecked()
            }

        return opciones

    def _habilitar_botones(self, habilitar):
        """Habilita o deshabilita los botones de generación"""
        self.btn_generar_taller.setEnabled(habilitar)
        self.btn_generar_pedido.setEnabled(habilitar)
        self.btn_generar_optimizacion.setEnabled(habilitar)
        self.btn_generar_presupuesto.setEnabled(habilitar)
        self.btn_generar_factura.setEnabled(habilitar)

    def _actualizar_progreso(self, valor, mensaje):
        """Actualiza la barra de progreso"""
        self.progress_bar.setValue(valor)
        self.label_progreso.setText(mensaje)

    def _reporte_completado(self, tipo_reporte, archivo):
        """Maneja la finalización exitosa de un reporte"""
        self.progress_frame.setVisible(False)
        self._habilitar_botones(True)

        # Mostrar mensaje de éxito
        QMessageBox.information(
            self,
            "✅ Reporte Generado",
            f"El {tipo_reporte} se ha generado correctamente:\n\n{archivo}\n\n"
            "¿Desea abrir el archivo?"
        )

        # Abrir el archivo
        try:
            import subprocess
            import platform

            if platform.system() == 'Windows':
                os.startfile(archivo)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.call(['open', archivo])
            else:  # Linux
                subprocess.call(['xdg-open', archivo])

        except Exception as e:
            print(f"No se pudo abrir el archivo: {e}")

    def _error_generacion(self, mensaje_error):
        """Maneja errores en la generación"""
        self.progress_frame.setVisible(False)
        self._habilitar_botones(True)

        QMessageBox.critical(
            self,
            "❌ Error en Generación",
            f"Ocurrió un error al generar el reporte:\n\n{mensaje_error}"
        )

    def ver_archivos_generados(self):
        """Abre la carpeta con los archivos generados"""
        try:
            # Crear carpeta de reportes si no existe
            carpeta_reportes = Path.home() / "PRO-2000_Reportes"
            carpeta_reportes.mkdir(exist_ok=True)

            # Abrir carpeta
            import subprocess
            import platform

            if platform.system() == 'Windows':
                os.startfile(str(carpeta_reportes))
            elif platform.system() == 'Darwin':  # macOS
                subprocess.call(['open', str(carpeta_reportes)])
            else:  # Linux
                subprocess.call(['xdg-open', str(carpeta_reportes)])

        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudo abrir la carpeta de reportes: {e}"
            )
