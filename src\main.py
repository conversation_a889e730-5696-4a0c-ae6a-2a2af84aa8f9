#!/usr/bin/env python3
"""
PRO-2000 - Sistema de Gestión Profesional
Punto de entrada principal de la aplicación
Versión: 2.1.0
"""

import sys
import os
import traceback
from datetime import datetime

# Añadir el directorio src al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Importaciones de PyQt6
try:
    from PyQt6.QtWidgets import QApplication, QMessageBox, QSplashScreen
    from PyQt6.QtCore import Qt, QTimer
    from PyQt6.QtGui import QPixmap, QFont
except ImportError as e:
    print(f"❌ Error: No se pudo importar PyQt6: {e}")
    print("💡 Ejecuta: pip install PyQt6")
    sys.exit(1)

# Importaciones del proyecto
try:
    from models.base import init_db, get_db
    from models.usuario import Usuario
    from ui.login_dialog import LoginDialog
    from ui.main_window_revolutionary import RevolutionaryMainWindow
    from ui.themes.theme_config import ThemeConfig
    from utils.config_manager import ConfigManager
    from utils.logger_config import setup_logger
    from utils.error_handler import ErrorHandler, ErrorCategory, ErrorLevel
except ImportError as e:
    print(f"❌ Error al importar módulos del proyecto: {e}")
    print(f"📁 Directorio actual: {os.getcwd()}")
    print("💡 Verifica que todos los archivos del proyecto estén presentes")
    sys.exit(1)


def configurar_aplicacion():
    """Configura la aplicación Qt"""
    app = QApplication(sys.argv)
    
    # Configuración básica de la aplicación
    app.setApplicationName("PRO-2000")
    app.setApplicationVersion("2.1.0")
    app.setOrganizationName("PRO-2000 Systems")
    app.setOrganizationDomain("pro-2000.local")
    
    # Configurar fuente por defecto
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    
    # Aplicar tema por defecto
    try:
        default_theme = ThemeConfig.get_theme("modern")
        app.setStyleSheet(default_theme)
    except Exception as e:
        print(f"⚠️ No se pudo cargar el tema por defecto: {e}")
    
    return app


def mostrar_splash_screen(app):
    """Muestra la pantalla de carga"""
    try:
        # Crear splash screen
        splash = QSplashScreen()
        splash.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.FramelessWindowHint)
        
        # Crear pixmap con texto
        pixmap = QPixmap(400, 300)
        pixmap.fill(Qt.GlobalColor.white)
        
        # Configurar splash
        splash.setPixmap(pixmap)
        splash.showMessage(
            "🚀 Iniciando PRO-2000 v2.1.0\n\n"
            "Cargando sistema profesional...",
            Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignBottom,
            Qt.GlobalColor.black
        )
        
        splash.show()
        app.processEvents()
        
        return splash
    except Exception as e:
        print(f"⚠️ No se pudo mostrar splash screen: {e}")
        return None


def inicializar_base_datos():
    """Inicializa la base de datos"""
    try:
        print("📊 Inicializando base de datos...")
        init_db()
        print("✅ Base de datos inicializada correctamente")
        return True
    except Exception as e:
        print(f"❌ Error al inicializar base de datos: {e}")
        return False


def configurar_logging():
    """Configura el sistema de logging"""
    try:
        logger = setup_logger()
        logger.info("Sistema de logging inicializado")
        return logger
    except Exception as e:
        print(f"⚠️ No se pudo configurar logging: {e}")
        return None


def inicializar_aplicacion():
    """Función principal de inicialización"""
    try:
        print("🚀 Iniciando PRO-2000 v2.1.0...")
        
        # Configurar aplicación Qt
        app = configurar_aplicacion()
        
        # Mostrar splash screen
        splash = mostrar_splash_screen(app)
        
        # Configurar logging
        logger = configurar_logging()
        if logger:
            logger.info("Iniciando aplicación PRO-2000 v2.1.0")
        
        # Configurar manejo de errores
        error_handler = ErrorHandler()
        
        # Inicializar base de datos
        if not inicializar_base_datos():
            raise Exception("No se pudo inicializar la base de datos")
        
        # Actualizar splash
        if splash:
            splash.showMessage(
                "🚀 Iniciando PRO-2000 v2.1.0\n\n"
                "Configurando interfaz...",
                Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignBottom,
                Qt.GlobalColor.black
            )
            app.processEvents()
        
        # Crear diálogo de login
        login_dialog = LoginDialog()
        
        # Variable para almacenar la ventana principal
        main_window = None
        
        def on_login_exitoso(usuario):
            """Callback cuando el login es exitoso"""
            nonlocal main_window
            
            try:
                # Cerrar splash si existe
                if splash:
                    splash.close()
                
                # Crear ventana principal
                main_window = RevolutionaryMainWindow(usuario)
                
                # Ocultar diálogo de login
                login_dialog.hide()
                
                # Mostrar ventana principal
                main_window.show()
                
                # Mostrar mensaje de bienvenida profesional
                mensaje_bienvenida = f"""
<div style='text-align: center; padding: 20px;'>
    <h2 style='color: #2563eb; margin-bottom: 16px;'>🎉 Bienvenido a PRO-2000 v2.1.0</h2>
    <h3 style='color: #0f172a; margin-bottom: 20px;'>Sistema Profesional Mejorado</h3>
    
    <p style='color: #374151; font-size: 16px; margin-bottom: 20px;'>
        <b>Usuario:</b> {usuario.nombre}<br>
        <b>Tipo:</b> {'Administrador' if usuario.tipo == 1 else 'Usuario'}<br>
        <b>Sesión iniciada:</b> Exitosamente
    </p>
    
    <div style='background: #f0f9ff; padding: 16px; border-radius: 8px; border-left: 4px solid #2563eb; margin: 16px 0;'>
        <h4 style='color: #1e40af; margin-bottom: 12px;'>✨ Nuevas Características Profesionales:</h4>
        <div style='text-align: left; color: #1e293b;'>
            🎨 <b>Diseño Unificado:</b> Interfaz completamente rediseñada<br>
            🛡️ <b>Sistema de Errores Robusto:</b> Manejo inteligente de errores<br>
            ⚙️ <b>Configuración Centralizada:</b> Personalización avanzada<br>
            💾 <b>Backup Automático:</b> Respaldos programados<br>
            📊 <b>Componentes Profesionales:</b> Tablas y formularios mejorados<br>
            🔍 <b>Búsqueda Avanzada:</b> Filtros inteligentes<br>
        </div>
    </div>
    
    <p style='color: #059669; font-weight: 600; font-size: 14px;'>
        🚀 ¡Disfruta de la experiencia profesional mejorada!
    </p>
</div>
"""
                
                msg_box = QMessageBox(main_window)
                msg_box.setWindowTitle("🎉 PRO-2000 v2.1.0 - Sistema Profesional")
                msg_box.setTextFormat(Qt.TextFormat.RichText)
                msg_box.setText(mensaje_bienvenida)
                msg_box.setIcon(QMessageBox.Icon.Information)
                msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
                
                # Aplicar estilo profesional al message box
                msg_box.setStyleSheet("""
                    QMessageBox {
                        background: #ffffff;
                        color: #0f172a;
                        min-width: 500px;
                    }
                    QMessageBox QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #2563eb, stop:1 #1d4ed8);
                        color: #ffffff;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 24px;
                        font-weight: 600;
                        min-width: 100px;
                    }
                    QMessageBox QPushButton:hover {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #1d4ed8, stop:1 #1e40af);
                    }
                """)
                
                msg_box.exec()
                
                if logger:
                    logger.info(f"Usuario {usuario.nombre} ha iniciado sesión exitosamente")
                
            except Exception as e:
                error_handler.handle_error(
                    error=e,
                    category=ErrorCategory.INTERFACE,
                    level=ErrorLevel.ERROR,
                    context="Creación de ventana principal",
                    show_user=True
                )
        
        # Conectar señal de inicio de sesión exitoso
        login_dialog.login_exitoso.connect(on_login_exitoso)
        
        # Cerrar splash después de un momento
        if splash:
            QTimer.singleShot(2000, splash.close)
        
        # Mostrar diálogo de inicio de sesión
        login_dialog.show()
        
        # Iniciar el bucle de eventos
        return app.exec()
        
    except Exception as e:
        # Usar el sistema de manejo de errores si está disponible
        if 'error_handler' in locals():
            error_handler.handle_error(
                error=e,
                category=ErrorCategory.GENERAL,
                level=ErrorLevel.CRITICAL,
                context="Inicialización de aplicación",
                show_user=True
            )
        else:
            # Fallback al sistema básico
            error_msg = (
                f"No se pudo iniciar la aplicación debido al siguiente error:\n\n"
                f"{str(e)}\n\n"
                f"{traceback.format_exc()}\n"
                "Por favor, contacte al soporte técnico."
            )
            
            print(error_msg)
            
            if QApplication.instance() is not None:
                QMessageBox.critical(
                    None,
                    "Error de Inicio",
                    error_msg,
                    QMessageBox.StandardButton.Ok
                )
        
        return 1


def main():
    """Función principal de entrada"""
    try:
        print("=" * 60)
        print("🎯 PRO-2000 - Sistema de Gestión Profesional")
        print("📅 Versión: 2.1.0")
        print("🚀 Iniciando aplicación...")
        print("=" * 60)
        
        # Verificar versión de Python
        if sys.version_info < (3, 8):
            print("❌ Error: Se requiere Python 3.8 o superior")
            return 1
        
        # Inicializar aplicación
        result = inicializar_aplicacion()
        
        print("=" * 60)
        print("👋 Aplicación cerrada")
        print("=" * 60)
        
        return result
        
    except KeyboardInterrupt:
        print("\n🛑 Aplicación interrumpida por el usuario")
        return 0
    except Exception as e:
        print(f"❌ Error crítico en main: {e}")
        print(f"📋 Traceback: {traceback.format_exc()}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
