"""
Script para crear la tabla 'persianas' en la base de datos según el modelo Persiana.
"""
import os
import sys
import sqlite3
from sqlite3 import Error

def create_connection(db_file):
    """Crear una conexión a la base de datos SQLite."""
    conn = None
    try:
        conn = sqlite3.connect(db_file)
        print(f"Conexión exitosa a SQLite versión {sqlite3.version}")
        return conn
    except Error as e:
        print(f"Error al conectar a la base de datos: {e}")
        return None

def table_exists(conn, table_name):
    """Verificar si una tabla existe en la base de datos."""
    cursor = conn.cursor()
    cursor.execute(f"""
        SELECT count(name) 
        FROM sqlite_master 
        WHERE type='table' AND name=?
    """, (table_name,))
    return cursor.fetchone()[0] == 1

def create_persianas_table(conn):
    """Crear la tabla persianas según el modelo."""
    try:
        cursor = conn.cursor()
        
        # Verificar si la tabla ya existe
        if table_exists(conn, 'persianas'):
            print("La tabla 'persianas' ya existe.")
            return False
            
        print("Creando tabla 'persianas'...")
        
        # Crear la tabla con todas las columnas según el modelo
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS persianas (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            referencia TEXT NOT NULL UNIQUE,
            nombre TEXT NOT NULL,
            altura_cajon INTEGER NOT NULL,
            tipo_cajon TEXT NOT NULL,
            tipo_operacion TEXT NOT NULL,
            posicion_mando TEXT,
            color TEXT NOT NULL,
            activo BOOLEAN DEFAULT 1,
            creado_en TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            actualizado_en TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # Crear índices para mejorar el rendimiento
        cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_persianas_referencia ON persianas (referencia);
        """)
        
        cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_persianas_activo ON persianas (activo);
        """)
        
        conn.commit()
        print("Tabla 'persianas' creada exitosamente.")
        return True
        
    except Error as e:
        print(f"Error al crear la tabla 'persianas': {e}")
        if conn:
            conn.rollback()
        return False

def main():
    # Ruta a la base de datos
    db_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'data', 'pro2000.db'))
    
    # Crear conexión
    conn = create_connection(db_path)
    if conn is not None:
        # Crear tabla persianas
        if create_persianas_table(conn):
            print("La tabla 'persianas' está lista para su uso.")
        conn.close()
        print("Conexión cerrada.")
    else:
        print("No se pudo establecer la conexión a la base de datos.")
        sys.exit(1)

if __name__ == "__main__":
    main()
