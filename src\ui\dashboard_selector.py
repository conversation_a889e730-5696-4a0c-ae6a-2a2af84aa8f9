"""
Selector de Dashboard para PRO-2000
Permite elegir entre diferentes tipos de dashboard
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QListWidget, QListWidgetItem, QDialogButtonBox, QGroupBox,
    QTextEdit, QFrame, QGridLayout, QWidget
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor

from ui.utils.window_utils import setup_maximized_dialog


class DashboardSelectorDialog(QDialog):
    """Diálogo para seleccionar tipo de dashboard."""
    
    dashboard_selected = pyqtSignal(str)  # Señal emitida cuando se selecciona un dashboard
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setModal(True)
        
        # ✅ CONFIGURACIÓN SIMPLE Y EFECTIVA
        setup_maximized_dialog(self, "Selector de Dashboard - PRO-2000")
        
        self._inicializar_ui()
        self._cargar_dashboards()
    
    def _inicializar_ui(self):
        """Configura la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)
        
        # Título
        titulo = QLabel("📊 Seleccionar Tipo de Dashboard")
        titulo.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        titulo.setAlignment(Qt.AlignmentFlag.AlignCenter)
        titulo.setStyleSheet("padding: 15px; color: #2c3e50;")
        layout_principal.addWidget(titulo)
        
        # Layout horizontal para lista y preview
        layout_contenido = QHBoxLayout()
        
        # Panel izquierdo: Lista de dashboards
        self._crear_panel_dashboards(layout_contenido)
        
        # Panel derecho: Preview y descripción
        self._crear_panel_preview(layout_contenido)
        
        layout_principal.addLayout(layout_contenido)
        
        # Botones de acción
        self._crear_botones_accion(layout_principal)
    
    def _crear_panel_dashboards(self, layout_contenido):
        """Crea el panel de selección de dashboards."""
        grupo_dashboards = QGroupBox("Dashboards Disponibles")
        layout_dashboards = QVBoxLayout(grupo_dashboards)
        
        # Lista de dashboards
        self.lista_dashboards = QListWidget()
        self.lista_dashboards.currentItemChanged.connect(self._dashboard_seleccionado)
        layout_dashboards.addWidget(self.lista_dashboards)
        
        # Información del dashboard actual
        self.label_dashboard_actual = QLabel()
        self.label_dashboard_actual.setStyleSheet("""
            background-color: #e8f4f8;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 10px;
            font-weight: bold;
        """)
        layout_dashboards.addWidget(self.label_dashboard_actual)
        
        grupo_dashboards.setMaximumWidth(300)
        layout_contenido.addWidget(grupo_dashboards)
    
    def _crear_panel_preview(self, layout_contenido):
        """Crea el panel de preview del dashboard."""
        grupo_preview = QGroupBox("Vista Previa")
        layout_preview = QVBoxLayout(grupo_preview)
        
        # Descripción del dashboard
        self.texto_descripcion = QTextEdit()
        self.texto_descripcion.setMaximumHeight(120)
        self.texto_descripcion.setReadOnly(True)
        layout_preview.addWidget(self.texto_descripcion)
        
        # Preview visual
        self.frame_preview = QFrame()
        self.frame_preview.setMinimumHeight(400)
        self.frame_preview.setStyleSheet("""
            QFrame {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
        """)
        layout_preview.addWidget(self.frame_preview)
        
        layout_contenido.addWidget(grupo_preview)
    
    def _crear_botones_accion(self, layout_principal):
        """Crea los botones de acción."""
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        
        botones.button(QDialogButtonBox.StandardButton.Ok).setText("Seleccionar Dashboard")
        botones.button(QDialogButtonBox.StandardButton.Cancel).setText("Cancelar")
        
        botones.accepted.connect(self._seleccionar_dashboard)
        botones.rejected.connect(self.reject)
        
        layout_principal.addWidget(botones)
    
    def _cargar_dashboards(self):
        """Carga la lista de dashboards disponibles."""
        dashboards = [
            {
                "id": "basico",
                "nombre": "🏠 Dashboard Básico",
                "descripcion": "Dashboard simple con tarjetas de navegación rápida a los módulos principales.",
                "caracteristicas": [
                    "• Tarjetas de acceso rápido",
                    "• Navegación simple",
                    "• Ideal para uso básico",
                    "• Carga rápida"
                ]
            },
            {
                "id": "moderno",
                "nombre": "🎨 Dashboard Moderno",
                "descripcion": "Dashboard con diseño moderno, métricas en tiempo real y visualización avanzada.",
                "caracteristicas": [
                    "• Métricas en tiempo real",
                    "• Diseño moderno y atractivo",
                    "• Gráficos y estadísticas",
                    "• Progreso de obras",
                    "• Tabla de obras recientes"
                ]
            },
            {
                "id": "ejecutivo",
                "nombre": "📈 Dashboard Ejecutivo",
                "descripcion": "Dashboard completo con KPIs, gráficos avanzados y análisis detallado para toma de decisiones.",
                "caracteristicas": [
                    "• KPIs ejecutivos completos",
                    "• Gráficos avanzados (pie, barras)",
                    "• Análisis de rendimiento",
                    "• Métricas de facturación",
                    "• Inventario detallado",
                    "• Reportes ejecutivos"
                ]
            }
        ]
        
        for dashboard in dashboards:
            item = QListWidgetItem(dashboard["nombre"])
            item.setData(Qt.ItemDataRole.UserRole, dashboard)
            self.lista_dashboards.addItem(item)
        
        # Seleccionar el primer dashboard por defecto
        if self.lista_dashboards.count() > 0:
            self.lista_dashboards.setCurrentRow(0)
    
    def _dashboard_seleccionado(self, current, previous):
        """Maneja la selección de un dashboard."""
        if not current:
            return
        
        dashboard = current.data(Qt.ItemDataRole.UserRole)
        
        # Actualizar descripción
        descripcion = f"<h3>{dashboard['nombre']}</h3>"
        descripcion += f"<p>{dashboard['descripcion']}</p>"
        descripcion += "<h4>Características:</h4><ul>"
        for caracteristica in dashboard['caracteristicas']:
            descripcion += f"<li>{caracteristica}</li>"
        descripcion += "</ul>"
        
        self.texto_descripcion.setHtml(descripcion)
        
        # Actualizar label de dashboard actual
        self.label_dashboard_actual.setText(f"📊 {dashboard['nombre']}")
        
        # Actualizar preview visual (simulado)
        self._actualizar_preview_visual(dashboard['id'])
    
    def _actualizar_preview_visual(self, dashboard_id):
        """Actualiza el preview visual del dashboard."""
        # Limpiar preview anterior
        for child in self.frame_preview.findChildren(QWidget):
            child.deleteLater()
        
        layout = QVBoxLayout(self.frame_preview)
        
        if dashboard_id == "basico":
            preview_label = QLabel("🏠 Vista previa del Dashboard Básico\n\n📄 Artículos  🏗️ Obras\n📏 Perfiles   👥 Clientes")
        elif dashboard_id == "moderno":
            preview_label = QLabel("🎨 Vista previa del Dashboard Moderno\n\n📊 Métricas: 15 obras | 25 clientes\n📈 Progreso: 47% completado\n📋 Obras recientes...")
        else:  # ejecutivo
            preview_label = QLabel("📈 Vista previa del Dashboard Ejecutivo\n\n💰 Facturación: 45,230 €\n⚡ Eficiencia: 89.2%\n📊 Gráficos avanzados\n📈 KPIs ejecutivos")
        
        preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        preview_label.setStyleSheet("color: #6c757d; font-size: 14px; padding: 20px;")
        layout.addWidget(preview_label)
    
    def _seleccionar_dashboard(self):
        """Selecciona el dashboard actual y cierra el diálogo."""
        current_item = self.lista_dashboards.currentItem()
        if current_item:
            dashboard = current_item.data(Qt.ItemDataRole.UserRole)
            self.dashboard_selected.emit(dashboard['id'])
            self.accept()
    
    def get_selected_dashboard(self):
        """Retorna el dashboard seleccionado."""
        current_item = self.lista_dashboards.currentItem()
        if current_item:
            dashboard = current_item.data(Qt.ItemDataRole.UserRole)
            return dashboard['id']
        return "basico"
