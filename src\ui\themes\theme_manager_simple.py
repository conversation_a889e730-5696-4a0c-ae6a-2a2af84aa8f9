"""
Gestor de temas simplificado para PRO-2000.
Solo estilos CSS básicos compatibles con Qt.
"""

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QSettings


class ThemeManager:
    """Gestor de temas simplificado sin propiedades problemáticas."""
    
    def __init__(self):
        self.settings = QSettings("PRO-2000", "ThemeSettings")
        self.current_theme = self.settings.value("current_theme", "claro")
        
        # Definir temas básicos
        self.themes = {
            "claro": self._get_light_theme(),
            "oscuro": self._get_dark_theme(),
            "azul": self._get_blue_theme()
        }
    
    def _get_light_theme(self):
        """Tema claro básico."""
        return {
            "name": "Tema Claro",
            "description": "Tema claro y limpio",
            "stylesheet": """
                QMainWindow {
                    background-color: #f8f9fa;
                    color: #212529;
                }
                QPushButton {
                    background-color: #0d6efd;
                    color: white;
                    border: none;
                    padding: 10px 16px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #0b5ed7;
                }
                QTabBar::tab {
                    background-color: #e9ecef;
                    color: #495057;
                    padding: 12px 20px;
                    margin-right: 2px;
                    border: 1px solid #cccccc;
                    border-bottom: none;
                    min-width: 100px;
                }
                QTabBar::tab:selected {
                    background-color: #ffffff;
                    color: #212529;
                    font-weight: bold;
                }
                QMenuBar {
                    background-color: #ffffff;
                    color: #212529;
                    border-bottom: 1px solid #cccccc;
                    padding: 4px;
                }
                QMenu {
                    background-color: #ffffff;
                    border: 1px solid #cccccc;
                    padding: 4px;
                }
                QMenu::item:selected {
                    background-color: #0d6efd;
                    color: white;
                }
            """
        }
    
    def _get_dark_theme(self):
        """Tema oscuro básico."""
        return {
            "name": "Tema Oscuro",
            "description": "Tema oscuro para reducir fatiga visual",
            "stylesheet": """
                QMainWindow {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QPushButton {
                    background-color: #0d6efd;
                    color: white;
                    border: none;
                    padding: 10px 16px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #0b5ed7;
                }
                QTabBar::tab {
                    background-color: #404040;
                    color: #ffffff;
                    padding: 12px 20px;
                    margin-right: 2px;
                    border: 1px solid #555555;
                    border-bottom: none;
                    min-width: 100px;
                }
                QTabBar::tab:selected {
                    background-color: #3c3c3c;
                    color: #ffffff;
                    font-weight: bold;
                }
                QMenuBar {
                    background-color: #404040;
                    color: #ffffff;
                    border-bottom: 1px solid #555555;
                    padding: 4px;
                }
                QMenu {
                    background-color: #404040;
                    color: #ffffff;
                    border: 1px solid #555555;
                    padding: 4px;
                }
                QMenu::item:selected {
                    background-color: #0d6efd;
                    color: white;
                }
                QLabel {
                    color: #ffffff;
                }
            """
        }
    
    def _get_blue_theme(self):
        """Tema azul básico."""
        return {
            "name": "Tema Azul",
            "description": "Tema azul profesional",
            "stylesheet": """
                QMainWindow {
                    background-color: #e3f2fd;
                    color: #0d47a1;
                }
                QPushButton {
                    background-color: #2196f3;
                    color: white;
                    border: none;
                    padding: 10px 16px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #1976d2;
                }
                QTabBar::tab {
                    background-color: #bbdefb;
                    color: #0d47a1;
                    padding: 12px 20px;
                    margin-right: 2px;
                    border: 1px solid #2196f3;
                    border-bottom: none;
                    min-width: 100px;
                }
                QTabBar::tab:selected {
                    background-color: #ffffff;
                    color: #0d47a1;
                    font-weight: bold;
                }
                QMenuBar {
                    background-color: #1976d2;
                    color: #ffffff;
                    border-bottom: 1px solid #0d47a1;
                    padding: 4px;
                }
                QMenu {
                    background-color: #1976d2;
                    color: #ffffff;
                    border: 1px solid #0d47a1;
                    padding: 4px;
                }
                QMenu::item:selected {
                    background-color: #0d47a1;
                    color: white;
                }
                QLabel {
                    color: #0d47a1;
                }
            """
        }
    
    def get_available_themes(self):
        """Obtiene la lista de temas disponibles."""
        return list(self.themes.keys())
    
    def get_theme_info(self, theme_name):
        """Obtiene información de un tema."""
        return self.themes.get(theme_name, {})
    
    def apply_theme(self, theme_name):
        """Aplica un tema a la aplicación."""
        if theme_name not in self.themes:
            theme_name = "claro"
        
        app = QApplication.instance()
        if app:
            theme = self.themes[theme_name]
            app.setStyleSheet(theme["stylesheet"])
            
            # Guardar tema actual
            self.current_theme = theme_name
            self.settings.setValue("current_theme", theme_name)
            
            return True
        return False
    
    def get_current_theme(self):
        """Obtiene el tema actual."""
        return self.current_theme
    
    def load_saved_theme(self):
        """Carga el tema guardado."""
        saved_theme = self.settings.value("current_theme", "claro")
        self.apply_theme(saved_theme)
