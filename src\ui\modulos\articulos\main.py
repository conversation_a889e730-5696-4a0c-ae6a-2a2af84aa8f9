"""
Módulo principal para la gestión de artículos.
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QMessageBox
from ui.utils.window_utils import smart_dialog_setup


class ArticulosModule(QWidget):
    """
    Módulo principal para la gestión de artículos.
    """
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        smart_dialog_setup(self, "Módulo de Artículos")
    
    def setup_ui(self):
        """
        Configura la interfaz de usuario.
        """
        try:
            layout = QVBoxLayout()
            
            # Título
            title_label = QLabel("Gestión de Artículos")
            title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
            layout.addWidget(title_label)
            
            # Botones principales
            btn_nuevo = QPushButton("Nuevo Artículo")
            btn_nuevo.clicked.connect(self.nuevo_articulo)
            layout.addWidget(btn_nuevo)
            
            btn_editar = QPushButton("Editar Artículo")
            btn_editar.clicked.connect(self.editar_articulo)
            layout.addWidget(btn_editar)
            
            btn_listar = QPushButton("Listar Artículos")
            btn_listar.clicked.connect(self.listar_articulos)
            layout.addWidget(btn_listar)
            
            self.setLayout(layout)
            
        except Exception as e:
            print(f"Error en setup_ui de ArticulosModule: {e}")
    
    def nuevo_articulo(self):
        """
        Crea un nuevo artículo.
        """
        try:
            from .articulo_dialog_simple import ArticuloDialog
            dialog = ArticuloDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"Error en nuevo_articulo: {e}")
            QMessageBox.critical(self, "Error", f"Error al abrir gestión de artículos: {str(e)}")
    
    def editar_articulo(self):
        """
        Edita un artículo existente.
        """
        try:
            from .articulo_dialog_simple import ArticuloDialog
            dialog = ArticuloDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"Error en editar_articulo: {e}")
            QMessageBox.critical(self, "Error", f"Error al abrir gestión de artículos: {str(e)}")
    
    def listar_articulos(self):
        """
        Lista todos los artículos.
        """
        try:
            from .articulo_dialog_simple import ArticuloDialog
            dialog = ArticuloDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"Error en listar_articulos: {e}")
            QMessageBox.critical(self, "Error", f"Error al abrir gestión de artículos: {str(e)}")


def main():
    """
    Función principal del módulo.
    """
    try:
        from PyQt6.QtWidgets import QApplication
        import sys
        
        app = QApplication(sys.argv)
        window = ArticulosModule()
        window.show()
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"Error en main de articulos: {e}")


if __name__ == "__main__":
    main()
