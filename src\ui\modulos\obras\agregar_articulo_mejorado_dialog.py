"""
Diálogo mejorado para agregar artículos a obras con medidas múltiples.
Incluye H, H1-H4, A, A1-A4 y previsualización de materiales.
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLineEdit, QLabel,
    QFormLayout, QDialogButtonBox, QComboBox, QDoubleSpinBox, QSpinBox,
    QMessageBox, QGroupBox, QTextEdit, QTabWidget, QWidget, QGridLayout,
    QScrollArea, QTableWidget, QTableWidgetItem, QHeaderView
)
from PyQt6.QtCore import Qt
import json
import time
from PyQt6.QtGui import QFont

from models.base import get_db
from models.articulo import Articulo, ObraArticulo
from models.obra import Obra
from ui.widgets.lienzo_profesional.integracion_principal import IntegracionSistemaProfesional
from ui.utils.window_utils import setup_maximized_dialog


class AgregarArticuloMejoradoDialog(QDialog):
    """Diálogo mejorado para agregar artículos a obras con medidas múltiples."""
    
    def __init__(self, parent=None, obra=None, obra_articulo=None):
        super().__init__(parent)
        self.obra = obra
        self.obra_articulo = obra_articulo  # Para edición
        self.diseno_data = None  # Para almacenar datos del diseñador
        
        self.setWindowTitle("Agregar Artículo a Obra" if not obra_articulo else "Editar Artículo en Obra")
        self.setModal(True)

        # ✅ CONFIGURACIÓN SIMPLE Y EFECTIVA
        force_dialog_maximized(self, "Agregar Artículo a Obra" if not obra_articulo else "Editar Artículo en Obra")

        self._inicializar_ui()
        self._cargar_articulos()

        # Centrar el diálogo en la pantalla
        self._centrar_ventana()
        
        if obra_articulo:
            self._cargar_datos_obra_articulo()
    
    def _inicializar_ui(self):
        """Configura la interfaz de usuario con pestañas."""
        layout_principal = QVBoxLayout(self)
        
        # Título
        titulo = QLabel(f"Obra: {self.obra.codigo} - {self.obra.nombre}")
        titulo.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        titulo.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout_principal.addWidget(titulo)
        
        # Crear pestañas
        self.tabs = QTabWidget()
        
        # Pestaña 1: Selección y Medidas
        self._crear_tab_seleccion_medidas()
        
        # Pestaña 2: Medidas Adicionales
        self._crear_tab_medidas_adicionales()
        
        # Pestaña 3: Previsualización
        self._crear_tab_previsualizacion()
        
        layout_principal.addWidget(self.tabs)
        
        # Botones de acción
        self._crear_botones_accion(layout_principal)
    
    def _crear_tab_seleccion_medidas(self):
        """Crea la pestaña de selección de artículo y medidas principales."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Grupo: Selección de Artículo
        grupo_articulo = QGroupBox("Selección de Artículo")
        layout_articulo = QFormLayout(grupo_articulo)
        
        self.combo_articulo = QComboBox()
        self.combo_articulo.currentTextChanged.connect(self._actualizar_info_articulo)
        layout_articulo.addRow("Artículo*:", self.combo_articulo)
        
        # Información del artículo seleccionado
        self.label_info_articulo = QLabel("Seleccione un artículo para ver su información")
        self.label_info_articulo.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        self.label_info_articulo.setWordWrap(True)
        layout_articulo.addRow("Información:", self.label_info_articulo)
        
        layout.addWidget(grupo_articulo)
        
        # Grupo: Medidas Principales
        grupo_medidas = QGroupBox("Medidas Principales")
        layout_medidas = QGridLayout(grupo_medidas)
        
        # Altura principal (H)
        layout_medidas.addWidget(QLabel("Altura (H):"), 0, 0)
        self.campo_altura = QDoubleSpinBox()
        self.campo_altura.setRange(1, 9999)
        self.campo_altura.setDecimals(1)
        self.campo_altura.setSuffix(" mm")
        self.campo_altura.setValue(1000.0)
        self.campo_altura.valueChanged.connect(self._actualizar_previsualizacion)
        layout_medidas.addWidget(self.campo_altura, 0, 1)
        
        # Anchura principal (A)
        layout_medidas.addWidget(QLabel("Anchura (A):"), 0, 2)
        self.campo_anchura = QDoubleSpinBox()
        self.campo_anchura.setRange(1, 9999)
        self.campo_anchura.setDecimals(1)
        self.campo_anchura.setSuffix(" mm")
        self.campo_anchura.setValue(800.0)
        self.campo_anchura.valueChanged.connect(self._actualizar_previsualizacion)
        layout_medidas.addWidget(self.campo_anchura, 0, 3)

        self.boton_diseno = QPushButton("🖼️ Diseñar Artículo")
        self.boton_diseno.clicked.connect(self._abrir_disenador)
        self.boton_diseno.setToolTip("Abrir el diseñador gráfico para un despiece detallado")
        layout_medidas.addWidget(self.boton_diseno, 0, 4, 1, 2) # Ocupa 2 columnas
        
        # Cantidad
        layout_medidas.addWidget(QLabel("Cantidad:"), 1, 0)
        self.campo_cantidad = QSpinBox()
        self.campo_cantidad.setRange(1, 999)
        self.campo_cantidad.setValue(1)
        self.campo_cantidad.valueChanged.connect(self._actualizar_previsualizacion)
        layout_medidas.addWidget(self.campo_cantidad, 1, 1)
        
        # Botones de medidas predefinidas
        layout_medidas.addWidget(QLabel("Medidas rápidas:"), 1, 2)
        layout_botones_medidas = QHBoxLayout()
        
        btn_ventana_pequena = QPushButton("1000x800")
        btn_ventana_pequena.clicked.connect(lambda: self._establecer_medidas(1000, 800))
        layout_botones_medidas.addWidget(btn_ventana_pequena)
        
        btn_ventana_mediana = QPushButton("1500x1200")
        btn_ventana_mediana.clicked.connect(lambda: self._establecer_medidas(1500, 1200))
        layout_botones_medidas.addWidget(btn_ventana_mediana)
        
        btn_ventana_grande = QPushButton("2000x1500")
        btn_ventana_grande.clicked.connect(lambda: self._establecer_medidas(2000, 1500))
        layout_botones_medidas.addWidget(btn_ventana_grande)
        
        widget_botones = QWidget()
        widget_botones.setLayout(layout_botones_medidas)
        layout_medidas.addWidget(widget_botones, 1, 3)
        
        layout.addWidget(grupo_medidas)
        
        # Grupo: Notas
        grupo_notas = QGroupBox("Notas y Observaciones")
        layout_notas = QVBoxLayout(grupo_notas)
        
        self.campo_notas = QTextEdit()
        self.campo_notas.setMaximumHeight(80)
        self.campo_notas.setPlaceholderText("Observaciones, características especiales, etc.")
        layout_notas.addWidget(self.campo_notas)
        
        layout.addWidget(grupo_notas)
        
        self.tabs.addTab(tab, "📏 Medidas Principales")
    
    def _crear_tab_medidas_adicionales(self):
        """Crea la pestaña de medidas adicionales H1-H4, A1-A4."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Información
        info_label = QLabel(
            "Las medidas adicionales son útiles para artículos complejos con múltiples dimensiones.\n"
            "Por ejemplo: ventanas con divisiones, marcos con diferentes alturas, etc.\n"
            "Deje en 0 las medidas que no necesite."
        )
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; font-style: italic; padding: 10px; background: #f0f0f0; border-radius: 5px;")
        layout.addWidget(info_label)
        
        # Grupo: Alturas Adicionales
        grupo_alturas = QGroupBox("Alturas Adicionales")
        layout_alturas = QGridLayout(grupo_alturas)
        
        self.campos_alturas_adicionales = {}
        
        for i, (codigo, descripcion) in enumerate([
            ('H1', 'Segunda altura'),
            ('H2', 'Tercera altura'),
            ('H3', 'Cuarta altura'),
            ('H4', 'Quinta altura')
        ]):
            row = i // 2
            col = (i % 2) * 2
            
            layout_alturas.addWidget(QLabel(f"{codigo} - {descripcion}:"), row, col)
            
            campo = QDoubleSpinBox()
            campo.setRange(0, 9999)
            campo.setDecimals(1)
            campo.setSuffix(" mm")
            campo.setValue(0.0)
            campo.valueChanged.connect(self._actualizar_previsualizacion)
            
            self.campos_alturas_adicionales[codigo] = campo
            layout_alturas.addWidget(campo, row, col + 1)
        
        layout.addWidget(grupo_alturas)
        
        # Grupo: Anchuras Adicionales
        grupo_anchuras = QGroupBox("Anchuras Adicionales")
        layout_anchuras = QGridLayout(grupo_anchuras)
        
        self.campos_anchuras_adicionales = {}
        
        for i, (codigo, descripcion) in enumerate([
            ('A1', 'Segunda anchura'),
            ('A2', 'Tercera anchura'),
            ('A3', 'Cuarta anchura'),
            ('A4', 'Quinta anchura')
        ]):
            row = i // 2
            col = (i % 2) * 2
            
            layout_anchuras.addWidget(QLabel(f"{codigo} - {descripcion}:"), row, col)
            
            campo = QDoubleSpinBox()
            campo.setRange(0, 9999)
            campo.setDecimals(1)
            campo.setSuffix(" mm")
            campo.setValue(0.0)
            campo.valueChanged.connect(self._actualizar_previsualizacion)
            
            self.campos_anchuras_adicionales[codigo] = campo
            layout_anchuras.addWidget(campo, row, col + 1)
        
        layout.addWidget(grupo_anchuras)
        
        # Botones de ayuda
        layout_botones_ayuda = QHBoxLayout()
        
        btn_copiar_h = QPushButton("Copiar H a todas las alturas")
        btn_copiar_h.clicked.connect(self._copiar_altura_principal)
        layout_botones_ayuda.addWidget(btn_copiar_h)
        
        btn_copiar_a = QPushButton("Copiar A a todas las anchuras")
        btn_copiar_a.clicked.connect(self._copiar_anchura_principal)
        layout_botones_ayuda.addWidget(btn_copiar_a)
        
        btn_limpiar = QPushButton("Limpiar adicionales")
        btn_limpiar.clicked.connect(self._limpiar_medidas_adicionales)
        layout_botones_ayuda.addWidget(btn_limpiar)
        
        layout_botones_ayuda.addStretch()
        layout.addLayout(layout_botones_ayuda)
        
        self.tabs.addTab(tab, "📐 Medidas Adicionales")
    
    def _crear_tab_previsualizacion(self):
        """Crea la pestaña de previsualización de materiales."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Información
        info_label = QLabel("Previsualización de materiales calculados con las medidas actuales:")
        info_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout.addWidget(info_label)
        
        # Tabla de materiales
        self.tabla_materiales = QTableWidget()
        self.tabla_materiales.setColumnCount(5)
        self.tabla_materiales.setHorizontalHeaderLabels([
            "Tipo", "Material", "Cantidad", "Medida/Superficie", "Coste"
        ])
        
        # Configurar tabla
        header = self.tabla_materiales.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        
        layout.addWidget(self.tabla_materiales)
        
        # Resumen de costes
        self.label_resumen_costes = QLabel("Seleccione un artículo para ver el resumen de costes")
        self.label_resumen_costes.setStyleSheet("font-weight: bold; padding: 10px; background: #e8f4f8; border-radius: 5px;")
        layout.addWidget(self.label_resumen_costes)
        
        self.tabs.addTab(tab, "👁️ Previsualización")
    
    def _crear_botones_accion(self, layout_principal):
        """Crea los botones de acción."""
        layout_botones = QHBoxLayout()
        
        # Botón de calcular precio
        btn_calcular = QPushButton("💰 Calcular Precio")
        btn_calcular.clicked.connect(self._calcular_precio)
        btn_calcular.setToolTip("Calcula el precio con las medidas actuales")
        layout_botones.addWidget(btn_calcular)
        
        layout_botones.addStretch()
        
        # Botones estándar
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )

        
        if self.obra_articulo:
            botones.button(QDialogButtonBox.StandardButton.Ok).setText("💾 Guardar Cambios")
        else:
            botones.button(QDialogButtonBox.StandardButton.Ok).setText("➕ Agregar a Obra")
        
        botones.button(QDialogButtonBox.StandardButton.Cancel).setText("❌ Cancelar")
        
        botones.accepted.connect(self._aceptar)
        botones.rejected.connect(self.reject)
        
        layout_botones.addWidget(botones)
        layout_principal.addLayout(layout_botones)

    def _abrir_disenador(self):
        """Abre el diálogo de diseño gráfico profesional."""
        print("🔍 DEBUG: Iniciando _abrir_disenador")

        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QDialogButtonBox, QMessageBox
        from PyQt6.QtCore import QDateTime
        import json

        try:
            print("🔍 DEBUG: Intentando crear instancia de IntegracionSistemaProfesional")
            # Crear el nuevo sistema profesional V2
            from src.ui.widgets.lienzo_profesional.sistema_profesional_v2 import SistemaProfesionalV2
            sistema_profesional = SistemaProfesionalV2()
            print("✅ DEBUG: Sistema Profesional V2 creado exitosamente")

            print("🔍 DEBUG: Creando diálogo")
            # Crear diálogo profesional con controles completos
            dialogo = QDialog(self)
            dialogo.setWindowTitle("🏗️ Sistema Profesional de Diseño de Artículos")
            dialogo.setModal(True)

            # Configurar ventana con controles completos
            dialogo.setWindowFlags(
                Qt.WindowType.Dialog |
                Qt.WindowType.WindowMaximizeButtonHint |
                Qt.WindowType.WindowMinimizeButtonHint |
                Qt.WindowType.WindowCloseButtonHint |
                Qt.WindowType.WindowSystemMenuHint
            )

            # Ajustar tamaño a la pantalla
            screen = self.screen().availableGeometry()
            ancho = min(1400, int(screen.width() * 0.95))
            alto = min(900, int(screen.height() * 0.9))
            dialogo.resize(ancho, alto)

            # Centrar en pantalla
            x = (screen.width() - ancho) // 2
            y = (screen.height() - alto) // 2
            dialogo.move(x, y)

            # Estilo profesional para el diálogo
            dialogo.setStyleSheet("""
                QDialog {
                    background-color: #f8f9fa;
                    border: 2px solid #dee2e6;
                }
                QDialogButtonBox {
                    background-color: #ffffff;
                    border-top: 1px solid #dee2e6;
                    padding: 10px;
                }
                QDialogButtonBox QPushButton {
                    background-color: #007bff;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QDialogButtonBox QPushButton:hover {
                    background-color: #0056b3;
                }
                QDialogButtonBox QPushButton:pressed {
                    background-color: #004085;
                }
            """)

            layout = QVBoxLayout(dialogo)
            layout.addWidget(sistema_profesional)
            print("✅ DEBUG: Sistema profesional V2 añadido al layout")

            # Conectar señal para procesar artículos generados
            sistema_profesional.articulo_creado.connect(
                lambda datos: self._procesar_articulo_generado(datos)
            )

            # Botones
            print("🔍 DEBUG: Creando botones del diálogo")
            botones = QDialogButtonBox(
                QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
            )
            botones.accepted.connect(dialogo.accept)
            botones.rejected.connect(dialogo.reject)
            layout.addWidget(botones)

            # Ejecutar diálogo
            print("🔍 DEBUG: Ejecutando diálogo...")
            resultado = dialogo.exec()
            print(f"✅ DEBUG: Diálogo ejecutado, resultado: {resultado}")

            if resultado == QDialog.DialogCode.Accepted:
                print("🔍 DEBUG: Diálogo aceptado, procesando datos")
                # Procesar datos del sistema profesional
                altura_actual = self.campo_altura.value()
                anchura_actual = self.campo_anchura.value()

                self.diseno_data = {
                    'altura': altura_actual,
                    'anchura': anchura_actual,
                    'sistema': 'profesional_integrado',
                    'timestamp': str(QDateTime.currentDateTime().toString())
                }

                self.campo_notas.setPlainText(json.dumps(self.diseno_data, indent=2, ensure_ascii=False))
                self._actualizar_previsualizacion()

                QMessageBox.information(
                    self,
                    "✅ Diseño Completado",
                    "El diseño profesional ha sido guardado correctamente."
                )
                print("✅ DEBUG: Proceso completado exitosamente")

        except ImportError as e:
            print(f"❌ DEBUG: Error de importación: {e}")
            QMessageBox.critical(
                self,
                "❌ Error de Importación",
                f"No se pudo importar el sistema profesional:\n\n{str(e)}\n\n"
                "Verifique que el módulo esté correctamente instalado."
            )
        except Exception as e:
            print(f"❌ DEBUG: Error general: {e}")
            print(f"❌ DEBUG: Tipo de error: {type(e)}")
            import traceback
            print(f"❌ DEBUG: Traceback completo:\n{traceback.format_exc()}")
            QMessageBox.critical(
                self,
                "❌ Error",
                f"Error al abrir el sistema profesional:\n\n{str(e)}\n\n"
                "Consulte la consola para más detalles."
            )



    def _cargar_articulos(self):
        """Carga los artículos disponibles."""
        db = next(get_db())
        try:
            articulos = db.query(Articulo).filter(Articulo.activo == True).order_by(Articulo.codigo).all()

            self.combo_articulo.clear()
            for articulo in articulos:
                texto = f"{articulo.codigo} - {articulo.descripcion}"
                if articulo.serie:
                    texto += f" ({articulo.serie})"
                self.combo_articulo.addItem(texto, articulo.id)

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al cargar artículos: {str(e)}")
        finally:
            db.close()

    def _actualizar_info_articulo(self):
        """Actualiza la información del artículo seleccionado."""
        articulo_id = self.combo_articulo.currentData()
        if not articulo_id:
            self.label_info_articulo.setText("Seleccione un artículo para ver su información")
            self._limpiar_tabla_materiales()
            return

        db = next(get_db())
        try:
            articulo = db.query(Articulo).filter(Articulo.id == articulo_id).first()
            if articulo:
                info = f"<b>Código:</b> {articulo.codigo}<br>"
                info += f"<b>Serie:</b> {articulo.serie or 'Sin serie'}<br>"
                info += f"<b>Descripción:</b> {articulo.descripcion}<br>"

                # Contar componentes
                num_perfiles = len(articulo.perfiles)
                num_accesorios = len(articulo.accesorios)
                num_cristales = len(articulo.cristales)

                info += f"<b>Componentes:</b> {num_perfiles} perfiles, {num_accesorios} accesorios, {num_cristales} cristales<br>"

                if articulo.tiempo_taller:
                    info += f"<b>Tiempo taller:</b> {articulo.tiempo_taller} h<br>"
                if articulo.tiempo_obra:
                    info += f"<b>Tiempo obra:</b> {articulo.tiempo_obra} h"

                self.label_info_articulo.setText(info)
                self._actualizar_previsualizacion()
            else:
                self.label_info_articulo.setText("Artículo no encontrado")
                self._limpiar_tabla_materiales()

        except Exception as e:
            self.label_info_articulo.setText(f"Error al cargar información: {str(e)}")
            self._limpiar_tabla_materiales()
        finally:
            db.close()

    def _establecer_medidas(self, altura, anchura):
        """Establece medidas predefinidas."""
        self.campo_altura.setValue(altura)
        self.campo_anchura.setValue(anchura)
        self._actualizar_previsualizacion()

    def _copiar_altura_principal(self):
        """Copia la altura principal a todas las alturas adicionales."""
        altura_principal = self.campo_altura.value()
        for campo in self.campos_alturas_adicionales.values():
            campo.setValue(altura_principal)
        self._actualizar_previsualizacion()

    def _copiar_anchura_principal(self):
        """Copia la anchura principal a todas las anchuras adicionales."""
        anchura_principal = self.campo_anchura.value()
        for campo in self.campos_anchuras_adicionales.values():
            campo.setValue(anchura_principal)
        self._actualizar_previsualizacion()

    def _limpiar_medidas_adicionales(self):
        """Limpia todas las medidas adicionales."""
        for campo in self.campos_alturas_adicionales.values():
            campo.setValue(0.0)
        for campo in self.campos_anchuras_adicionales.values():
            campo.setValue(0.0)
        self._actualizar_previsualizacion()

    def _actualizar_previsualizacion(self):
        """Actualiza la previsualización de materiales."""
        articulo_id = self.combo_articulo.currentData()
        if not articulo_id:
            self._limpiar_tabla_materiales()
            return

        try:
            # Obtener medidas
            altura = self.campo_altura.value()
            anchura = self.campo_anchura.value()
            cantidad = self.campo_cantidad.value()

            # Medidas adicionales
            altura_1 = self.campos_alturas_adicionales['H1'].value() or None
            altura_2 = self.campos_alturas_adicionales['H2'].value() or None
            altura_3 = self.campos_alturas_adicionales['H3'].value() or None
            altura_4 = self.campos_alturas_adicionales['H4'].value() or None
            anchura_1 = self.campos_anchuras_adicionales['A1'].value() or None
            anchura_2 = self.campos_anchuras_adicionales['A2'].value() or None
            anchura_3 = self.campos_anchuras_adicionales['A3'].value() or None
            anchura_4 = self.campos_anchuras_adicionales['A4'].value() or None

            # Obtener artículo y calcular materiales
            db = next(get_db())
            try:
                articulo = db.query(Articulo).filter(Articulo.id == articulo_id).first()
                if not articulo:
                    self._limpiar_tabla_materiales()
                    return

                materiales = articulo.calcular_materiales(
                    altura, anchura, altura_1, altura_2, altura_3, altura_4,
                    anchura_1, anchura_2, anchura_3, anchura_4
                )

                self._mostrar_materiales_en_tabla(materiales, cantidad)

            finally:
                db.close()

        except Exception as e:
            QMessageBox.warning(self, "Error", f"Error al calcular materiales: {str(e)}")
            self._limpiar_tabla_materiales()

    def _mostrar_materiales_en_tabla(self, materiales, cantidad):
        """Muestra los materiales calculados en la tabla."""
        self.tabla_materiales.setRowCount(0)

        coste_total = 0.0
        fila = 0

        # Mostrar perfiles
        for material in materiales['perfiles']:
            self.tabla_materiales.insertRow(fila)

            self.tabla_materiales.setItem(fila, 0, QTableWidgetItem("Perfil"))
            self.tabla_materiales.setItem(fila, 1, QTableWidgetItem(
                f"{material['perfil'].codigo} - {material['perfil'].descripcion}"
            ))
            self.tabla_materiales.setItem(fila, 2, QTableWidgetItem(f"{material['cantidad'] * cantidad:.0f}"))
            self.tabla_materiales.setItem(fila, 3, QTableWidgetItem(f"{material['metros_totales'] * cantidad:.2f} m"))

            coste_material = 0.0
            if material['perfil'].precio_metro:
                coste_material = material['metros_totales'] * material['perfil'].precio_metro * cantidad
                coste_total += coste_material

            self.tabla_materiales.setItem(fila, 4, QTableWidgetItem(f"{coste_material:.2f} €"))
            fila += 1

        # Mostrar accesorios
        for material in materiales['accesorios']:
            self.tabla_materiales.insertRow(fila)

            self.tabla_materiales.setItem(fila, 0, QTableWidgetItem("Accesorio"))
            self.tabla_materiales.setItem(fila, 1, QTableWidgetItem(
                f"{material['accesorio'].codigo} - {material['accesorio'].descripcion}"
            ))
            self.tabla_materiales.setItem(fila, 2, QTableWidgetItem(f"{material['cantidad'] * cantidad:.0f}"))
            self.tabla_materiales.setItem(fila, 3, QTableWidgetItem("unidades"))

            coste_material = 0.0
            if material['accesorio'].precio:
                coste_material = material['cantidad'] * material['accesorio'].precio * cantidad
                coste_total += coste_material

            self.tabla_materiales.setItem(fila, 4, QTableWidgetItem(f"{coste_material:.2f} €"))
            fila += 1

        # Mostrar cristales
        for material in materiales['cristales']:
            self.tabla_materiales.insertRow(fila)

            self.tabla_materiales.setItem(fila, 0, QTableWidgetItem("Cristal"))
            self.tabla_materiales.setItem(fila, 1, QTableWidgetItem(
                f"{material['cristal'].codigo} - {material['cristal'].descripcion}"
            ))
            self.tabla_materiales.setItem(fila, 2, QTableWidgetItem(f"{material['cantidad'] * cantidad:.0f}"))
            self.tabla_materiales.setItem(fila, 3, QTableWidgetItem(f"{material['metros_cuadrados'] * cantidad:.2f} m²"))

            coste_material = 0.0
            if material['cristal'].precio_metro_cuadrado:
                coste_material = material['metros_cuadrados'] * material['cristal'].precio_metro_cuadrado * cantidad
                coste_total += coste_material

            self.tabla_materiales.setItem(fila, 4, QTableWidgetItem(f"{coste_material:.2f} €"))
            fila += 1

        # Actualizar resumen
        self.label_resumen_costes.setText(
            f"💰 <b>Coste Total: {coste_total:.2f} €</b> | "
            f"Coste por unidad: {coste_total/cantidad:.2f} € | "
            f"Cantidad: {cantidad} unidades"
        )

    def _limpiar_tabla_materiales(self):
        """Limpia la tabla de materiales."""
        self.tabla_materiales.setRowCount(0)
        self.label_resumen_costes.setText("Seleccione un artículo para ver el resumen de costes")

    def _cargar_datos_obra_articulo(self):
        """Carga los datos del artículo en obra para edición."""
        if not self.obra_articulo:
            return

        # Buscar el artículo en el combo
        for i in range(self.combo_articulo.count()):
            if self.combo_articulo.itemData(i) == self.obra_articulo.articulo_id:
                self.combo_articulo.setCurrentIndex(i)
                break

        # Cargar medidas principales
        self.campo_altura.setValue(self.obra_articulo.altura)
        self.campo_anchura.setValue(self.obra_articulo.anchura)
        self.campo_cantidad.setValue(self.obra_articulo.cantidad)

        # Cargar medidas adicionales
        if hasattr(self.obra_articulo, 'altura_1') and self.obra_articulo.altura_1:
            self.campos_alturas_adicionales['H1'].setValue(self.obra_articulo.altura_1)
        if hasattr(self.obra_articulo, 'altura_2') and self.obra_articulo.altura_2:
            self.campos_alturas_adicionales['H2'].setValue(self.obra_articulo.altura_2)
        if hasattr(self.obra_articulo, 'altura_3') and self.obra_articulo.altura_3:
            self.campos_alturas_adicionales['H3'].setValue(self.obra_articulo.altura_3)
        if hasattr(self.obra_articulo, 'altura_4') and self.obra_articulo.altura_4:
            self.campos_alturas_adicionales['H4'].setValue(self.obra_articulo.altura_4)

        if hasattr(self.obra_articulo, 'anchura_1') and self.obra_articulo.anchura_1:
            self.campos_anchuras_adicionales['A1'].setValue(self.obra_articulo.anchura_1)
        if hasattr(self.obra_articulo, 'anchura_2') and self.obra_articulo.anchura_2:
            self.campos_anchuras_adicionales['A2'].setValue(self.obra_articulo.anchura_2)
        if hasattr(self.obra_articulo, 'anchura_3') and self.obra_articulo.anchura_3:
            self.campos_anchuras_adicionales['A3'].setValue(self.obra_articulo.anchura_3)
        if hasattr(self.obra_articulo, 'anchura_4') and self.obra_articulo.anchura_4:
            self.campos_anchuras_adicionales['A4'].setValue(self.obra_articulo.anchura_4)

        # Cargar notas y datos de diseño
        notas = self.obra_articulo.notas or ""
        self.campo_notas.setPlainText(notas)

        try:
            datos_guardados = json.loads(notas)
            if isinstance(datos_guardados, dict) and 'celdas' in datos_guardados:
                self.diseno_data = datos_guardados
                print("Datos de diseño cargados desde las notas.")
        except (json.JSONDecodeError, TypeError):
            self.diseno_data = None # No es un JSON de diseño válido

        # Actualizar previsualización
        self._actualizar_previsualizacion()

    def _calcular_precio(self):
        """Calcula y muestra el precio detallado."""
        articulo_id = self.combo_articulo.currentData()
        if not articulo_id:
            QMessageBox.warning(self, "Error", "Debe seleccionar un artículo.")
            return

        try:
            # Crear un ObraArticulo temporal para calcular el precio
            obra_articulo_temp = ObraArticulo()
            obra_articulo_temp.obra_id = self.obra.id
            obra_articulo_temp.articulo_id = articulo_id
            obra_articulo_temp.altura = self.campo_altura.value()
            obra_articulo_temp.anchura = self.campo_anchura.value()
            obra_articulo_temp.cantidad = self.campo_cantidad.value()

            # Asignar medidas adicionales
            obra_articulo_temp.altura_1 = self.campos_alturas_adicionales['H1'].value() or None
            obra_articulo_temp.altura_2 = self.campos_alturas_adicionales['H2'].value() or None
            obra_articulo_temp.altura_3 = self.campos_alturas_adicionales['H3'].value() or None
            obra_articulo_temp.altura_4 = self.campos_alturas_adicionales['H4'].value() or None
            obra_articulo_temp.anchura_1 = self.campos_anchuras_adicionales['A1'].value() or None
            obra_articulo_temp.anchura_2 = self.campos_anchuras_adicionales['A2'].value() or None
            obra_articulo_temp.anchura_3 = self.campos_anchuras_adicionales['A3'].value() or None
            obra_articulo_temp.anchura_4 = self.campos_anchuras_adicionales['A4'].value() or None

            # Obtener artículo
            db = next(get_db())
            try:
                articulo = db.query(Articulo).filter(Articulo.id == articulo_id).first()
                obra_articulo_temp.articulo = articulo

                precio_total = obra_articulo_temp.calcular_precio()
                precio_unitario = precio_total / obra_articulo_temp.cantidad if obra_articulo_temp.cantidad > 0 else 0

                QMessageBox.information(
                    self,
                    "💰 Cálculo de Precio",
                    f"<b>Artículo:</b> {articulo.codigo} - {articulo.descripcion}<br><br>"
                    f"<b>Medidas:</b> {obra_articulo_temp.altura:.0f} x {obra_articulo_temp.anchura:.0f} mm<br>"
                    f"<b>Cantidad:</b> {obra_articulo_temp.cantidad} unidades<br><br>"
                    f"<b>Precio por unidad:</b> {precio_unitario:.2f} €<br>"
                    f"<b>Precio total:</b> <span style='color: green; font-size: 14px;'>{precio_total:.2f} €</span>"
                )

            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al calcular precio: {str(e)}")

    def _aceptar(self):
        """Valida y guarda el artículo en la obra."""
        if self.combo_articulo.currentData() is None:
            QMessageBox.warning(self, "Error", "Debe seleccionar un artículo.")
            self.tabs.setCurrentIndex(0)
            return

        if self.campo_altura.value() <= 0 or self.campo_anchura.value() <= 0:
            QMessageBox.warning(self, "Error", "Las medidas principales deben ser mayores que 0.")
            self.tabs.setCurrentIndex(0)
            return

        if self._guardar_obra_articulo():
            self.accept()

    def _guardar_obra_articulo(self):
        """Guarda el artículo en la obra."""
        db = next(get_db())

        try:
            if self.obra_articulo:
                # Editar artículo existente en obra
                obra_articulo_db = db.query(ObraArticulo).filter(
                    ObraArticulo.id == self.obra_articulo.id
                ).first()
                if not obra_articulo_db:
                    QMessageBox.critical(self, "Error", "No se encontró el artículo en la obra.")
                    return False
            else:
                # Crear nuevo artículo en obra
                obra_articulo_db = ObraArticulo()
                obra_articulo_db.obra_id = self.obra.id
                db.add(obra_articulo_db)

            # Asignar valores
            obra_articulo_db.articulo_id = self.combo_articulo.currentData()
            obra_articulo_db.altura = self.campo_altura.value()
            obra_articulo_db.anchura = self.campo_anchura.value()
            obra_articulo_db.cantidad = self.campo_cantidad.value()

            # Guardar el diseño en las notas si existe, sino, las notas del usuario
            if self.diseno_data:
                obra_articulo_db.notas = json.dumps(self.diseno_data, indent=2)
            else:
                obra_articulo_db.notas = self.campo_notas.toPlainText().strip() or None

            # Asignar medidas adicionales
            obra_articulo_db.altura_1 = self.campos_alturas_adicionales['H1'].value() or None
            obra_articulo_db.altura_2 = self.campos_alturas_adicionales['H2'].value() or None
            obra_articulo_db.altura_3 = self.campos_alturas_adicionales['H3'].value() or None
            obra_articulo_db.altura_4 = self.campos_alturas_adicionales['H4'].value() or None
            obra_articulo_db.anchura_1 = self.campos_anchuras_adicionales['A1'].value() or None
            obra_articulo_db.anchura_2 = self.campos_anchuras_adicionales['A2'].value() or None
            obra_articulo_db.anchura_3 = self.campos_anchuras_adicionales['A3'].value() or None
            obra_articulo_db.anchura_4 = self.campos_anchuras_adicionales['A4'].value() or None

            # Calcular precio
            articulo = db.query(Articulo).filter(Articulo.id == obra_articulo_db.articulo_id).first()
            obra_articulo_db.articulo = articulo
            obra_articulo_db.calcular_precio()

            db.commit()

            # Mensaje de éxito
            articulo_desc = self.combo_articulo.currentText().split(' - ')[0]

            QMessageBox.information(
                self,
                "✅ Éxito",
                f"Artículo '{articulo_desc}' {'actualizado' if self.obra_articulo else 'agregado'} correctamente a la obra.\n\n"
                f"Medidas: {obra_articulo_db.altura:.0f} x {obra_articulo_db.anchura:.0f} mm\n"
                f"Cantidad: {obra_articulo_db.cantidad} unidades\n"
                f"Precio total: {obra_articulo_db.precio_total:.2f} €"
            )

            return True

        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self,
                "❌ Error",
                f"No se pudo guardar el artículo en la obra: {str(e)}"
            )
            return False
        finally:
            db.close()

    def _centrar_ventana(self):
        """Centra la ventana en la pantalla."""
        from PyQt6.QtGui import QGuiApplication

        # Obtener la geometría de la pantalla principal
        screen = QGuiApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()

        # Calcular la posición para centrar
        x = (screen_geometry.width() - self.width()) // 2
        y = (screen_geometry.height() - self.height()) // 2

        # Mover la ventana al centro
        self.move(x, y)

    def _procesar_articulo_generado(self, datos_articulo):
        """Procesa un artículo generado desde el sistema profesional."""
        print(f"🔍 DEBUG: Procesando artículo generado: {datos_articulo}")

        try:
            # Crear artículo con los datos del sistema profesional
            nuevo_articulo = {
                'codigo': f"ART-{self._generar_codigo_unico()}",
                'descripcion': f"{datos_articulo['material']} {datos_articulo['serie']} - {datos_articulo['ancho']}x{datos_articulo['alto']}mm",
                'altura': datos_articulo['alto'],
                'anchura': datos_articulo['ancho'],
                'precio_venta': self._calcular_precio_venta(datos_articulo),
                'precio_coste': self._calcular_precio_coste(datos_articulo),
                'categoria': 'Ventana',
                'material': datos_articulo['material'],
                'color': datos_articulo['color'],
                'serie': datos_articulo['serie'],
                'tipo_apertura': datos_articulo['tipo_apertura'],
                'cristal': datos_articulo['cristal'],
                'herraje': datos_articulo['herraje']
            }

            print(f"✅ DEBUG: Artículo procesado: {nuevo_articulo}")

            # Crear el artículo en la base de datos
            articulo_id = self._crear_articulo_en_bd(nuevo_articulo)

            if articulo_id:
                # Recargar el combo de artículos
                self._cargar_articulos()

                # Seleccionar el nuevo artículo
                for i in range(self.combo_articulo.count()):
                    if self.combo_articulo.itemData(i) == articulo_id:
                        self.combo_articulo.setCurrentIndex(i)
                        break

                # Actualizar campos del formulario
                self.campo_altura.setValue(datos_articulo['alto'])
                self.campo_anchura.setValue(datos_articulo['ancho'])

                # Actualizar notas con datos del diseño
                self.diseno_data = datos_articulo
                self.campo_notas.setPlainText(json.dumps(datos_articulo, indent=2, ensure_ascii=False))
                self._actualizar_previsualizacion()

                QMessageBox.information(
                    self,
                    "✅ Artículo Generado",
                    f"Artículo '{nuevo_articulo['codigo']}' creado y seleccionado correctamente.\n\n"
                    f"Descripción: {nuevo_articulo['descripcion']}\n"
                    f"Precio: {nuevo_articulo['precio_venta']:.2f} €"
                )
            else:
                QMessageBox.warning(
                    self,
                    "⚠️ Advertencia",
                    "El artículo se procesó pero no se pudo crear en la base de datos."
                )

        except Exception as e:
            print(f"❌ DEBUG: Error procesando artículo: {e}")
            QMessageBox.critical(
                self,
                "❌ Error",
                f"Error al procesar el artículo generado:\n\n{str(e)}"
            )

    def _calcular_precio_venta(self, datos):
        """Calcula el precio de venta basado en los datos del artículo."""
        # Precio base por m²
        precio_base_m2 = 150.0

        # Calcular área en m²
        area_m2 = (datos['ancho'] * datos['alto']) / 1000000

        # Multiplicadores según configuración
        multiplicador_material = {'Aluminio': 1.0, 'PVC': 0.8, 'Madera': 1.3}.get(datos['material'], 1.0)
        multiplicador_apertura = {'Fijo': 0.8, 'Practicable': 1.0, 'Oscilobatiente': 1.2, 'Corredera': 1.1, 'Plegable': 1.4}.get(datos['tipo_apertura'], 1.0)
        multiplicador_cristal = {'Simple 4mm': 0.7, 'Doble 4+12+4': 1.0, 'Doble 6+12+6': 1.1, 'Triple 4+12+4+12+4': 1.3, 'Bajo Emisivo': 1.2}.get(datos['cristal'], 1.0)

        precio_final = precio_base_m2 * area_m2 * multiplicador_material * multiplicador_apertura * multiplicador_cristal

        return round(precio_final, 2)

    def _calcular_precio_coste(self, datos):
        """Calcula el precio de coste basado en el precio de venta."""
        precio_venta = self._calcular_precio_venta(datos)
        return round(precio_venta * 0.75, 2)  # 75% del precio de venta

    def _generar_codigo_unico(self):
        """Genera un código único para el artículo."""
        import random
        import string
        timestamp = str(int(time.time()))[-6:]  # Últimos 6 dígitos del timestamp
        random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
        return f"{timestamp}{random_part}"

    def _crear_articulo_en_bd(self, datos_articulo):
        """Crea un nuevo artículo en la base de datos."""
        print(f"🔍 DEBUG: Intentando crear artículo en BD: {datos_articulo}")

        db = next(get_db())
        try:
            # Crear nuevo artículo
            print("🔍 DEBUG: Creando instancia de Articulo...")
            nuevo_articulo = Articulo(
                codigo=datos_articulo['codigo'],
                descripcion=datos_articulo['descripcion'],
                serie=datos_articulo.get('serie', 'Serie 70'),
                activo=True
            )
            print("✅ DEBUG: Instancia de Articulo creada")

            print("🔍 DEBUG: Agregando a la sesión...")
            db.add(nuevo_articulo)
            print("✅ DEBUG: Agregado a la sesión")

            print("🔍 DEBUG: Haciendo commit...")
            db.commit()
            print("✅ DEBUG: Commit exitoso")

            print(f"✅ DEBUG: Artículo creado en BD con ID: {nuevo_articulo.id}")
            return nuevo_articulo.id

        except Exception as e:
            print(f"❌ DEBUG: Error creando artículo en BD: {e}")
            print(f"❌ DEBUG: Tipo de error: {type(e)}")
            import traceback
            print(f"❌ DEBUG: Traceback: {traceback.format_exc()}")
            db.rollback()
            return None
        finally:
            db.close()
