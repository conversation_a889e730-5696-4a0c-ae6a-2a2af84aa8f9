"""
Migración para agregar la columna 'referencia' a la tabla 'persianas'.
"""
from alembic import op
import sqlalchemy as sa

# Identificador de revisión
revision = 'add_referencia_to_persianas'
down_revision = None  # O el identificador de la revisión anterior si existe
branch_labels = None
depends_on = None

def upgrade():
    """Ejecuta la migración hacia adelante."""
    # Verificar si la columna ya existe para evitar errores
    conn = op.get_bind()
    inspector = sa.inspect(conn.engine)
    columns = [col['name'] for col in inspector.get_columns('persianas')]
    
    # Agregar la columna 'referencia' si no existe
    if 'referencia' not in columns:
        op.add_column('persianas', 
                     sa.Column('referencia', sa.String(50), 
                              nullable=False, 
                              server_default='',  # Valor por defecto para registros existentes
                              comment='Referencia única de la persiana'))
        
        # Si hay registros existentes, necesitamos actualizarlos con valores únicos
        # Usamos el ID como referencia temporal
        op.execute("""
            UPDATE persianas 
            SET referencia = 'PERS-' || id 
            WHERE referencia = '' OR referencia IS NULL
        """)
        
        # Hacer la columna única después de actualizar los valores
        op.create_unique_constraint('uq_persianas_referencia', 'persianas', ['referencia'])

def downgrade():
    """Revierte la migración."""
    # Eliminar la restricción única primero
    op.drop_constraint('uq_persianas_referencia', 'persianas', type_='unique')
    # Luego eliminar la columna
    op.drop_column('persianas', 'referencia')
