"""
Generador de artículos profesional.
Genera artículos completos con todos los parámetros para añadir a la obra.
"""

from datetime import datetime
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QTextEdit,
    QSpinBox, QDoubleSpinBox, QComboBox, QPushButton, QGroupBox,
    QFormLayout, QDialogButtonBox, QMessageBox, QTabWidget, QWidget,
    QTableWidget, QTableWidgetItem, QHeaderView
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from models.base import get_db
from models.articulo import Articulo, ArticuloPerfil


class GeneradorArticulosProfesional(QDialog):
    """
    Generador profesional de artículos de carpintería.
    Crea artículos completos con todos los perfiles y parámetros configurados.
    """
    
    # Señales
    articulo_generado = pyqtSignal(object)  # Emite el artículo generado
    
    def __init__(self, elementos_lienzo, parent=None):
        super().__init__(parent)
        self.elementos_lienzo = elementos_lienzo
        self.articulo_generado_obj = None
        
        self.setWindowTitle("🏗️ Generador de Artículos Profesional")
        self.setModal(True)
        self.resize(900, 700)
        
        self._crear_interfaz()
        self._analizar_elementos()
        self._conectar_señales()
    
    def _crear_interfaz(self):
        """Crea la interfaz del generador."""
        layout = QVBoxLayout(self)
        
        # Título
        titulo = QLabel("🏗️ Generar Artículo Profesional para la Obra")
        titulo.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        titulo.setStyleSheet("color: #0078d4; margin: 10px;")
        layout.addWidget(titulo)
        
        # Tabs principales
        self.tabs = QTabWidget()
        layout.addWidget(self.tabs)
        
        # Tab 1: Información General
        self._crear_tab_general()
        
        # Tab 2: Lista de Perfiles
        self._crear_tab_perfiles()
        
        # Tab 3: Cálculos y Costes
        self._crear_tab_calculos()
        
        # Tab 4: Resumen Final
        self._crear_tab_resumen()
        
        # Botones
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        botones.accepted.connect(self._generar_articulo)
        botones.rejected.connect(self.reject)
        layout.addWidget(botones)
    
    def _crear_tab_general(self):
        """Crea el tab de información general."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Información básica del artículo
        grupo_basico = QGroupBox("📋 Información Básica del Artículo")
        form_basico = QFormLayout(grupo_basico)
        
        self.line_codigo = QLineEdit()
        self.line_codigo.setPlaceholderText("Ej: VEN-001")
        form_basico.addRow("Código del artículo:", self.line_codigo)
        
        self.line_descripcion = QLineEdit()
        self.line_descripcion.setPlaceholderText("Ej: Ventana PVC 120x140 cm")
        form_basico.addRow("Descripción:", self.line_descripcion)
        
        self.combo_tipo = QComboBox()
        self.combo_tipo.addItems([
            "Ventana", "Puerta", "Puerta-ventana", "Fijo", 
            "Corredera", "Plegable", "Otro"
        ])
        form_basico.addRow("Tipo de artículo:", self.combo_tipo)
        
        self.combo_categoria = QComboBox()
        self.combo_categoria.addItems([
            "Carpintería exterior", "Carpintería interior", 
            "Cerramiento", "División", "Especial"
        ])
        form_basico.addRow("Categoría:", self.combo_categoria)
        
        layout.addWidget(grupo_basico)
        
        # Dimensiones del artículo
        grupo_dimensiones = QGroupBox("📏 Dimensiones del Artículo")
        form_dimensiones = QFormLayout(grupo_dimensiones)
        
        self.spin_ancho_total = QSpinBox()
        self.spin_ancho_total.setRange(100, 5000)
        self.spin_ancho_total.setSuffix(" mm")
        form_dimensiones.addRow("Ancho total:", self.spin_ancho_total)
        
        self.spin_alto_total = QSpinBox()
        self.spin_alto_total.setRange(100, 5000)
        self.spin_alto_total.setSuffix(" mm")
        form_dimensiones.addRow("Alto total:", self.spin_alto_total)
        
        self.spin_profundidad = QSpinBox()
        self.spin_profundidad.setRange(50, 300)
        self.spin_profundidad.setSuffix(" mm")
        form_dimensiones.addRow("Profundidad:", self.spin_profundidad)
        
        layout.addWidget(grupo_dimensiones)
        
        # Información adicional
        grupo_adicional = QGroupBox("📝 Información Adicional")
        form_adicional = QFormLayout(grupo_adicional)
        
        self.text_observaciones = QTextEdit()
        self.text_observaciones.setMaximumHeight(80)
        self.text_observaciones.setPlaceholderText("Observaciones, instrucciones especiales, etc.")
        form_adicional.addRow("Observaciones:", self.text_observaciones)
        
        self.line_cliente = QLineEdit()
        self.line_cliente.setPlaceholderText("Nombre del cliente o proyecto")
        form_adicional.addRow("Cliente/Proyecto:", self.line_cliente)
        
        layout.addWidget(grupo_adicional)
        
        self.tabs.addTab(tab, "📋 General")
    
    def _crear_tab_perfiles(self):
        """Crea el tab de lista de perfiles."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Información
        info = QLabel("📋 Lista de Perfiles que Componen el Artículo")
        info.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        info.setStyleSheet("color: #0078d4; margin-bottom: 10px;")
        layout.addWidget(info)
        
        # Tabla de perfiles
        self.tabla_perfiles = QTableWidget()
        self.tabla_perfiles.setColumnCount(8)
        self.tabla_perfiles.setHorizontalHeaderLabels([
            "Elemento", "Tipo", "Material", "Dimensiones", 
            "Longitud", "Cantidad", "Precio €/m", "Total €"
        ])
        
        # Configurar tabla
        header = self.tabla_perfiles.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Stretch)
        
        self.tabla_perfiles.setAlternatingRowColors(True)
        layout.addWidget(self.tabla_perfiles)
        
        # Botones de gestión
        layout_botones = QHBoxLayout()
        
        btn_recalcular = QPushButton("🔄 Recalcular")
        btn_recalcular.clicked.connect(self._recalcular_perfiles)
        layout_botones.addWidget(btn_recalcular)
        
        btn_exportar = QPushButton("📤 Exportar Lista")
        btn_exportar.clicked.connect(self._exportar_lista)
        layout_botones.addWidget(btn_exportar)
        
        layout_botones.addStretch()
        layout.addLayout(layout_botones)
        
        self.tabs.addTab(tab, "📋 Perfiles")
    
    def _crear_tab_calculos(self):
        """Crea el tab de cálculos y costes."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Resumen de costes
        grupo_costes = QGroupBox("💰 Resumen de Costes")
        form_costes = QFormLayout(grupo_costes)
        
        self.label_coste_perfiles = QLabel("€0.00")
        self.label_coste_perfiles.setStyleSheet("font-weight: bold; color: #0078d4;")
        form_costes.addRow("Coste de perfiles:", self.label_coste_perfiles)
        
        self.spin_coste_herrajes = QDoubleSpinBox()
        self.spin_coste_herrajes.setRange(0, 10000)
        self.spin_coste_herrajes.setDecimals(2)
        self.spin_coste_herrajes.setSuffix(" €")
        form_costes.addRow("Coste de herrajes:", self.spin_coste_herrajes)
        
        self.spin_coste_cristal = QDoubleSpinBox()
        self.spin_coste_cristal.setRange(0, 10000)
        self.spin_coste_cristal.setDecimals(2)
        self.spin_coste_cristal.setSuffix(" €")
        form_costes.addRow("Coste de cristal:", self.spin_coste_cristal)
        
        self.spin_coste_mano_obra = QDoubleSpinBox()
        self.spin_coste_mano_obra.setRange(0, 10000)
        self.spin_coste_mano_obra.setDecimals(2)
        self.spin_coste_mano_obra.setSuffix(" €")
        form_costes.addRow("Mano de obra:", self.spin_coste_mano_obra)
        
        self.label_coste_total = QLabel("€0.00")
        self.label_coste_total.setStyleSheet("font-weight: bold; font-size: 14px; color: #d83b01;")
        form_costes.addRow("TOTAL:", self.label_coste_total)
        
        layout.addWidget(grupo_costes)
        
        # Márgenes y precios
        grupo_precios = QGroupBox("💼 Márgenes y Precio de Venta")
        form_precios = QFormLayout(grupo_precios)
        
        self.spin_margen = QDoubleSpinBox()
        self.spin_margen.setRange(0, 200)
        self.spin_margen.setDecimals(1)
        self.spin_margen.setSuffix(" %")
        self.spin_margen.setValue(30.0)
        form_precios.addRow("Margen de beneficio:", self.spin_margen)
        
        self.label_precio_venta = QLabel("€0.00")
        self.label_precio_venta.setStyleSheet("font-weight: bold; font-size: 16px; color: #107c10;")
        form_precios.addRow("Precio de venta:", self.label_precio_venta)
        
        layout.addWidget(grupo_precios)
        
        # Tiempos de fabricación
        grupo_tiempos = QGroupBox("⏱️ Tiempos de Fabricación")
        form_tiempos = QFormLayout(grupo_tiempos)
        
        self.spin_tiempo_corte = QDoubleSpinBox()
        self.spin_tiempo_corte.setRange(0, 100)
        self.spin_tiempo_corte.setDecimals(1)
        self.spin_tiempo_corte.setSuffix(" h")
        form_tiempos.addRow("Tiempo de corte:", self.spin_tiempo_corte)
        
        self.spin_tiempo_ensamble = QDoubleSpinBox()
        self.spin_tiempo_ensamble.setRange(0, 100)
        self.spin_tiempo_ensamble.setDecimals(1)
        self.spin_tiempo_ensamble.setSuffix(" h")
        form_tiempos.addRow("Tiempo de ensamble:", self.spin_tiempo_ensamble)
        
        self.spin_tiempo_total = QDoubleSpinBox()
        self.spin_tiempo_total.setRange(0, 200)
        self.spin_tiempo_total.setDecimals(1)
        self.spin_tiempo_total.setSuffix(" h")
        self.spin_tiempo_total.setReadOnly(True)
        form_tiempos.addRow("Tiempo total:", self.spin_tiempo_total)
        
        layout.addWidget(grupo_tiempos)
        
        self.tabs.addTab(tab, "💰 Cálculos")
    
    def _crear_tab_resumen(self):
        """Crea el tab de resumen final."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Resumen del artículo
        info = QLabel("📄 Resumen Final del Artículo")
        info.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        info.setStyleSheet("color: #0078d4; margin-bottom: 10px;")
        layout.addWidget(info)
        
        self.text_resumen = QTextEdit()
        self.text_resumen.setReadOnly(True)
        layout.addWidget(self.text_resumen)
        
        # Botón de actualizar resumen
        btn_actualizar = QPushButton("🔄 Actualizar Resumen")
        btn_actualizar.clicked.connect(self._actualizar_resumen)
        layout.addWidget(btn_actualizar)
        
        self.tabs.addTab(tab, "📄 Resumen")
    
    def _analizar_elementos(self):
        """Analiza los elementos del lienzo para generar datos iniciales."""
        if not self.elementos_lienzo:
            return
        
        # Calcular dimensiones totales basadas en los elementos
        ancho_total = 1200  # Valor por defecto
        alto_total = 1400   # Valor por defecto
        
        # Buscar elementos de marco para calcular dimensiones
        for elemento in self.elementos_lienzo:
            if elemento.tipo == 'marco_superior':
                ancho_total = elemento.propiedades.get('medida', 1200)
            elif elemento.tipo == 'marco_izquierdo':
                alto_total = elemento.propiedades.get('medida', 1400)
        
        # Establecer dimensiones
        self.spin_ancho_total.setValue(ancho_total)
        self.spin_alto_total.setValue(alto_total)
        
        # Generar código automático
        timestamp = datetime.now().strftime("%Y%m%d%H%M")
        codigo_auto = f"ART-{timestamp}"
        self.line_codigo.setText(codigo_auto)
        
        # Generar descripción automática
        tipo_material = "PVC"  # Por defecto
        if self.elementos_lienzo:
            tipo_material = self.elementos_lienzo[0].propiedades.get('material', 'PVC')
        
        descripcion_auto = f"Artículo {tipo_material} {ancho_total}x{alto_total} mm"
        self.line_descripcion.setText(descripcion_auto)
        
        # Llenar tabla de perfiles
        self._llenar_tabla_perfiles()
    
    def _llenar_tabla_perfiles(self):
        """Llena la tabla con los perfiles del lienzo."""
        self.tabla_perfiles.setRowCount(len(self.elementos_lienzo))
        
        for row, elemento in enumerate(self.elementos_lienzo):
            props = elemento.propiedades
            
            # Elemento
            self.tabla_perfiles.setItem(row, 0, QTableWidgetItem(props.get('categoria', 'N/A')))
            
            # Tipo
            self.tabla_perfiles.setItem(row, 1, QTableWidgetItem(props.get('tipo_perfil', 'N/A')))
            
            # Material
            self.tabla_perfiles.setItem(row, 2, QTableWidgetItem(props.get('material', 'N/A')))
            
            # Dimensiones
            ancho = props.get('ancho', 70)
            alto = props.get('alto', 70)
            dimensiones = f"{ancho}x{alto} mm"
            self.tabla_perfiles.setItem(row, 3, QTableWidgetItem(dimensiones))
            
            # Longitud
            longitud = props.get('medida', 1000)
            self.tabla_perfiles.setItem(row, 4, QTableWidgetItem(f"{longitud} mm"))
            
            # Cantidad
            self.tabla_perfiles.setItem(row, 5, QTableWidgetItem("1"))
            
            # Precio
            precio = props.get('precio_metro', 15.0)
            self.tabla_perfiles.setItem(row, 6, QTableWidgetItem(f"{precio:.2f}"))
            
            # Total
            total = (longitud / 1000) * precio
            self.tabla_perfiles.setItem(row, 7, QTableWidgetItem(f"{total:.2f}"))
        
        self._recalcular_costes()
    
    def _conectar_señales(self):
        """Conecta las señales de los controles."""
        # Conectar cambios para recalcular automáticamente
        self.spin_coste_herrajes.valueChanged.connect(self._recalcular_costes)
        self.spin_coste_cristal.valueChanged.connect(self._recalcular_costes)
        self.spin_coste_mano_obra.valueChanged.connect(self._recalcular_costes)
        self.spin_margen.valueChanged.connect(self._recalcular_costes)
        
        # Tiempos
        self.spin_tiempo_corte.valueChanged.connect(self._recalcular_tiempos)
        self.spin_tiempo_ensamble.valueChanged.connect(self._recalcular_tiempos)
        
        # Actualizar resumen cuando cambien datos básicos
        self.line_codigo.textChanged.connect(self._actualizar_resumen)
        self.line_descripcion.textChanged.connect(self._actualizar_resumen)
    
    def _recalcular_perfiles(self):
        """Recalcula los datos de los perfiles."""
        self._llenar_tabla_perfiles()
    
    def _recalcular_costes(self):
        """Recalcula todos los costes."""
        # Calcular coste de perfiles
        coste_perfiles = 0
        for row in range(self.tabla_perfiles.rowCount()):
            item_total = self.tabla_perfiles.item(row, 7)
            if item_total:
                coste_perfiles += float(item_total.text())
        
        self.label_coste_perfiles.setText(f"€{coste_perfiles:.2f}")
        
        # Calcular coste total
        coste_total = (coste_perfiles + 
                      self.spin_coste_herrajes.value() + 
                      self.spin_coste_cristal.value() + 
                      self.spin_coste_mano_obra.value())
        
        self.label_coste_total.setText(f"€{coste_total:.2f}")
        
        # Calcular precio de venta
        margen = self.spin_margen.value() / 100
        precio_venta = coste_total * (1 + margen)
        self.label_precio_venta.setText(f"€{precio_venta:.2f}")
    
    def _recalcular_tiempos(self):
        """Recalcula los tiempos totales."""
        tiempo_total = self.spin_tiempo_corte.value() + self.spin_tiempo_ensamble.value()
        self.spin_tiempo_total.setValue(tiempo_total)
    
    def _actualizar_resumen(self):
        """Actualiza el resumen final."""
        resumen = f"""
<h2>📄 RESUMEN DEL ARTÍCULO</h2>

<h3>📋 Información General</h3>
<b>Código:</b> {self.line_codigo.text()}<br>
<b>Descripción:</b> {self.line_descripcion.text()}<br>
<b>Tipo:</b> {self.combo_tipo.currentText()}<br>
<b>Categoría:</b> {self.combo_categoria.currentText()}<br>

<h3>📏 Dimensiones</h3>
<b>Ancho:</b> {self.spin_ancho_total.value()} mm<br>
<b>Alto:</b> {self.spin_alto_total.value()} mm<br>
<b>Profundidad:</b> {self.spin_profundidad.value()} mm<br>

<h3>📋 Composición</h3>
<b>Número de perfiles:</b> {len(self.elementos_lienzo)}<br>
<b>Material principal:</b> {self.elementos_lienzo[0].propiedades.get('material', 'N/A') if self.elementos_lienzo else 'N/A'}<br>

<h3>💰 Costes</h3>
<b>Coste total:</b> {self.label_coste_total.text()}<br>
<b>Precio de venta:</b> {self.label_precio_venta.text()}<br>
<b>Margen:</b> {self.spin_margen.value()}%<br>

<h3>⏱️ Tiempos</h3>
<b>Tiempo total:</b> {self.spin_tiempo_total.value()} horas<br>

<h3>📝 Observaciones</h3>
{self.text_observaciones.toPlainText() or 'Sin observaciones'}
        """
        
        self.text_resumen.setHtml(resumen)
    
    def _exportar_lista(self):
        """Exporta la lista de perfiles."""
        QMessageBox.information(self, "Exportar", "Funcionalidad de exportación pendiente de implementar.")
    
    def _generar_articulo(self):
        """Genera el artículo final."""
        try:
            # Validar datos básicos
            if not self.line_codigo.text().strip():
                QMessageBox.warning(self, "Error", "El código del artículo es obligatorio.")
                return
            
            if not self.line_descripcion.text().strip():
                QMessageBox.warning(self, "Error", "La descripción del artículo es obligatoria.")
                return
            
            # Crear objeto artículo (simulado por ahora)
            articulo_data = {
                'codigo': self.line_codigo.text().strip(),
                'descripcion': self.line_descripcion.text().strip(),
                'tipo': self.combo_tipo.currentText(),
                'categoria': self.combo_categoria.currentText(),
                'ancho': self.spin_ancho_total.value(),
                'alto': self.spin_alto_total.value(),
                'profundidad': self.spin_profundidad.value(),
                'coste_total': float(self.label_coste_total.text().replace('€', '')),
                'precio_venta': float(self.label_precio_venta.text().replace('€', '')),
                'tiempo_fabricacion': self.spin_tiempo_total.value(),
                'observaciones': self.text_observaciones.toPlainText(),
                'perfiles': self.elementos_lienzo,
                'fecha_creacion': datetime.now()
            }
            
            self.articulo_generado_obj = articulo_data
            
            # Emitir señal
            self.articulo_generado.emit(articulo_data)
            
            QMessageBox.information(
                self, 
                "Éxito", 
                f"Artículo '{articulo_data['codigo']}' generado correctamente.\n"
                f"Precio de venta: €{articulo_data['precio_venta']:.2f}"
            )
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al generar el artículo: {str(e)}")
    
    def obtener_articulo_generado(self):
        """Obtiene el artículo generado."""
        return self.articulo_generado_obj
