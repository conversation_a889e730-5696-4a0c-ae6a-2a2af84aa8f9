import os
import sqlite3
import json
from dbfread import DBF
from datetime import datetime

def mapear_tipo_dbf_a_sqlite(tipo_dbf, longitud=0, decimales=0):
    """Mapea tipos de datos DBF a tipos SQLite"""
    tipo_dbf = tipo_dbf.upper()
    if tipo_dbf in ('C', 'M'):  # Carácter y Memo
        return 'TEXT'
    elif tipo_dbf in ('N', 'F'):  # Numérico y Flotante
        if decimales > 0:
            return 'REAL'
        else:
            return 'INTEGER'
    elif tipo_dbf == 'D':  # Fecha
        return 'TEXT'  # SQLite no tiene tipo fecha nativo
    elif tipo_dbf == 'L':  # Lógico
        return 'INTEGER'  # 0 o 1
    else:
        return 'TEXT'  # Por defecto, texto

def crear_tabla_sqlite(conexion, nombre_tabla, campos):
    """Crea una tabla en SQLite con la estructura de la tabla DBF"""
    cursor = conexion.cursor()
    
    # Crear sentencia SQL para crear la tabla
    columnas = []
    for campo in campos:
        nombre = campo['nombre']
        tipo = mapear_tipo_dbf_a_sqlite(
            campo['tipo'], 
            campo.get('longitud', 0), 
            campo.get('decimales', 0)
        )
        # Escapar nombres de columnas que son palabras reservadas en SQL
        if nombre.upper() in ('GROUP', 'ORDER', 'TABLE', 'INDEX', 'VIEW'):
            nombre = f'"{nombre}"'
        columnas.append(f"{nombre} {tipo}")
    
    # Añadir columna para metadatos adicionales
    columnas.append("dbf_original_filename TEXT")
    columnas.append("dbf_import_date TEXT")
    
    # Crear la tabla
    sql = f"CREATE TABLE IF NOT EXISTS {nombre_tabla} (\n  "
    sql += ",\n  ".join(columnas)
    sql += "\n)"
    
    cursor.execute(sql)
    conexion.commit()

def exportar_tabla_dbf_a_sqlite(dbf_path, sqlite_conn, tabla_destino=None):
    """Exporta una tabla DBF a una tabla SQLite"""
    try:
        # Obtener el nombre de la tabla del nombre del archivo
        if not tabla_destino:
            tabla_destino = os.path.splitext(os.path.basename(dbf_path))[0].lower()
        
        print(f"Exportando {dbf_path} a tabla {tabla_destino}...")
        
        # Leer la estructura del archivo DBF
        dbf = DBF(dbf_path, encoding='latin1')
        
        # Obtener información de los campos
        campos = []
        for field in dbf.fields:
            campos.append({
                'nombre': field.name,
                'tipo': field.type,
                'longitud': field.length,
                'decimales': field.decimal_count or 0
            })
        
        # Crear la tabla en SQLite
        crear_tabla_sqlite(sqlite_conn, tabla_destino, campos)
        
        # Insertar los datos
        cursor = sqlite_conn.cursor()
        
        # Preparar la sentencia INSERT
        nombres_campos = [campo['nombre'] for campo in campos]
        placeholders = ['?' for _ in nombres_campos]
        
        # Añadir campos adicionales para metadatos
        nombres_campos.extend(['dbf_original_filename', 'dbf_import_date'])
        placeholders.extend(['?', '?'])
        
        sql = f"INSERT INTO {tabla_destino} ({', '.join(nombres_campos)}) VALUES ({', '.join(placeholders)})"
        
        # Insertar los registros
        for record in dbf:
            valores = [record.get(campo['nombre']) for campo in campos]
            # Añadir metadatos
            valores.extend([os.path.basename(dbf_path), datetime.now().isoformat()])
            cursor.execute(sql, valores)
        
        sqlite_conn.commit()
        print(f"  - Exportados {len(dbf)} registros a la tabla {tabla_destino}")
        return True
        
    except Exception as e:
        print(f"Error al exportar {dbf_path}: {str(e)}")
        return False

def exportar_todas_las_tablas(directorio, archivo_sqlite):
    """Exporta todas las tablas DBF de un directorio a una base de datos SQLite"""
    # Conectar a la base de datos SQLite
    conexion = sqlite3.connect(archivo_sqlite)
    conexion.row_factory = sqlite3.Row
    
    # Crear tabla de metadatos
    cursor = conexion.cursor()
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS _metadatos_importacion (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tabla_origen TEXT,
        tabla_destino TEXT,
        fecha_importacion TEXT,
        registros_importados INTEGER,
        exito BOOLEAN,
        mensaje_error TEXT
    )
    ''')
    conexion.commit()
    
    # Buscar archivos DBF en el directorio
    archivos_dbf = [f for f in os.listdir(directorio) if f.lower().endswith('.dbf')]
    
    print(f"\nIniciando exportación de {len(archivos_dbf)} tablas a {archivo_sqlite}")
    print("=" * 50)
    
    for archivo in archivos_dbf:
        ruta_completa = os.path.join(directorio, archivo)
        tabla_destino = os.path.splitext(archivo)[0].lower()
        
        try:
            # Registrar inicio de importación
            cursor.execute(
                'INSERT INTO _metadatos_importacion (tabla_origen, fecha_importacion) VALUES (?, ?)',
                (archivo, datetime.now().isoformat())
            )
            id_import = cursor.lastrowid
            
            # Exportar la tabla
            exito = exportar_tabla_dbf_a_sqlite(ruta_completa, conexion, tabla_destino)
            
            # Actualizar metadatos
            cursor.execute('''
            UPDATE _metadatos_importacion 
            SET 
                tabla_destino = ?,
                exito = ?,
                mensaje_error = ?
            WHERE id = ?
            ''', (
                tabla_destino,
                exito,
                'Éxito' if exito else 'Error en la exportación',
                id_import
            ))
            
            conexion.commit()
            
        except Exception as e:
            print(f"Error inesperado al procesar {archivo}: {str(e)}")
            conexion.rollback()
    
    # Cerrar conexión
    conexion.close()
    print("\nExportación completada.")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 3:
        print("Uso: python exportar_a_sqlite.py <directorio_dbf> <archivo_sqlite>")
        sys.exit(1)
    
    directorio = sys.argv[1]
    archivo_sqlite = sys.argv[2]
    
    if not os.path.isdir(directorio):
        print(f"Error: El directorio {directorio} no existe.")
        sys.exit(1)
    
    exportar_todas_las_tablas(directorio, archivo_sqlite)
