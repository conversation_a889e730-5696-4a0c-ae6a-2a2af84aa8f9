from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from .base import Base

class Informe(Base):
    """
    Modelo que representa un informe en el sistema.
    Corresponde a la tabla 'informes' en la base de datos.
    """
    __tablename__ = 'informes'
    
    # Columnas
    id = Column(Integer, primary_key=True, autoincrement=True)
    codigo = Column(String(50), unique=True, nullable=False, comment='Código único del informe')
    descripcion = Column(String(255), nullable=True, comment='Descripción del informe')
    fecha_creacion = Column(DateTime(timezone=True), server_default=func.now())
    fecha_actualizacion = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relaciones
    lineas = relationship("LineaInforme", back_populates="informe", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Informe(id={self.id}, codigo='{self.codigo}')>"
    
    @classmethod
    def buscar_por_codigo(cls, db, codigo):
        """Busca un informe por su código"""
        return db.query(cls).filter(cls.codigo == codigo).first()

class LineaInforme(Base):
    """
    Modelo que representa una línea de un informe.
    Corresponde a la tabla 'l_inf' en la base de datos.
    """
    __tablename__ = 'l_inf'
    
    # Columnas
    id = Column(Integer, primary_key=True, autoincrement=True)
    tipo = Column(String(10), nullable=False, comment='Tipo de línea')
    linea = Column(Integer, nullable=False, comment='Número de línea')
    texto = Column(Text, nullable=True, comment='Contenido de la línea')
    formulario = Column(String(50), nullable=True, comment='Formulario relacionado')
    id_informe = Column(Integer, ForeignKey('informes.id'), nullable=False, comment='ID del informe relacionado')
    fecha_creacion = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relaciones
    informe = relationship("Informe", back_populates="lineas")
    
    def __repr__(self):
        return f"<LineaInforme(id={self.id}, tipo='{self.tipo}', linea={self.linea})>"
    
    @classmethod
    def obtener_por_informe(cls, db, id_informe):
        """Obtiene todas las líneas de un informe ordenadas por número de línea"""
        return (db.query(cls)
                .filter(cls.id_informe == id_informe)
                .order_by(cls.linea)
                .all())
