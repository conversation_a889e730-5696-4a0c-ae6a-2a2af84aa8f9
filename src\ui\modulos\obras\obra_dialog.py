"""
<PERSON><PERSON><PERSON><PERSON> que implementa el diálogo de gestión de obras.
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QTableWidget,
    QTableWidgetItem, QHeaderView, QMessageBox, QLineEdit, QLabel,
    QComboBox, QFormLayout, QDialogButtonBox, QDateEdit, QTextEdit,
    QScrollArea, QWidget, QTabWidget, QGroupBox, QDoubleSpinBox, QSpinBox
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QIcon, QPixmap, QFont, QColor
from PyQt6.QtWidgets import QStyle

from models.base import get_db
from models.obra import Obra
from models.cliente import Cliente
from models.perfil import Perfil
from models.cristal import Cristal
from models.articulo import Articulo, ObraArticulo
from ui.utils.window_utils import smart_dialog_setup, force_dialog_maximized

class ObraDialog(QDialog):
    """Diálogo para gestionar obras."""

    def __init__(self, parent=None):
        """Inicializa el diálogo de gestión de obras."""
        super().__init__(parent)
        self.setWindowTitle("Gestión de Obras")

        # Variables
        self.obras = []

        # ✅ FORZAR MAXIMIZACIÓN COMPLETA
        force_dialog_maximized(self, "Gestión de Obras")

        # Inicializar la interfaz
        self._inicializar_ui()

        # Cargar datos
        self._cargar_obras()
    
    def _inicializar_ui(self):
        """Configura la interfaz de usuario del diálogo."""
        # Layout principal
        layout_principal = QVBoxLayout(self)
        
        # Barra de herramientas
        self._crear_barra_herramientas(layout_principal)
        
        # Filtros
        self._crear_filtros(layout_principal)
        
        # Tabla de obras
        self._crear_tabla_obras(layout_principal)
        
        # Botones de acción
        self._crear_botones_accion(layout_principal)
        
        # Establecer el layout
        self.setLayout(layout_principal)
    
    def _crear_barra_herramientas(self, layout_principal):
        """Crea la barra de herramientas."""
        barra_herramientas = QHBoxLayout()
        
        # Botón de nueva obra
        self.boton_nuevo = QPushButton("Nueva Obra")
        self.boton_nuevo.setIcon(self.style().standardIcon(
            getattr(QStyle.StandardPixmap, 'SP_FileIcon', None) or 
            QStyle.StandardPixmap.SP_FileIcon
        ))
        self.boton_nuevo.clicked.connect(self.nueva_obra)
        barra_herramientas.addWidget(self.boton_nuevo)
        
        # Botón de refrescar
        boton_refrescar = QPushButton("Refrescar")
        boton_refrescar.setIcon(self.style().standardIcon(
            getattr(QStyle.StandardPixmap, 'SP_BrowserReload', None) or 
            QStyle.StandardPixmap.SP_BrowserReload
        ))
        boton_refrescar.clicked.connect(self._cargar_obras)
        barra_herramientas.addWidget(boton_refrescar)
        
        # Espaciador
        barra_herramientas.addStretch()
        
        # Añadir la barra de herramientas al layout principal
        layout_principal.addLayout(barra_herramientas)
    
    def _crear_filtros(self, layout_principal):
        """Crea los controles de filtrado."""
        grupo_filtros = QGroupBox("Filtros")
        layout_filtros = QHBoxLayout()
        
        # Campo de búsqueda
        self.campo_busqueda = QLineEdit()
        self.campo_busqueda.setPlaceholderText("Buscar por código, nombre o dirección...")
        self.campo_busqueda.textChanged.connect(self._aplicar_filtros)
        layout_filtros.addWidget(self.campo_busqueda)
        
        # Filtro por estado
        self.combo_estado = QComboBox()
        self.combo_estado.addItem("Todos los estados", None)
        self.combo_estado.addItem("Pendiente", "Pendiente")
        self.combo_estado.addItem("En curso", "En curso")
        self.combo_estado.addItem("Finalizada", "Finalizada")
        self.combo_estado.addItem("Facturada", "Facturada")
        self.combo_estado.currentIndexChanged.connect(self._aplicar_filtros)
        layout_filtros.addWidget(QLabel("Estado:"))
        layout_filtros.addWidget(self.combo_estado)
        
        # Botón de limpiar filtros
        boton_limpiar = QPushButton("Limpiar")
        boton_limpiar.clicked.connect(self._limpiar_filtros)
        layout_filtros.addWidget(boton_limpiar)
        
        grupo_filtros.setLayout(layout_filtros)
        layout_principal.addWidget(grupo_filtros)
    
    def _crear_tabla_obras(self, layout_principal):
        """Crea la tabla de obras."""
        self.tabla_obras = QTableWidget()
        self.tabla_obras.setColumnCount(8)
        self.tabla_obras.setHorizontalHeaderLabels([
            "Código", "Nombre", "Cliente", "Dirección", "Fecha Inicio", 
            "Fecha Fin", "Estado", "Presupuesto"
        ])
        
        # Configurar la tabla
        self.tabla_obras.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.tabla_obras.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.tabla_obras.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.tabla_obras.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        self.tabla_obras.horizontalHeader().setStretchLastSection(True)
        self.tabla_obras.verticalHeader().setVisible(False)
        
        # Conectar señales
        self.tabla_obras.itemSelectionChanged.connect(self._actualizar_botones_accion)
        self.tabla_obras.itemDoubleClicked.connect(self.editar_obra)
        
        # Ajustar el ancho de las columnas
        self.tabla_obras.setColumnWidth(0, 100)  # Código
        self.tabla_obras.setColumnWidth(1, 200)  # Nombre
        self.tabla_obras.setColumnWidth(2, 150)  # Cliente
        self.tabla_obras.setColumnWidth(3, 200)  # Dirección
        self.tabla_obras.setColumnWidth(4, 100)  # Fecha Inicio
        self.tabla_obras.setColumnWidth(5, 100)  # Fecha Fin
        self.tabla_obras.setColumnWidth(6, 100)  # Estado
        self.tabla_obras.setColumnWidth(7, 100)  # Presupuesto
        
        # Añadir la tabla al layout
        layout_principal.addWidget(self.tabla_obras)
    
    def _crear_botones_accion(self, layout_principal):
        """Crea los botones de acción."""
        layout_botones = QHBoxLayout()
        
        # Botón de editar
        self.boton_editar = QPushButton("Editar")
        self.boton_editar.setIcon(self.style().standardIcon(
            getattr(QStyle.StandardPixmap, 'SP_FileDialogDetailedView', None) or 
            QStyle.StandardPixmap.SP_FileDialogDetailedView
        ))
        self.boton_editar.clicked.connect(self.editar_obra)
        self.boton_editar.setEnabled(False)
        layout_botones.addWidget(self.boton_editar)
        
        # Botón de eliminar
        self.boton_eliminar = QPushButton("Eliminar")
        self.boton_eliminar.setIcon(self.style().standardIcon(
            getattr(QStyle.StandardPixmap, 'SP_TrashIcon', None) or 
            QStyle.StandardPixmap.SP_TrashIcon
        ))
        self.boton_eliminar.clicked.connect(self.eliminar_obra)
        self.boton_eliminar.setEnabled(False)
        layout_botones.addWidget(self.boton_eliminar)
        
        # Espaciador
        layout_botones.addStretch()
        
        # Botón de cerrar
        boton_cerrar = QPushButton("Cerrar")
        boton_cerrar.clicked.connect(self.close)
        layout_botones.addWidget(boton_cerrar)
        
        # Añadir los botones al layout principal
        layout_principal.addLayout(layout_botones)
    
    def _cargar_obras(self):
        """Carga las obras desde la base de datos."""
        db = next(get_db())

        try:
            # Limpiar la tabla
            self.tabla_obras.setRowCount(0)

            # Obtener todas las obras con la información del cliente
            query = db.query(Obra).join(Cliente).order_by(Obra.fecha_inicio.desc())
            
            # Aplicar filtros si existen
            texto_busqueda = self.campo_busqueda.text().strip()
            if texto_busqueda:
                query = query.filter(
                    (Obra.codigo.ilike(f'%{texto_busqueda}%')) |
                    (Obra.nombre.ilike(f'%{texto_busqueda}%')) |
                    (Obra.direccion.ilike(f'%{texto_busqueda}%')) |
                    (Cliente.nombre.ilike(f'%{texto_busqueda}%'))
                )
            
            estado_filtro = self.combo_estado.currentData()
            if estado_filtro:
                query = query.filter(Obra.estado == estado_filtro)
            
            # Obtener las obras
            self.obras = query.all()
            
            # Llenar la tabla
            for obra in self.obras:
                fila = self.tabla_obras.rowCount()
                self.tabla_obras.insertRow(fila)
                
                # Obtener el nombre del cliente
                nombre_cliente = obra.cliente.nombre if obra.cliente else ""
                
                # Formatear fechas
                fecha_inicio = obra.fecha_inicio.strftime("%d/%m/%Y") if obra.fecha_inicio else ""
                fecha_fin = obra.fecha_fin.strftime("%d/%m/%Y") if obra.fecha_fin else ""
                
                # Formatear presupuesto
                presupuesto = f"{obra.presupuesto:,.2f} €" if obra.presupuesto is not None else "0,00 €"
                
                # Añadir celdas
                self.tabla_obras.setItem(fila, 0, QTableWidgetItem(obra.codigo or ""))
                self.tabla_obras.setItem(fila, 1, QTableWidgetItem(obra.nombre or ""))
                self.tabla_obras.setItem(fila, 2, QTableWidgetItem(nombre_cliente))
                self.tabla_obras.setItem(fila, 3, QTableWidgetItem(obra.direccion or ""))
                self.tabla_obras.setItem(fila, 4, QTableWidgetItem(fecha_inicio))
                self.tabla_obras.setItem(fila, 5, QTableWidgetItem(fecha_fin))
                self.tabla_obras.setItem(fila, 6, QTableWidgetItem(obra.estado or ""))
                self.tabla_obras.setItem(fila, 7, QTableWidgetItem(presupuesto))
                
                # Almacenar el ID de la obra en la fila
                self.tabla_obras.item(fila, 0).setData(Qt.ItemDataRole.UserRole, obra.id)
            
            # Actualizar el contador en el título
            self.setWindowTitle(f"Gestión de Obras ({len(self.obras)} obras)")

        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Error al cargar las obras: {str(e)}"
            )
        finally:
            db.close()
    
    def _aplicar_filtros(self):
        """Aplica los filtros de búsqueda."""
        self._cargar_obras()
    
    def _limpiar_filtros(self):
        """Limpia todos los filtros de búsqueda."""
        self.campo_busqueda.clear()
        self.combo_estado.setCurrentIndex(0)
        self._cargar_obras()
    
    def _actualizar_botones_accion(self):
        """Actualiza el estado de los botones según la selección."""
        seleccion = self.tabla_obras.selectedItems()
        habilitar = len(seleccion) > 0
        
        self.boton_editar.setEnabled(habilitar)
        self.boton_eliminar.setEnabled(habilitar)

        # Habilitar botones de informes si hay una obra seleccionada
        if hasattr(self, 'boton_informe_taller'):
            self.boton_informe_taller.setEnabled(habilitar)
    
    def nueva_obra(self):
        """Abre el diálogo para crear una nueva obra."""
        dialog = ObraEditarDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self._cargar_obras()
    
    def editar_obra(self):
        """Abre el diálogo para editar la obra seleccionada."""
        fila = self.tabla_obras.currentRow()
        if fila < 0:
            return
        
        # Obtener el ID de la obra seleccionada
        obra_id = self.tabla_obras.item(fila, 0).data(Qt.ItemDataRole.UserRole)
        
        # Buscar la obra en la lista
        obra = next((o for o in self.obras if o.id == obra_id), None)
        if not obra:
            return
        
        # Abrir el diálogo de edición
        dialog = ObraEditarDialog(self, obra)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self._cargar_obras()
    
    def eliminar_obra(self):
        """Elimina la obra seleccionada."""
        fila = self.tabla_obras.currentRow()
        if fila < 0:
            return
        
        # Obtener el ID de la obra seleccionada
        obra_id = self.tabla_obras.item(fila, 0).data(Qt.ItemDataRole.UserRole)
        
        # Buscar la obra en la lista
        obra = next((o for o in self.obras if o.id == obra_id), None)
        if not obra:
            return
        
        # Confirmar eliminación
        respuesta = QMessageBox.question(
            self,
            "Confirmar eliminación",
            f"¿Está seguro de que desea eliminar la obra '{obra.nombre}'?\n"
            "Esta acción no se puede deshacer.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if respuesta == QMessageBox.StandardButton.Yes:
            db = next(get_db())
            try:
                obra_db = db.query(Obra).filter(Obra.id == obra.id).first()
                if obra_db:
                    db.delete(obra_db)
                    db.commit()
                    self._cargar_obras()
                    QMessageBox.information(
                        self,
                        "Éxito",
                        "La obra ha sido eliminada correctamente."
                    )
            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self,
                    "Error",
                    f"No se pudo eliminar la obra: {str(e)}"
                )
            finally:
                db.close()
    



class ObraEditarDialog(QDialog):
    """Diálogo para crear o editar una obra."""

    def __init__(self, parent=None, obra=None):
        """Inicializa el diálogo de edición de obra.

        Args:
            parent: Widget padre
            obra: Instancia de Obra a editar (None para nueva obra)
        """
        super().__init__(parent)
        self.obra = obra
        self.setWindowTitle("Nueva Obra" if obra is None else f"Editar Obra: {obra.nombre}")

        # Variables
        self.cliente = None

        # Configurar tamaño doblado para el diálogo
        # Restaurar controles de ventana y tamaño razonable
        self.setWindowFlags(Qt.WindowType.Window)
        self.setMinimumSize(700, 500)
        self.resize(900, 700)

        # Inicializar la interfaz
        self._inicializar_ui()

        # Cargar datos si es una edición
        if obra is not None:
            self._cargar_datos()
    
    def _inicializar_ui(self):
        """Configura la interfaz de usuario del diálogo."""
        # Layout principal
        layout_principal = QVBoxLayout(self)
        
        # Pestañas
        pestanias = QTabWidget()
        
        # Pestaña de datos generales
        pestania_datos = QWidget()
        layout_datos = QFormLayout(pestania_datos)
        
        # Código
        self.campo_codigo = QLineEdit()
        layout_datos.addRow("Código:", self.campo_codigo)
        
        # Nombre
        self.campo_nombre = QLineEdit()
        self.campo_nombre.setPlaceholderText("Nombre de la obra")
        layout_datos.addRow("Nombre*:", self.campo_nombre)
        
        # Cliente
        self.combo_cliente = QComboBox()
        self._cargar_clientes()
        layout_datos.addRow("Cliente*:", self.combo_cliente)
        
        # Dirección
        self.campo_direccion = QTextEdit()
        self.campo_direccion.setMaximumHeight(80)
        self.campo_direccion.setPlaceholderText("Dirección de la obra")
        layout_datos.addRow("Dirección:", self.campo_direccion)
        
        # Fechas
        layout_fechas = QHBoxLayout()
        
        # Fecha de inicio
        self.fecha_inicio = QDateEdit()
        self.fecha_inicio.setCalendarPopup(True)
        self.fecha_inicio.setDate(QDate.currentDate())
        layout_fechas.addWidget(QLabel("Inicio:"))
        layout_fechas.addWidget(self.fecha_inicio)
        
        # Fecha de fin
        self.fecha_fin = QDateEdit()
        self.fecha_fin.setCalendarPopup(True)
        self.fecha_fin.setDate(QDate.currentDate())
        layout_fechas.addWidget(QLabel("Fin:"))
        layout_fechas.addWidget(self.fecha_fin)
        
        layout_datos.addRow("Fechas:", layout_fechas)
        
        # Estado
        self.combo_estado = QComboBox()
        self.combo_estado.addItem("Pendiente", "Pendiente")
        self.combo_estado.addItem("En curso", "En curso")
        self.combo_estado.addItem("Finalizada", "Finalizada")
        self.combo_estado.addItem("Facturada", "Facturada")
        layout_datos.addRow("Estado*:", self.combo_estado)
        
        # Presupuesto
        self.campo_presupuesto = QDoubleSpinBox()
        self.campo_presupuesto.setMaximum(999999.99)
        self.campo_presupuesto.setPrefix("€ ")
        self.campo_presupuesto.setDecimals(2)
        self.campo_presupuesto.setValue(0.00)
        layout_datos.addRow("Presupuesto:", self.campo_presupuesto)
        
        # Coste actual
        self.campo_coste_actual = QDoubleSpinBox()
        self.campo_coste_actual.setMaximum(999999.99)
        self.campo_coste_actual.setPrefix("€ ")
        self.campo_coste_actual.setDecimals(2)
        self.campo_coste_actual.setValue(0.00)
        self.campo_coste_actual.setReadOnly(True)
        layout_datos.addRow("Coste actual:", self.campo_coste_actual)
        
        # Descripción
        self.campo_descripcion = QTextEdit()
        self.campo_descripcion.setPlaceholderText("Descripción detallada de la obra")
        layout_datos.addRow("Descripción:", self.campo_descripcion)
        
        # Añadir pestaña de datos generales
        pestanias.addTab(pestania_datos, "Datos Generales")
        
        # Pestaña de artículos
        pestania_articulos = QWidget()
        self._crear_pestania_articulos(pestania_articulos)
        pestanias.addTab(pestania_articulos, "Artículos")
        
        # Pestaña de tareas (pendiente de implementar)
        pestania_tareas = QWidget()
        layout_tareas = QVBoxLayout(pestania_tareas)
        layout_tareas.addWidget(QLabel("Gestión de tareas (pendiente de implementar)"))
        pestanias.addTab(pestania_tareas, "Tareas")
        
        # Añadir pestañas al layout principal
        layout_principal.addWidget(pestanias)
        
        # Botones de acción
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel
        )
        botones.accepted.connect(self.aceptar)
        botones.rejected.connect(self.reject)
        layout_principal.addWidget(botones)
        
        # Establecer el layout
        self.setLayout(layout_principal)

    def _crear_pestania_articulos(self, pestania_articulos):
        """Crea la pestaña de gestión de artículos."""
        layout_articulos = QVBoxLayout(pestania_articulos)

        # Barra de herramientas de artículos
        barra_articulos = QHBoxLayout()

        self.boton_agregar_articulo = QPushButton("Agregar Artículo")
        self.boton_agregar_articulo.clicked.connect(self._agregar_articulo)
        barra_articulos.addWidget(self.boton_agregar_articulo)

        self.boton_editar_articulo = QPushButton("Editar")
        self.boton_editar_articulo.clicked.connect(self._editar_articulo)
        self.boton_editar_articulo.setEnabled(False)
        barra_articulos.addWidget(self.boton_editar_articulo)

        self.boton_eliminar_articulo = QPushButton("Eliminar")
        self.boton_eliminar_articulo.clicked.connect(self._eliminar_articulo)
        self.boton_eliminar_articulo.setEnabled(False)
        barra_articulos.addWidget(self.boton_eliminar_articulo)

        barra_articulos.addStretch()

        self.boton_calcular_coste = QPushButton("Recalcular Coste Total")
        self.boton_calcular_coste.clicked.connect(self._recalcular_coste_total)
        barra_articulos.addWidget(self.boton_calcular_coste)

        self.boton_informe_taller = QPushButton("📋 Informe de Taller Completo")
        self.boton_informe_taller.clicked.connect(self._generar_informe_taller_completo)
        self.boton_informe_taller.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        barra_articulos.addWidget(self.boton_informe_taller)

        layout_articulos.addLayout(barra_articulos)

        # Tabla de artículos
        self.tabla_articulos = QTableWidget()
        self.tabla_articulos.setColumnCount(8)
        self.tabla_articulos.setHorizontalHeaderLabels([
            "Artículo", "Descripción", "Medidas", "Cantidad",
            "Precio Unit.", "Precio Total", "Notas", "ID"
        ])

        # Configurar tabla
        self.tabla_articulos.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.tabla_articulos.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.tabla_articulos.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.tabla_articulos.itemSelectionChanged.connect(self._actualizar_botones_articulos)

        # Configurar columnas
        header = self.tabla_articulos.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Fixed)

        # Ocultar columna ID
        self.tabla_articulos.setColumnHidden(7, True)

        layout_articulos.addWidget(self.tabla_articulos)

        # Información de totales
        layout_totales = QHBoxLayout()
        layout_totales.addStretch()

        self.label_total_articulos = QLabel("Total artículos: 0")
        layout_totales.addWidget(self.label_total_articulos)

        self.label_coste_total = QLabel("Coste total: € 0,00")
        self.label_coste_total.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout_totales.addWidget(self.label_coste_total)

        layout_articulos.addLayout(layout_totales)
    
    def _cargar_clientes(self):
        """Carga la lista de clientes en el combo box."""
        db = next(get_db())

        try:
            clientes = db.query(Cliente).filter(Cliente.activo == True).order_by(Cliente.nombre).all()
            self.combo_cliente.clear()

            for cliente in clientes:
                self.combo_cliente.addItem(cliente.nombre, cliente.id)

            # Si hay una obra y tiene cliente, seleccionarlo
            if self.obra and self.obra.cliente_id:
                index = self.combo_cliente.findData(self.obra.cliente_id)
                if index >= 0:
                    self.combo_cliente.setCurrentIndex(index)
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Error al cargar los clientes: {str(e)}"
            )
        finally:
            db.close()
    
    def _cargar_datos(self):
        """Carga los datos de la obra en el formulario."""
        if not self.obra:
            return
        
        self.campo_codigo.setText(self.obra.codigo or "")
        self.campo_nombre.setText(self.obra.nombre or "")
        self.campo_direccion.setPlainText(self.obra.direccion or "")
        
        if self.obra.fecha_inicio:
            self.fecha_inicio.setDate(self.obra.fecha_inicio)
        if self.obra.fecha_fin:
            self.fecha_fin.setDate(self.obra.fecha_fin)
        
        # Seleccionar el estado actual
        index = self.combo_estado.findData(self.obra.estado)
        if index >= 0:
            self.combo_estado.setCurrentIndex(index)
        
        self.campo_presupuesto.setValue(float(self.obra.presupuesto or 0))
        self.campo_coste_actual.setValue(float(self.obra.coste_actual or 0))
        self.campo_descripcion.setPlainText(self.obra.descripcion or "")

        # Cargar artículos de la obra
        self._cargar_articulos_obra()
    
    def aceptar(self):
        """Valida y guarda los datos del formulario."""
        # Validar campos obligatorios
        if not self.campo_nombre.text().strip():
            QMessageBox.warning(self, "Error", "El nombre es obligatorio.")
            self.campo_nombre.setFocus()
            return
        
        if self.combo_cliente.currentIndex() < 0:
            QMessageBox.warning(self, "Error", "Debe seleccionar un cliente.")
            self.combo_cliente.setFocus()
            return
        
        # Crear o actualizar la obra
        db = next(get_db())

        try:
            if self.obra is None:
                # Crear nueva obra
                datos = {
                    'codigo': self.campo_codigo.text().strip() or None,
                    'nombre': self.campo_nombre.text().strip(),
                    'cliente_id': self.combo_cliente.currentData(),
                    'direccion': self.campo_direccion.toPlainText().strip() or None,
                    'fecha_inicio': self.fecha_inicio.date().toPyDate(),
                    'fecha_fin': self.fecha_fin.date().toPyDate(),
                    'estado': self.combo_estado.currentData(),
                    'presupuesto': self.campo_presupuesto.value(),
                    'coste_actual': self.campo_coste_actual.value(),
                    'descripcion': self.campo_descripcion.toPlainText().strip() or None
                }

                nueva_obra = Obra(**datos)
                db.add(nueva_obra)
                db.commit()

                QMessageBox.information(
                    self,
                    "Éxito",
                    f"Obra '{datos['nombre']}' creada correctamente.",
                    QMessageBox.StandardButton.Ok
                )
            else:
                # Actualizar obra existente
                obra_db = db.query(Obra).filter(Obra.id == self.obra.id).first()
                if obra_db:
                    obra_db.codigo = self.campo_codigo.text().strip() or None
                    obra_db.nombre = self.campo_nombre.text().strip()
                    obra_db.cliente_id = self.combo_cliente.currentData()
                    obra_db.direccion = self.campo_direccion.toPlainText().strip() or None
                    obra_db.fecha_inicio = self.fecha_inicio.date().toPyDate()
                    obra_db.fecha_fin = self.fecha_fin.date().toPyDate()
                    obra_db.estado = self.combo_estado.currentData()
                    obra_db.presupuesto = self.campo_presupuesto.value()
                    obra_db.coste_actual = self.campo_coste_actual.value()
                    obra_db.descripcion = self.campo_descripcion.toPlainText().strip() or None

                    db.commit()

                    QMessageBox.information(
                        self,
                        "Éxito",
                        f"Obra '{obra_db.nombre}' actualizada correctamente.",
                        QMessageBox.StandardButton.Ok
                    )

            self.accept()

        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudo guardar la obra: {str(e)}"
            )
        finally:
            db.close()

    def _cargar_articulos_obra(self):
        """Carga los artículos de la obra en la tabla."""
        if not self.obra:
            return

        db = next(get_db())

        try:
            # Obtener artículos de la obra
            obra_articulos = db.query(ObraArticulo).filter(
                ObraArticulo.obra_id == self.obra.id
            ).all()

            # Configurar la tabla
            self.tabla_articulos.setRowCount(len(obra_articulos))

            coste_total = 0.0

            for fila, obra_articulo in enumerate(obra_articulos):
                # Artículo
                articulo_codigo = obra_articulo.articulo.codigo if obra_articulo.articulo else "N/A"
                self.tabla_articulos.setItem(fila, 0, QTableWidgetItem(articulo_codigo))

                # Descripción
                articulo_desc = obra_articulo.articulo.descripcion if obra_articulo.articulo else "N/A"
                self.tabla_articulos.setItem(fila, 1, QTableWidgetItem(articulo_desc))

                # Medidas
                medidas = f"{obra_articulo.altura:.0f} x {obra_articulo.anchura:.0f} mm"
                self.tabla_articulos.setItem(fila, 2, QTableWidgetItem(medidas))

                # Cantidad
                self.tabla_articulos.setItem(fila, 3, QTableWidgetItem(str(obra_articulo.cantidad)))

                # Precio unitario
                precio_unit = f"€ {obra_articulo.precio_unitario:.2f}" if obra_articulo.precio_unitario else "€ 0,00"
                self.tabla_articulos.setItem(fila, 4, QTableWidgetItem(precio_unit))

                # Precio total
                precio_total = f"€ {obra_articulo.precio_total:.2f}" if obra_articulo.precio_total else "€ 0,00"
                self.tabla_articulos.setItem(fila, 5, QTableWidgetItem(precio_total))
                coste_total += obra_articulo.precio_total or 0.0

                # Notas
                notas = obra_articulo.notas or ""
                self.tabla_articulos.setItem(fila, 6, QTableWidgetItem(notas))

                # ID (oculto)
                self.tabla_articulos.setItem(fila, 7, QTableWidgetItem(str(obra_articulo.id)))

            # Actualizar totales
            self.label_total_articulos.setText(f"Total artículos: {len(obra_articulos)}")
            self.label_coste_total.setText(f"Coste total: € {coste_total:,.2f}")

            # Actualizar coste actual en el formulario
            self.campo_coste_actual.setValue(coste_total)

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al cargar artículos: {str(e)}")
        finally:
            db.close()

    def _actualizar_botones_articulos(self):
        """Actualiza el estado de los botones de artículos."""
        seleccion = self.tabla_articulos.selectedItems()
        habilitar = len(seleccion) > 0

        self.boton_editar_articulo.setEnabled(habilitar)
        self.boton_eliminar_articulo.setEnabled(habilitar)

    def _agregar_articulo(self):
        """Agregar artículo a la obra."""
        if not self.obra:
            QMessageBox.warning(self, "Error", "Debe guardar la obra antes de agregar artículos.")
            return

        # Usar el diálogo mejorado con medidas múltiples
        from .agregar_articulo_mejorado_dialog import AgregarArticuloMejoradoDialog

        dialog = AgregarArticuloMejoradoDialog(self, self.obra)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self._cargar_articulos_obra()

    def _editar_articulo(self):
        """Editar artículo seleccionado."""
        fila_actual = self.tabla_articulos.currentRow()
        if fila_actual < 0:
            return

        # Obtener ID del artículo
        item_id = self.tabla_articulos.item(fila_actual, 7)
        if not item_id:
            return

        obra_articulo_id = int(item_id.text())

        db = next(get_db())
        try:
            obra_articulo = db.query(ObraArticulo).filter(ObraArticulo.id == obra_articulo_id).first()
            if obra_articulo:
                # Usar el diálogo mejorado para editar
                from .agregar_articulo_mejorado_dialog import AgregarArticuloMejoradoDialog

                dialog = AgregarArticuloMejoradoDialog(self, self.obra, obra_articulo)
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    self._cargar_articulos_obra()
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al editar artículo: {str(e)}")
        finally:
            db.close()

    def _eliminar_articulo(self):
        """Eliminar artículo seleccionado."""
        fila_actual = self.tabla_articulos.currentRow()
        if fila_actual < 0:
            return

        # Obtener información del artículo
        item_codigo = self.tabla_articulos.item(fila_actual, 0)
        item_id = self.tabla_articulos.item(fila_actual, 7)

        if not item_codigo or not item_id:
            return

        codigo_articulo = item_codigo.text()
        obra_articulo_id = int(item_id.text())

        respuesta = QMessageBox.question(
            self,
            "Confirmar eliminación",
            f"¿Está seguro de que desea eliminar el artículo '{codigo_articulo}' de la obra?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if respuesta == QMessageBox.StandardButton.Yes:
            db = next(get_db())
            try:
                obra_articulo = db.query(ObraArticulo).filter(ObraArticulo.id == obra_articulo_id).first()
                if obra_articulo:
                    db.delete(obra_articulo)
                    db.commit()
                    self._cargar_articulos_obra()
                    QMessageBox.information(self, "Éxito", "Artículo eliminado correctamente.")
            except Exception as e:
                db.rollback()
                QMessageBox.critical(self, "Error", f"Error al eliminar artículo: {str(e)}")
            finally:
                db.close()

    def _recalcular_coste_total(self):
        """Recalcula el coste total de todos los artículos."""
        if not self.obra:
            return

        db = next(get_db())

        try:
            # Obtener todos los artículos de la obra
            obra_articulos = db.query(ObraArticulo).filter(
                ObraArticulo.obra_id == self.obra.id
            ).all()

            coste_total = 0.0

            for obra_articulo in obra_articulos:
                # Recalcular precio del artículo
                precio_total = obra_articulo.calcular_precio()
                coste_total += precio_total

            db.commit()

            # Recargar la tabla
            self._cargar_articulos_obra()

            QMessageBox.information(
                self,
                "Éxito",
                f"Coste total recalculado: € {coste_total:,.2f}"
            )

        except Exception as e:
            db.rollback()
            QMessageBox.critical(self, "Error", f"Error al recalcular coste: {str(e)}")
        finally:
            db.close()



    def _generar_informe_taller_completo(self):
        """Genera el informe de taller completo con imágenes de ventanas."""
        if not self.obra:
            QMessageBox.warning(self, "Error", "Debe guardar la obra antes de generar el informe de taller.")
            return

        # Verificar que la obra tenga artículos
        db = next(get_db())
        try:
            obra_articulos = db.query(ObraArticulo).filter(
                ObraArticulo.obra_id == self.obra.id
            ).count()

            if obra_articulos == 0:
                QMessageBox.warning(
                    self,
                    "Sin artículos",
                    "La obra no tiene artículos. Agregue artículos antes de generar el informe de taller."
                )
                return
        finally:
            db.close()

        # Abrir diálogo de informe de taller mejorado
        from .informe_taller_mejorado_dialog import InformeTallerMejoradoDialog

        dialog = InformeTallerMejoradoDialog(self, self.obra)
        dialog.exec()

