"""
Theme manager simplificado sin dependencias externas
"""

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtGui import QFont


class SimpleThemeManager(QObject):
    """Gestor de temas simplificado sin dependencias externas"""
    
    theme_changed = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.current_theme = "professional"
    
    def apply_professional_theme_to_widget(self, widget):
        """Aplica un tema profesional simple a un widget"""
        try:
            widget.setStyleSheet("""
                QWidget {
                    background-color: #f5f5f5;
                    color: #333333;
                    font-family: 'Segoe UI', Arial, sans-serif;
                }
                QPushButton {
                    background-color: #0078d4;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #106ebe;
                }
                QPushButton:pressed {
                    background-color: #005a9e;
                }
                QPushButton:disabled {
                    background-color: #cccccc;
                    color: #666666;
                }
                QLineEdit {
                    border: 2px solid #e1e1e1;
                    border-radius: 4px;
                    padding: 8px;
                    background-color: white;
                }
                QLineEdit:focus {
                    border-color: #0078d4;
                }
                QComboBox {
                    border: 2px solid #e1e1e1;
                    border-radius: 4px;
                    padding: 6px;
                    background-color: white;
                }
                QComboBox:focus {
                    border-color: #0078d4;
                }
                QTableWidget {
                    background-color: white;
                    border: 1px solid #e1e1e1;
                    gridline-color: #f0f0f0;
                }
                QHeaderView::section {
                    background-color: #f8f8f8;
                    border: 1px solid #e1e1e1;
                    padding: 8px;
                    font-weight: bold;
                }
                QGroupBox {
                    font-weight: bold;
                    border: 2px solid #e1e1e1;
                    border-radius: 4px;
                    margin-top: 8px;
                    padding-top: 8px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 8px;
                    padding: 0 5px 0 5px;
                    background-color: #f5f5f5;
                }
            """)
        except Exception as e:
            print(f"Error aplicando tema: {e}")
    
    def apply_theme(self, theme_name="professional"):
        """Aplica un tema a toda la aplicación"""
        app = QApplication.instance()
        if app:
            self.apply_professional_theme_to_widget(app)
