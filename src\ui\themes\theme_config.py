"""
Configuración de temas para PRO-2000
Permite cambiar fácilmente entre diferentes estilos visuales
"""

class ThemeConfig:
    """Configuración de temas para la aplicación"""
    
    # Tema Claro (por defecto)
    LIGHT_THEME = """
    QWidget {
        color: #1e293b;
        font-family: 'Segoe UI', 'SF Pro Display', 'Inter', system-ui, sans-serif;
        font-size: 14px;
        background: #ffffff;
    }

    QDialog {
        background: #ffffff;
        color: #1e293b;
    }

    QLabel {
        color: #1e293b;
        background: transparent;
    }

    QPushButton {
        color: #1e293b;
        background: #f1f5f9;
        border: 1px solid #cbd5e1;
        border-radius: 8px;
        padding: 8px 16px;
        font-weight: 500;
    }

    QPushButton:hover {
        background: #e2e8f0;
        border-color: #94a3b8;
    }

    QLineEdit, QTextEdit, QPlainTextEdit {
        background: #ffffff;
        color: #1e293b;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        padding: 8px 12px;
    }

    QComboBox {
        background: #ffffff;
        color: #1e293b;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        padding: 8px 12px;
    }

    QTableWidget, QTableView {
        background: #ffffff;
        color: #1e293b;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        gridline-color: #f1f5f9;
        selection-background-color: #3b82f6;
        selection-color: #ffffff;
        alternate-background-color: #ffffff;
        outline: none;
    }

    QTableWidget::item, QTableView::item {
        color: #1e293b;
        background: #ffffff;
        padding: 12px 8px;
        border: none;
        border-bottom: 1px solid #f1f5f9;
    }

    QTableWidget::item:selected, QTableView::item:selected {
        background: #3b82f6;
        color: #ffffff;
    }

    QTableWidget::item:hover, QTableView::item:hover {
        background: #f8fafc;
        color: #1e293b;
    }

    QHeaderView::section {
        background: #f8fafc;
        color: #374151;
        border: none;
        border-bottom: 2px solid #e2e8f0;
        border-right: 1px solid #e2e8f0;
        padding: 12px 8px;
        font-weight: 600;
        text-align: left;
    }
    """
    
    # Tema Oscuro
    DARK_THEME = """
    QWidget {
        color: #f1f5f9;
        font-family: 'Segoe UI', 'SF Pro Display', 'Inter', system-ui, sans-serif;
        font-size: 14px;
        background: #1e293b;
    }

    QDialog {
        background: #1e293b;
        color: #f1f5f9;
    }

    QLabel {
        color: #f1f5f9;
        background: transparent;
    }

    QPushButton {
        color: #f1f5f9;
        background: #374151;
        border: 1px solid #4b5563;
        border-radius: 8px;
        padding: 8px 16px;
        font-weight: 500;
    }

    QPushButton:hover {
        background: #4b5563;
        border-color: #6b7280;
    }

    QLineEdit, QTextEdit, QPlainTextEdit {
        background: #374151;
        color: #f1f5f9;
        border: 2px solid #4b5563;
        border-radius: 8px;
        padding: 8px 12px;
    }

    QComboBox {
        background: #374151;
        color: #f1f5f9;
        border: 2px solid #4b5563;
        border-radius: 8px;
        padding: 8px 12px;
    }

    QTableWidget, QTableView {
        background: #374151;
        color: #f1f5f9;
        border: 1px solid #4b5563;
        border-radius: 8px;
        gridline-color: #4b5563;
        selection-background-color: #3b82f6;
        selection-color: #ffffff;
        alternate-background-color: #374151;
        outline: none;
    }

    QTableWidget::item, QTableView::item {
        color: #f1f5f9;
        background: #374151;
        padding: 12px 8px;
        border: none;
        border-bottom: 1px solid #4b5563;
    }

    QTableWidget::item:selected, QTableView::item:selected {
        background: #3b82f6;
        color: #ffffff;
    }

    QTableWidget::item:hover, QTableView::item:hover {
        background: #4b5563;
        color: #f1f5f9;
    }

    QHeaderView::section {
        background: #1e293b;
        color: #f1f5f9;
        border: none;
        border-bottom: 2px solid #4b5563;
        border-right: 1px solid #4b5563;
        padding: 12px 8px;
        font-weight: 600;
        text-align: left;
    }
    """
    
    # Tema Azul Profesional
    BLUE_THEME = """
    QWidget {
        color: #1e293b;
        font-family: 'Segoe UI', 'SF Pro Display', 'Inter', system-ui, sans-serif;
        font-size: 14px;
        background: #f8fafc;
    }

    QDialog {
        background: #f8fafc;
        color: #1e293b;
    }

    QLabel {
        color: #1e293b;
        background: transparent;
    }

    QPushButton {
        color: #ffffff;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #3b82f6, stop:1 #1d4ed8);
        border: none;
        border-radius: 8px;
        padding: 8px 16px;
        font-weight: 600;
    }

    QPushButton:hover {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #2563eb, stop:1 #1e40af);
    }

    QLineEdit, QTextEdit, QPlainTextEdit {
        background: #ffffff;
        color: #1e293b;
        border: 2px solid #dbeafe;
        border-radius: 8px;
        padding: 8px 12px;
    }

    QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
        border-color: #3b82f6;
    }

    QComboBox {
        background: #ffffff;
        color: #1e293b;
        border: 2px solid #dbeafe;
        border-radius: 8px;
        padding: 8px 12px;
    }

    QTableWidget, QTableView {
        background: #ffffff;
        color: #1e293b;
        border: 1px solid #dbeafe;
        border-radius: 8px;
        gridline-color: #f0f9ff;
        selection-background-color: #3b82f6;
        selection-color: #ffffff;
        alternate-background-color: #ffffff;
        outline: none;
    }

    QTableWidget::item, QTableView::item {
        color: #1e293b;
        background: #ffffff;
        padding: 12px 8px;
        border: none;
        border-bottom: 1px solid #f0f9ff;
    }

    QTableWidget::item:selected, QTableView::item:selected {
        background: #3b82f6;
        color: #ffffff;
    }

    QTableWidget::item:hover, QTableView::item:hover {
        background: #f0f9ff;
        color: #1e293b;
    }

    QHeaderView::section {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #dbeafe, stop:1 #bfdbfe);
        color: #1e40af;
        border: none;
        border-bottom: 2px solid #3b82f6;
        border-right: 1px solid #bfdbfe;
        padding: 12px 8px;
        font-weight: 600;
        text-align: left;
    }
    """
    
    @staticmethod
    def get_theme(theme_name):
        """Obtiene el CSS de un tema específico"""
        themes = {
            "light": ThemeConfig.LIGHT_THEME,
            "dark": ThemeConfig.DARK_THEME,
            "blue": ThemeConfig.BLUE_THEME
        }
        return themes.get(theme_name, ThemeConfig.LIGHT_THEME)
    
    @staticmethod
    def get_available_themes():
        """Obtiene la lista de temas disponibles"""
        return {
            "light": "🌞 Tema Claro",
            "dark": "🌙 Tema Oscuro", 
            "blue": "💙 Tema Azul Profesional"
        }
