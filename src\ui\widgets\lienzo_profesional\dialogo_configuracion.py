"""
Diálogo profesional de configuración de perfiles.
Sistema completo para configurar cada perfil con todos sus parámetros técnicos.
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget, QFormLayout,
    QLabel, QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox, QPushButton,
    QGroupBox, QGridLayout, QTextEdit, QCheckBox, QDialogButtonBox,
    QListWidget, QListWidgetItem, QSplitter, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

from models.base import get_db
from models.perfil import Perfil, TipoPerfil
from .selector_perfiles import SelectorPerfilesProfesional


class DialogoConfiguracionProfesional(QDialog):
    """
    Diálogo profesional para configurar perfiles individuales.
    Incluye selección de perfil de la base de datos, configuración de medidas,
    materiales y parámetros técnicos.
    """
    
    def __init__(self, elemento, parent=None):
        super().__init__(parent)
        self.elemento = elemento
        self.propiedades_modificadas = elemento.propiedades.copy()
        
        self.setWindowTitle(f"Configuración Profesional - {elemento.propiedades.get('categoria', 'Perfil')}")
        self.setModal(True)
        self.resize(800, 600)
        
        self._crear_interfaz()
        self._cargar_datos_elemento()
        self._conectar_señales()
    
    def _crear_interfaz(self):
        """Crea la interfaz del diálogo."""
        layout = QVBoxLayout(self)
        
        # Título
        titulo = QLabel(f"🔧 {self.elemento.propiedades.get('categoria', 'Configuración de Perfil')}")
        titulo.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        titulo.setStyleSheet("color: #0078d4; margin: 10px;")
        layout.addWidget(titulo)
        
        # Tabs principales
        self.tabs = QTabWidget()
        layout.addWidget(self.tabs)
        
        # Tab 1: Selección de Perfil
        self._crear_tab_perfil()
        
        # Tab 2: Medidas y Dimensiones
        self._crear_tab_medidas()
        
        # Tab 3: Propiedades Técnicas
        self._crear_tab_propiedades()
        
        # Tab 4: Configuración Avanzada (solo para ciertos tipos)
        if self.elemento.tipo in ['hoja', 'division_v', 'division_h']:
            self._crear_tab_avanzado()
        
        # Botones
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        botones.accepted.connect(self.accept)
        botones.rejected.connect(self.reject)
        layout.addWidget(botones)
    
    def _crear_tab_perfil(self):
        """Crea el tab de selección de perfil."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Selector de perfiles
        grupo_selector = QGroupBox("📋 Selección de Perfil de la Base de Datos")
        layout_selector = QVBoxLayout(grupo_selector)
        
        # Información del perfil actual
        self.info_perfil_actual = QLabel("Perfil actual: Sin seleccionar")
        self.info_perfil_actual.setStyleSheet("font-weight: bold; color: #0078d4;")
        layout_selector.addWidget(self.info_perfil_actual)
        
        # Botón para abrir selector
        self.btn_seleccionar_perfil = QPushButton("🔍 Seleccionar Perfil de la Base de Datos")
        self.btn_seleccionar_perfil.clicked.connect(self._abrir_selector_perfiles)
        layout_selector.addWidget(self.btn_seleccionar_perfil)
        
        # Información detallada del perfil seleccionado
        self.info_perfil_detalle = QTextEdit()
        self.info_perfil_detalle.setMaximumHeight(150)
        self.info_perfil_detalle.setReadOnly(True)
        layout_selector.addWidget(self.info_perfil_detalle)
        
        layout.addWidget(grupo_selector)
        
        # Configuración rápida
        grupo_rapido = QGroupBox("⚡ Configuración Rápida")
        layout_rapido = QFormLayout(grupo_rapido)
        
        self.combo_tipo_rapido = QComboBox()
        self._cargar_tipos_rapidos()
        layout_rapido.addRow("Tipo de perfil:", self.combo_tipo_rapido)
        
        self.combo_material_rapido = QComboBox()
        self.combo_material_rapido.addItems(["PVC", "Aluminio", "Madera", "Acero"])
        layout_rapido.addRow("Material:", self.combo_material_rapido)
        
        layout.addWidget(grupo_rapido)
        
        self.tabs.addTab(tab, "🔧 Perfil")
    
    def _crear_tab_medidas(self):
        """Crea el tab de medidas y dimensiones."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Medidas principales
        grupo_medidas = QGroupBox("📏 Medidas Principales")
        layout_medidas = QFormLayout(grupo_medidas)
        
        self.spin_medida_principal = QSpinBox()
        self.spin_medida_principal.setRange(100, 5000)
        self.spin_medida_principal.setSuffix(" mm")
        layout_medidas.addRow("Medida principal:", self.spin_medida_principal)
        
        self.spin_grosor = QSpinBox()
        self.spin_grosor.setRange(20, 200)
        self.spin_grosor.setSuffix(" mm")
        layout_medidas.addRow("Grosor del perfil:", self.spin_grosor)
        
        layout.addWidget(grupo_medidas)
        
        # Ángulos de corte (para perfiles de marco)
        if self.elemento.tipo.startswith('marco'):
            grupo_angulos = QGroupBox("📐 Ángulos de Corte")
            layout_angulos = QFormLayout(grupo_angulos)
            
            self.spin_angulo_izq = QDoubleSpinBox()
            self.spin_angulo_izq.setRange(0, 180)
            self.spin_angulo_izq.setValue(45)
            self.spin_angulo_izq.setSuffix("°")
            layout_angulos.addRow("Ángulo izquierdo:", self.spin_angulo_izq)
            
            self.spin_angulo_der = QDoubleSpinBox()
            self.spin_angulo_der.setRange(0, 180)
            self.spin_angulo_der.setValue(45)
            self.spin_angulo_der.setSuffix("°")
            layout_angulos.addRow("Ángulo derecho:", self.spin_angulo_der)
            
            layout.addWidget(grupo_angulos)
        
        self.tabs.addTab(tab, "📏 Medidas")
    
    def _crear_tab_propiedades(self):
        """Crea el tab de propiedades técnicas."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Propiedades del material
        grupo_material = QGroupBox("🔬 Propiedades del Material")
        layout_material = QFormLayout(grupo_material)
        
        self.combo_material = QComboBox()
        self.combo_material.addItems(["PVC", "Aluminio", "Madera", "Acero"])
        layout_material.addRow("Material:", self.combo_material)
        
        self.combo_color = QComboBox()
        self.combo_color.addItems(["Blanco", "Marrón", "Negro", "Gris", "Roble", "Nogal"])
        layout_material.addRow("Color:", self.combo_color)
        
        self.combo_acabado = QComboBox()
        self.combo_acabado.addItems(["Liso", "Texturizado", "Foliado", "Anodizado", "Lacado"])
        layout_material.addRow("Acabado:", self.combo_acabado)
        
        layout.addWidget(grupo_material)
        
        # Propiedades técnicas
        grupo_tecnico = QGroupBox("⚙️ Propiedades Técnicas")
        layout_tecnico = QFormLayout(grupo_tecnico)
        
        self.line_codigo = QLineEdit()
        layout_tecnico.addRow("Código del perfil:", self.line_codigo)
        
        self.line_referencia = QLineEdit()
        layout_tecnico.addRow("Referencia:", self.line_referencia)
        
        self.text_observaciones = QTextEdit()
        self.text_observaciones.setMaximumHeight(80)
        layout_tecnico.addRow("Observaciones:", self.text_observaciones)
        
        layout.addWidget(grupo_tecnico)
        
        self.tabs.addTab(tab, "🔬 Propiedades")
    
    def _crear_tab_avanzado(self):
        """Crea el tab de configuración avanzada."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        if self.elemento.tipo == 'hoja':
            # Configuración específica para hojas
            grupo_hoja = QGroupBox("🚪 Configuración de Hoja")
            layout_hoja = QFormLayout(grupo_hoja)
            
            self.combo_tipo_apertura = QComboBox()
            self.combo_tipo_apertura.addItems([
                "Practicable derecha",
                "Practicable izquierda", 
                "Oscilobatiente derecha",
                "Oscilobatiente izquierda",
                "Corredera",
                "Fija"
            ])
            layout_hoja.addRow("Tipo de apertura:", self.combo_tipo_apertura)
            
            self.combo_herrajes = QComboBox()
            self.combo_herrajes.addItems(["Estándar", "Reforzado", "Seguridad", "Premium"])
            layout_hoja.addRow("Herrajes:", self.combo_herrajes)
            
            layout.addWidget(grupo_hoja)
        
        elif self.elemento.tipo in ['division_v', 'division_h']:
            # Configuración específica para divisiones
            grupo_division = QGroupBox("➗ Configuración de División")
            layout_division = QFormLayout(grupo_division)
            
            self.combo_tipo_division = QComboBox()
            if self.elemento.tipo == 'division_v':
                self.combo_tipo_division.addItems([
                    "Travesaño vertical simple",
                    "Travesaño vertical reforzado",
                    "Montante estructural"
                ])
            else:
                self.combo_tipo_division.addItems([
                    "Travesaño horizontal simple", 
                    "Travesaño horizontal reforzado",
                    "Dintel estructural"
                ])
            layout_division.addRow("Tipo de división:", self.combo_tipo_division)
            
            self.check_desmontable = QCheckBox("División desmontable")
            layout_division.addRow("", self.check_desmontable)
            
            layout.addWidget(grupo_division)
        
        self.tabs.addTab(tab, "⚙️ Avanzado")
    
    def _cargar_tipos_rapidos(self):
        """Carga los tipos de perfil según el elemento."""
        tipos = {
            'marco_superior': ['Marco superior', 'Marco superior reforzado'],
            'marco_inferior': ['Marco inferior', 'Marco inferior con desagüe'],
            'marco_izquierdo': ['Marco lateral', 'Jamba izquierda'],
            'marco_derecho': ['Marco lateral', 'Jamba derecha'],
            'division_v': ['Travesaño vertical', 'Montante', 'División vertical'],
            'division_h': ['Travesaño horizontal', 'Dintel', 'División horizontal'],
            'hoja': ['Hoja practicable', 'Hoja oscilobatiente', 'Hoja corredera']
        }
        
        items = tipos.get(self.elemento.tipo, ['Perfil estándar'])
        self.combo_tipo_rapido.addItems(items)
    
    def _cargar_datos_elemento(self):
        """Carga los datos actuales del elemento en la interfaz."""
        props = self.elemento.propiedades
        
        # Tab Perfil
        if 'codigo_perfil' in props:
            self.info_perfil_actual.setText(f"Perfil actual: {props['codigo_perfil']}")
        
        self.combo_material_rapido.setCurrentText(props.get('material', 'PVC'))
        
        # Tab Medidas
        self.spin_medida_principal.setValue(props.get('medida', 1200))
        self.spin_grosor.setValue(props.get('grosor', 70))
        
        # Tab Propiedades
        self.combo_material.setCurrentText(props.get('material', 'PVC'))
        self.combo_color.setCurrentText(props.get('color', 'Blanco'))
        self.combo_acabado.setCurrentText(props.get('acabado', 'Liso'))
        self.line_codigo.setText(props.get('codigo', ''))
        self.line_referencia.setText(props.get('referencia', ''))
        self.text_observaciones.setText(props.get('observaciones', ''))
    
    def _conectar_señales(self):
        """Conecta las señales de los controles."""
        # Actualizar propiedades cuando cambien los valores
        self.combo_material_rapido.currentTextChanged.connect(self._actualizar_propiedades)
        self.spin_medida_principal.valueChanged.connect(self._actualizar_propiedades)
        self.spin_grosor.valueChanged.connect(self._actualizar_propiedades)
    
    def _actualizar_propiedades(self):
        """Actualiza las propiedades modificadas."""
        self.propiedades_modificadas.update({
            'material': self.combo_material.currentText(),
            'medida': self.spin_medida_principal.value(),
            'grosor': self.spin_grosor.value(),
            'color': self.combo_color.currentText(),
            'acabado': self.combo_acabado.currentText(),
            'codigo': self.line_codigo.text(),
            'referencia': self.line_referencia.text(),
            'observaciones': self.text_observaciones.toPlainText()
        })
    
    def _abrir_selector_perfiles(self):
        """Abre el selector de perfiles de la base de datos."""
        selector = SelectorPerfilesProfesional(self.elemento.tipo, self)
        if selector.exec() == selector.DialogCode.Accepted:
            perfil_seleccionado = selector.obtener_perfil_seleccionado()
            if perfil_seleccionado:
                self._aplicar_perfil_seleccionado(perfil_seleccionado)
    
    def _aplicar_perfil_seleccionado(self, perfil):
        """Aplica los datos del perfil seleccionado."""
        self.propiedades_modificadas.update({
            'codigo_perfil': perfil.codigo,
            'descripcion_perfil': perfil.descripcion,
            'material': perfil.material or 'PVC',
            'color': perfil.color or 'Blanco',
            'grosor': perfil.ancho or 70,
            'precio_metro': perfil.precio_metro or 0
        })
        
        # Actualizar interfaz
        self.info_perfil_actual.setText(f"Perfil actual: {perfil.codigo} - {perfil.descripcion}")
        self.info_perfil_detalle.setText(
            f"Código: {perfil.codigo}\n"
            f"Descripción: {perfil.descripcion}\n"
            f"Material: {perfil.material or 'N/A'}\n"
            f"Dimensiones: {perfil.ancho}x{perfil.alto} mm\n"
            f"Precio: €{perfil.precio_metro:.2f}/m"
        )
        
        # Actualizar controles
        if perfil.material:
            self.combo_material.setCurrentText(perfil.material)
            self.combo_material_rapido.setCurrentText(perfil.material)
        if perfil.color:
            self.combo_color.setCurrentText(perfil.color)
        if perfil.ancho:
            self.spin_grosor.setValue(int(perfil.ancho))
    
    def obtener_propiedades(self):
        """Obtiene las propiedades modificadas."""
        self._actualizar_propiedades()
        return self.propiedades_modificadas
