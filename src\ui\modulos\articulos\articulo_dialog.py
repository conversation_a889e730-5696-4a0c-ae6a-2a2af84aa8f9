"""
Módulo que implementa el diálogo de gestión de artículos.
"""

import os

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QTableWidget,
    QTableWidgetItem, QHeaderView, QMessageBox, QLineEdit, QLabel,
    QComboBox, QFormLayout, QDialogButtonBox, QTextEdit, QSplitter,
    QWidget, QTabWidget, QGroupBox, QDoubleSpinBox, QSpinBox, QCheckBox,
    QTreeWidget, QTreeWidgetItem, QFrame, QFileDialog
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QPixmap, QFont, QColor
from PyQt6.QtWidgets import QStyle

from models.base import get_db
from models.articulo import Articulo, ArticuloPerfil, ArticuloAccesorio, ArticuloCristal
from models.perfil import Perfil
from models.cristal import Cristal
from models.accesorio import Accesorio
from models.persiana import Persiana
from ui.utils.window_utils import smart_dialog_setup, force_dialog_maximized

# Importar sistema de iconos
try:
    from ui.icons.icon_manager import icon_manager
    ICONS_AVAILABLE = True
except ImportError:
    ICONS_AVAILABLE = False


class ArticuloDialog(QDialog):
    """Diálogo para gestionar artículos."""
    
    def __init__(self, parent=None):
        """Inicializa el diálogo de gestión de artículos."""
        super().__init__(parent)
        self.setWindowTitle("Gestión de Artículos")

        # ✅ FORZAR MAXIMIZACIÓN COMPLETA
        force_dialog_maximized(self, "Gestión de Artículos")
        
        # Variables
        self.articulos = []
        self.articulo_actual = None
        
        # Inicializar la interfaz
        self._inicializar_ui()
        
        # Cargar datos
        self._cargar_articulos()
    
    def _inicializar_ui(self):
        """Configura la interfaz de usuario del diálogo."""
        # Layout principal
        layout_principal = QVBoxLayout(self)
        
        # Barra de herramientas
        self._crear_barra_herramientas(layout_principal)
        
        # Splitter principal (izquierda: lista, derecha: detalles)
        splitter_principal = QSplitter(Qt.Orientation.Horizontal)
        
        # Panel izquierdo: Lista de artículos
        self._crear_panel_lista(splitter_principal)
        
        # Panel derecho: Detalles del artículo
        self._crear_panel_detalles(splitter_principal)
        
        # Configurar proporciones del splitter
        splitter_principal.setSizes([400, 1000])
        
        layout_principal.addWidget(splitter_principal)
        
        # Botones de acción
        self._crear_botones_accion(layout_principal)
    
    def _crear_barra_herramientas(self, layout_principal):
        """Crea la barra de herramientas."""
        barra_herramientas = QHBoxLayout()
        
        # Botón de nuevo artículo
        self.boton_nuevo = QPushButton("📄 Nuevo Artículo")
        if ICONS_AVAILABLE:
            self.boton_nuevo.setIcon(icon_manager.get_icon('add', 'success'))
        else:
            self.boton_nuevo.setIcon(self.establecer_icono("file"))
        self.boton_nuevo.clicked.connect(self.nuevo_articulo)
        self.boton_nuevo.setToolTip("Crear un nuevo artículo (Ctrl+N)")
        self.boton_nuevo.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        barra_herramientas.addWidget(self.boton_nuevo)

        # Botón de duplicar
        self.boton_duplicar = QPushButton("📋 Duplicar")
        if ICONS_AVAILABLE:
            self.boton_duplicar.setIcon(icon_manager.get_icon('file', 'info'))
        else:
            self.boton_duplicar.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogDetailedView))
        self.boton_duplicar.clicked.connect(self.duplicar_articulo)
        self.boton_duplicar.setEnabled(False)
        self.boton_duplicar.setToolTip("Duplicar el artículo seleccionado")
        barra_herramientas.addWidget(self.boton_duplicar)

        # Botón de eliminar
        self.boton_eliminar = QPushButton("🗑️ Eliminar")
        if ICONS_AVAILABLE:
            self.boton_eliminar.setIcon(icon_manager.get_icon('delete', 'danger'))
        else:
            self.boton_eliminar.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_TrashIcon))
        self.boton_eliminar.clicked.connect(self.eliminar_articulo)
        self.boton_eliminar.setEnabled(False)
        self.boton_eliminar.setToolTip("Eliminar el artículo seleccionado")
        self.boton_eliminar.setStyleSheet("""
            QPushButton:enabled {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:enabled:hover {
                background-color: #c82333;
            }
        """)
        barra_herramientas.addWidget(self.boton_eliminar)
        
        # Separador
        barra_herramientas.addWidget(self._crear_separador())
        
        # Campo de búsqueda mejorado
        self.campo_busqueda = QLineEdit()
        self.campo_busqueda.setPlaceholderText("🔍 Buscar por código, descripción o serie...")
        self.campo_busqueda.textChanged.connect(self._filtrar_articulos)
        self.campo_busqueda.setMinimumWidth(250)
        self.campo_busqueda.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #ced4da;
                border-radius: 6px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #86b7fe;
                background-color: #f8f9ff;
            }
        """)

        # Botón de limpiar búsqueda
        boton_limpiar = QPushButton("❌")
        boton_limpiar.setMaximumWidth(30)
        boton_limpiar.clicked.connect(lambda: self.campo_busqueda.clear())
        boton_limpiar.setToolTip("Limpiar búsqueda")

        barra_herramientas.addWidget(QLabel("🔍 Buscar:"))
        barra_herramientas.addWidget(self.campo_busqueda)
        barra_herramientas.addWidget(boton_limpiar)

        # Espaciador
        barra_herramientas.addStretch()

        # Botón de exportar
        boton_exportar = QPushButton("📤 Exportar")
        if ICONS_AVAILABLE:
            boton_exportar.setIcon(icon_manager.get_icon('export', 'info'))
        boton_exportar.clicked.connect(self._exportar_articulos)
        boton_exportar.setToolTip("Exportar lista de artículos")
        barra_herramientas.addWidget(boton_exportar)

        # Botón de refrescar
        boton_refrescar = QPushButton("🔄 Refrescar")
        if ICONS_AVAILABLE:
            boton_refrescar.setIcon(icon_manager.get_icon('refresh', 'primary'))
        else:
            boton_refrescar.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_BrowserReload))
        boton_refrescar.clicked.connect(self._cargar_articulos)
        boton_refrescar.setToolTip("Actualizar lista de artículos (F5)")
        barra_herramientas.addWidget(boton_refrescar)
        
        layout_principal.addLayout(barra_herramientas)
    
    def _crear_separador(self):
        """Crea un separador vertical."""
        separador = QFrame()
        separador.setFrameShape(QFrame.Shape.VLine)
        separador.setFrameShadow(QFrame.Shadow.Sunken)
        return separador
    
    def _crear_panel_lista(self, splitter):
        """Crea el panel izquierdo con la lista de artículos."""
        widget_lista = QWidget()
        layout_lista = QVBoxLayout(widget_lista)
        
        # Título
        titulo = QLabel("Artículos")
        titulo.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout_lista.addWidget(titulo)
        
        # Lista de artículos
        self.lista_articulos = QTreeWidget()
        self.lista_articulos.setHeaderLabels(["Código", "Descripción", "Serie"])
        self.lista_articulos.setRootIsDecorated(False)
        self.lista_articulos.setAlternatingRowColors(False)  # Desactivar filas alternadas
        self.lista_articulos.itemSelectionChanged.connect(self._on_articulo_seleccionado)
        
        # Configurar columnas
        header = self.lista_articulos.header()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        
        layout_lista.addWidget(self.lista_articulos)
        
        splitter.addWidget(widget_lista)
    
    def _crear_panel_detalles(self, splitter):
        """Crea el panel derecho con los detalles del artículo."""
        widget_detalles = QWidget()
        layout_detalles = QVBoxLayout(widget_detalles)
        
        # Título
        self.titulo_detalles = QLabel("Seleccione un artículo")
        self.titulo_detalles.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout_detalles.addWidget(self.titulo_detalles)
        
        # Pestañas de detalles
        self.pestanias_detalles = QTabWidget()
        
        # Pestaña de datos generales
        self._crear_pestania_general()
        
        # Pestaña de perfiles
        self._crear_pestania_perfiles()
        
        # Pestaña de accesorios
        self._crear_pestania_accesorios()
        
        # Pestaña de cristales
        self._crear_pestania_cristales()
        
        # Pestaña de persianas (temporalmente deshabilitada)
        # self._crear_pestania_persianas()
        
        # Pestaña de simulador
        self._crear_pestania_simulador()
        
        layout_detalles.addWidget(self.pestanias_detalles)
        
        # Inicialmente ocultar las pestañas
        self.pestanias_detalles.setVisible(False)
        
        splitter.addWidget(widget_detalles)
    
    def _crear_pestania_general(self):
        """Crea la pestaña de datos generales."""
        widget_general = QWidget()
        layout_general = QFormLayout(widget_general)
        
        # Código con validación
        self.campo_codigo = QLineEdit()
        self.campo_codigo.setMaxLength(20)
        self.campo_codigo.setPlaceholderText("Ej: V001, P002, etc.")
        self.campo_codigo.textChanged.connect(self._validar_codigo)
        self.campo_codigo.setStyleSheet("""
            QLineEdit {
                padding: 6px;
                border: 2px solid #ced4da;
                border-radius: 4px;
            }
            QLineEdit:focus {
                border-color: #86b7fe;
            }
        """)
        layout_general.addRow("Código*:", self.campo_codigo)

        # Etiqueta de validación de código
        self.label_validacion_codigo = QLabel("")
        self.label_validacion_codigo.setStyleSheet("color: red; font-size: 11px;")
        layout_general.addRow("", self.label_validacion_codigo)

        # Serie
        self.campo_serie = QLineEdit()
        self.campo_serie.setMaxLength(50)
        self.campo_serie.setPlaceholderText("Serie del artículo (opcional)")
        layout_general.addRow("Serie:", self.campo_serie)

        # Descripción con contador de caracteres
        self.campo_descripcion = QLineEdit()
        self.campo_descripcion.setMaxLength(200)
        self.campo_descripcion.setPlaceholderText("Descripción detallada del artículo")
        self.campo_descripcion.textChanged.connect(self._actualizar_contador_descripcion)
        layout_general.addRow("Descripción*:", self.campo_descripcion)

        # Contador de caracteres
        self.label_contador_descripcion = QLabel("0/200 caracteres")
        self.label_contador_descripcion.setStyleSheet("color: #6c757d; font-size: 11px;")
        layout_general.addRow("", self.label_contador_descripcion)
        
        # Tiempos
        layout_tiempos = QHBoxLayout()
        
        self.campo_tiempo_taller = QDoubleSpinBox()
        self.campo_tiempo_taller.setRange(0, 999.99)
        self.campo_tiempo_taller.setDecimals(2)
        self.campo_tiempo_taller.setSuffix(" h")
        layout_tiempos.addWidget(QLabel("Taller:"))
        layout_tiempos.addWidget(self.campo_tiempo_taller)
        
        self.campo_tiempo_obra = QDoubleSpinBox()
        self.campo_tiempo_obra.setRange(0, 999.99)
        self.campo_tiempo_obra.setDecimals(2)
        self.campo_tiempo_obra.setSuffix(" h")
        layout_tiempos.addWidget(QLabel("Obra:"))
        layout_tiempos.addWidget(self.campo_tiempo_obra)
        
        layout_general.addRow("Tiempos:", layout_tiempos)
        
        # Dibujo
        layout_dibujo = QHBoxLayout()
        self.campo_dibujo = QLineEdit()
        self.campo_dibujo.setPlaceholderText("Ruta al archivo de dibujo")
        layout_dibujo.addWidget(self.campo_dibujo)

        boton_seleccionar_dibujo = QPushButton("📁")
        boton_seleccionar_dibujo.setMaximumWidth(40)
        boton_seleccionar_dibujo.clicked.connect(self._seleccionar_dibujo)
        boton_seleccionar_dibujo.setToolTip("Seleccionar archivo de dibujo")
        layout_dibujo.addWidget(boton_seleccionar_dibujo)

        layout_general.addRow("Dibujo:", layout_dibujo)

        # Imagen del artículo
        layout_imagen = QHBoxLayout()
        self.campo_imagen = QLineEdit()
        self.campo_imagen.setPlaceholderText("Ruta a la imagen del artículo")
        layout_imagen.addWidget(self.campo_imagen)

        boton_seleccionar_imagen = QPushButton("🖼️")
        boton_seleccionar_imagen.setMaximumWidth(40)
        boton_seleccionar_imagen.clicked.connect(self._seleccionar_imagen)
        boton_seleccionar_imagen.setToolTip("Seleccionar imagen del artículo")
        layout_imagen.addWidget(boton_seleccionar_imagen)

        layout_general.addRow("Imagen:", layout_imagen)

        # Vista previa de imagen
        self.label_preview_imagen = QLabel()
        self.label_preview_imagen.setMaximumHeight(150)
        self.label_preview_imagen.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_preview_imagen.setStyleSheet("""
            QLabel {
                border: 2px dashed #ced4da;
                border-radius: 6px;
                background-color: #f8f9fa;
                color: #6c757d;
            }
        """)
        self.label_preview_imagen.setText("Sin imagen seleccionada")
        layout_general.addRow("Vista previa:", self.label_preview_imagen)
        
        # Activo
        self.campo_activo = QCheckBox("Artículo activo")
        self.campo_activo.setChecked(True)
        layout_general.addRow("", self.campo_activo)
        
        # Botón de guardar
        self.boton_guardar_general = QPushButton("Guardar Datos Generales")
        self.boton_guardar_general.clicked.connect(self._guardar_datos_generales)
        layout_general.addRow("", self.boton_guardar_general)
        
        self.pestanias_detalles.addTab(widget_general, "General")
    
    def _crear_pestania_perfiles(self):
        """Crea la pestaña de perfiles."""
        widget_perfiles = QWidget()
        layout_perfiles = QVBoxLayout(widget_perfiles)
        
        # Barra de herramientas
        barra_perfiles = QHBoxLayout()
        
        boton_agregar_perfil = QPushButton("Agregar Perfil")
        boton_agregar_perfil.clicked.connect(self._agregar_perfil)
        barra_perfiles.addWidget(boton_agregar_perfil)
        
        boton_editar_perfil = QPushButton("Editar")
        boton_editar_perfil.clicked.connect(self._editar_perfil)
        barra_perfiles.addWidget(boton_editar_perfil)

        boton_eliminar_perfil = QPushButton("Eliminar")
        boton_eliminar_perfil.clicked.connect(self._eliminar_perfil)
        barra_perfiles.addWidget(boton_eliminar_perfil)
        
        barra_perfiles.addStretch()
        layout_perfiles.addLayout(barra_perfiles)
        
        # Tabla de perfiles
        self.tabla_perfiles = QTableWidget()
        self.tabla_perfiles.setColumnCount(9)
        self.tabla_perfiles.setHorizontalHeaderLabels([
            "Perfil", "Tipo", "Medida", "Variable", "Ángulos", "Condición", "Cantidad", "Fórmula Cant.", "Orden"
        ])
        
        # Configurar tabla
        header = self.tabla_perfiles.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        
        layout_perfiles.addWidget(self.tabla_perfiles)
        
        self.pestanias_detalles.addTab(widget_perfiles, "Perfiles")
    
    def _crear_pestania_accesorios(self):
        """Crea la pestaña de accesorios."""
        widget_accesorios = QWidget()
        layout_accesorios = QVBoxLayout(widget_accesorios)
        
        # Barra de herramientas
        barra_accesorios = QHBoxLayout()
        
        boton_agregar_accesorio = QPushButton("Agregar Accesorio")
        boton_agregar_accesorio.clicked.connect(self._agregar_accesorio)
        barra_accesorios.addWidget(boton_agregar_accesorio)
        
        boton_eliminar_accesorio = QPushButton("Eliminar")
        boton_eliminar_accesorio.clicked.connect(self._eliminar_accesorio)
        barra_accesorios.addWidget(boton_eliminar_accesorio)
        
        barra_accesorios.addStretch()
        layout_accesorios.addLayout(barra_accesorios)
        
        # Tabla de accesorios
        self.tabla_accesorios = QTableWidget()
        self.tabla_accesorios.setColumnCount(5)
        self.tabla_accesorios.setHorizontalHeaderLabels([
            "Accesorio", "Condición", "Cantidad", "Fórmula Cant.", "Orden"
        ])
        
        # Configurar tabla
        header = self.tabla_accesorios.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        
        layout_accesorios.addWidget(self.tabla_accesorios)
        
        self.pestanias_detalles.addTab(widget_accesorios, "Accesorios")
    
    def _crear_pestania_cristales(self):
        """Crea la pestaña de cristales."""
        widget_cristales = QWidget()
        layout_cristales = QVBoxLayout(widget_cristales)
        
        # Barra de herramientas
        barra_cristales = QHBoxLayout()
        
        boton_agregar_cristal = QPushButton("Agregar Cristal")
        boton_agregar_cristal.clicked.connect(self._agregar_cristal)
        barra_cristales.addWidget(boton_agregar_cristal)
        
        boton_eliminar_cristal = QPushButton("Eliminar")
        boton_eliminar_cristal.clicked.connect(self._eliminar_cristal)
        barra_cristales.addWidget(boton_eliminar_cristal)
        
        barra_cristales.addStretch()
        layout_cristales.addLayout(barra_cristales)
        
        # Tabla de cristales
        self.tabla_cristales = QTableWidget()
        self.tabla_cristales.setColumnCount(6)
        self.tabla_cristales.setHorizontalHeaderLabels([
            "Cristal", "Medida", "Condición", "Cantidad", "Fórmula Cant.", "Orden"
        ])
        
        # Configurar tabla
        header = self.tabla_cristales.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        
        layout_cristales.addWidget(self.tabla_cristales)
        
        self.pestanias_detalles.addTab(widget_cristales, "Cristales")
    
    # def _crear_pestania_persianas(self):
    #     """Crea la pestaña de persianas."""
    #     widget_persianas = QWidget()
    #     layout_persianas = QVBoxLayout(widget_persianas)
    #
    #     # Barra de herramientas
    #     barra_persianas = QHBoxLayout()
    #
    #     boton_agregar_persiana = QPushButton("Agregar Persiana")
    #     boton_agregar_persiana.clicked.connect(self._agregar_persiana)
    #     barra_persianas.addWidget(boton_agregar_persiana)
    #
    #     boton_eliminar_persiana = QPushButton("Eliminar")
    #     boton_eliminar_persiana.clicked.connect(self._eliminar_persiana)
    #     barra_persianas.addWidget(boton_eliminar_persiana)
    #
    #     barra_persianas.addStretch()
    #     layout_persianas.addLayout(barra_persianas)
    #
    #     # Tabla de persianas
    #     self.tabla_persianas = QTableWidget()
    #     self.tabla_persianas.setColumnCount(6)
    #     self.tabla_persianas.setHorizontalHeaderLabels([
    #         "Persiana", "Medida", "Condición", "Cantidad", "Fórmula Cant.", "Orden"
    #     ])
    #
    #     # Configurar tabla
    #     header = self.tabla_persianas.horizontalHeader()
    #     header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
    #     header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
    #     header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
    #     header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
    #     header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
    #     header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
    #
    #     layout_persianas.addWidget(self.tabla_persianas)
    #
    #     self.pestanias_detalles.addTab(widget_persianas, "Persianas")
    
    def _crear_pestania_simulador(self):
        """Crea la pestaña del simulador de medidas."""
        widget_simulador = QWidget()
        layout_simulador = QVBoxLayout(widget_simulador)
        
        # Grupo de entrada de medidas
        grupo_medidas = QGroupBox("Medidas de Prueba")
        layout_medidas = QFormLayout(grupo_medidas)
        
        self.campo_altura_sim = QSpinBox()
        self.campo_altura_sim.setRange(100, 5000)
        self.campo_altura_sim.setValue(1200)
        self.campo_altura_sim.setSuffix(" mm")
        layout_medidas.addRow("Altura:", self.campo_altura_sim)
        
        self.campo_anchura_sim = QSpinBox()
        self.campo_anchura_sim.setRange(100, 5000)
        self.campo_anchura_sim.setValue(800)
        self.campo_anchura_sim.setSuffix(" mm")
        layout_medidas.addRow("Anchura:", self.campo_anchura_sim)
        
        boton_calcular = QPushButton("Calcular Materiales")
        boton_calcular.clicked.connect(self._calcular_materiales)
        layout_medidas.addRow("", boton_calcular)
        
        layout_simulador.addWidget(grupo_medidas)
        
        # Área de resultados
        self.texto_resultados = QTextEdit()
        self.texto_resultados.setReadOnly(True)
        self.texto_resultados.setFont(QFont("Courier", 9))
        layout_simulador.addWidget(self.texto_resultados)
        
        self.pestanias_detalles.addTab(widget_simulador, "Simulador")
    
    def _crear_botones_accion(self, layout_principal):
        """Crea los botones de acción."""
        layout_botones = QHBoxLayout()
        
        # Espaciador
        layout_botones.addStretch()
        
        # Botón de cerrar
        boton_cerrar = QPushButton("Cerrar")
        boton_cerrar.clicked.connect(self.close)
        layout_botones.addWidget(boton_cerrar)
        
        layout_principal.addLayout(layout_botones)

    def _cargar_articulos(self):
        """Carga la lista de artículos desde la base de datos."""
        db = next(get_db())

        try:
            # Limpiar la lista
            self.lista_articulos.clear()

            # Obtener todos los artículos
            self.articulos = db.query(Articulo).order_by(Articulo.codigo).all()

            # Llenar la lista
            for articulo in self.articulos:
                item = QTreeWidgetItem([
                    articulo.codigo,
                    articulo.descripcion,
                    articulo.serie or ""
                ])
                item.setData(0, Qt.ItemDataRole.UserRole, articulo.id)
                self.lista_articulos.addTopLevelItem(item)

            # Actualizar el título
            self.setWindowTitle(f"Gestión de Artículos ({len(self.articulos)} artículos)")

        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Error al cargar los artículos: {str(e)}"
            )
        finally:
            db.close()

    def _exportar_articulos(self):
        """Exporta la lista de artículos a CSV."""
        try:
            import csv
            from datetime import datetime

            # Seleccionar archivo
            archivo, _ = QFileDialog.getSaveFileName(
                self,
                "Exportar Artículos",
                f"articulos_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "Archivos CSV (*.csv);;Todos los archivos (*)"
            )

            if not archivo:
                return

            # Exportar datos
            with open(archivo, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # Escribir encabezados
                writer.writerow([
                    'Código', 'Serie', 'Descripción', 'Tiempo Taller',
                    'Tiempo Obra', 'Dibujo', 'Imagen', 'Activo', 'Fecha Creación'
                ])

                # Escribir datos
                for articulo in self.articulos:
                    writer.writerow([
                        articulo.codigo,
                        articulo.serie or '',
                        articulo.descripcion,
                        articulo.tiempo_taller or 0,
                        articulo.tiempo_obra or 0,
                        articulo.dibujo_path or '',
                        articulo.imagen_path or '',
                        'Sí' if articulo.activo else 'No',
                        articulo.fecha_creacion.strftime('%Y-%m-%d %H:%M:%S') if articulo.fecha_creacion else ''
                    ])

            QMessageBox.information(
                self,
                "Exportación Exitosa",
                f"Se han exportado {len(self.articulos)} artículos a:\n{archivo}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "Error de Exportación",
                f"Error al exportar artículos: {str(e)}"
            )

    def _filtrar_articulos(self):
        """Filtra los artículos según el texto de búsqueda."""
        texto_busqueda = self.campo_busqueda.text().lower()

        for i in range(self.lista_articulos.topLevelItemCount()):
            item = self.lista_articulos.topLevelItem(i)

            # Buscar en código, descripción y serie
            codigo = item.text(0).lower()
            descripcion = item.text(1).lower()
            serie = item.text(2).lower()

            visible = (texto_busqueda in codigo or
                      texto_busqueda in descripcion or
                      texto_busqueda in serie)

            item.setHidden(not visible)

    def _validar_codigo(self):
        """Valida el código del artículo en tiempo real."""
        codigo = self.campo_codigo.text().strip().upper()

        if not codigo:
            self.label_validacion_codigo.setText("")
            self.campo_codigo.setStyleSheet("""
                QLineEdit {
                    padding: 6px;
                    border: 2px solid #ced4da;
                    border-radius: 4px;
                }
            """)
            return

        # Validar formato (letras y números)
        import re
        if not re.match(r'^[A-Z0-9]+$', codigo):
            self.label_validacion_codigo.setText("❌ Solo letras y números permitidos")
            self.campo_codigo.setStyleSheet("""
                QLineEdit {
                    padding: 6px;
                    border: 2px solid #dc3545;
                    border-radius: 4px;
                    background-color: #f8d7da;
                }
            """)
            return

        # Verificar si ya existe (solo si no es el artículo actual)
        if self._codigo_existe(codigo):
            self.label_validacion_codigo.setText("❌ Este código ya existe")
            self.campo_codigo.setStyleSheet("""
                QLineEdit {
                    padding: 6px;
                    border: 2px solid #dc3545;
                    border-radius: 4px;
                    background-color: #f8d7da;
                }
            """)
        else:
            self.label_validacion_codigo.setText("✅ Código válido")
            self.campo_codigo.setStyleSheet("""
                QLineEdit {
                    padding: 6px;
                    border: 2px solid #198754;
                    border-radius: 4px;
                    background-color: #d1e7dd;
                }
            """)

    def _codigo_existe(self, codigo):
        """Verifica si un código ya existe."""
        if not codigo:
            return False

        # Si estamos editando, excluir el artículo actual
        articulo_actual_id = None
        if self.articulo_actual:
            articulo_actual_id = self.articulo_actual.id

        for articulo in self.articulos:
            if articulo.codigo.upper() == codigo.upper() and articulo.id != articulo_actual_id:
                return True

        return False

    def _actualizar_contador_descripcion(self):
        """Actualiza el contador de caracteres de la descripción."""
        texto = self.campo_descripcion.text()
        longitud = len(texto)
        max_longitud = 200

        self.label_contador_descripcion.setText(f"{longitud}/{max_longitud} caracteres")

        if longitud > max_longitud * 0.9:  # 90% del límite
            self.label_contador_descripcion.setStyleSheet("color: #dc3545; font-size: 11px; font-weight: bold;")
        elif longitud > max_longitud * 0.7:  # 70% del límite
            self.label_contador_descripcion.setStyleSheet("color: #ffc107; font-size: 11px;")
        else:
            self.label_contador_descripcion.setStyleSheet("color: #6c757d; font-size: 11px;")

    def _seleccionar_dibujo(self):
        """Selecciona un archivo de dibujo."""
        archivo, _ = QFileDialog.getOpenFileName(
            self,
            "Seleccionar Archivo de Dibujo",
            "",
            "Archivos de dibujo (*.dwg *.dxf *.pdf);;Todos los archivos (*)"
        )

        if archivo:
            self.campo_dibujo.setText(archivo)

    def _seleccionar_imagen(self):
        """Selecciona una imagen para el artículo."""
        archivo, _ = QFileDialog.getOpenFileName(
            self,
            "Seleccionar Imagen del Artículo",
            "",
            "Archivos de imagen (*.png *.jpg *.jpeg *.bmp *.gif);;Todos los archivos (*)"
        )

        if archivo:
            self.campo_imagen.setText(archivo)
            self._actualizar_preview_imagen(archivo)

    def _actualizar_preview_imagen(self, ruta_imagen):
        """Actualiza la vista previa de la imagen."""
        try:
            if ruta_imagen and os.path.exists(ruta_imagen):
                pixmap = QPixmap(ruta_imagen)
                if not pixmap.isNull():
                    # Escalar imagen manteniendo proporción
                    pixmap_escalado = pixmap.scaled(
                        150, 150,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    self.label_preview_imagen.setPixmap(pixmap_escalado)
                    self.label_preview_imagen.setText("")
                else:
                    self.label_preview_imagen.clear()
                    self.label_preview_imagen.setText("❌ Error al cargar imagen")
            else:
                self.label_preview_imagen.clear()
                self.label_preview_imagen.setText("Sin imagen seleccionada")
        except Exception as e:
            self.label_preview_imagen.clear()
            self.label_preview_imagen.setText(f"❌ Error: {str(e)}")

    def _on_articulo_seleccionado(self):
        """Maneja la selección de un artículo."""
        items_seleccionados = self.lista_articulos.selectedItems()

        if items_seleccionados:
            item = items_seleccionados[0]
            articulo_id = item.data(0, Qt.ItemDataRole.UserRole)

            # Buscar el artículo en la lista
            self.articulo_actual = next((a for a in self.articulos if a.id == articulo_id), None)

            if self.articulo_actual:
                self._cargar_detalles_articulo()
                self.pestanias_detalles.setVisible(True)
                self.boton_duplicar.setEnabled(True)
                self.boton_eliminar.setEnabled(True)
        else:
            self.articulo_actual = None
            self.pestanias_detalles.setVisible(False)
            self.titulo_detalles.setText("Seleccione un artículo")
            self.boton_duplicar.setEnabled(False)
            self.boton_eliminar.setEnabled(False)

    def _cargar_detalles_articulo(self):
        """Carga los detalles del artículo seleccionado."""
        if not self.articulo_actual:
            return

        # Actualizar título
        self.titulo_detalles.setText(f"Artículo: {self.articulo_actual.codigo} - {self.articulo_actual.descripcion}")

        # Cargar datos generales
        self.campo_codigo.setText(self.articulo_actual.codigo)
        self.campo_serie.setText(self.articulo_actual.serie or "")
        self.campo_descripcion.setText(self.articulo_actual.descripcion)
        self.campo_tiempo_taller.setValue(self.articulo_actual.tiempo_taller or 0.0)
        self.campo_tiempo_obra.setValue(self.articulo_actual.tiempo_obra or 0.0)
        self.campo_dibujo.setText(self.articulo_actual.dibujo_path or "")
        self.campo_imagen.setText(self.articulo_actual.imagen_path or "")
        self.campo_activo.setChecked(self.articulo_actual.activo)

        # Actualizar la vista previa de la imagen
        self._actualizar_preview_imagen(self.articulo_actual.imagen_path or "")

        # Cargar componentes
        self._cargar_perfiles_articulo()
        self._cargar_accesorios_articulo()
        self._cargar_cristales_articulo()
        # self._cargar_persianas_articulo()  # Temporalmente deshabilitado

    def nuevo_articulo(self):
        """Crea un nuevo artículo."""
        from .articulo_editar_dialog import ArticuloEditarDialog

        dialog = ArticuloEditarDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self._cargar_articulos()

    def duplicar_articulo(self):
        """Duplica el artículo seleccionado."""
        if not self.articulo_actual:
            return

        from .articulo_editar_dialog import ArticuloEditarDialog

        dialog = ArticuloEditarDialog(self, self.articulo_actual, duplicar=True)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self._cargar_articulos()

    def eliminar_articulo(self):
        """Elimina el artículo seleccionado."""
        if not self.articulo_actual:
            return

        respuesta = QMessageBox.question(
            self,
            "Confirmar eliminación",
            f"¿Está seguro de que desea eliminar el artículo '{self.articulo_actual.codigo}'?\n"
            "Esta acción eliminará también todos sus componentes y no se puede deshacer.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if respuesta == QMessageBox.StandardButton.Yes:
            db = next(get_db())
            try:
                articulo_db = db.query(Articulo).filter(Articulo.id == self.articulo_actual.id).first()
                if articulo_db:
                    db.delete(articulo_db)
                    db.commit()

                    QMessageBox.information(
                        self,
                        "Éxito",
                        "El artículo ha sido eliminado correctamente."
                    )

                    self._cargar_articulos()

            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self,
                    "Error",
                    f"No se pudo eliminar el artículo: {str(e)}"
                )
            finally:
                db.close()

    def _guardar_datos_generales(self):
        """Guarda los datos generales del artículo."""
        if not self.articulo_actual:
            return

        # Validar datos
        if not self.campo_codigo.text().strip():
            QMessageBox.warning(self, "Error", "El código es obligatorio.")
            return

        if not self.campo_descripcion.text().strip():
            QMessageBox.warning(self, "Error", "La descripción es obligatoria.")
            return

        db = next(get_db())

        try:
            # Verificar que el código no esté duplicado
            if self.campo_codigo.text().strip() != self.articulo_actual.codigo:
                existe = db.query(Articulo).filter(
                    Articulo.codigo == self.campo_codigo.text().strip(),
                    Articulo.id != self.articulo_actual.id
                ).first()

                if existe:
                    QMessageBox.warning(self, "Error", "Ya existe un artículo con este código.")
                    return

            # Actualizar el artículo
            articulo_db = db.query(Articulo).filter(Articulo.id == self.articulo_actual.id).first()
            if articulo_db:
                articulo_db.codigo = self.campo_codigo.text().strip()
                articulo_db.serie = self.campo_serie.text().strip() or None
                articulo_db.descripcion = self.campo_descripcion.text().strip()
                articulo_db.tiempo_taller = self.campo_tiempo_taller.value()
                articulo_db.tiempo_obra = self.campo_tiempo_obra.value()
                articulo_db.dibujo_path = self.campo_dibujo.text().strip() or None
                articulo_db.imagen_path = self.campo_imagen.text().strip() or None  # <-- Guardar imagen
                articulo_db.activo = self.campo_activo.isChecked()

                db.commit()

                # Actualizar el objeto local
                self.articulo_actual.codigo = articulo_db.codigo
                self.articulo_actual.serie = articulo_db.serie
                self.articulo_actual.descripcion = articulo_db.descripcion
                self.articulo_actual.tiempo_taller = articulo_db.tiempo_taller
                self.articulo_actual.tiempo_obra = articulo_db.tiempo_obra
                self.articulo_actual.dibujo_path = articulo_db.dibujo_path
                self.articulo_actual.imagen_path = articulo_db.imagen_path  # <-- Actualizar local
                self.articulo_actual.activo = articulo_db.activo

                QMessageBox.information(
                    self,
                    "Éxito",
                    "Los datos generales han sido guardados correctamente."
                )

                # Recargar la lista para reflejar los cambios
                self._cargar_articulos()

        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudieron guardar los datos: {str(e)}"
            )
        finally:
            db.close()

    def _cargar_perfiles_articulo(self):
        """Carga los perfiles del artículo en la tabla."""
        if not self.articulo_actual:
            return

        db = next(get_db())

        try:
            # Obtener perfiles del artículo
            perfiles = db.query(ArticuloPerfil).filter(
                ArticuloPerfil.articulo_id == self.articulo_actual.id
            ).order_by(ArticuloPerfil.orden).all()

            # Configurar la tabla
            self.tabla_perfiles.setRowCount(len(perfiles))

            for fila, perfil_comp in enumerate(perfiles):
                # Perfil
                item_perfil = QTableWidgetItem(perfil_comp.perfil.descripcion if perfil_comp.perfil else "")
                item_perfil.setData(Qt.ItemDataRole.UserRole, perfil_comp.id)
                self.tabla_perfiles.setItem(fila, 0, item_perfil)

                # Tipo de perfil
                tipo_perfil = getattr(perfil_comp, 'tipo_perfil', 'marco') or 'marco'
                self.tabla_perfiles.setItem(fila, 1, QTableWidgetItem(tipo_perfil.title()))

                # Medida
                self.tabla_perfiles.setItem(fila, 2, QTableWidgetItem(perfil_comp.medida_formula or ""))

                # Variable de medida
                variable_medida = getattr(perfil_comp, 'variable_medida', 'H') or 'H'
                self.tabla_perfiles.setItem(fila, 3, QTableWidgetItem(variable_medida))

                # Ángulos de corte
                angulo_izq = getattr(perfil_comp, 'angulo_izquierdo', 90.0) or 90.0
                angulo_der = getattr(perfil_comp, 'angulo_derecho', 90.0) or 90.0
                angulos_texto = f"{angulo_izq:.0f}°/{angulo_der:.0f}°"
                self.tabla_perfiles.setItem(fila, 4, QTableWidgetItem(angulos_texto))

                # Condición
                self.tabla_perfiles.setItem(fila, 5, QTableWidgetItem(perfil_comp.condicion or ""))

                # Cantidad
                self.tabla_perfiles.setItem(fila, 6, QTableWidgetItem(str(perfil_comp.cantidad_base)))

                # Fórmula cantidad
                self.tabla_perfiles.setItem(fila, 7, QTableWidgetItem(perfil_comp.cantidad_formula or ""))

                # Orden
                self.tabla_perfiles.setItem(fila, 8, QTableWidgetItem(str(perfil_comp.orden)))

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al cargar perfiles: {str(e)}")
        finally:
            db.close()

    def _cargar_accesorios_articulo(self):
        """Carga los accesorios del artículo en la tabla."""
        if not self.articulo_actual:
            return

        db = next(get_db())

        try:
            # Obtener accesorios del artículo
            accesorios = db.query(ArticuloAccesorio).filter(
                ArticuloAccesorio.articulo_id == self.articulo_actual.id
            ).order_by(ArticuloAccesorio.orden).all()

            # Configurar la tabla
            self.tabla_accesorios.setRowCount(len(accesorios))

            for fila, acc_comp in enumerate(accesorios):
                # Accesorio
                item_accesorio = QTableWidgetItem(acc_comp.accesorio.descripcion if acc_comp.accesorio else "")
                item_accesorio.setData(Qt.ItemDataRole.UserRole, acc_comp.id)
                self.tabla_accesorios.setItem(fila, 0, item_accesorio)

                # Condición
                self.tabla_accesorios.setItem(fila, 1, QTableWidgetItem(acc_comp.condicion or ""))

                # Cantidad
                self.tabla_accesorios.setItem(fila, 2, QTableWidgetItem(str(acc_comp.cantidad_base)))

                # Fórmula cantidad
                self.tabla_accesorios.setItem(fila, 3, QTableWidgetItem(acc_comp.cantidad_formula or ""))

                # Orden
                self.tabla_accesorios.setItem(fila, 4, QTableWidgetItem(str(acc_comp.orden)))

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al cargar accesorios: {str(e)}")
        finally:
            db.close()

    def _cargar_cristales_articulo(self):
        """Carga los cristales del artículo en la tabla."""
        if not self.articulo_actual:
            return

        db = next(get_db())

        try:
            # Obtener cristales del artículo
            cristales = db.query(ArticuloCristal).filter(
                ArticuloCristal.articulo_id == self.articulo_actual.id
            ).order_by(ArticuloCristal.orden).all()

            # Configurar la tabla
            self.tabla_cristales.setRowCount(len(cristales))

            for fila, cristal_comp in enumerate(cristales):
                # Cristal
                item_cristal = QTableWidgetItem(cristal_comp.cristal.descripcion if cristal_comp.cristal else "")
                item_cristal.setData(Qt.ItemDataRole.UserRole, cristal_comp.id)
                self.tabla_cristales.setItem(fila, 0, item_cristal)

                # Medida
                self.tabla_cristales.setItem(fila, 1, QTableWidgetItem(cristal_comp.medida_formula or ""))

                # Condición
                self.tabla_cristales.setItem(fila, 2, QTableWidgetItem(cristal_comp.condicion or ""))

                # Cantidad
                self.tabla_cristales.setItem(fila, 3, QTableWidgetItem(str(cristal_comp.cantidad_base)))

                # Fórmula cantidad
                self.tabla_cristales.setItem(fila, 4, QTableWidgetItem(cristal_comp.cantidad_formula or ""))

                # Orden
                self.tabla_cristales.setItem(fila, 5, QTableWidgetItem(str(cristal_comp.orden)))

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al cargar cristales: {str(e)}")
        finally:
            db.close()

    # def _cargar_persianas_articulo(self):
    #     """Carga las persianas del artículo en la tabla."""
    #     if not self.articulo_actual:
    #         return
    #
    #     db = next(get_db())
    #
    #     try:
    #         # Obtener persianas del artículo
    #         persianas = db.query(ArticuloPersiana).filter(
    #             ArticuloPersiana.articulo_id == self.articulo_actual.id
    #         ).order_by(ArticuloPersiana.orden).all()
    #
    #         # Configurar la tabla
    #         self.tabla_persianas.setRowCount(len(persianas))
    #
    #         for fila, persiana_comp in enumerate(persianas):
    #             # Persiana
    #             item_persiana = QTableWidgetItem(persiana_comp.persiana.descripcion if persiana_comp.persiana else "")
    #             item_persiana.setData(Qt.ItemDataRole.UserRole, persiana_comp.id)
    #             self.tabla_persianas.setItem(fila, 0, item_persiana)
    #
    #             # Medida
    #             self.tabla_persianas.setItem(fila, 1, QTableWidgetItem(persiana_comp.medida_formula or ""))
    #
    #             # Condición
    #             self.tabla_persianas.setItem(fila, 2, QTableWidgetItem(persiana_comp.condicion or ""))
    #
    #             # Cantidad
    #             self.tabla_persianas.setItem(fila, 3, QTableWidgetItem(str(persiana_comp.cantidad_base)))
    #
    #             # Fórmula cantidad
    #             self.tabla_persianas.setItem(fila, 4, QTableWidgetItem(persiana_comp.cantidad_formula or ""))
    #
    #             # Orden
    #             self.tabla_persianas.setItem(fila, 5, QTableWidgetItem(str(persiana_comp.orden)))
    #
    #     except Exception as e:
    #         QMessageBox.critical(self, "Error", f"Error al cargar persianas: {str(e)}")
    #     finally:
    #         db.close()

    def _calcular_materiales(self):
        """Calcula los materiales necesarios con las medidas del simulador."""
        if not self.articulo_actual:
            self.texto_resultados.setText("No hay artículo seleccionado.")
            return

        altura = self.campo_altura_sim.value()
        anchura = self.campo_anchura_sim.value()

        try:
            materiales = self.articulo_actual.calcular_materiales(altura, anchura)

            # Formatear resultados
            resultado = f"CÁLCULO DE MATERIALES\n"
            resultado += f"Artículo: {self.articulo_actual.codigo} - {self.articulo_actual.descripcion}\n"
            resultado += f"Medidas: {altura} x {anchura} mm\n"
            resultado += f"Variables: H={altura}, A={anchura}, L={max(altura, anchura)}, P={2*altura + 2*anchura}, S={altura*anchura}\n"
            resultado += "=" * 80 + "\n\n"

            # Perfiles
            if materiales['perfiles']:
                resultado += "PERFILES:\n"
                resultado += "-" * 40 + "\n"
                total_metros = 0
                for material in materiales['perfiles']:
                    perfil = material['perfil']
                    cantidad = material['cantidad']
                    medida = material['medida']
                    metros = material['metros_totales']
                    total_metros += metros

                    resultado += f"{perfil.codigo}: {perfil.descripcion}\n"
                    resultado += f"  Cantidad: {cantidad:.2f} uds\n"
                    if medida:
                        resultado += f"  Medida: {medida:.0f} mm\n"
                    resultado += f"  Metros: {metros:.3f} m\n"
                    if perfil.precio_metro:
                        precio = metros * perfil.precio_metro
                        resultado += f"  Precio: {precio:.2f} €\n"
                    resultado += "\n"

                resultado += f"Total metros perfiles: {total_metros:.3f} m\n\n"

            # Accesorios
            if materiales['accesorios']:
                resultado += "ACCESORIOS:\n"
                resultado += "-" * 40 + "\n"
                for material in materiales['accesorios']:
                    accesorio = material['accesorio']
                    cantidad = material['cantidad']

                    resultado += f"{accesorio.codigo}: {accesorio.descripcion}\n"
                    resultado += f"  Cantidad: {cantidad:.2f} uds\n"
                    if accesorio.precio:
                        precio = cantidad * accesorio.precio
                        resultado += f"  Precio: {precio:.2f} €\n"
                    resultado += "\n"

            # Cristales
            if materiales['cristales']:
                resultado += "CRISTALES:\n"
                resultado += "-" * 40 + "\n"
                total_m2 = 0
                for material in materiales['cristales']:
                    cristal = material['cristal']
                    cantidad = material['cantidad']
                    medida = material['medida']
                    m2 = material['metros_cuadrados']
                    total_m2 += m2

                    resultado += f"{cristal.codigo}: {cristal.descripcion}\n"
                    resultado += f"  Cantidad: {cantidad:.2f} uds\n"
                    if medida:
                        resultado += f"  Superficie: {medida:.0f} mm²\n"
                    resultado += f"  Metros²: {m2:.3f} m²\n"
                    if cristal.precio_metro_cuadrado:
                        precio = m2 * cristal.precio_metro_cuadrado
                        resultado += f"  Precio: {precio:.2f} €\n"
                    resultado += "\n"

                resultado += f"Total metros² cristales: {total_m2:.3f} m²\n\n"

            # Persianas
            if materiales['persianas']:
                resultado += "PERSIANAS:\n"
                resultado += "-" * 40 + "\n"
                for material in materiales['persianas']:
                    persiana = material['persiana']
                    cantidad = material['cantidad']
                    medida = material['medida']

                    resultado += f"{persiana.codigo}: {persiana.descripcion}\n"
                    resultado += f"  Cantidad: {cantidad:.2f} uds\n"
                    if medida:
                        resultado += f"  Medida: {medida:.0f} mm\n"
                    if persiana.precio:
                        precio = cantidad * persiana.precio
                        resultado += f"  Precio: {precio:.2f} €\n"
                    resultado += "\n"

            self.texto_resultados.setText(resultado)

        except Exception as e:
            self.texto_resultados.setText(f"Error al calcular materiales: {str(e)}")

    # Métodos para agregar/eliminar componentes
    def _agregar_perfil(self):
        """Agregar perfil al artículo."""
        if not self.articulo_actual:
            QMessageBox.warning(self, "Error", "No hay artículo seleccionado.")
            return

        # Usar el diálogo profesional mejorado
        from .perfil_profesional_dialog import PerfilProfesionalDialog

        dialog = PerfilProfesionalDialog(self, self.articulo_actual)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self._cargar_perfiles_articulo()

    def _editar_perfil(self):
        """Editar perfil del artículo."""
        if not self.articulo_actual:
            QMessageBox.warning(self, "Error", "No hay artículo seleccionado.")
            return

        fila_actual = self.tabla_perfiles.currentRow()
        if fila_actual < 0:
            QMessageBox.warning(self, "Error", "Seleccione un perfil para editar.")
            return

        item = self.tabla_perfiles.item(fila_actual, 0)
        if not item:
            return

        componente_id = item.data(Qt.ItemDataRole.UserRole)
        if not componente_id:
            return

        # Obtener el componente de la base de datos
        db = next(get_db())
        try:
            componente = db.query(ArticuloPerfil).filter(ArticuloPerfil.id == componente_id).first()
            if not componente:
                QMessageBox.warning(self, "Error", "No se encontró el perfil.")
                return

            # Usar el diálogo profesional para editar
            from .perfil_profesional_dialog import PerfilProfesionalDialog

            dialog = PerfilProfesionalDialog(self, self.articulo_actual, componente)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self._cargar_perfiles_articulo()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al editar perfil: {str(e)}")
        finally:
            db.close()

    def _eliminar_perfil(self):
        """Eliminar perfil del artículo."""
        if not self.articulo_actual:
            return

        fila_actual = self.tabla_perfiles.currentRow()
        if fila_actual < 0:
            QMessageBox.warning(self, "Error", "Seleccione un perfil para eliminar.")
            return

        item = self.tabla_perfiles.item(fila_actual, 0)
        if not item:
            return

        componente_id = item.data(Qt.ItemDataRole.UserRole)
        if not componente_id:
            return

        respuesta = QMessageBox.question(
            self,
            "Confirmar eliminación",
            "¿Está seguro de que desea eliminar este perfil del artículo?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if respuesta == QMessageBox.StandardButton.Yes:
            db = next(get_db())
            try:
                componente = db.query(ArticuloPerfil).filter(ArticuloPerfil.id == componente_id).first()
                if componente:
                    db.delete(componente)
                    db.commit()
                    self._cargar_perfiles_articulo()
                    QMessageBox.information(self, "Éxito", "Perfil eliminado correctamente.")
            except Exception as e:
                db.rollback()
                QMessageBox.critical(self, "Error", f"Error al eliminar perfil: {str(e)}")
            finally:
                db.close()

    def _agregar_accesorio(self):
        """Agregar accesorio al artículo."""
        if not self.articulo_actual:
            QMessageBox.warning(self, "Error", "No hay artículo seleccionado.")
            return

        from .componente_dialog import ComponenteAccesorioDialog

        dialog = ComponenteAccesorioDialog(self, self.articulo_actual)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self._cargar_accesorios_articulo()

    def _eliminar_accesorio(self):
        """Eliminar accesorio del artículo."""
        if not self.articulo_actual:
            return

        fila_actual = self.tabla_accesorios.currentRow()
        if fila_actual < 0:
            QMessageBox.warning(self, "Error", "Seleccione un accesorio para eliminar.")
            return

        item = self.tabla_accesorios.item(fila_actual, 0)
        if not item:
            return

        componente_id = item.data(Qt.ItemDataRole.UserRole)
        if not componente_id:
            return

        respuesta = QMessageBox.question(
            self,
            "Confirmar eliminación",
            "¿Está seguro de que desea eliminar este accesorio del artículo?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if respuesta == QMessageBox.StandardButton.Yes:
            db = next(get_db())
            try:
                componente = db.query(ArticuloAccesorio).filter(ArticuloAccesorio.id == componente_id).first()
                if componente:
                    db.delete(componente)
                    db.commit()
                    self._cargar_accesorios_articulo()
                    QMessageBox.information(self, "Éxito", "Accesorio eliminado correctamente.")
            except Exception as e:
                db.rollback()
                QMessageBox.critical(self, "Error", f"Error al eliminar accesorio: {str(e)}")
            finally:
                db.close()

    def _agregar_cristal(self):
        """Agregar cristal al artículo."""
        if not self.articulo_actual:
            QMessageBox.warning(self, "Error", "No hay artículo seleccionado.")
            return

        from .componente_dialog import ComponenteCristalDialog

        dialog = ComponenteCristalDialog(self, self.articulo_actual)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self._cargar_cristales_articulo()

    def _eliminar_cristal(self):
        """Eliminar cristal del artículo."""
        if not self.articulo_actual:
            return

        fila_actual = self.tabla_cristales.currentRow()
        if fila_actual < 0:
            QMessageBox.warning(self, "Error", "Seleccione un cristal para eliminar.")
            return

        item = self.tabla_cristales.item(fila_actual, 0)
        if not item:
            return

        componente_id = item.data(Qt.ItemDataRole.UserRole)
        if not componente_id:
            return

        respuesta = QMessageBox.question(
            self,
            "Confirmar eliminación",
            "¿Está seguro de que desea eliminar este cristal del artículo?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if respuesta == QMessageBox.StandardButton.Yes:
            db = next(get_db())
            try:
                componente = db.query(ArticuloCristal).filter(ArticuloCristal.id == componente_id).first()
                if componente:
                    db.delete(componente)
                    db.commit()
                    self._cargar_cristales_articulo()
                    QMessageBox.information(self, "Éxito", "Cristal eliminado correctamente.")
            except Exception as e:
                db.rollback()
                QMessageBox.critical(self, "Error", f"Error al eliminar cristal: {str(e)}")
            finally:
                db.close()

    def _agregar_persiana(self):
        """Agregar persiana al artículo."""
        QMessageBox.information(self, "Info", "Funcionalidad de persianas temporalmente deshabilitada.")

    def _eliminar_persiana(self):
        """Eliminar persiana del artículo."""
        QMessageBox.information(self, "Info", "Funcionalidad de persianas temporalmente deshabilitada.")
