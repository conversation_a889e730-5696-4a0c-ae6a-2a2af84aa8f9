"""
Sistema de temas moderno y elegante para PRO-2000
Inspirado en aplicaciones profesionales como PrefSuite
"""

import qtawesome as qta
from PyQt6.QtCore import QPropertyAnimation, QEasingCurve, QRect, pyqtSignal, QTimer
from PyQt6.QtWidgets import QGraphicsDropShadowEffect, QWidget
from PyQt6.QtGui import QColor, QPalette, QFont
import qdarkstyle


class ModernTheme:
    """Tema moderno profesional para aplicaciones de gestión"""
    
    # Colores principales
    PRIMARY_BLUE = "#2563eb"      # Azul profesional
    PRIMARY_DARK = "#1e40af"      # Azul oscuro
    SECONDARY_BLUE = "#3b82f6"    # Azul secundario
    ACCENT_BLUE = "#60a5fa"       # Azul claro
    
    # Colores de fondo
    BG_DARK = "#0f172a"          # Fondo oscuro principal
    BG_DARK_SECONDARY = "#1e293b" # Fondo oscuro secundario
    BG_LIGHT = "#ffffff"         # Fondo claro principal
    BG_LIGHT_SECONDARY = "#f8fafc" # Fondo claro secundario
    
    # Colores de superficie
    SURFACE_DARK = "#334155"     # Superficie oscura
    SURFACE_LIGHT = "#e2e8f0"    # Superficie clara
    
    # Colores de texto
    TEXT_PRIMARY_DARK = "#f1f5f9"    # Texto principal oscuro
    TEXT_SECONDARY_DARK = "#cbd5e1"  # Texto secundario oscuro
    TEXT_PRIMARY_LIGHT = "#0f172a"   # Texto principal claro
    TEXT_SECONDARY_LIGHT = "#475569" # Texto secundario claro
    
    # Colores de estado
    SUCCESS = "#10b981"          # Verde éxito
    WARNING = "#f59e0b"          # Amarillo advertencia
    ERROR = "#ef4444"            # Rojo error
    INFO = "#3b82f6"             # Azul información
    
    # Colores de bordes
    BORDER_DARK = "#475569"      # Borde oscuro
    BORDER_LIGHT = "#e2e8f0"     # Borde claro
    
    # Sombras
    SHADOW_LIGHT = "0 1px 3px rgba(0, 0, 0, 0.1)"
    SHADOW_MEDIUM = "0 4px 6px rgba(0, 0, 0, 0.1)"
    SHADOW_HEAVY = "0 10px 15px rgba(0, 0, 0, 0.1)"
    
    # Radios de borde
    RADIUS_SMALL = "4px"
    RADIUS_MEDIUM = "8px"
    RADIUS_LARGE = "12px"
    
    # Espaciado
    SPACING_XS = "4px"
    SPACING_SM = "8px"
    SPACING_MD = "16px"
    SPACING_LG = "24px"
    SPACING_XL = "32px"
    
    # Fuentes
    FONT_FAMILY = "Segoe UI, system-ui, sans-serif"
    FONT_SIZE_SM = "12px"
    FONT_SIZE_MD = "14px"
    FONT_SIZE_LG = "16px"
    FONT_SIZE_XL = "18px"
    FONT_SIZE_XXL = "24px"
    
    @classmethod
    def get_dark_theme_css(cls):
        """Retorna el CSS para el tema oscuro"""
        return f"""
        /* === TEMA OSCURO MODERNO === */
        
        /* Ventana principal */
        QMainWindow {{
            background-color: {cls.BG_DARK};
            color: {cls.TEXT_PRIMARY_DARK};
            font-family: {cls.FONT_FAMILY};
            font-size: {cls.FONT_SIZE_MD};
        }}
        
        /* Sidebar de navegación */
        QFrame#sidebar {{
            background-color: {cls.BG_DARK_SECONDARY};
            border-right: 1px solid {cls.BORDER_DARK};
            min-width: 250px;
            max-width: 250px;
        }}
        
        /* Botones del sidebar */
        QPushButton#sidebar_button {{
            background-color: transparent;
            border: none;
            color: {cls.TEXT_SECONDARY_DARK};
            text-align: left;
            padding: {cls.SPACING_MD};
            margin: {cls.SPACING_XS};
            border-radius: {cls.RADIUS_MEDIUM};
            font-size: {cls.FONT_SIZE_MD};
            font-weight: 500;
        }}
        
        QPushButton#sidebar_button:hover {{
            background-color: {cls.SURFACE_DARK};
            color: {cls.TEXT_PRIMARY_DARK};
        }}
        
        QPushButton#sidebar_button:pressed {{
            background-color: {cls.PRIMARY_DARK};
            color: white;
        }}
        
        QPushButton#sidebar_button_active {{
            background-color: {cls.PRIMARY_BLUE};
            color: white;
            font-weight: 600;
        }}
        
        /* Área de contenido principal */
        QFrame#content_area {{
            background-color: {cls.BG_DARK};
            padding: {cls.SPACING_LG};
        }}
        
        /* Cards/Paneles */
        QFrame#card {{
            background-color: {cls.BG_DARK_SECONDARY};
            border: 1px solid {cls.BORDER_DARK};
            border-radius: {cls.RADIUS_LARGE};
            padding: {cls.SPACING_LG};
            margin: {cls.SPACING_SM};
        }}
        
        /* Títulos */
        QLabel#title {{
            color: {cls.TEXT_PRIMARY_DARK};
            font-size: {cls.FONT_SIZE_XXL};
            font-weight: 700;
            margin-bottom: {cls.SPACING_MD};
        }}
        
        QLabel#subtitle {{
            color: {cls.TEXT_SECONDARY_DARK};
            font-size: {cls.FONT_SIZE_LG};
            font-weight: 600;
            margin-bottom: {cls.SPACING_SM};
        }}
        
        /* Botones principales */
        QPushButton#primary_button {{
            background-color: {cls.PRIMARY_BLUE};
            color: white;
            border: none;
            border-radius: {cls.RADIUS_MEDIUM};
            padding: {cls.SPACING_MD} {cls.SPACING_LG};
            font-size: {cls.FONT_SIZE_MD};
            font-weight: 600;
            min-height: 40px;
        }}
        
        QPushButton#primary_button:hover {{
            background-color: {cls.PRIMARY_DARK};
        }}
        
        QPushButton#primary_button:pressed {{
            background-color: {cls.SECONDARY_BLUE};
        }}
        
        /* Botones secundarios */
        QPushButton#secondary_button {{
            background-color: transparent;
            color: {cls.TEXT_PRIMARY_DARK};
            border: 1px solid {cls.BORDER_DARK};
            border-radius: {cls.RADIUS_MEDIUM};
            padding: {cls.SPACING_MD} {cls.SPACING_LG};
            font-size: {cls.FONT_SIZE_MD};
            font-weight: 500;
            min-height: 40px;
        }}
        
        QPushButton#secondary_button:hover {{
            background-color: {cls.SURFACE_DARK};
            border-color: {cls.PRIMARY_BLUE};
        }}
        
        /* Campos de entrada */
        QLineEdit {{
            background-color: {cls.BG_DARK_SECONDARY};
            border: 1px solid {cls.BORDER_DARK};
            border-radius: {cls.RADIUS_MEDIUM};
            padding: {cls.SPACING_MD};
            color: {cls.TEXT_PRIMARY_DARK};
            font-size: {cls.FONT_SIZE_MD};
            min-height: 20px;
        }}
        
        QLineEdit:focus {{
            border-color: {cls.PRIMARY_BLUE};
            background-color: {cls.BG_DARK};
        }}
        
        /* ComboBox */
        QComboBox {{
            background-color: {cls.BG_DARK_SECONDARY};
            border: 1px solid {cls.BORDER_DARK};
            border-radius: {cls.RADIUS_MEDIUM};
            padding: {cls.SPACING_MD};
            color: {cls.TEXT_PRIMARY_DARK};
            font-size: {cls.FONT_SIZE_MD};
            min-height: 20px;
        }}
        
        QComboBox:focus {{
            border-color: {cls.PRIMARY_BLUE};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 30px;
        }}
        
        QComboBox::down-arrow {{
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzk0YTNiOCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
        }}
        
        /* Tablas */
        QTableWidget {{
            background-color: {cls.BG_DARK_SECONDARY};
            border: 1px solid {cls.BORDER_DARK};
            border-radius: {cls.RADIUS_MEDIUM};
            color: {cls.TEXT_PRIMARY_DARK};
            gridline-color: {cls.BORDER_DARK};
            font-size: {cls.FONT_SIZE_MD};
        }}
        
        QTableWidget::item {{
            padding: {cls.SPACING_MD};
            border-bottom: 1px solid {cls.BORDER_DARK};
        }}
        
        QTableWidget::item:selected {{
            background-color: {cls.PRIMARY_BLUE};
            color: white;
        }}
        
        QHeaderView::section {{
            background-color: {cls.SURFACE_DARK};
            color: {cls.TEXT_PRIMARY_DARK};
            padding: {cls.SPACING_MD};
            border: none;
            border-bottom: 1px solid {cls.BORDER_DARK};
            font-weight: 600;
        }}
        
        /* Scrollbars */
        QScrollBar:vertical {{
            background-color: {cls.BG_DARK_SECONDARY};
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {cls.SURFACE_DARK};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {cls.PRIMARY_BLUE};
        }}
        
        /* Menús */
        QMenuBar {{
            background-color: {cls.BG_DARK_SECONDARY};
            color: {cls.TEXT_PRIMARY_DARK};
            border-bottom: 1px solid {cls.BORDER_DARK};
            padding: {cls.SPACING_SM};
        }}
        
        QMenuBar::item {{
            background-color: transparent;
            padding: {cls.SPACING_SM} {cls.SPACING_MD};
            border-radius: {cls.RADIUS_SMALL};
        }}
        
        QMenuBar::item:selected {{
            background-color: {cls.SURFACE_DARK};
        }}
        
        /* Diálogos */
        QDialog {{
            background-color: {cls.BG_DARK};
            color: {cls.TEXT_PRIMARY_DARK};
        }}
        
        /* Pestañas */
        QTabWidget::pane {{
            border: 1px solid {cls.BORDER_DARK};
            border-radius: {cls.RADIUS_MEDIUM};
            background-color: {cls.BG_DARK_SECONDARY};
        }}
        
        QTabBar::tab {{
            background-color: {cls.SURFACE_DARK};
            color: {cls.TEXT_SECONDARY_DARK};
            padding: {cls.SPACING_MD} {cls.SPACING_LG};
            margin-right: {cls.SPACING_XS};
            border-top-left-radius: {cls.RADIUS_MEDIUM};
            border-top-right-radius: {cls.RADIUS_MEDIUM};
        }}
        
        QTabBar::tab:selected {{
            background-color: {cls.PRIMARY_BLUE};
            color: white;
        }}
        
        QTabBar::tab:hover {{
            background-color: {cls.PRIMARY_DARK};
            color: white;
        }}
        """
    
    @classmethod
    def get_light_theme_css(cls):
        """Retorna el CSS para el tema claro"""
        return f"""
        /* === TEMA CLARO MODERNO === */
        
        /* Ventana principal */
        QMainWindow {{
            background-color: {cls.BG_LIGHT};
            color: {cls.TEXT_PRIMARY_LIGHT};
            font-family: {cls.FONT_FAMILY};
            font-size: {cls.FONT_SIZE_MD};
        }}
        
        /* Sidebar de navegación */
        QFrame#sidebar {{
            background-color: {cls.BG_LIGHT_SECONDARY};
            border-right: 1px solid {cls.BORDER_LIGHT};
            min-width: 250px;
            max-width: 250px;
        }}
        
        /* Botones del sidebar */
        QPushButton#sidebar_button {{
            background-color: transparent;
            border: none;
            color: {cls.TEXT_SECONDARY_LIGHT};
            text-align: left;
            padding: {cls.SPACING_MD};
            margin: {cls.SPACING_XS};
            border-radius: {cls.RADIUS_MEDIUM};
            font-size: {cls.FONT_SIZE_MD};
            font-weight: 500;
        }}
        
        QPushButton#sidebar_button:hover {{
            background-color: {cls.SURFACE_LIGHT};
            color: {cls.TEXT_PRIMARY_LIGHT};
        }}
        
        QPushButton#sidebar_button:pressed {{
            background-color: {cls.PRIMARY_BLUE};
            color: white;
        }}
        
        QPushButton#sidebar_button_active {{
            background-color: {cls.PRIMARY_BLUE};
            color: white;
            font-weight: 600;
        }}
        
        /* Área de contenido principal */
        QFrame#content_area {{
            background-color: {cls.BG_LIGHT};
            padding: {cls.SPACING_LG};
        }}
        
        /* Cards/Paneles */
        QFrame#card {{
            background-color: white;
            border: 1px solid {cls.BORDER_LIGHT};
            border-radius: {cls.RADIUS_LARGE};
            padding: {cls.SPACING_LG};
            margin: {cls.SPACING_SM};
        }}
        
        /* Resto del CSS similar al tema oscuro pero con colores claros... */
        """

    @staticmethod
    def apply_shadow_effect(widget, blur_radius=15, offset=(0, 2), color=QColor(0, 0, 0, 30)):
        """Aplica efecto de sombra a un widget"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(blur_radius)
        shadow.setOffset(offset[0], offset[1])
        shadow.setColor(color)
        widget.setGraphicsEffect(shadow)

    @staticmethod
    def get_icon(name, color=None):
        """Obtiene iconos modernos usando QtAwesome"""
        if color is None:
            color = ModernTheme.PRIMARY_BLUE
        return qta.icon(name, color=color)

    @staticmethod
    def setup_modern_font(app):
        """Configura fuentes modernas para la aplicación"""
        font = QFont("Segoe UI", 10)
        font.setStyleHint(QFont.StyleHint.System)
        app.setFont(font)

    @staticmethod
    def get_professional_stylesheet():
        """Retorna un stylesheet profesional completo compatible con PyQt6"""
        return f"""
        /* === TEMA PROFESIONAL MODERNO === */

        /* Aplicación base */
        QApplication {{
            font-family: 'Segoe UI', 'SF Pro Display', system-ui, sans-serif;
            font-size: 14px;
        }}

        /* Ventana principal */
        QMainWindow {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8fafc, stop:1 #e2e8f0);
            color: {ModernTheme.TEXT_PRIMARY_LIGHT};
        }}

        /* Barra de menú elegante */
        QMenuBar {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f1f5f9);
            border: none;
            border-bottom: 1px solid {ModernTheme.BORDER_LIGHT};
            padding: 6px 12px;
            font-weight: 500;
        }}

        QMenuBar::item {{
            background: transparent;
            padding: 8px 16px;
            border-radius: 8px;
            margin: 2px;
            color: {ModernTheme.TEXT_PRIMARY_LIGHT};
        }}

        QMenuBar::item:selected {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {ModernTheme.PRIMARY_BLUE}, stop:1 {ModernTheme.PRIMARY_DARK});
            color: white;
        }}

        /* Menús desplegables */
        QMenu {{
            background: white;
            border: 1px solid {ModernTheme.BORDER_LIGHT};
            border-radius: 12px;
            padding: 8px;
        }}

        QMenu::item {{
            padding: 10px 16px;
            border-radius: 6px;
            margin: 2px;
            color: {ModernTheme.TEXT_PRIMARY_LIGHT};
        }}

        QMenu::item:selected {{
            background: {ModernTheme.PRIMARY_BLUE};
            color: white;
        }}

        /* Barra de herramientas moderna */
        QToolBar {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f8fafc);
            border: none;
            border-bottom: 1px solid {ModernTheme.BORDER_LIGHT};
            spacing: 8px;
            padding: 12px;
        }}

        QToolButton {{
            background: transparent;
            border: none;
            border-radius: 10px;
            padding: 10px;
            min-width: 44px;
            min-height: 44px;
            color: {ModernTheme.TEXT_SECONDARY_LIGHT};
        }}

        QToolButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {ModernTheme.SURFACE_LIGHT}, stop:1 #cbd5e1);
            color: {ModernTheme.PRIMARY_BLUE};
        }}

        QToolButton:pressed {{
            background: {ModernTheme.PRIMARY_BLUE};
            color: white;
        }}

        /* Botones principales elegantes */
        QPushButton {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {ModernTheme.PRIMARY_BLUE}, stop:1 {ModernTheme.PRIMARY_DARK});
            color: white;
            border: none;
            border-radius: 10px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 14px;
            min-height: 20px;
        }}

        QPushButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {ModernTheme.SECONDARY_BLUE}, stop:1 {ModernTheme.PRIMARY_BLUE});
        }}

        QPushButton:pressed {{
            background: {ModernTheme.PRIMARY_DARK};
        }}

        QPushButton:disabled {{
            background: {ModernTheme.SURFACE_LIGHT};
            color: {ModernTheme.TEXT_SECONDARY_LIGHT};
        }}

        /* Botones secundarios */
        QPushButton[class="secondary"] {{
            background: white;
            color: {ModernTheme.TEXT_PRIMARY_LIGHT};
            border: 2px solid {ModernTheme.BORDER_LIGHT};
        }}

        QPushButton[class="secondary"]:hover {{
            background: {ModernTheme.SURFACE_LIGHT};
            border-color: {ModernTheme.PRIMARY_BLUE};
            color: {ModernTheme.PRIMARY_BLUE};
        }}

        /* Botones de éxito */
        QPushButton[class="success"] {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {ModernTheme.SUCCESS}, stop:1 #059669);
        }}

        QPushButton[class="success"]:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #34d399, stop:1 {ModernTheme.SUCCESS});
        }}

        /* Botones de peligro */
        QPushButton[class="danger"] {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {ModernTheme.ERROR}, stop:1 #dc2626);
        }}

        QPushButton[class="danger"]:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f87171, stop:1 {ModernTheme.ERROR});
        }}

        /* Campos de entrada elegantes */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background: white;
            border: 2px solid {ModernTheme.BORDER_LIGHT};
            border-radius: 10px;
            padding: 12px 16px;
            font-size: 14px;
            color: {ModernTheme.TEXT_PRIMARY_LIGHT};
            selection-background-color: {ModernTheme.ACCENT_BLUE};
        }}

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {ModernTheme.PRIMARY_BLUE};
        }}

        /* ComboBox moderno */
        QComboBox {{
            background: white;
            border: 2px solid {ModernTheme.BORDER_LIGHT};
            border-radius: 10px;
            padding: 10px 16px;
            min-height: 20px;
            color: {ModernTheme.TEXT_PRIMARY_LIGHT};
        }}

        QComboBox:focus {{
            border-color: {ModernTheme.PRIMARY_BLUE};
        }}

        QComboBox::drop-down {{
            border: none;
            width: 40px;
            border-top-right-radius: 10px;
            border-bottom-right-radius: 10px;
        }}

        QComboBox::down-arrow {{
            image: none;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 6px solid {ModernTheme.TEXT_SECONDARY_LIGHT};
            margin-right: 12px;
        }}

        QComboBox QAbstractItemView {{
            background: white;
            border: 1px solid {ModernTheme.BORDER_LIGHT};
            border-radius: 10px;
            selection-background-color: {ModernTheme.PRIMARY_BLUE};
            outline: none;
            padding: 4px;
        }}

        /* Tablas modernas */
        QTableWidget {{
            background: white;
            border: 1px solid {ModernTheme.BORDER_LIGHT};
            border-radius: 10px;
            gridline-color: #f1f5f9;
            selection-background-color: {ModernTheme.ACCENT_BLUE};
        }}

        QTableWidget::item {{
            padding: 8px;
            border: none;
        }}

        QTableWidget::item:selected {{
            background: {ModernTheme.PRIMARY_BLUE};
            color: white;
        }}

        QHeaderView::section {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8fafc, stop:1 #f1f5f9);
            border: none;
            border-bottom: 1px solid {ModernTheme.BORDER_LIGHT};
            padding: 12px 8px;
            font-weight: 600;
            color: {ModernTheme.TEXT_PRIMARY_LIGHT};
        }}

        /* GroupBox moderno */
        QGroupBox {{
            font-weight: 600;
            border: 2px solid {ModernTheme.BORDER_LIGHT};
            border-radius: 10px;
            margin-top: 12px;
            padding-top: 8px;
            color: {ModernTheme.TEXT_PRIMARY_LIGHT};
        }}

        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 12px;
            padding: 0 8px 0 8px;
            background: white;
            color: {ModernTheme.TEXT_PRIMARY_LIGHT};
        }}

        /* Scrollbars modernos */
        QScrollBar:vertical {{
            background: #f1f5f9;
            width: 12px;
            border-radius: 6px;
        }}

        QScrollBar::handle:vertical {{
            background: {ModernTheme.SURFACE_LIGHT};
            border-radius: 6px;
            min-height: 20px;
        }}

        QScrollBar::handle:vertical:hover {{
            background: {ModernTheme.PRIMARY_BLUE};
        }}

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}

        /* Diálogos modernos */
        QDialog {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f8fafc);
            color: {ModernTheme.TEXT_PRIMARY_LIGHT};
        }}

        /* Barra de estado moderna */
        QStatusBar {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8fafc, stop:1 #f1f5f9);
            border-top: 1px solid {ModernTheme.BORDER_LIGHT};
            padding: 4px 8px;
        }}

        /* Splitter moderno */
        QSplitter::handle {{
            background: {ModernTheme.BORDER_LIGHT};
        }}

        QSplitter::handle:horizontal {{
            width: 2px;
        }}

        QSplitter::handle:vertical {{
            height: 2px;
        }}
        """
