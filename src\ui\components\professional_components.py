"""
Componentes UI Profesionales Reutilizables para PRO-2000
Componentes base que mantienen consistencia visual en toda la aplicación
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QPushButton, QLabel, 
    QFrame, QScrollArea, QDialog, QGroupBox, QFormLayout, QLineEdit,
    QComboBox, QTextEdit, QTableWidget, QHeaderView, QSizePolicy,
    QSpacerItem, QMessageBox, QProgressBar
)
from PyQt6.QtCore import Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QFont, QPalette

class ProfessionalButton(QPushButton):
    """Botón profesional con estilos predefinidos"""
    
    def __init__(self, text="", button_type="primary", icon_text="", parent=None):
        super().__init__(parent)
        self.button_type = button_type
        self.icon_text = icon_text
        
        # Configurar texto
        if icon_text:
            self.setText(f"{icon_text}  {text}")
        else:
            self.setText(text)
        
        # Aplicar estilo según el tipo
        self.setProperty("class", button_type)
        self.setMinimumHeight(44)
        self.setMinimumWidth(120)
        
        # Configurar cursor
        self.setCursor(Qt.CursorShape.PointingHandCursor)

class ProfessionalCard(QFrame):
    """Tarjeta profesional para agrupar contenido"""
    
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet(f"""
            QFrame {{
                background: #ffffff;
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                padding: 0px;
            }}
            QFrame:hover {{
                border-color: #cbd5e1;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
            }}
        """)
        
        # Layout principal
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(24, 24, 24, 24)
        self.main_layout.setSpacing(16)
        
        # Título si se proporciona
        if title:
            self.title_label = QLabel(title)
            self.title_label.setFont(QFont("Segoe UI", 16, QFont.Weight.Bold))
            self.title_label.setStyleSheet("color: #0f172a; margin-bottom: 8px;")
            self.main_layout.addWidget(self.title_label)
        
        # Área de contenido
        self.content_layout = QVBoxLayout()
        self.content_layout.setSpacing(12)
        self.main_layout.addLayout(self.content_layout)
    
    def add_widget(self, widget):
        """Añade un widget al contenido de la tarjeta"""
        self.content_layout.addWidget(widget)
    
    def add_layout(self, layout):
        """Añade un layout al contenido de la tarjeta"""
        self.content_layout.addLayout(layout)

class ProfessionalFormSection(QGroupBox):
    """Sección de formulario profesional"""
    
    def __init__(self, title, parent=None):
        super().__init__(title, parent)
        self.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: 600;
                color: #0f172a;
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                margin-top: 16px;
                padding-top: 16px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 16px;
                padding: 0 8px;
                background: #ffffff;
                color: #2563eb;
            }
        """)
        
        # Layout de formulario
        self.form_layout = QFormLayout(self)
        self.form_layout.setSpacing(16)
        self.form_layout.setContentsMargins(20, 24, 20, 20)
        self.form_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
    
    def add_field(self, label_text, widget, required=False):
        """Añade un campo al formulario"""
        label = QLabel(label_text)
        if required:
            label.setText(f"{label_text} *")
            label.setStyleSheet("color: #dc2626; font-weight: 600;")
        else:
            label.setStyleSheet("color: #374151; font-weight: 600;")
        
        # Configurar widget
        if isinstance(widget, (QLineEdit, QComboBox, QTextEdit)):
            widget.setMinimumHeight(44)
        
        self.form_layout.addRow(label, widget)
        return widget

class ProfessionalGridLayout(QWidget):
    """Layout de grilla profesional para organizar elementos"""
    
    def __init__(self, columns=2, parent=None):
        super().__init__(parent)
        self.grid_layout = QGridLayout(self)
        self.grid_layout.setSpacing(16)
        self.grid_layout.setContentsMargins(0, 0, 0, 0)
        self.columns = columns
        self.current_row = 0
        self.current_col = 0
    
    def add_widget(self, widget, colspan=1, rowspan=1):
        """Añade un widget a la grilla"""
        self.grid_layout.addWidget(widget, self.current_row, self.current_col, rowspan, colspan)
        
        # Actualizar posición
        self.current_col += colspan
        if self.current_col >= self.columns:
            self.current_col = 0
            self.current_row += 1
    
    def add_full_width_widget(self, widget):
        """Añade un widget que ocupa todo el ancho"""
        self.add_widget(widget, colspan=self.columns)

class ProfessionalTable(QTableWidget):
    """Tabla profesional con estilos consistentes"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_professional_style()
    
    def setup_professional_style(self):
        """Configura el estilo profesional de la tabla"""
        # Configuración básica
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.setSortingEnabled(True)
        
        # Headers
        self.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.verticalHeader().setVisible(False)
        
        # Altura de filas
        self.verticalHeader().setDefaultSectionSize(48)
        
        # Estilo específico
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #f1f5f9;
                background: #ffffff;
                border: 2px solid #e2e8f0;
                border-radius: 12px;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border: none;
                border-bottom: 1px solid #f1f5f9;
            }
            QTableWidget::item:selected {
                background: #2563eb;
                color: #ffffff;
            }
            QTableWidget::item:hover {
                background: #f8fafc;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8fafc, stop:1 #f1f5f9);
                color: #374151;
                border: none;
                border-bottom: 2px solid #e2e8f0;
                border-right: 1px solid #e2e8f0;
                padding: 16px 8px;
                font-weight: 700;
                font-size: 12px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
        """)

class ProfessionalDialog(QDialog):
    """Diálogo base profesional"""
    
    def __init__(self, title="", parent=None, width=600, height=400):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(width, height)
        self.setMinimumSize(400, 300)
        
        # Layout principal
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # Header del diálogo
        self.create_header(title)
        
        # Área de contenido con scroll
        self.create_content_area()
        
        # Footer con botones
        self.create_footer()
        
        # Aplicar estilo
        self.setStyleSheet("""
            QDialog {
                background: #ffffff;
                border-radius: 16px;
            }
        """)
    
    def create_header(self, title):
        """Crea el header del diálogo"""
        self.header = QFrame()
        self.header.setFixedHeight(80)
        self.header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2563eb, stop:1 #1d4ed8);
                border-top-left-radius: 16px;
                border-top-right-radius: 16px;
            }
        """)
        
        header_layout = QHBoxLayout(self.header)
        header_layout.setContentsMargins(24, 16, 24, 16)
        
        # Título
        self.title_label = QLabel(title)
        self.title_label.setFont(QFont("Segoe UI", 18, QFont.Weight.Bold))
        self.title_label.setStyleSheet("color: #ffffff;")
        header_layout.addWidget(self.title_label)
        
        header_layout.addStretch()
        
        self.main_layout.addWidget(self.header)
    
    def create_content_area(self):
        """Crea el área de contenido"""
        # Área de scroll
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setFrameStyle(QFrame.Shape.NoFrame)
        self.scroll_area.setStyleSheet("background: #ffffff;")
        
        # Widget de contenido
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(24, 24, 24, 24)
        self.content_layout.setSpacing(20)
        
        self.scroll_area.setWidget(self.content_widget)
        self.main_layout.addWidget(self.scroll_area)
    
    def create_footer(self):
        """Crea el footer con botones"""
        self.footer = QFrame()
        self.footer.setFixedHeight(80)
        self.footer.setStyleSheet("""
            QFrame {
                background: #f8fafc;
                border-bottom-left-radius: 16px;
                border-bottom-right-radius: 16px;
                border-top: 1px solid #e2e8f0;
            }
        """)
        
        self.footer_layout = QHBoxLayout(self.footer)
        self.footer_layout.setContentsMargins(24, 16, 24, 16)
        
        # Espacio para botones personalizados
        self.footer_layout.addStretch()
        
        # Botones por defecto
        self.btn_cancel = ProfessionalButton("Cancelar", "secondary")
        self.btn_cancel.clicked.connect(self.reject)
        self.footer_layout.addWidget(self.btn_cancel)
        
        self.btn_accept = ProfessionalButton("Aceptar", "primary")
        self.btn_accept.clicked.connect(self.accept)
        self.footer_layout.addWidget(self.btn_accept)
        
        self.main_layout.addWidget(self.footer)
    
    def add_content_widget(self, widget):
        """Añade un widget al área de contenido"""
        self.content_layout.addWidget(widget)
    
    def add_content_layout(self, layout):
        """Añade un layout al área de contenido"""
        self.content_layout.addLayout(layout)
    
    def set_accept_button_text(self, text):
        """Cambia el texto del botón aceptar"""
        self.btn_accept.setText(text)
    
    def set_cancel_button_text(self, text):
        """Cambia el texto del botón cancelar"""
        self.btn_cancel.setText(text)

class ProfessionalActionBar(QFrame):
    """Barra de acciones profesional"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(60)
        self.setStyleSheet("""
            QFrame {
                background: #f8fafc;
                border-bottom: 2px solid #e2e8f0;
            }
        """)
        
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(16, 8, 16, 8)
        self.layout.setSpacing(8)
    
    def add_action(self, text, icon_text="", button_type="primary", callback=None):
        """Añade una acción a la barra"""
        button = ProfessionalButton(text, button_type, icon_text)
        if callback:
            button.clicked.connect(callback)
        self.layout.addWidget(button)
        return button
    
    def add_separator(self):
        """Añade un separador"""
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.VLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        separator.setStyleSheet("color: #e2e8f0;")
        self.layout.addWidget(separator)
    
    def add_stretch(self):
        """Añade espacio flexible"""
        self.layout.addStretch()

class ProfessionalStatusBar(QFrame):
    """Barra de estado profesional"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(40)
        self.setStyleSheet("""
            QFrame {
                background: #f1f5f9;
                border-top: 1px solid #e2e8f0;
            }
        """)
        
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(16, 8, 16, 8)
        
        # Label de estado
        self.status_label = QLabel("Listo")
        self.status_label.setStyleSheet("color: #64748b; font-weight: 500;")
        self.layout.addWidget(self.status_label)
        
        self.layout.addStretch()
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setFixedWidth(200)
        self.progress_bar.setFixedHeight(20)
        self.layout.addWidget(self.progress_bar)
    
    def set_status(self, message, level="info"):
        """Establece el mensaje de estado"""
        colors = {
            "info": "#64748b",
            "success": "#059669", 
            "warning": "#d97706",
            "error": "#dc2626"
        }
        
        color = colors.get(level, "#64748b")
        self.status_label.setText(message)
        self.status_label.setStyleSheet(f"color: {color}; font-weight: 500;")
    
    def show_progress(self, visible=True):
        """Muestra u oculta la barra de progreso"""
        self.progress_bar.setVisible(visible)
    
    def set_progress(self, value):
        """Establece el valor del progreso"""
        self.progress_bar.setValue(value)

class ProfessionalSearchBar(QFrame):
    """Barra de búsqueda profesional"""
    
    search_changed = pyqtSignal(str)
    
    def __init__(self, placeholder="Buscar...", parent=None):
        super().__init__(parent)
        self.setFixedHeight(50)
        self.setStyleSheet("""
            QFrame {
                background: #ffffff;
                border: 2px solid #e2e8f0;
                border-radius: 25px;
            }
            QFrame:hover {
                border-color: #cbd5e1;
            }
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(16, 8, 16, 8)
        
        # Icono de búsqueda
        search_icon = QLabel("🔍")
        search_icon.setStyleSheet("color: #64748b; font-size: 16px;")
        layout.addWidget(search_icon)
        
        # Campo de búsqueda
        self.search_field = QLineEdit()
        self.search_field.setPlaceholderText(placeholder)
        self.search_field.setStyleSheet("""
            QLineEdit {
                border: none;
                background: transparent;
                font-size: 14px;
                color: #0f172a;
            }
            QLineEdit:focus {
                outline: none;
            }
        """)
        self.search_field.textChanged.connect(self.search_changed.emit)
        layout.addWidget(self.search_field)
    
    def get_text(self):
        """Obtiene el texto de búsqueda"""
        return self.search_field.text()
    
    def clear(self):
        """Limpia el campo de búsqueda"""
        self.search_field.clear()

# Funciones de utilidad para crear componentes comunes

def create_professional_message_box(title, message, message_type="info", parent=None):
    """Crea un message box profesional"""
    msg_box = QMessageBox(parent)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    
    # Configurar según el tipo
    if message_type == "info":
        msg_box.setIcon(QMessageBox.Icon.Information)
    elif message_type == "warning":
        msg_box.setIcon(QMessageBox.Icon.Warning)
    elif message_type == "error":
        msg_box.setIcon(QMessageBox.Icon.Critical)
    elif message_type == "question":
        msg_box.setIcon(QMessageBox.Icon.Question)
        msg_box.setStandardButtons(
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
    
    # Aplicar estilo profesional
    msg_box.setStyleSheet("""
        QMessageBox {
            background: #ffffff;
            color: #0f172a;
        }
        QMessageBox QPushButton {
            min-width: 80px;
            min-height: 32px;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 600;
        }
    """)
    
    return msg_box

def create_form_field_row(label_text, widget, required=False):
    """Crea una fila de formulario profesional"""
    layout = QHBoxLayout()
    layout.setSpacing(12)
    
    # Label
    label = QLabel(label_text)
    if required:
        label.setText(f"{label_text} *")
        label.setStyleSheet("color: #dc2626; font-weight: 600;")
    else:
        label.setStyleSheet("color: #374151; font-weight: 600;")
    
    label.setFixedWidth(150)
    layout.addWidget(label)
    
    # Widget
    if isinstance(widget, (QLineEdit, QComboBox)):
        widget.setMinimumHeight(44)
    layout.addWidget(widget)
    
    return layout
