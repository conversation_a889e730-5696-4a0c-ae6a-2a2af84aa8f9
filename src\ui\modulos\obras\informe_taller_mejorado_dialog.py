"""
Diálogo mejorado para generar informes de taller profesionales.
Incluye hojas individuales con imágenes, medidas en imágenes, agrupación por tipo, etc.
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLineEdit, QLabel,
    QFormLayout, QComboBox, QDoubleSpinBox, QSpinBox, QCheckBox,
    QMessageBox, QGroupBox, QTextEdit, QTableWidget, QTableWidgetItem,
    QHeaderView, QTabWidget, QWidget, QProgressBar, QSplitter,
    QFileDialog, QScrollArea, QFrame, QApplication
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QPainter, QPen, QBrush, QColor
from PyQt6.QtPrintSupport import QPrinter, QPrintDialog

import os
from datetime import datetime

# Verificar disponibilidad de ReportLab para PDFs
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.units import cm, mm
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
from pathlib import Path

# Importar reportlab para PDFs profesionales
try:
    from reportlab.lib.pagesizes import A4, landscape
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak, Image
    from reportlab.lib.units import cm, mm
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
    from reportlab.graphics.shapes import Drawing, Rect, String
    from reportlab.graphics import renderPDF
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

from sqlalchemy.orm import joinedload
from models.base import get_db
from models.obra import Obra
from models.articulo import ObraArticulo, Articulo, ArticuloPerfil, ArticuloAccesorio, ArticuloCristal
from models.perfil import Perfil
from models.cristal import Cristal
from models.accesorio import Accesorio
from models.formula_calculator import FormulaCalculator


class InformeTallerMejoradoDialog(QDialog):
    """Diálogo mejorado para informes de taller profesionales."""
    
    def __init__(self, parent=None, obra=None):
        super().__init__(parent)
        self.obra = obra
        self.datos_procesados = None
        
        self.setWindowTitle(f"Informe de Taller Profesional - {obra.codigo if obra else 'Nueva Obra'}")
        self.setMinimumSize(1000, 600)
        self.resize(1200, 700)  # Tamaño inicial más razonable
        self.setModal(True)
        
        self._inicializar_ui()
        
        if obra:
            self._cargar_datos_obra()
    
    def _inicializar_ui(self):
        """Configura la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)
        
        # Título
        titulo = QLabel("📋 Generador de Informes de Taller Profesional")
        titulo.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        titulo.setAlignment(Qt.AlignmentFlag.AlignCenter)
        titulo.setStyleSheet("color: #2c3e50; padding: 15px; background-color: #ecf0f1; border-radius: 8px;")
        layout_principal.addWidget(titulo)
        
        # Información de la obra
        if self.obra:
            info_obra = QLabel(f"🏗️ Obra: {self.obra.codigo} - {self.obra.nombre}")
            info_obra.setStyleSheet("color: #34495e; font-size: 14px; font-weight: bold; padding: 10px;")
            layout_principal.addWidget(info_obra)
        
        # Crear pestañas principales
        self.tabs_principales = QTabWidget()
        
        # Pestaña 1: Configuración
        self._crear_tab_configuracion()
        
        # Pestaña 2: Vista Previa
        self._crear_tab_vista_previa()
        
        # Pestaña 3: Hojas Individuales
        self._crear_tab_hojas_individuales()
        
        layout_principal.addWidget(self.tabs_principales)
        
        # Botones de acción
        self._crear_botones_accion(layout_principal)
    
    def _crear_tab_configuracion(self):
        """Crea la pestaña de configuración."""
        tab = QWidget()
        layout = QHBoxLayout(tab)
        
        # Panel izquierdo - Configuración general
        panel_izq = QWidget()
        layout_izq = QVBoxLayout(panel_izq)
        
        # Configuración del informe
        grupo_config = QGroupBox("⚙️ Configuración General")
        layout_config = QFormLayout(grupo_config)
        
        self.campo_nombre = QLineEdit()
        self.campo_nombre.setText(f"Informe_Taller_{datetime.now().strftime('%Y%m%d_%H%M')}")
        layout_config.addRow("Nombre del informe:", self.campo_nombre)
        
        self.campo_empresa = QLineEdit()
        self.campo_empresa.setText("PRO-2000 Carpintería")
        layout_config.addRow("Empresa:", self.campo_empresa)
        
        self.check_incluir_imagenes = QCheckBox("Incluir imágenes de artículos")
        self.check_incluir_imagenes.setChecked(True)
        layout_config.addRow("", self.check_incluir_imagenes)
        
        self.check_medidas_en_imagen = QCheckBox("Mostrar medidas en las imágenes")
        self.check_medidas_en_imagen.setChecked(True)
        layout_config.addRow("", self.check_medidas_en_imagen)
        
        self.check_hojas_individuales = QCheckBox("Generar hojas individuales por artículo")
        self.check_hojas_individuales.setChecked(True)
        layout_config.addRow("", self.check_hojas_individuales)
        
        layout_izq.addWidget(grupo_config)
        
        # Agrupación y orden
        grupo_orden = QGroupBox("📊 Agrupación y Orden")
        layout_orden = QVBoxLayout(grupo_orden)
        
        self.check_agrupar_tipo = QCheckBox("Agrupar por tipo de material")
        self.check_agrupar_tipo.setChecked(True)
        layout_orden.addWidget(self.check_agrupar_tipo)
        
        self.check_orden_perfiles = QCheckBox("1. Perfiles primero")
        self.check_orden_perfiles.setChecked(True)
        layout_orden.addWidget(self.check_orden_perfiles)
        
        self.check_orden_cristales = QCheckBox("2. Cristales segundo")
        self.check_orden_cristales.setChecked(True)
        layout_orden.addWidget(self.check_orden_cristales)
        
        self.check_orden_accesorios = QCheckBox("3. Accesorios tercero")
        self.check_orden_accesorios.setChecked(True)
        layout_orden.addWidget(self.check_orden_accesorios)
        
        self.check_orden_persianas = QCheckBox("4. Persianas último")
        self.check_orden_persianas.setChecked(True)
        layout_orden.addWidget(self.check_orden_persianas)
        
        layout_izq.addWidget(grupo_orden)
        
        layout_izq.addStretch()
        layout.addWidget(panel_izq)
        
        # Panel derecho - Opciones avanzadas
        panel_der = QWidget()
        layout_der = QVBoxLayout(panel_der)
        
        # Opciones de contenido
        grupo_contenido = QGroupBox("📋 Contenido del Informe")
        layout_contenido = QVBoxLayout(grupo_contenido)
        
        self.check_resumen_general = QCheckBox("Resumen general de la obra")
        self.check_resumen_general.setChecked(True)
        layout_contenido.addWidget(self.check_resumen_general)
        
        self.check_lista_materiales = QCheckBox("Lista de materiales agrupada")
        self.check_lista_materiales.setChecked(True)
        layout_contenido.addWidget(self.check_lista_materiales)
        
        self.check_optimizacion_cortes = QCheckBox("Optimización de cortes")
        self.check_optimizacion_cortes.setChecked(True)
        layout_contenido.addWidget(self.check_optimizacion_cortes)
        
        self.check_pedido_individual = QCheckBox("Pedido individual completo")
        self.check_pedido_individual.setChecked(True)
        layout_contenido.addWidget(self.check_pedido_individual)
        
        layout_der.addWidget(grupo_contenido)
        
        # Configuración de imágenes
        grupo_imagenes = QGroupBox("🖼️ Configuración de Imágenes")
        layout_imagenes = QFormLayout(grupo_imagenes)
        
        self.campo_carpeta_imagenes = QLineEdit()
        self.campo_carpeta_imagenes.setText("imagenes/articulos")
        layout_imagenes.addRow("Carpeta de imágenes:", self.campo_carpeta_imagenes)
        
        self.btn_seleccionar_carpeta = QPushButton("📁 Seleccionar")
        self.btn_seleccionar_carpeta.clicked.connect(self._seleccionar_carpeta_imagenes)
        layout_imagenes.addRow("", self.btn_seleccionar_carpeta)
        
        self.combo_formato_imagen = QComboBox()
        self.combo_formato_imagen.addItems(["PNG", "JPG", "JPEG", "BMP"])
        layout_imagenes.addRow("Formato de imagen:", self.combo_formato_imagen)
        
        layout_der.addWidget(grupo_imagenes)
        
        # Botón de generar informe de taller profesional
        self.btn_generar_taller = QPushButton("🔧 Generar Informe de Taller Profesional")
        self.btn_generar_taller.clicked.connect(self._generar_informe_taller_profesional)
        self.btn_generar_taller.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 15px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        layout_der.addWidget(self.btn_generar_taller)

        # Botón de generar informe general
        self.btn_generar_informe = QPushButton("📋 Generar Informe General")
        self.btn_generar_informe.clicked.connect(self._generar_informe_completo)
        self.btn_generar_informe.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px;
                font-size: 12px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout_der.addWidget(self.btn_generar_informe)
        
        # Barra de progreso
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout_der.addWidget(self.progress_bar)
        
        layout_der.addStretch()
        layout.addWidget(panel_der)
        
        self.tabs_principales.addTab(tab, "⚙️ Configuración")
    
    def _crear_tab_vista_previa(self):
        """Crea la pestaña de vista previa."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Controles de vista previa
        controles = QHBoxLayout()
        
        self.combo_vista_previa = QComboBox()
        self.combo_vista_previa.addItems([
            "Resumen General",
            "Lista de Perfiles",
            "Lista de Cristales", 
            "Lista de Accesorios",
            "Lista de Persianas",
            "Pedido Individual"
        ])
        self.combo_vista_previa.currentTextChanged.connect(self._actualizar_vista_previa)
        controles.addWidget(QLabel("Vista:"))
        controles.addWidget(self.combo_vista_previa)
        
        controles.addStretch()
        
        self.btn_actualizar_vista = QPushButton("🔄 Actualizar")
        self.btn_actualizar_vista.clicked.connect(self._actualizar_vista_previa)
        controles.addWidget(self.btn_actualizar_vista)
        
        layout.addLayout(controles)
        
        # Área de vista previa
        self.scroll_vista_previa = QScrollArea()
        self.scroll_vista_previa.setWidgetResizable(True)
        
        self.widget_vista_previa = QTextEdit()
        self.widget_vista_previa.setReadOnly(True)
        self.widget_vista_previa.setFont(QFont("Courier", 10))
        
        self.scroll_vista_previa.setWidget(self.widget_vista_previa)
        layout.addWidget(self.scroll_vista_previa)
        
        self.tabs_principales.addTab(tab, "👁️ Vista Previa")
    
    def _crear_tab_hojas_individuales(self):
        """Crea la pestaña de hojas individuales."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Información
        info = QLabel("📄 Aquí se mostrarán las hojas individuales de cada artículo con imágenes y medidas")
        info.setStyleSheet("background-color: #e8f4fd; padding: 10px; border-radius: 5px; color: #2980b9;")
        layout.addWidget(info)
        
        # Lista de artículos
        self.lista_articulos_hojas = QTableWidget()
        self.lista_articulos_hojas.setColumnCount(5)
        self.lista_articulos_hojas.setHorizontalHeaderLabels([
            "Artículo", "Cantidad", "Imagen", "Estado", "Acciones"
        ])
        
        # Configurar tabla
        header = self.lista_articulos_hojas.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        
        layout.addWidget(self.lista_articulos_hojas)
        
        self.tabs_principales.addTab(tab, "📄 Hojas Individuales")
    
    def _crear_botones_accion(self, layout_principal):
        """Crea los botones de acción."""
        layout_botones = QHBoxLayout()
        
        self.btn_exportar_pdf = QPushButton("📄 Exportar PDF Completo")
        self.btn_exportar_pdf.clicked.connect(self._exportar_pdf_completo)
        self.btn_exportar_pdf.setEnabled(False)
        layout_botones.addWidget(self.btn_exportar_pdf)
        
        self.btn_imprimir = QPushButton("🖨️ Imprimir")
        self.btn_imprimir.clicked.connect(self._imprimir_informe)
        self.btn_imprimir.setEnabled(False)
        layout_botones.addWidget(self.btn_imprimir)
        
        self.btn_generar_pedido = QPushButton("📦 Generar Pedido Individual")
        self.btn_generar_pedido.clicked.connect(self._generar_pedido_individual)
        self.btn_generar_pedido.setEnabled(False)
        layout_botones.addWidget(self.btn_generar_pedido)
        
        layout_botones.addStretch()
        
        btn_cerrar = QPushButton("❌ Cerrar")
        btn_cerrar.clicked.connect(self.reject)
        layout_botones.addWidget(btn_cerrar)
        
        layout_principal.addLayout(layout_botones)
    
    def _cargar_datos_obra(self):
        """Carga los datos de la obra."""
        if not self.obra:
            return
            
        # Actualizar nombre del informe
        self.campo_nombre.setText(f"Informe_Taller_{self.obra.codigo}_{datetime.now().strftime('%Y%m%d')}")

        # Cargar artículos en la tabla de hojas individuales
        self._cargar_articulos_tabla()
    
    def _seleccionar_carpeta_imagenes(self):
        """Selecciona la carpeta de imágenes."""
        carpeta = QFileDialog.getExistingDirectory(
            self,
            "Seleccionar carpeta de imágenes",
            self.campo_carpeta_imagenes.text()
        )

        if carpeta:
            self.campo_carpeta_imagenes.setText(carpeta)

    def _cargar_articulos_tabla(self):
        """Carga los artículos de la obra en la tabla."""
        if not self.obra:
            return

        db = next(get_db())
        try:
            obra_articulos = db.query(ObraArticulo).filter(
                ObraArticulo.obra_id == self.obra.id
            ).all()

            self.lista_articulos_hojas.setRowCount(len(obra_articulos))

            for i, obra_articulo in enumerate(obra_articulos):
                try:
                    if obra_articulo.articulo:
                        # Código del artículo
                        self.lista_articulos_hojas.setItem(i, 0, QTableWidgetItem(
                            f"{obra_articulo.articulo.codigo} - {obra_articulo.articulo.descripcion}"
                        ))

                        # Cantidad
                        self.lista_articulos_hojas.setItem(i, 1, QTableWidgetItem(
                            str(obra_articulo.cantidad)
                        ))

                        # Estado de imagen (simplificado para evitar errores)
                        self.lista_articulos_hojas.setItem(i, 2, QTableWidgetItem("❌ No"))

                        # Estado
                        self.lista_articulos_hojas.setItem(i, 3, QTableWidgetItem("⏳ Pendiente"))

                        # Botón de vista previa
                        btn_vista = QPushButton("👁️ Ver Hoja")
                        btn_vista.clicked.connect(lambda checked, articulo=obra_articulo.articulo: self._ver_hoja_taller_articulo(articulo))
                        self.lista_articulos_hojas.setCellWidget(i, 4, btn_vista)
                except Exception as e:
                    print(f"Error cargando artículo {i}: {e}")
                    continue

        except Exception as e:
            print(f"Error general cargando artículos: {e}")
        finally:
            db.close()

    def _generar_informe_taller_completo(self):
        """Genera el informe de taller completo con imágenes de ventanas y toda la información."""
        if not self.obra:
            QMessageBox.warning(self, "Error", "No hay obra seleccionada.")
            return

        # Validar datos básicos con una nueva sesión
        db = next(get_db())
        try:
            # Recargar la obra con la sesión activa
            obra = db.query(Obra).filter(Obra.id == self.obra.id).first()
            if not obra or not obra.articulos:
                QMessageBox.warning(self, "Error", "La obra no tiene artículos asignados.")
                return
        finally:
            db.close()

        # Mostrar progreso
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.btn_generar_taller.setEnabled(False)

        try:
            # Verificar que hay imágenes disponibles si está marcada la opción
            if self.check_incluir_imagenes.isChecked():
                carpeta_imagenes = Path(self.campo_carpeta_imagenes.text())
                if not carpeta_imagenes.exists():
                    QMessageBox.warning(self, "Advertencia",
                        f"La carpeta de imágenes '{carpeta_imagenes}' no existe. "
                        "Las imágenes no se incluirán en el informe.")
                    self.check_incluir_imagenes.setChecked(False)
            # Paso 1: Cargar datos de la obra (20%)
            self.progress_bar.setValue(20)
            QApplication.processEvents()
            self._cargar_datos_completos_obra()

            # Paso 2: Generar imágenes de ventanas (40%)
            self.progress_bar.setValue(40)
            QApplication.processEvents()
            self._generar_imagenes_ventanas()

            # Paso 3: Procesar materiales (60%)
            self.progress_bar.setValue(60)
            QApplication.processEvents()
            self._procesar_materiales_agrupados()

            # Paso 4: Generar hojas de taller individuales (80%)
            self.progress_bar.setValue(80)
            QApplication.processEvents()
            self._generar_hojas_taller_completas()

            # Paso 5: Finalizar (100%)
            self.progress_bar.setValue(100)
            QApplication.processEvents()

            # Habilitar botones
            self.btn_exportar_pdf.setEnabled(True)
            self.btn_imprimir.setEnabled(True)
            self.btn_generar_pedido.setEnabled(True)

            # Actualizar vista previa
            self._actualizar_vista_previa()

            # Cambiar a pestaña de hojas individuales
            self.tabs_principales.setCurrentIndex(2)

            QMessageBox.information(
                self,
                "✅ Informe de Taller Generado",
                "El informe de taller completo ha sido generado correctamente.\n\n"
                "Incluye:\n"
                "• Imágenes de ventanas con medidas\n"
                "• Hojas individuales por artículo\n"
                "• Lista completa de materiales\n"
                "• Información detallada para el taller\n\n"
                "Puede exportar a PDF o imprimir."
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ Error",
                f"Error al generar el informe de taller: {str(e)}"
            )
        finally:
            self.progress_bar.setVisible(False)
            self.btn_generar_taller.setEnabled(True)

    def _generar_informe_taller_profesional(self):
        """Genera un informe de taller profesional con formato mejorado similar a la imagen de referencia."""
        if not self.obra:
            QMessageBox.warning(self, "Error", "No hay obra seleccionada.")
            return

        # Validar datos básicos
        db = next(get_db())
        try:
            obra = db.query(Obra).filter(Obra.id == self.obra.id).first()
            if not obra or not obra.articulos:
                QMessageBox.warning(self, "Error", "La obra no tiene artículos asignados.")
                return
        finally:
            db.close()

        # Mostrar progreso
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.btn_generar_taller.setEnabled(False)

        try:
            # Paso 1: Cargar datos completos (25%)
            self.progress_bar.setValue(25)
            QApplication.processEvents()
            self._cargar_datos_completos_obra()

            # Paso 2: Procesar materiales con fórmulas (50%)
            self.progress_bar.setValue(50)
            QApplication.processEvents()
            self._procesar_materiales_agrupados()

            # Paso 3: Generar hojas profesionales (75%)
            self.progress_bar.setValue(75)
            QApplication.processEvents()
            self._generar_hojas_profesionales()

            # Paso 4: Finalizar (100%)
            self.progress_bar.setValue(100)
            QApplication.processEvents()

            # Habilitar botones
            self.btn_exportar_pdf.setEnabled(True)
            self.btn_imprimir.setEnabled(True)
            self.btn_generar_pedido.setEnabled(True)

            # Actualizar vista previa
            self._actualizar_vista_previa()

            # Cambiar a pestaña de hojas individuales
            self.tabs_principales.setCurrentIndex(2)

            QMessageBox.information(
                self,
                "✅ Informe Profesional Generado",
                "El informe de taller profesional ha sido generado correctamente.\n\n"
                "Características:\n"
                "• Formato profesional similar a la imagen de referencia\n"
                "• Dibujos técnicos con medidas precisas\n"
                "• Tablas organizadas por tipo de material\n"
                "• Cálculos automáticos de fórmulas\n"
                "• Información completa para el taller\n\n"
                "Puede exportar a PDF o imprimir."
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ Error",
                f"Error al generar el informe profesional: {str(e)}"
            )
        finally:
            self.progress_bar.setVisible(False)
            self.btn_generar_taller.setEnabled(True)

    def _generar_hojas_profesionales(self):
        """Genera hojas de taller profesionales para cada artículo."""
        if not self.datos_procesados:
            return

        for i, articulo in enumerate(self.datos_procesados['articulos']):
            # Actualizar estado en la tabla
            if i < self.lista_articulos_hojas.rowCount():
                self.lista_articulos_hojas.setItem(i, 3, QTableWidgetItem("✅ Generado"))

            # Generar datos profesionales de la hoja
            articulo['hoja_profesional'] = self._crear_hoja_profesional(articulo)

    def _organizar_perfiles_profesional(self, perfiles):
        """Organiza los datos de perfiles para el informe profesional.
        
        Args:
            perfiles (list): Lista de perfiles a organizar.
            
        Returns:
            list: Lista de perfiles organizados con formato profesional.
        """
        perfiles_organizados = []
        
        for perfil in perfiles:
            # Crear una copia del perfil para no modificar el original
            perfil_org = perfil.copy()
            
            # Asegurarse de que todos los campos necesarios existan
            perfil_org.setdefault('codigo', '')
            perfil_org.setdefault('descripcion', '')
            perfil_org.setdefault('cantidad', 0)
            perfil_org.setdefault('longitud', 0.0)
            perfil_org.setdefault('unidad', 'mm')
            perfil_org.setdefault('color', '')
            perfil_org.setdefault('angulo_izquierdo', 90)  # Valor por defecto 90° (corte recto)
            perfil_org.setdefault('angulo_derecho', 90)    # Valor por defecto 90° (corte recto)
            perfil_org.setdefault('variable_medida', 'H')  # 'H' para altura, 'A' para anchura
            
            # Calcular la longitud total
            perfil_org['longitud_total'] = perfil_org['cantidad'] * perfil_org['longitud']
            
            # Determinar el tipo de corte
            angulo_izq = perfil_org['angulo_izquierdo']
            angulo_der = perfil_org['angulo_derecho']
            tipo_corte = "Recto"
            
            if angulo_izq != 90 or angulo_der != 90:
                tipo_corte = f"{angulo_izq}°/{angulo_der}°"
            
            # Formatear la descripción para mostrar
            perfil_org['descripcion_completa'] = (
                f"{perfil_org['codigo']} - {perfil_org['descripcion']} "
                f"({perfil_org['longitud']:.0f}{perfil_org['unidad']}, "
                f"Corte: {tipo_corte}, Posición: {perfil_org['variable_medida']})"
            )
            
            # Agregar a la lista de perfiles organizados
            perfiles_organizados.append(perfil_org)
            
        return sorted(perfiles_organizados, key=lambda x: x.get('codigo', ''))
        
    def _organizar_cristales_profesional(self, cristales):
        """Organiza los datos de cristales para el informe profesional.
        
        Args:
            cristales (list): Lista de cristales a organizar.
            
        Returns:
            list: Lista de cristales organizados con formato profesional.
        """
        cristales_organizados = []
        
        for cristal in cristales:
            # Crear una copia del cristal para no modificar el original
            cristal_org = cristal.copy()
            
            # Asegurarse de que todos los campos necesarios existan
            cristal_org.setdefault('codigo', '')
            cristal_org.setdefault('descripcion', '')
            cristal_org.setdefault('cantidad', 0)
            cristal_org.setdefault('alto', 0.0)
            cristal_org.setdefault('ancho', 0.0)
            cristal_org.setdefault('unidad', 'mm')
            cristal_org.setdefault('espesor', 0.0)
            cristal_org.setdefault('color', '')
            cristal_org.setdefault('observaciones', '')
            
            # Calcular el área total
            area = (cristal_org['alto'] * cristal_org['ancho']) / 1000000  # Convertir a m²
            cristal_org['area_total'] = cristal_org['cantidad'] * area
            
            # Formatear la descripción para mostrar
            cristal_org['descripcion_completa'] = (
                f"{cristal_org['codigo']} - {cristal_org['descripcion']} "
                f"({cristal_org['espesor']:.1f}mm, {cristal_org['color']})"
            )
            
            # Formatear las dimensiones
            cristal_org['dimensiones'] = (
                f"{cristal_org['ancho']:.0f}x{cristal_org['alto']:.0f}{cristal_org['unidad']}"
            )
            
            # Agregar a la lista de cristales organizados
            cristales_organizados.append(cristal_org)
        
        # Ordenar los cristales por código para mejor presentación
        return sorted(cristales_organizados, key=lambda x: x.get('codigo', ''))
        
    def _organizar_accesorios_profesional(self, accesorios):
        """Organiza los datos de accesorios para el informe profesional.
        
        Args:
            accesorios (list): Lista de accesorios a organizar.
            
        Returns:
            list: Lista de accesorios organizados con formato profesional.
        """
        accesorios_organizados = []
        
        for accesorio in accesorios:
            # Crear una copia del accesorio para no modificar el original
            accesorio_org = accesorio.copy()
            
            # Asegurarse de que todos los campos necesarios existan
            accesorio_org.setdefault('codigo', '')
            accesorio_org.setdefault('descripcion', '')
            accesorio_org.setdefault('cantidad', 0)
            accesorio_org.setdefault('unidad', 'un')
            accesorio_org.setdefault('color', '')
            accesorio_org.setdefault('observaciones', '')
            accesorio_org.setdefault('posicion', '')
            
            # Formatear la descripción para mostrar
            descripcion = accesorio_org['descripcion']
            if accesorio_org['posicion']:
                descripcion = f"{descripcion} ({accesorio_org['posicion']})"
                
            accesorio_org['descripcion_completa'] = (
                f"{accesorio_org['codigo']} - {descripcion}"
            )
            
            # Agregar a la lista de accesorios organizados
            accesorios_organizados.append(accesorio_org)
        
        # Ordenar los accesorios por código y posición para mejor presentación
        return sorted(
            accesorios_organizados, 
            key=lambda x: (x.get('codigo', ''), x.get('posicion', ''))
        )
        
    def _organizar_persianas_profesional(self, persianas):
        """Organiza los datos de persianas para el informe profesional.
        
        Args:
            persianas (list): Lista de persianas a organizar.
            
        Returns:
            list: Lista de persianas organizadas con formato profesional.
        """
        persianas_organizadas = []
        
        for persiana in persianas:
            # Crear una copia de la persiana para no modificar el original
            persiana_org = persiana.copy()
            
            # Asegurarse de que todos los campos necesarios existan
            persiana_org.setdefault('codigo', '')
            persiana_org.setdefault('descripcion', '')
            persiana_org.setdefault('cantidad', 0)
            persiana_org.setdefault('ancho', 0.0)
            persiana_org.setdefault('alto', 0.0)
            persiana_org.setdefault('unidad', 'mm')
            persiana_org.setdefault('tipo', '')
            persiana_org.setdefault('color', '')
            persiana_org.setdefault('observaciones', '')
            
            # Calcular el área total
            area = (persiana_org['ancho'] * persiana_org['alto']) / 1000000  # Convertir a m²
            persiana_org['area_total'] = persiana_org['cantidad'] * area
            
            # Formatear la descripción para mostrar
            persiana_org['descripcion_completa'] = (
                f"{persiana_org['codigo']} - {persiana_org['descripcion']} "
                f"({persiana_org['tipo']}, {persiana_org['color']})"
            )
            
            # Formatear las dimensiones
            persiana_org['dimensiones'] = (
                f"{persiana_org['ancho']:.0f}x{persiana_org['alto']:.0f}{persiana_org['unidad']}"
            )
            
            # Agregar a la lista de persianas organizadas
            persianas_organizadas.append(persiana_org)
        
        # Ordenar las persianas por código para mejor presentación
        return sorted(
            persianas_organizadas,
            key=lambda x: x.get('codigo', '')
        )

    def _crear_hoja_profesional(self, articulo):
        """Crea una hoja de taller profesional con formato mejorado."""
        # Obtener información adicional de la obra_articulo
        db = next(get_db())
        try:
            obra_articulo = db.query(ObraArticulo).filter(
                ObraArticulo.obra_id == self.obra.id,
                ObraArticulo.articulo_id == articulo.get('id')
            ).first()

            cantidad = obra_articulo.cantidad if obra_articulo else 1

        except:
            cantidad = 1
        finally:
            db.close()

        # Estructura profesional de la hoja
        hoja = {
            'encabezado': {
                'empresa': self.campo_empresa.text() or "PRO-2000 Carpintería",
                'fecha': datetime.now().strftime('%d/%m/%Y'),
                'obra_codigo': self.obra.codigo,
                'obra_nombre': self.obra.nombre,
                'cliente': self.obra.cliente.nombre if self.obra.cliente else 'Sin cliente',
                'observaciones': getattr(self.obra, 'observaciones', '') or ''
            },
            'ventana': {
                'numero': 1,
                'articulo_codigo': articulo['codigo'],
                'articulo_nombre': articulo['nombre'],
                'tipo': 'Oscillobatiente de una hoja',  # Esto podría venir de la BD
                'cantidad': cantidad,
                'dimensiones': {
                    'altura': articulo['alto'],
                    'anchura': articulo['ancho'],
                    'texto': f"{articulo['alto']:.0f} x {articulo['ancho']:.0f} mm"
                },
                'acabado': 'Lac. Blanco',  # Esto podría venir de la BD
                'accesorio_acabado': 'Lac. Blanco',
                'montaje': 'No',
                'imagen_ventana': self._crear_imagen_ventana_profesional(articulo)
            },
            'materiales': {
                'perfiles': self._organizar_perfiles_profesional(articulo['perfiles']),
                'cristales': self._organizar_cristales_profesional(articulo['cristales']),
                'accesorios': self._organizar_accesorios_profesional(articulo['accesorios']),
                'persianas': self._organizar_persianas_profesional(articulo.get('persianas', []))
            },
            'campos_taller': {
                'encargado': '',
                'fecha_inicio': '',
                'horas_empleadas': '',
                'observaciones_taller': ''
            }
        }

        return hoja

    def _crear_imagen_ventana_profesional(self, articulo):
        """Crea una imagen profesional de la ventana con medidas como en la imagen de referencia."""
        try:
            alto = float(articulo['alto']) if articulo['alto'] else 1200.0
            ancho = float(articulo['ancho']) if articulo['ancho'] else 800.0

            # Tamaño del canvas más grande para mejor calidad
            canvas_width = 500
            canvas_height = 400

            pixmap = QPixmap(canvas_width, canvas_height)
            pixmap.fill(Qt.GlobalColor.white)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # Calcular dimensiones proporcionales del dibujo
            margen = 80
            max_width = canvas_width - 2 * margen
            max_height = canvas_height - 2 * margen

            # Mantener proporción
            ratio = min(max_width / ancho, max_height / alto)
            dibujo_width = int(ancho * ratio)
            dibujo_height = int(alto * ratio)

            # Centrar el dibujo
            x_centro = (canvas_width - dibujo_width) // 2
            y_centro = (canvas_height - dibujo_height) // 2

            # Dibujar el marco de la ventana
            painter.setPen(QPen(Qt.GlobalColor.black, 3))
            painter.setBrush(QBrush(Qt.GlobalColor.white))
            painter.drawRect(x_centro, y_centro, dibujo_width, dibujo_height)

            # Dibujar líneas interiores para simular el marco
            painter.setPen(QPen(Qt.GlobalColor.black, 2))
            margen_interior = 15
            painter.drawRect(
                x_centro + margen_interior,
                y_centro + margen_interior,
                dibujo_width - 2 * margen_interior,
                dibujo_height - 2 * margen_interior
            )

            # Dibujar medida de ALTURA (izquierda) - ROJO
            x_medida_altura = x_centro - 60
            painter.setPen(QPen(Qt.GlobalColor.red, 2))

            # Línea principal vertical
            painter.drawLine(x_medida_altura, y_centro, x_medida_altura, y_centro + dibujo_height)

            # Marcas horizontales
            painter.drawLine(x_medida_altura - 15, y_centro, x_medida_altura + 15, y_centro)
            painter.drawLine(x_medida_altura - 15, y_centro + dibujo_height, x_medida_altura + 15, y_centro + dibujo_height)

            # Texto de altura (rotado)
            painter.save()
            painter.translate(x_medida_altura - 35, y_centro + dibujo_height // 2)
            painter.rotate(-90)
            painter.setFont(QFont("Arial", 12, QFont.Weight.Bold))
            painter.setPen(QPen(Qt.GlobalColor.red, 2))
            painter.drawText(0, 0, f"{alto:.0f} mm")
            painter.restore()

            # Dibujar medida de ANCHURA (abajo) - ROJO
            y_medida_anchura = y_centro + dibujo_height + 60
            painter.setPen(QPen(Qt.GlobalColor.red, 2))

            # Línea principal horizontal
            painter.drawLine(x_centro, y_medida_anchura, x_centro + dibujo_width, y_medida_anchura)

            # Marcas verticales
            painter.drawLine(x_centro, y_medida_anchura - 15, x_centro, y_medida_anchura + 15)
            painter.drawLine(x_centro + dibujo_width, y_medida_anchura - 15, x_centro + dibujo_width, y_medida_anchura + 15)

            # Texto de anchura
            painter.setFont(QFont("Arial", 12, QFont.Weight.Bold))
            text_width = painter.fontMetrics().horizontalAdvance(f"{ancho:.0f} mm")
            painter.drawText(x_centro + (dibujo_width - text_width) // 2, y_medida_anchura + 30, f"{ancho:.0f} mm")

            painter.end()
            return pixmap

        except Exception as e:
            print(f"Error creando imagen profesional: {e}")
            # Imagen de error simple
            pixmap = QPixmap(500, 400)
            pixmap.fill(Qt.GlobalColor.white)
            painter = QPainter(pixmap)
            painter.drawText(50, 200, f"Error: {articulo['codigo']}")
            painter.end()
            return pixmap

    def _generar_informe_completo(self):
        """Genera el informe completo con todas las opciones."""
        if not self.obra:
            QMessageBox.warning(self, "Error", "No hay obra seleccionada.")
            return

        # Mostrar progreso
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.btn_generar_informe.setEnabled(False)

        try:
            # Paso 1: Cargar datos de la obra (20%)
            self.progress_bar.setValue(20)
            QApplication.processEvents()

            self._cargar_datos_completos_obra()

            # Paso 2: Procesar materiales (40%)
            self.progress_bar.setValue(40)
            QApplication.processEvents()

            self._procesar_materiales_agrupados()

            # Paso 3: Generar hojas individuales (60%)
            self.progress_bar.setValue(60)
            QApplication.processEvents()

            if self.check_hojas_individuales.isChecked():
                self._generar_hojas_individuales()

            # Paso 4: Generar pedido individual (80%)
            self.progress_bar.setValue(80)
            QApplication.processEvents()

            if self.check_pedido_individual.isChecked():
                self._generar_datos_pedido_individual()

            # Paso 5: Finalizar (100%)
            self.progress_bar.setValue(100)
            QApplication.processEvents()

            # Habilitar botones
            self.btn_exportar_pdf.setEnabled(True)
            self.btn_imprimir.setEnabled(True)
            self.btn_generar_pedido.setEnabled(True)

            # Actualizar vista previa
            self._actualizar_vista_previa()

            # Cambiar a pestaña de vista previa
            self.tabs_principales.setCurrentIndex(1)

            QMessageBox.information(
                self,
                "✅ Informe Generado",
                "El informe de taller ha sido generado correctamente.\n\n"
                "Puede ver la vista previa, exportar a PDF o imprimir."
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ Error",
                f"Error al generar el informe: {str(e)}"
            )
        finally:
            self.progress_bar.setVisible(False)
            self.btn_generar_informe.setEnabled(True)

    def _cargar_datos_completos_obra(self):
        """Carga todos los datos necesarios de la obra."""
        db = next(get_db())
        try:
            # Obtener artículos de la obra con todas las relaciones
            obra_articulos = db.query(ObraArticulo).filter(
                ObraArticulo.obra_id == self.obra.id
            ).all()

            self.datos_procesados = {
                'obra': {
                    'codigo': self.obra.codigo,
                    'nombre': self.obra.nombre,
                    'cliente': self.obra.cliente.nombre if self.obra.cliente else 'Sin cliente',
                    'estado': self.obra.estado or 'Pendiente',
                    'fecha_inicio': self.obra.fecha_inicio,
                    'presupuesto': self.obra.presupuesto or 0
                },
                'articulos': [],
                'materiales_agrupados': {
                    'perfiles': [],
                    'cristales': [],
                    'accesorios': [],
                    'persianas': []
                },
                'fecha_generacion': datetime.now()
            }

            # Procesar cada artículo
            for obra_articulo in obra_articulos:
                if obra_articulo.articulo:
                    articulo_data = {
                        'codigo': obra_articulo.articulo.codigo,
                        'nombre': obra_articulo.articulo.descripcion,
                        'cantidad_obra': obra_articulo.cantidad,
                        'alto': obra_articulo.altura,
                        'ancho': obra_articulo.anchura,
                        'imagen_path': self._buscar_imagen_articulo(obra_articulo.articulo.codigo),
                        'perfiles': [],
                        'cristales': [],
                        'accesorios': [],
                        'persianas': []
                    }

                    # Procesar componentes del artículo
                    self._procesar_componentes_articulo(articulo_data, obra_articulo.articulo)

                    self.datos_procesados['articulos'].append(articulo_data)

        finally:
            db.close()

    def _buscar_imagen_articulo(self, codigo_articulo):
        """Obtiene la ruta de la imagen asociada al artículo desde la base de datos."""
        db = next(get_db())
        try:
            articulo = db.query(Articulo).filter(Articulo.codigo == codigo_articulo).first()
            if articulo and articulo.imagen_path:
                return articulo.imagen_path
            return None
        finally:
            db.close()

    def _generar_imagenes_ventanas(self):
        """Genera imágenes de ventanas con medidas para cada artículo."""
        if not self.datos_procesados:
            return

        for articulo in self.datos_procesados['articulos']:
            # Generar imagen de ventana con medidas
            imagen_ventana = self._crear_imagen_ventana_con_medidas(
                articulo['alto'],
                articulo['ancho'],
                articulo['codigo']
            )

            # Guardar la imagen generada
            if imagen_ventana:
                articulo['imagen_ventana_generada'] = imagen_ventana

    def _crear_imagen_ventana_con_medidas(self, alto, ancho, codigo_articulo):
        """Crea imagen con medidas profesionales: altura izquierda, anchura abajo."""
        try:
            alto = float(alto) if alto else 1200.0
            ancho = float(ancho) if ancho else 800.0
            
            # Configuración de dibujo
            pixmap = QPixmap(600, 500)  # Tamaño aumentado para mejor legibilidad
            pixmap.fill(Qt.GlobalColor.white)
            
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            
            # Dimensiones proporcionales
            margen = 100
            ancho_dibujo = 300
            alto_dibujo = int((alto/ancho) * ancho_dibujo) if ancho > 0 else 250
            x_centro = (pixmap.width() - ancho_dibujo) // 2
            y_centro = (pixmap.height() - alto_dibujo) // 2
            
            # Dibujar marco
            painter.setPen(QPen(Qt.GlobalColor.black, 3))
            painter.drawRect(x_centro, y_centro, ancho_dibujo, alto_dibujo)
            
            # Medida de ALTURA (izquierda)
            x_medida = x_centro - 50
            painter.setPen(QPen(Qt.GlobalColor.red, 2))
            painter.drawLine(x_medida, y_centro, x_medida, y_centro + alto_dibujo)  # Línea principal
            # Marcas de medida
            painter.drawLine(x_medida-10, y_centro, x_medida+10, y_centro)  # Superior
            painter.drawLine(x_medida-10, y_centro+alto_dibujo, x_medida+10, y_centro+alto_dibujo)  # Inferior
            # Texto de altura (rotado)
            painter.save()
            painter.translate(x_medida-30, y_centro + alto_dibujo//2)
            painter.rotate(-90)
            painter.setFont(QFont("Arial", 14, QFont.Weight.Bold))
            painter.drawText(0, 0, f"{alto:.0f} mm")
            painter.restore()
            
            # Medida de ANCHURA (abajo)
            y_medida = y_centro + alto_dibujo + 50
            painter.drawLine(x_centro, y_medida, x_centro + ancho_dibujo, y_medida)  # Línea principal
            # Marcas de medida
            painter.drawLine(x_centro, y_medida-10, x_centro, y_medida+10)  # Izquierda
            painter.drawLine(x_centro+ancho_dibujo, y_medida-10, x_centro+ancho_dibujo, y_medida+10)  # Derecha
            # Texto de anchura
            painter.setFont(QFont("Arial", 14, QFont.Weight.Bold))
            painter.drawText(x_centro + ancho_dibujo//2 - 50, y_medida + 30, f"{ancho:.0f} mm")
            
            painter.end()
            return pixmap

        except Exception as e:
            print(f"Error creando imagen de ventana: {e}")
            # Crear imagen simple en caso de error
            pixmap = QPixmap(500, 400)
            pixmap.fill(Qt.GlobalColor.white)
            painter = QPainter(pixmap)
            painter.drawText(50, 200, f"Error: {codigo_articulo}")
            painter.end()
            return pixmap

    def _generar_hojas_taller_completas(self):
        """Genera las hojas de taller completas para cada artículo."""
        if not self.datos_procesados:
            return

        for i, articulo in enumerate(self.datos_procesados['articulos']):
            # Actualizar estado en la tabla
            self.lista_articulos_hojas.setItem(i, 3, QTableWidgetItem("✅ Generado"))

            # Generar datos completos de la hoja de taller
            articulo['hoja_taller'] = self._crear_datos_hoja_taller(articulo)

    def _crear_datos_hoja_taller(self, articulo):
        """Crea los datos completos para la hoja de taller de un artículo."""
        datos = {
            'encabezado': {
                'titulo': 'Hoja de Taller',
                'obra': f"{self.obra.codigo} - {self.obra.nombre}",
                'cliente': self.obra.cliente.nombre if self.obra.cliente else 'Sin cliente',
                'articulo': f"{articulo['codigo']} - {articulo['nombre']}",
                'cantidad': articulo['cantidad_obra'],
                'fecha': datetime.now().strftime('%d/%m/%Y'),
                'encargado': '',
                'horas_empleadas': ''
            },
            'ventana': {
                'numero': 1,
                'tipo': 'Oscillobatiente de una hoja',
                'dimensiones': f"{articulo['alto']} x {articulo['ancho']} mm",
                'acabado': 'Lac. Blanco',
                'accesorio': 'Lac. Blanco',
                'montaje': 'No',
                'imagen': articulo.get('imagen_ventana_generada'),
                'imagen_path': articulo.get('imagen_path')
            },
            'materiales': {
                'perfiles': [],
                'cristales': articulo['cristales'],
                'accesorios': articulo['accesorios'],
                'persianas': articulo.get('persianas', [])
            }
        }

        # Procesar perfiles con medidas separadas
        for perfil in articulo['perfiles']:
            datos['materiales']['perfiles'].append({
                'codigo': perfil['codigo'],
                'descripcion': perfil['descripcion'],
                'altura': perfil.get('altura', 'N/A'),
                'anchura': perfil.get('anchura', 'N/A'),
                'medida': perfil.get('medida', 'N/A'),
                'cantidad': perfil['cantidad'],
                'tipo': perfil.get('tipo', 'marco')
            })

        return datos

    def _ver_hoja_taller_articulo(self, articulo):
        """Muestra la hoja de taller completa de un artículo."""
        # Crear ventana de vista previa
        dialog = QDialog(self)
        dialog.setWindowTitle(f"Hoja de Taller - {articulo.codigo}")
        dialog.setMinimumSize(900, 700)
        dialog.setModal(True)

        layout = QVBoxLayout(dialog)

        # Encabezado
        encabezado = QFrame()
        encabezado.setFrameStyle(QFrame.Shape.Box)
        encabezado.setStyleSheet("background-color: #f8f9fa; padding: 10px;")

        layout_encabezado = QHBoxLayout(encabezado)

        # Campos del encabezado
        layout_campos = QHBoxLayout()

        # Encargado
        layout_campos.addWidget(QLabel("Encargado:"))
        campo_encargado = QLineEdit()
        campo_encargado.setMaximumWidth(200)
        layout_campos.addWidget(campo_encargado)

        layout_campos.addStretch()

        # Fecha inicio
        layout_campos.addWidget(QLabel("Fecha inicio:"))
        campo_fecha = QLineEdit()
        campo_fecha.setText(datetime.now().strftime("%d/%m/%Y"))
        campo_fecha.setMaximumWidth(100)
        layout_campos.addWidget(campo_fecha)

        layout_campos.addStretch()

        # Horas empleadas
        layout_campos.addWidget(QLabel("Horas empleadas:"))
        campo_horas = QLineEdit()
        campo_horas.setMaximumWidth(100)
        layout_campos.addWidget(campo_horas)

        layout_encabezado.addLayout(layout_campos)
        layout.addWidget(encabezado)

        # Información del artículo y cliente
        frame_info = QFrame()
        frame_info.setFrameStyle(QFrame.Shape.Box)
        frame_info.setStyleSheet("padding: 10px;")

        layout_info = QHBoxLayout(frame_info)

        # Título "Hoja de taller"
        titulo_hoja = QLabel("Hoja de taller")
        titulo_hoja.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout_info.addWidget(titulo_hoja)

        layout_info.addStretch()

        # Información de la obra y artículo
        info_texto = f"Obra: {self.obra.codigo}"
        if hasattr(self.obra, 'observaciones') and self.obra.observaciones:
            info_texto += f"    Observaciones: {self.obra.observaciones}"

        label_obra = QLabel(info_texto)
        layout_info.addWidget(label_obra)

        # Cliente
        if self.obra.cliente:
            label_cliente = QLabel(f"Cliente: {self.obra.cliente.nombre}")
            layout_info.addWidget(label_cliente)

        layout.addWidget(frame_info)

        # Área de dibujo y información
        frame_dibujo = QFrame()
        frame_dibujo.setFrameStyle(QFrame.Shape.Box)
        frame_dibujo.setMinimumHeight(350)
        frame_dibujo.setStyleSheet("background-color: white;")

        layout_dibujo = QHBoxLayout(frame_dibujo)

        # Panel izquierdo con el dibujo
        widget_dibujo = QWidget()
        widget_dibujo.setMinimumSize(450, 330)
        widget_dibujo.setMaximumSize(450, 330)

        # Obtener las dimensiones de la obra_articulo
        db = next(get_db())
        try:
            obra_articulo = db.query(ObraArticulo).filter(
                ObraArticulo.obra_id == self.obra.id,
                ObraArticulo.articulo_id == articulo.id
            ).first()

            alto = obra_articulo.altura if obra_articulo else 1200
            ancho = obra_articulo.anchura if obra_articulo else 800
        finally:
            db.close()

        # Crear el dibujo de la ventana
        imagen_ventana = self._crear_imagen_ventana_con_medidas(
            alto,
            ancho,
            articulo.codigo
        )

        label_dibujo = QLabel()
        label_dibujo.setPixmap(imagen_ventana)
        label_dibujo.setAlignment(Qt.AlignmentFlag.AlignCenter)

        layout_dibujo_widget = QVBoxLayout(widget_dibujo)
        layout_dibujo_widget.addWidget(label_dibujo)

        layout_dibujo.addWidget(widget_dibujo)

        # Panel derecho con información de la ventana
        widget_info = QWidget()
        layout_info_ventana = QVBoxLayout(widget_info)

        # Información del artículo
        info_frame = QFrame()
        info_frame.setFrameStyle(QFrame.Shape.Box)
        info_frame.setStyleSheet("background-color: #f0f0f0; padding: 10px;")

        layout_info_frame = QVBoxLayout(info_frame)

        # Título de la ventana
        titulo_ventana = QLabel(f"VENTANA Nº: 1")
        titulo_ventana.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        titulo_ventana.setAlignment(Qt.AlignmentFlag.AlignCenter)
        titulo_ventana.setStyleSheet("background-color: white; padding: 5px; border: 1px solid black;")
        layout_info_frame.addWidget(titulo_ventana)

        # Información detallada
        info_detalle = QVBoxLayout()

        # Artículo
        articulo_info = f"Artículo: {articulo.codigo}    {articulo.descripcion}"
        if hasattr(articulo, 'observaciones') and articulo.observaciones:
            articulo_info += f"\n{articulo.observaciones}"
        else:
            articulo_info += "\nOscillobatiente de una hoja"

        label_articulo = QLabel(articulo_info)
        label_articulo.setWordWrap(True)
        info_detalle.addWidget(label_articulo)

        # Cantidad
        db = next(get_db())
        try:
            obra_articulo = db.query(ObraArticulo).filter(
                ObraArticulo.obra_id == self.obra.id,
                ObraArticulo.articulo_id == articulo.id
            ).first()
            cantidad = obra_articulo.cantidad if obra_articulo else 1
        finally:
            db.close()

        cantidad_info = f"Cantidad: {cantidad}"
        label_cantidad = QLabel(cantidad_info)
        info_detalle.addWidget(label_cantidad)

        # Acabado
        acabado_info = "Acabado: Lac. Blanco"
        label_acabado = QLabel(acabado_info)
        info_detalle.addWidget(label_acabado)

        # Accesorio
        accesorio_info = "Accesorio: Lac. Blanco"
        label_accesorio = QLabel(accesorio_info)
        info_detalle.addWidget(label_accesorio)

        # Montaje
        montaje_info = "Montaje: No"
        label_montaje = QLabel(montaje_info)
        info_detalle.addWidget(label_montaje)

        # Cristal
        info_detalle.addWidget(QLabel("Cristal:"))

        # Persiana
        info_detalle.addWidget(QLabel("Persiana:"))

        layout_info_frame.addLayout(info_detalle)
        layout_info_ventana.addWidget(info_frame)
        layout_info_ventana.addStretch()

        layout_dibujo.addWidget(widget_info)
        layout.addWidget(frame_dibujo)

        # Tabla de perfiles
        titulo_perfiles = QLabel("Perfiles")
        titulo_perfiles.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        titulo_perfiles.setStyleSheet("background-color: #e0e0e0; padding: 5px; border: 1px solid black;")
        layout.addWidget(titulo_perfiles)

        # Crear tabla de perfiles
        tabla_perfiles = QTableWidget()
        tabla_perfiles.setColumnCount(6)
        tabla_perfiles.setHorizontalHeaderLabels([
            "Código", "Descripción", "Medida", "Cantidad", "Acabado", "Ingletes"
        ])

        # Configurar tabla
        header = tabla_perfiles.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)

        # Cargar datos de perfiles del artículo
        db = next(get_db())
        try:
            from models.articulo import ArticuloPerfil
            perfiles_articulo = db.query(ArticuloPerfil).filter(
                ArticuloPerfil.articulo_id == articulo.id
            ).all()

            tabla_perfiles.setRowCount(len(perfiles_articulo))

            for i, perfil_art in enumerate(perfiles_articulo):
                if perfil_art.perfil:
                    # Calcular medida usando la fórmula o usar cantidad_base
                    try:
                        if perfil_art.medida_formula:
                            # Aquí se podría evaluar la fórmula, por ahora usar valor por defecto
                            medida = 1000  # Valor por defecto en mm
                        else:
                            medida = 1000  # Valor por defecto en mm
                    except:
                        medida = 1000

                    tabla_perfiles.setItem(i, 0, QTableWidgetItem(perfil_art.perfil.codigo))
                    tabla_perfiles.setItem(i, 1, QTableWidgetItem(perfil_art.perfil.descripcion))
                    tabla_perfiles.setItem(i, 2, QTableWidgetItem(f"{medida} mm"))
                    tabla_perfiles.setItem(i, 3, QTableWidgetItem(str(perfil_art.cantidad_base or 1)))
                    tabla_perfiles.setItem(i, 4, QTableWidgetItem("Lac. Blanco"))
                    tabla_perfiles.setItem(i, 5, QTableWidgetItem("90°/90°"))

        except Exception as e:
            print(f"Error cargando perfiles: {e}")
        finally:
            db.close()

        tabla_perfiles.setMaximumHeight(200)
        layout.addWidget(tabla_perfiles)

        # Botones
        layout_botones = QHBoxLayout()

        btn_exportar_hoja = QPushButton("📄 Exportar Hoja PDF")
        btn_exportar_hoja.clicked.connect(lambda: self._exportar_hoja_individual_pdf(articulo))
        layout_botones.addWidget(btn_exportar_hoja)

        btn_imprimir_hoja = QPushButton("🖨️ Imprimir Hoja")
        btn_imprimir_hoja.clicked.connect(lambda: self._imprimir_hoja_individual(articulo))
        layout_botones.addWidget(btn_imprimir_hoja)

        layout_botones.addStretch()

        btn_cerrar = QPushButton("❌ Cerrar")
        btn_cerrar.clicked.connect(dialog.reject)
        layout_botones.addWidget(btn_cerrar)

        layout.addLayout(layout_botones)

        dialog.exec()

    def _exportar_hoja_individual_pdf(self, articulo):
        """Exporta una hoja individual a PDF."""
        if not REPORTLAB_AVAILABLE:
            QMessageBox.warning(
                self,
                "ReportLab no disponible",
                "Para exportar a PDF, instale ReportLab:\npip install reportlab"
            )
            return

        # Seleccionar archivo
        nombre_archivo = f"Hoja_Taller_{articulo.codigo}_{datetime.now().strftime('%Y%m%d')}.pdf"
        archivo, _ = QFileDialog.getSaveFileName(
            self,
            "Exportar Hoja de Taller",
            nombre_archivo,
            "PDF (*.pdf);;Todos los archivos (*)"
        )

        if not archivo:
            return

        try:
            # Crear documento PDF
            doc = SimpleDocTemplate(archivo, pagesize=A4)
            story = []
            styles = getSampleStyleSheet()

            # Título
            titulo = Paragraph("HOJA DE TALLER", styles['Title'])
            story.append(titulo)
            story.append(Spacer(1, 12))

            # Información de la obra
            info_obra = f"""
            <b>Obra:</b> {self.obra.codigo} - {self.obra.nombre}<br/>
            <b>Cliente:</b> {self.obra.cliente.nombre if self.obra.cliente else 'Sin cliente'}<br/>
            <b>Fecha:</b> {datetime.now().strftime('%d/%m/%Y')}<br/>
            <b>Artículo:</b> {articulo.codigo} - {articulo.descripcion}
            """
            story.append(Paragraph(info_obra, styles['Normal']))
            story.append(Spacer(1, 20))

            # Obtener dimensiones de la obra_articulo
            db_temp = next(get_db())
            try:
                obra_articulo_temp = db_temp.query(ObraArticulo).filter(
                    ObraArticulo.obra_id == self.obra.id,
                    ObraArticulo.articulo_id == articulo.id
                ).first()

                alto_temp = obra_articulo_temp.altura if obra_articulo_temp else 1200
                ancho_temp = obra_articulo_temp.anchura if obra_articulo_temp else 800
            finally:
                db_temp.close()

            # Información de la ventana
            ventana_info = f"""
            <b>VENTANA Nº 1</b><br/>
            <b>Tipo:</b> Oscillobatiente de una hoja<br/>
            <b>Dimensiones:</b> {alto_temp} x {ancho_temp} mm<br/>
            <b>Acabado:</b> Lac. Blanco<br/>
            <b>Montaje:</b> No
            """
            story.append(Paragraph(ventana_info, styles['Normal']))
            story.append(Spacer(1, 20))

            # Tabla de perfiles
            story.append(Paragraph("PERFILES", styles['Heading2']))

            # Obtener perfiles del artículo
            db = next(get_db())
            try:
                from models.articulo import ArticuloPerfil
                perfiles_articulo = db.query(ArticuloPerfil).filter(
                    ArticuloPerfil.articulo_id == articulo.id
                ).all()

                if perfiles_articulo:
                    # Crear tabla de perfiles
                    perfiles_data = [['Código', 'Descripción', 'Medida', 'Cantidad', 'Ángulo Izq.', 'Ángulo Der.', 'Posición']]

                    for perfil_art in perfiles_articulo:
                        if perfil_art.perfil:
                            # Calcular medida usando la fórmula o usar valor por defecto
                            try:
                                # Calcular medida real basada en variable_medida
                                if perfil_art.variable_medida in ['H','H1','H2','H3','H4']:
                                    medida = articulo.altura
                                else:
                                    medida = articulo.anchura
                            except Exception as e:
                                print(f"Error calculando medida: {str(e)}")
                                medida = 1000  # Valor por defecto
                                try:
                                    if perfil_art.variable_medida in ['H','H1','H2','H3','H4']:
                                        medida = articulo.altura
                                    else:
                                        medida = articulo.anchura
                                    
                                    # Asegurar que todos los campos existan
                                    try:
                                        perfiles_data.append([
                                            perfil_art.perfil.codigo,
                                            perfil_art.perfil.descripcion,
                                            f"{medida:.0f} mm" if medida else "N/A",
                                            str(perfil_art.cantidad_base or 1),
                                            f"{perfil_art.angulo_izquierdo or 90}°",
                                            f"{perfil_art.angulo_derecho or 90}°",
                                            perfil_art.descripcion_posicion or ""
                                        ])
                                    except Exception as e:
                                        print(f"Error formateando perfil: {str(e)}")
                                        continue
                                except Exception as e:
                                    print(f"Error procesando perfil: {str(e)}")
                                    continue

                    # Configurar tabla profesional de perfiles
                    perfiles_table = Table(perfiles_data,
                                        colWidths=[2*cm, 5*cm, 2*cm, 1.5*cm, 1.5*cm, 1.5*cm, 3*cm])
                    
                    estilo_profesional = TableStyle([
                        ('BACKGROUND', (0,0), (-1,0), colors.HexColor('#2c3e50')),
                        ('TEXTCOLOR', (0,0), (-1,0), colors.white),
                        ('ALIGN', (0,0), (-1,-1), 'CENTER'),
                        ('FONTNAME', (0,0), (-1,0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0,0), (-1,0), 9),
                        ('BOTTOMPADDING', (0,0), (-1,0), 6),
                        ('BACKGROUND', (0,1), (-1,-1), colors.HexColor('#ecf0f1')),
                        ('GRID', (0,0), (-1,-1), 0.5, colors.HexColor('#bdc3c7')),
                        ('VALIGN', (0,0), (-1,-1), 'MIDDLE')
                    ])
                    perfiles_table.setStyle(estilo_profesional)

                    story.append(perfiles_table)
                else:
                    story.append(Paragraph("No hay perfiles definidos para este artículo.", styles['Normal']))

            finally:
                db.close()

            # Generar PDF
            doc.build(story)

            QMessageBox.information(
                self,
                "✅ Exportación Exitosa",
                f"Hoja de taller exportada correctamente a:\n{archivo}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ Error",
                f"Error al exportar la hoja de taller: {str(e)}"
            )

    def _imprimir_hoja_individual(self, articulo):
        """Imprime una hoja individual."""
        if REPORTLAB_AVAILABLE:
            # Crear PDF temporal e imprimir
            try:
                import tempfile
                with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
                    temp_path = temp_file.name

                # Generar PDF temporal
                self._exportar_hoja_individual_pdf_temp(articulo, temp_path)

                # Abrir con visor predeterminado
                import subprocess
                import platform

                if platform.system() == 'Windows':
                    os.startfile(temp_path)
                elif platform.system() == 'Darwin':  # macOS
                    subprocess.call(['open', temp_path])
                else:  # Linux
                    subprocess.call(['xdg-open', temp_path])

                QMessageBox.information(
                    self,
                    "Impresión",
                    "Se ha abierto la hoja de taller en el visor predeterminado.\nPuede imprimirla desde allí."
                )

            except Exception as e:
                QMessageBox.critical(
                    self,
                    "Error de impresión",
                    f"Error al preparar la impresión: {str(e)}"
                )
        else:
            QMessageBox.information(
                self,
                "Funcionalidad limitada",
                "Para imprimir directamente, instale ReportLab:\npip install reportlab"
            )

    def _exportar_hoja_individual_pdf_temp(self, articulo, archivo):
        """Exporta una hoja individual a un archivo temporal."""
        # Crear documento PDF
        doc = SimpleDocTemplate(archivo, pagesize=A4)
        story = []
        styles = getSampleStyleSheet()

        # Título
        titulo = Paragraph("HOJA DE TALLER", styles['Title'])
        story.append(titulo)
        story.append(Spacer(1, 12))

        # Información de la obra
        info_obra = f"""
        <b>Obra:</b> {self.obra.codigo} - {self.obra.nombre}<br/>
        <b>Cliente:</b> {self.obra.cliente.nombre if self.obra.cliente else 'Sin cliente'}<br/>
        <b>Fecha:</b> {datetime.now().strftime('%d/%m/%Y')}<br/>
        <b>Artículo:</b> {articulo.codigo} - {articulo.descripcion}
        """
        story.append(Paragraph(info_obra, styles['Normal']))
        story.append(Spacer(1, 20))

        # Obtener dimensiones de la obra_articulo
        db_temp = next(get_db())
        try:
            obra_articulo_temp = db_temp.query(ObraArticulo).filter(
                ObraArticulo.obra_id == self.obra.id,
                ObraArticulo.articulo_id == articulo.id
            ).first()

            alto_temp = obra_articulo_temp.altura if obra_articulo_temp else 1200
            ancho_temp = obra_articulo_temp.anchura if obra_articulo_temp else 800
        finally:
            db_temp.close()

        # Información de la ventana
        ventana_info = f"""
        <b>VENTANA Nº 1</b><br/>
        <b>Tipo:</b> Oscillobatiente de una hoja<br/>
        <b>Dimensiones:</b> {alto_temp} x {ancho_temp} mm<br/>
        <b>Acabado:</b> Lac. Blanco<br/>
        <b>Montaje:</b> No
        """
        story.append(Paragraph(ventana_info, styles['Normal']))
        story.append(Spacer(1, 20))

        # Tabla de perfiles
        story.append(Paragraph("PERFILES", styles['Heading2']))

        # Obtener perfiles del artículo
        db = next(get_db())
        try:
            from models.articulo import ArticuloPerfil
            perfiles_articulo = db.query(ArticuloPerfil).filter(
                ArticuloPerfil.articulo_id == articulo.id
            ).all()

            if perfiles_articulo:
                # Crear tabla de perfiles
                perfiles_data = [['Código', 'Descripción', 'Medida', 'Cantidad', 'Acabado', 'Ingletes']]

                for perfil_art in perfiles_articulo:
                    if perfil_art.perfil:
                        # Calcular medida usando la fórmula o usar valor por defecto
                        try:
                            if perfil_art.medida_formula:
                                medida = 1000  # Valor por defecto en mm
                            else:
                                medida = 1000  # Valor por defecto en mm
                        except:
                            medida = 1000

                        perfiles_data.append([
                            perfil_art.perfil.codigo,
                            perfil_art.perfil.descripcion,
                            f"{medida} mm",
                            str(perfil_art.cantidad_base or 1),
                            "Lac. Blanco",
                            "90°/90°"
                        ])

                perfiles_table = Table(perfiles_data, colWidths=[2*cm, 5*cm, 2*cm, 1.5*cm, 2*cm, 1.5*cm])
                perfiles_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                story.append(perfiles_table)
            else:
                story.append(Paragraph("No hay perfiles definidos para este artículo.", styles['Normal']))

        finally:
            db.close()

        # Generar PDF
        doc.build(story)

    def _procesar_componentes_articulo(self, articulo_data, articulo):
        """Procesa componentes con sesión activa para evitar errores de carga."""
        db = next(get_db())
        try:
            # Recargar artículo con todas las relaciones necesarias
            articulo_db = db.query(Articulo).options(
                joinedload(Articulo.perfiles),
                joinedload(Articulo.cristales),
                joinedload(Articulo.accesorios)
            ).filter(Articulo.id == articulo.id).first()
            
            if not articulo_db:
                return
                
            # Procesar perfiles
            if hasattr(articulo_db, 'perfiles') and articulo_db.perfiles:
                from models.formula_calculator import FormulaCalculator
                
                # Obtener dimensiones del artículo
                try:
                    alto = float(articulo_data.get('alto', 0))
                    ancho = float(articulo_data.get('ancho', 0))
                except (ValueError, TypeError):
                    alto = 0
                    ancho = 0
                
                # Variables adicionales para la calculadora
                variables_adicionales = {
                    'H1': alto,
                    'A1': ancho,
                    'H2': alto / 2 if alto else 0,
                    'A2': ancho / 2 if ancho else 0
                }
                
                # Inicializar calculadora con altura y anchura principales
                calculator = FormulaCalculator(
                    altura=alto,
                    anchura=ancho,
                    variables_adicionales=variables_adicionales
                )
                
                for perfil_articulo in articulo_db.perfiles:
                    # Evaluar fórmula de longitud si existe
                    if perfil_articulo.medida_formula:
                        try:
                            longitud = calculator.evaluar(perfil_articulo.medida_formula)
                        except Exception as e:
                            print(f"Error evaluando fórmula de longitud para perfil {perfil_articulo.id}: {str(e)}")
                            longitud = 0
                    else:
                        longitud = 0
                    
                    # Evaluar fórmula de cantidad si existe
                    if perfil_articulo.cantidad_formula:
                        try:
                            cantidad = calculator.evaluar(perfil_articulo.cantidad_formula)
                        except Exception as e:
                            print(f"Error evaluando fórmula de cantidad para perfil {perfil_articulo.id}: {str(e)}")
                            cantidad = perfil_articulo.cantidad_base
                    else:
                        cantidad = perfil_articulo.cantidad_base
                    
                    perfil_data = {
                        'codigo': perfil_articulo.perfil.codigo,
                        'descripcion': perfil_articulo.perfil.descripcion,
                        'longitud': longitud,  # Usar longitud evaluada
                        'medida': longitud,    # Mantener compatibilidad con código existente
                        'cantidad': cantidad,
                        'variable_medida': perfil_articulo.variable_medida or 'H',
                        'angulo_izquierdo': perfil_articulo.angulo_izquierdo or 90,
                        'angulo_derecho': perfil_articulo.angulo_derecho or 90,
                        'descripcion_posicion': perfil_articulo.descripcion_posicion or ''
                    }
                    articulo_data['perfiles'].append(perfil_data)
            
            # Procesar cristales
            if hasattr(articulo_db, 'cristales') and articulo_db.cristales:
                for cristal_articulo in articulo_db.cristales:
                    # Calcular dimensiones usando las medidas de la obra
                    try:
                        # Usar las dimensiones de la obra_articulo
                        alto_cristal = articulo_data['alto'] - 30  # Restar marco
                        ancho_cristal = articulo_data['ancho'] - 30  # Restar marco
                        metros_cuadrados = (alto_cristal * ancho_cristal) / 1000000
                    except:
                        alto_cristal = 1000
                        ancho_cristal = 800
                        metros_cuadrados = 0.8

                    cristal_data = {
                        'codigo': cristal_articulo.cristal.codigo,
                        'descripcion': cristal_articulo.cristal.descripcion,
                        'alto': alto_cristal,
                        'ancho': ancho_cristal,
                        'cantidad': cristal_articulo.cantidad_base,
                        'metros_cuadrados': metros_cuadrados
                    }
                    articulo_data['cristales'].append(cristal_data)

            # Procesar accesorios
            if hasattr(articulo_db, 'accesorios') and articulo_db.accesorios:
                for accesorio_articulo in articulo_db.accesorios:
                    accesorio_data = {
                        'codigo': accesorio_articulo.accesorio.codigo,
                        'descripcion': accesorio_articulo.accesorio.descripcion,
                        'cantidad': accesorio_articulo.cantidad_base,
                        'unidad': getattr(accesorio_articulo.accesorio, 'unidad', 'ud')
                    }
                    articulo_data['accesorios'].append(accesorio_data)
                    
        except Exception as e:
            print(f"Error al procesar componentes del artículo: {str(e)}")
        finally:
            db.close()

    def _procesar_materiales_agrupados(self):
        """Procesa y agrupa todos los materiales por tipo."""
        if not self.datos_procesados:
            return

        # Diccionarios para agrupar materiales
        perfiles_agrupados = {}
        cristales_agrupados = {}
        accesorios_agrupados = {}

        # Procesar cada artículo
        for articulo in self.datos_procesados['articulos']:
            cantidad_obra = articulo['cantidad_obra']
            
            # Agrupar perfiles
            for perfil in articulo['perfiles']:
                # Crear clave única que incluya código, medida, ángulos y posición
                angulo_izq = perfil.get('angulo_izquierdo', 90)
                angulo_der = perfil.get('angulo_derecho', 90)
                posicion = perfil.get('variable_medida', 'H')
                clave = f"{perfil['codigo']}_{perfil['medida']}_{posicion}_{angulo_izq}_{angulo_der}"
                
                # Determinar tipo de corte para mostrar
                if angulo_izq == 90 and angulo_der == 90:
                    tipo_corte = 'Recto'
                else:
                    tipo_corte = f"{angulo_izq}°/{angulo_der}°"
                
                if clave not in perfiles_agrupados:
                    perfiles_agrupados[clave] = {
                        'codigo': perfil['codigo'],
                        'descripcion': perfil['descripcion'],
                        'medida': perfil['medida'],
                        'posicion': posicion.upper(),
                        'tipo_corte': tipo_corte,
                        'angulo_izquierdo': angulo_izq,
                        'angulo_derecho': angulo_der,
                        'cantidad_total': 0,
                        'articulos_origen': []
                    }
                
                cantidad_total = perfil['cantidad'] * cantidad_obra
                perfiles_agrupados[clave]['cantidad_total'] += cantidad_total
                perfiles_agrupados[clave]['articulos_origen'].append({
                    'articulo': articulo['codigo'],
                    'cantidad': cantidad_total
                })

            # Agrupar cristales
            for cristal in articulo['cristales']:
                clave = f"{cristal['codigo']}_{cristal['alto']}_{cristal['ancho']}"

                if clave not in cristales_agrupados:
                    cristales_agrupados[clave] = {
                        'codigo': cristal['codigo'],
                        'descripcion': cristal['descripcion'],
                        'alto': cristal['alto'],
                        'ancho': cristal['ancho'],
                        'cantidad_total': 0,
                        'metros_cuadrados_total': 0,
                        'articulos_origen': []
                    }

                cantidad_total = cristal['cantidad'] * cantidad_obra
                m2_total = cristal['metros_cuadrados'] * cantidad_obra

                cristales_agrupados[clave]['cantidad_total'] += cantidad_total
                cristales_agrupados[clave]['metros_cuadrados_total'] += m2_total
                cristales_agrupados[clave]['articulos_origen'].append({
                    'articulo': articulo['codigo'],
                    'cantidad': cantidad_total
                })

            # Agrupar accesorios
            for accesorio in articulo['accesorios']:
                clave = accesorio['codigo']

                if clave not in accesorios_agrupados:
                    accesorios_agrupados[clave] = {
                        'codigo': accesorio['codigo'],
                        'descripcion': accesorio['descripcion'],
                        'unidad': accesorio['unidad'],
                        'cantidad_total': 0,
                        'articulos_origen': []
                    }

                cantidad_total = accesorio['cantidad'] * cantidad_obra
                accesorios_agrupados[clave]['cantidad_total'] += cantidad_total
                accesorios_agrupados[clave]['articulos_origen'].append({
                    'articulo': articulo['codigo'],
                    'cantidad': cantidad_total
                })

        # Convertir a listas ordenadas
        self.datos_procesados['materiales_agrupados']['perfiles'] = list(perfiles_agrupados.values())
        self.datos_procesados['materiales_agrupados']['cristales'] = list(cristales_agrupados.values())
        self.datos_procesados['materiales_agrupados']['accesorios'] = list(accesorios_agrupados.values())

        # Ordenar por tipo de perfil y código
        self.datos_procesados['materiales_agrupados']['perfiles'].sort(
            key=lambda x: x['codigo']
        )

    def _generar_hojas_individuales(self):
        """Genera las hojas individuales para cada artículo."""
        if not self.datos_procesados:
            return

        # Actualizar tabla de hojas individuales
        articulos = self.datos_procesados['articulos']
        self.lista_articulos_hojas.setRowCount(len(articulos))

        for i, articulo in enumerate(articulos):
            # Código del artículo
            self.lista_articulos_hojas.setItem(i, 0, QTableWidgetItem(
                f"{articulo['codigo']} - {articulo['nombre']}"
            ))

            # Cantidad
            self.lista_articulos_hojas.setItem(i, 1, QTableWidgetItem(
                str(articulo['cantidad_obra'])
            ))

            # Estado de imagen
            tiene_imagen = "✅ Sí" if articulo['imagen_path'] else "❌ No"
            self.lista_articulos_hojas.setItem(i, 2, QTableWidgetItem(tiene_imagen))

            # Estado
            self.lista_articulos_hojas.setItem(i, 3, QTableWidgetItem("✅ Listo"))

            # Botón de vista previa
            btn_vista = QPushButton("👁️ Ver")
            btn_vista.clicked.connect(lambda checked, idx=i: self._ver_hoja_individual(idx))
            self.lista_articulos_hojas.setCellWidget(i, 4, btn_vista)

    def _generar_datos_pedido_individual(self):
        """Genera los datos para el pedido individual."""
        if not self.datos_procesados:
            return

        # Crear estructura del pedido
        self.datos_procesados['pedido_individual'] = {
            'fecha_pedido': datetime.now(),
            'numero_pedido': f"PED-{self.obra.codigo}-{datetime.now().strftime('%Y%m%d')}",
            'proveedor_sugerido': "Proveedor Principal",
            'materiales': []
        }

        # Agregar materiales al pedido
        materiales_pedido = []

        # Perfiles
        for perfil in self.datos_procesados['materiales_agrupados']['perfiles']:
            materiales_pedido.append({
                'tipo': 'Perfil',
                'codigo': perfil['codigo'],
                'descripcion': perfil['descripcion'],
                'cantidad': perfil['cantidad_total'],
                'unidad': 'ud',
                'medida': perfil['medida'],
                'observaciones': ""
            })

        # Cristales
        for cristal in self.datos_procesados['materiales_agrupados']['cristales']:
            materiales_pedido.append({
                'tipo': 'Cristal',
                'codigo': cristal['codigo'],
                'descripcion': cristal['descripcion'],
                'cantidad': cristal['cantidad_total'],
                'unidad': 'ud',
                'dimensiones': f"{cristal['alto']}x{cristal['ancho']}mm",
                'metros_cuadrados': cristal['metros_cuadrados_total'],
                'observaciones': f"Total: {cristal['metros_cuadrados_total']:.2f} m²"
            })

        # Accesorios
        for accesorio in self.datos_procesados['materiales_agrupados']['accesorios']:
            materiales_pedido.append({
                'tipo': 'Accesorio',
                'codigo': accesorio['codigo'],
                'descripcion': accesorio['descripcion'],
                'cantidad': accesorio['cantidad_total'],
                'unidad': accesorio['unidad'],
                'observaciones': ''
            })

        self.datos_procesados['pedido_individual']['materiales'] = materiales_pedido

    def _actualizar_vista_previa(self):
        """Actualiza la vista previa según la selección."""
        if not self.datos_procesados:
            self.widget_vista_previa.setText("Genere el informe primero")
            return

        vista_seleccionada = self.combo_vista_previa.currentText()

        if vista_seleccionada == "Resumen General":
            self._mostrar_resumen_general()
        elif vista_seleccionada == "Lista de Perfiles":
            self._mostrar_lista_perfiles()
        elif vista_seleccionada == "Lista de Cristales":
            self._mostrar_lista_cristales()
        elif vista_seleccionada == "Lista de Accesorios":
            self._mostrar_lista_accesorios()
        elif vista_seleccionada == "Pedido Individual":
            self._mostrar_pedido_individual()

    def _mostrar_resumen_general(self):
        """Muestra el resumen general de la obra."""
        obra = self.datos_procesados['obra']
        fecha = self.datos_procesados['fecha_generacion']

        resumen = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                           INFORME DE TALLER PROFESIONAL                     ║
╚══════════════════════════════════════════════════════════════════════════════╝

📋 INFORMACIÓN DE LA OBRA
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🏗️  Código de Obra:     {obra['codigo']}
📝  Nombre:             {obra['nombre']}
👤  Cliente:            {obra['cliente']}
📊  Estado:             {obra['estado']}
💰  Presupuesto:        {obra['presupuesto']:.2f} €
📅  Fecha Generación:   {fecha.strftime('%d/%m/%Y %H:%M')}

📊 RESUMEN DE MATERIALES
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""

        # Resumen de perfiles
        perfiles = self.datos_procesados['materiales_agrupados']['perfiles']
        if perfiles:
            total_perfiles = sum(p['cantidad_total'] for p in perfiles)
            tipos_diferentes = len(set(p['codigo'] for p in perfiles))

            resumen += f"""
🔧 PERFILES:
   • Total:           {total_perfiles} uds
   • Referencias:     {tipos_diferentes} diferentes

"""

        # Estadísticas de cristales
        cristales = self.datos_procesados['materiales_agrupados']['cristales']
        if cristales:
            total_cristales = sum(c['cantidad_total'] for c in cristales)
            total_m2 = sum(c['metros_cuadrados_total'] for c in cristales)

            resumen += f"""
💎 CRISTALES:
   • Total de unidades:  {total_cristales}
   • Total en m²:        {total_m2:.2f} m²
   • Referencias:        {len(cristales)}
"""

        # Estadísticas de accesorios
        accesorios = self.datos_procesados['materiales_agrupados']['accesorios']
        if accesorios:
            total_accesorios = sum(a['cantidad_total'] for a in accesorios)

            resumen += f"""
🔩 ACCESORIOS:
   • Total de piezas:    {total_accesorios}
   • Referencias:        {len(accesorios)}
"""

        # Artículos
        articulos = self.datos_procesados['articulos']
        resumen += f"""
📄 ARTÍCULOS:
   • Total de artículos: {len(articulos)}
   • Con imagen:         {sum(1 for a in articulos if a['imagen_path'])}
   • Sin imagen:         {sum(1 for a in articulos if not a['imagen_path'])}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""

        self.widget_vista_previa.setText(resumen)

    def _mostrar_lista_perfiles(self):
        """Muestra la lista detallada de perfiles."""
        perfiles = self.datos_procesados['materiales_agrupados']['perfiles']

        if not perfiles:
            self.widget_vista_previa.setText("No hay perfiles en esta obra.")
            return

        lista = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                              LISTA DE PERFILES                              ║
╚══════════════════════════════════════════════════════════════════════════════╝

"""

        # Generar sección para todos los perfiles
        if perfiles:
            lista += f"""
🔧 PERFILES
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""

            for perfil in perfiles:
                lista += f"""
📏 {perfil['codigo']} - {perfil['descripcion']}
   • Medida:     {perfil['medida']} mm
   • Cantidad:     {perfil['cantidad_total']} unidades
   • Artículos:    {', '.join([a['articulo'] for a in perfil['articulos_origen']])}
"""

        self.widget_vista_previa.setText(lista)

    def _mostrar_lista_cristales(self):
        """Muestra la lista detallada de cristales."""
        cristales = self.datos_procesados['materiales_agrupados']['cristales']

        if not cristales:
            self.widget_vista_previa.setText("No hay cristales en esta obra.")
            return

        lista = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                              LISTA DE CRISTALES                             ║
╚══════════════════════════════════════════════════════════════════════════════╝

"""

        total_m2 = 0
        for cristal in cristales:
            total_m2 += cristal['metros_cuadrados_total']

            lista += f"""
💎 {cristal['codigo']} - {cristal['descripcion']}
   • Dimensiones:  {cristal['alto']} x {cristal['ancho']} mm
   • Cantidad:     {cristal['cantidad_total']} unidades
   • Metros²:      {cristal['metros_cuadrados_total']:.3f} m²
   • Artículos:    {', '.join([a['articulo'] for a in cristal['articulos_origen']])}

"""

        lista += f"""
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 TOTAL CRISTALES: {total_m2:.2f} m²
"""

        self.widget_vista_previa.setText(lista)

    def _mostrar_lista_accesorios(self):
        """Muestra la lista detallada de accesorios."""
        accesorios = self.datos_procesados['materiales_agrupados']['accesorios']

        if not accesorios:
            self.widget_vista_previa.setText("No hay accesorios en esta obra.")
            return

        lista = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                             LISTA DE ACCESORIOS                             ║
╚══════════════════════════════════════════════════════════════════════════════╝

"""

        for accesorio in accesorios:
            lista += f"""
🔩 {accesorio['codigo']} - {accesorio['descripcion']}
   • Cantidad:     {accesorio['cantidad_total']} {accesorio['unidad']}
   • Artículos:    {', '.join([a['articulo'] for a in accesorio['articulos_origen']])}

"""

        self.widget_vista_previa.setText(lista)

    def _mostrar_pedido_individual(self):
        """Muestra el pedido individual completo."""
        if 'pedido_individual' not in self.datos_procesados:
            self.widget_vista_previa.setText("Genere el pedido individual primero.")
            return

        pedido = self.datos_procesados['pedido_individual']
        obra = self.datos_procesados['obra']

        lista = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                            PEDIDO INDIVIDUAL                                ║
╚══════════════════════════════════════════════════════════════════════════════╝

📋 INFORMACIÓN DEL PEDIDO
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🔢 Número de Pedido:    {pedido['numero_pedido']}
📅 Fecha de Pedido:     {pedido['fecha_pedido'].strftime('%d/%m/%Y')}
🏗️  Para Obra:          {obra['codigo']} - {obra['nombre']}
👤 Cliente:             {obra['cliente']}
🏢 Proveedor Sugerido:  {pedido['proveedor_sugerido']}

📦 MATERIALES A PEDIR
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
"""

        # Agrupar materiales por tipo
        tipos_material = {}
        for material in pedido['materiales']:
            tipo = material['tipo']
            if tipo not in tipos_material:
                tipos_material[tipo] = []
            tipos_material[tipo].append(material)

        # Mostrar cada tipo en el orden especificado
        orden_tipos = ['Perfil', 'Cristal', 'Accesorio', 'Persiana']

        for tipo in orden_tipos:
            if tipo in tipos_material:
                lista += f"""
{self._get_icono_tipo(tipo)} {tipo.upper()}S:
"""
                for material in tipos_material[tipo]:
                    lista += f"""   • {material['codigo']} - {material['descripcion']}
     Cantidad: {material['cantidad']} {material['unidad']}"""

                    if 'longitud' in material:
                        lista += f" (Longitud: {material['longitud']} mm)"
                    if 'dimensiones' in material:
                        lista += f" (Dimensiones: {material['dimensiones']})"
                    if 'metros_cuadrados' in material:
                        lista += f" (Total: {material['metros_cuadrados']:.2f} m²)"

                    lista += f"""
     {material['observaciones']}

"""

        self.widget_vista_previa.setText(lista)

    def _get_icono_tipo(self, tipo):
        """Obtiene el icono para cada tipo de material."""
        iconos = {
            'Perfil': '🔧',
            'Cristal': '💎',
            'Accesorio': '🔩',
            'Persiana': '🪟'
        }
        return iconos.get(tipo, '📦')

    def _ver_hoja_individual(self, indice):
        """Muestra la vista previa de una hoja individual."""
        if indice >= len(self.datos_procesados['articulos']):
            return

        articulo = self.datos_procesados['articulos'][indice]

        # Crear ventana de vista previa
        dialog = QDialog(self)
        dialog.setWindowTitle(f"Hoja Individual - {articulo['codigo']}")
        dialog.setMinimumSize(800, 600)

        layout = QVBoxLayout(dialog)

        # Información del artículo
        info = QLabel(f"""
        <h2>{articulo['codigo']} - {articulo['nombre']}</h2>
        <p><b>Cantidad en obra:</b> {articulo['cantidad_obra']}</p>
        <p><b>Dimensiones:</b> {articulo['alto']} x {articulo['ancho']} mm</p>
        """)
        info.setWordWrap(True)
        layout.addWidget(info)

        # Imagen si existe
        if articulo['imagen_path'] and os.path.exists(articulo['imagen_path']):
            label_imagen = QLabel()
            pixmap = QPixmap(articulo['imagen_path'])
            if not pixmap.isNull():
                # Redimensionar imagen
                pixmap = pixmap.scaled(400, 300, Qt.AspectRatioMode.KeepAspectRatio,
                                     Qt.TransformationMode.SmoothTransformation)

                # Agregar medidas a la imagen si está habilitado
                if self.check_medidas_en_imagen.isChecked():
                    pixmap = self._agregar_medidas_a_imagen(pixmap, articulo)

                label_imagen.setPixmap(pixmap)
                label_imagen.setAlignment(Qt.AlignmentFlag.AlignCenter)
                layout.addWidget(label_imagen)
        else:
            no_imagen = QLabel("❌ No hay imagen disponible para este artículo")
            no_imagen.setAlignment(Qt.AlignmentFlag.AlignCenter)
            no_imagen.setStyleSheet("color: #e74c3c; font-size: 14px; padding: 20px;")
            layout.addWidget(no_imagen)

        # Botón cerrar
        btn_cerrar = QPushButton("Cerrar")
        btn_cerrar.clicked.connect(dialog.accept)
        layout.addWidget(btn_cerrar)

        dialog.exec()

    def _agregar_medidas_a_imagen(self, pixmap, articulo):
        """Agrega las medidas del artículo a la imagen."""
        # Crear una copia del pixmap para dibujar encima
        nuevo_pixmap = QPixmap(pixmap.size())
        nuevo_pixmap.fill(Qt.GlobalColor.white)

        painter = QPainter(nuevo_pixmap)

        # Dibujar la imagen original
        painter.drawPixmap(0, 0, pixmap)

        # Configurar el pincel para las medidas
        painter.setPen(QPen(QColor(255, 0, 0), 2))
        painter.setFont(QFont("Arial", 12, QFont.Weight.Bold))

        # Obtener dimensiones
        ancho_img = pixmap.width()
        alto_img = pixmap.height()

        # Dibujar medida del ancho (parte inferior)
        texto_ancho = f"{articulo['ancho']} mm"
        painter.drawText(ancho_img // 2 - 40, alto_img - 10, texto_ancho)
        painter.drawLine(10, alto_img - 30, ancho_img - 10, alto_img - 30)

        # Dibujar medida del alto (lado derecho)
        texto_alto = f"{articulo['alto']} mm"
        painter.save()
        painter.translate(ancho_img - 20, alto_img // 2)
        painter.rotate(-90)
        painter.drawText(-30, 0, texto_alto)
        painter.restore()
        painter.drawLine(ancho_img - 40, 10, ancho_img - 40, alto_img - 40)

        painter.end()

        return nuevo_pixmap

    def _exportar_pdf_completo(self):
        """Exporta el informe completo a PDF."""
        if not REPORTLAB_AVAILABLE:
            QMessageBox.warning(
                self,
                "ReportLab no disponible",
                "Para exportar a PDF necesita instalar ReportLab:\npip install reportlab"
            )
            return

        if not self.datos_procesados:
            QMessageBox.warning(self, "Error", "Genere el informe primero.")
            return

        # Seleccionar archivo de destino
        archivo, _ = QFileDialog.getSaveFileName(
            self,
            "Guardar Informe PDF",
            f"Informe_Taller_{self.obra.codigo}_{datetime.now().strftime('%Y%m%d')}.pdf",
            "Archivos PDF (*.pdf)"
        )

        if not archivo:
            return

        try:
            self._generar_pdf_profesional(archivo)
            QMessageBox.information(
                self,
                "✅ PDF Exportado",
                f"El informe se ha exportado correctamente a:\n{archivo}"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ Error",
                f"Error al exportar PDF: {str(e)}"
            )

    def _generar_pdf_profesional(self, archivo):
        """Genera un PDF profesional con formato de Hoja de Taller."""
        if not REPORTLAB_AVAILABLE:
            QMessageBox.critical(
                self,
                "Error",
                "El módulo ReportLab no está disponible. No se puede generar el PDF.",
                QMessageBox.StandardButton.Ok
            )
            return

        # Configuración del documento
        doc = SimpleDocTemplate(
            archivo,
            pagesize=A4,
            leftMargin=15*mm,
            rightMargin=15*mm,
            topMargin=10*mm,
            bottomMargin=15*mm
        )

        # Estilos
        estilos = getSampleStyleSheet()
        
        # Crear nuevos nombres de estilos para evitar conflictos
        if 'TituloPrincipal' not in estilos:
            estilos.add(ParagraphStyle(
                'TituloPrincipal',
                parent=estilos['Heading1'],
                fontSize=20,
                spaceAfter=10,
                alignment=TA_CENTER,
                textColor=colors.HexColor('#000000'),
                fontName='Helvetica-Bold',
                leading=24
            ))
        
        if 'Subtitulo' not in estilos:
            estilos.add(ParagraphStyle(
                'Subtitulo',
                parent=estilos['Heading2'],
                fontSize=12,
                spaceAfter=10,
                alignment=TA_LEFT,
                textColor=colors.HexColor('#000000'),
                fontName='Helvetica-Bold',
                leading=14
            ))
            
        # Usar el estilo Normal existente en lugar de redefinirlo
        # Solo modificamos sus propiedades si es necesario
        if not hasattr(estilos['Normal'], 'spaceAfter'):
            estilos['Normal'].spaceAfter = 6
        if not hasattr(estilos['Normal'], 'textColor'):
            estilos['Normal'].textColor = colors.HexColor('#000000')
        if not hasattr(estilos['Normal'], 'fontName'):
            estilos['Normal'].fontName = 'Helvetica'
        if not hasattr(estilos['Normal'], 'leading'):
            estilos['Normal'].leading = 12

        # Elementos del documento
        elementos = []
        
        # Encabezado con logo y datos de la empresa
        header_data = [
            ["PRO-2000 CARPINTERÍA METÁLICA", "HOJA DE TALLER"],
            ["C/ Ejemplo, 123 - 28000 Madrid", f"Nº Pedido: {self.obra.codigo}"],
            ["Tel: 91 123 45 67 - <EMAIL>", f"Fecha: {datetime.now().strftime('%d/%m/%Y')}"]
        ]
        
        header_table = Table(header_data, colWidths=[doc.width/2.0]*2)
        header_table.setStyle(TableStyle([
            ('FONTNAME', (0,0), (-1,-1), 'Helvetica-Bold'),
            ('FONTSIZE', (0,0), (-1,0), 14),
            ('FONTSIZE', (0,1), (-1,-1), 10),
            ('VALIGN', (0,0), (-1,-1), 'MIDDLE'),
            ('ALIGN', (0,0), (0,-1), 'LEFT'),
            ('ALIGN', (1,0), (1,-1), 'RIGHT'),
            ('BOTTOMPADDING', (0,0), (-1,-1), 6),
            ('TOPPADDING', (0,0), (-1,0), 12),
            ('BOTTOMPADDING', (0,0), (-1,0), 12),
            ('LINEBELOW', (0,0), (-1,0), 1, colors.black)
        ]))
        
        elementos.append(header_table)
        elementos.append(Spacer(1, 10))
        
        # Datos del cliente
        cliente_data = [
            ["CLIENTE:", self.obra.cliente.nombre if self.obra.cliente else ""],
            ["DIRECCIÓN:", self.obra.direccion if self.obra.direccion else ""],
            ["TELÉFONO:", self.obra.telefono if hasattr(self.obra, 'telefono') else ""],
            ["EMAIL:", self.obra.email if hasattr(self.obra, 'email') else ""]
        ]
        
        cliente_table = Table(cliente_data, colWidths=[3*cm, doc.width-3*cm])
        cliente_table.setStyle(TableStyle([
            ('FONTNAME', (0,0), (-1,-1), 'Helvetica'),
            ('FONTSIZE', (0,0), (-1,-1), 10),
            ('VALIGN', (0,0), (-1,-1), 'MIDDLE'),
            ('FONTNAME', (0,0), (0,-1), 'Helvetica-Bold'),
            ('GRID', (0,0), (-1,-1), 1, colors.black),
            ('PADDING', (0,0), (-1,-1), 4)
        ]))
        
        elementos.append(cliente_table)
        elementos.append(Spacer(1, 15))
        
        # Tabla de artículos
        articulos_data = [
            [
                'CANT.', 'CÓDIGO', 'DESCRIPCIÓN', 'ANCHO', 'ALTO', 'UD.', 'OBSERVACIONES'
            ]
        ]
        
        # Agregar artículos a la tabla
        for art in self.datos_procesados.get('articulos', []):
            articulos_data.append([
                str(art.get('cantidad', 1)),
                art.get('codigo', ''),
                art.get('descripcion', ''),
                f"{art.get('ancho', 0)} mm",
                f"{art.get('alto', 0)} mm",
                art.get('unidad', 'ud.'),
                art.get('observaciones', '')
            ])
        
        # Crear tabla de artículos
        if len(articulos_data) > 1:  # Si hay artículos
            articulos_table = Table(
                articulos_data,
                colWidths=[1.5*cm, 2.5*cm, 6*cm, 2*cm, 2*cm, 1.5*cm, 4*cm],
                repeatRows=1
            )
            
            # Estilo de la tabla de artículos
            articulos_table.setStyle(TableStyle([
                # Estilo del encabezado
                ('BACKGROUND', (0,0), (-1,0), colors.HexColor('#f0f0f0')),
                ('TEXTCOLOR', (0,0), (-1,0), colors.black),
                ('FONTNAME', (0,0), (-1,0), 'Helvetica-Bold'),
                ('FONTSIZE', (0,0), (-1,0), 9),
                ('ALIGN', (0,0), (-1,0), 'CENTER'),
                ('VALIGN', (0,0), (-1,0), 'MIDDLE'),
                ('GRID', (0,0), (-1,-1), 1, colors.black),
                ('FONTSIZE', (0,1), (-1,-1), 8),
                ('VALIGN', (0,1), (-1,-1), 'MIDDLE'),
                ('ALIGN', (0,1), (-1,-1), 'CENTER'),
                ('ALIGN', (2,1), (2,-1), 'LEFT'),  # Alinear descripción a la izquierda
                ('ALIGN', (-1,1), (-1,-1), 'LEFT'),  # Alinear observaciones a la izquierda
                ('TOPPADDING', (0,0), (-1,-1), 3),
                ('BOTTOMPADDING', (0,0), (-1,-1), 3),
                ('LEFTPADDING', (0,0), (-1,-1), 3),
                ('RIGHTPADDING', (0,0), (-1,-1), 3),
            ]))
            
            elementos.append(articulos_table)
        else:
            elementos.append(Paragraph("No hay artículos para mostrar.", estilos['Normal']))
        
        elementos.append(Spacer(1, 15))
        
        # Procesar materiales agrupados si no se ha hecho ya
        if 'materiales_agrupados' not in self.datos_procesados:
            self._procesar_materiales_agrupados()
        
        # Sección de perfiles
        self._agregar_seccion_perfiles_pdf(elementos, estilos)
        
        # Sección de cristales
        self._agregar_seccion_cristales_pdf(elementos, estilos)
        
        # Sección de accesorios
        self._agregar_seccion_accesorios_pdf(elementos, estilos)
        
        # Sección de pedido individual
        self._agregar_seccion_pedido_pdf(elementos, estilos)
        
        # Espacio para dibujos o planos
        elementos.append(PageBreak())
        elementos.append(Paragraph("PLANOS Y DIBUJOS TÉCNICOS", estilos['Subtitulo']))
        elementos.append(Spacer(1, 10))
        
        # Cuadro para dibujos técnicos (se puede rellenar con imágenes si están disponibles)
        if 'imagenes' in self.datos_procesados and self.datos_procesados['imagenes']:
            for img_path in self.datos_procesados['imagenes']:
                try:
                    img = Image(img_path, width=16*cm, height=12*cm)
                    elementos.append(img)
                    elementos.append(Spacer(1, 10))
                except Exception as e:
                    print(f"Error cargando imagen técnica: {e}")
        else:
            # Cuadro vacío para dibujos si no hay imágenes
            dibujo_frame = Drawing(16*cm, 12*cm)
            dibujo_frame.add(Rect(0, 0, 16*cm, 12*cm, strokeColor=colors.black, fillColor=None))
            elementos.append(dibujo_frame)
            elementos.append(Spacer(1, 10))
        
        # Sección de firmas
        elementos.append(Spacer(1, 20))
        firmas_data = [
            ["ELABORADO POR:", "APROBADO POR:", "RECIBIDO POR:"],
            ["", "", ""],  # Líneas para firmas
            ["Firma y sello", "Firma y sello", "Firma y sello"],
            ["Nombre:", "Nombre:", "Nombre:"],
            ["DNI:", "DNI:", "DNI:"],
            ["Fecha:", "Fecha:", "Fecha:"]
        ]
        
        firmas_table = Table(firmas_data, colWidths=[doc.width/3.0]*3)
        firmas_table.setStyle(TableStyle([
            ('FONTNAME', (0,0), (-1,0), 'Helvetica-Bold'),
            ('FONTSIZE', (0,0), (-1,-1), 10),
            ('ALIGN', (0,0), (-1,-1), 'CENTER'),
            ('VALIGN', (0,0), (-1,-1), 'MIDDLE'),
            ('GRID', (0,1), (-1,1), 1, colors.black),  # Línea para firmas
            ('TOPPADDING', (0,0), (-1,-1), 5),
            ('BOTTOMPADDING', (0,0), (-1,-1), 5),
            ('LINEBELOW', (0,1), (0,1), 1, colors.black),
            ('LINEBELOW', (1,1), (1,1), 1, colors.black),
            ('LINEBELOW', (2,1), (2,1), 1, colors.black),
        ]))
        
        elementos.append(firmas_table)
        
        # Notas finales
        elementos.append(Spacer(1, 15))
        notas = [
            "NOTAS:",
            "1. Las medidas deben verificarse en obra antes de la fabricación.",
            "2. Cualquier modificación debe ser aprobada por el departamento técnico.",
            "3. Los plazos de entrega comienzan una vez aprobado el presupuesto.",
            "4. Se recomienda revisar las condiciones de instalación antes de la entrega."
        ]
        
        for nota in notas:
            if nota.startswith("NOTAS:"):
                elementos.append(Paragraph(f"<b>{nota}</b>", estilos['Normal']))
            else:
                elementos.append(Paragraph(nota, estilos['Normal']))
        
        # Pie de página
        def footer(canvas, doc):
            canvas.saveState()
            canvas.setFont('Helvetica', 8)
            canvas.setFillColor(colors.HexColor('#666666'))
            
            # Línea divisoria
            canvas.line(doc.leftMargin, 2*cm, doc.width + doc.leftMargin, 2*cm)
            
            # Texto del pie de página
            texto_pie = f"PRO-2000 CARPINTERÍA METÁLICA • C/ Ejemplo, 123 • 28000 Madrid • Tel: 91 123 45 67 • <EMAIL> • Página {doc.page}"
            
            canvas.drawCentredString(
                doc.width/2.0 + doc.leftMargin,
                1.5*cm,
                texto_pie
            )
            canvas.restoreState()
        
        # Construir PDF con pie de página personalizado
        doc.build(elementos, onFirstPage=footer, onLaterPages=footer)

    def _agregar_seccion_perfiles_pdf(self, elementos, estilos):
        """Agrega la sección de perfiles al PDF con diseño optimizado."""
        elementos.append(Paragraph("PERFILES REQUERIDOS", estilos['Heading2']))
        elementos.append(Spacer(1, 6))

        perfiles = self.datos_procesados['materiales_agrupados']['perfiles']

        if not perfiles:
            elementos.append(Paragraph("No hay perfiles para mostrar.", estilos['Normal']))
            elementos.append(Spacer(1, 10))
            return

        # Preparar datos de la tabla
        datos_tabla = [
            ['Código', 'Descripción', 'Posición', 'Medida (mm)', 'Cant.', 'Corte']
        ]

        # Agregar perfiles a la tabla
        for perfil in perfiles:
            try:
                # Obtener artículos de origen para las observaciones
                articulos_origen = ", ".join([f"{a['articulo']} ({a['cantidad']})" 
                                           for a in perfil.get('articulos_origen', [])])
                
                # Limitar la longitud de las observaciones para que no sean demasiado largas
                if len(articulos_origen) > 40:
                    articulos_origen = articulos_origen[:37] + "..."
                
                datos_tabla.append([
                    perfil.get('codigo', 'N/A'),
                    perfil.get('descripcion', 'Sin descripción'),
                    perfil.get('posicion', 'N/A'),
                    str(perfil.get('medida', 'N/A')),
                    str(perfil.get('cantidad_total', 0)),
                    perfil.get('tipo_corte', 'Recto')
                ])
            except Exception as e:
                print(f"Error procesando perfil: {str(e)}")
                continue

        # Crear tabla
        if len(datos_tabla) > 1:
            tabla = Table(
                datos_tabla,
                colWidths=[2.5*cm, 6*cm, 2*cm, 2*cm, 1.5*cm, 2*cm],
                repeatRows=1,
                hAlign='LEFT'
            )

            # Estilo de la tabla
            estilo_tabla = TableStyle([
                # Encabezado
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3a7bd5')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('ALIGN', (1, 0), (1, -1), 'LEFT'),  # Alinear descripción a la izquierda
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 8),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 6),
                ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#e0e0e0')),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                # Filas alternadas
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f9f9f9')])
            ])

            tabla.setStyle(estilo_tabla)
            elementos.append(tabla)
            elementos.append(Spacer(1, 10))
            
            # Agregar nota sobre las unidades
            elementos.append(Paragraph(
                "<i>Nota: Las medidas están en milímetros (mm)</i>", 
                estilos['Italic']
            ))
        else:
            elementos.append(Paragraph("No se requieren perfiles para esta obra.", estilos['Normal']))

    def _agregar_seccion_cristales_pdf(self, elementos, estilos):
        """Agrega la sección de cristales al PDF."""
        elementos.append(PageBreak())
        elementos.append(Paragraph("LISTA DE CRISTALES", estilos['Heading2']))
        elementos.append(Spacer(1, 12))

        cristales = self.datos_procesados['materiales_agrupados']['cristales']

        if cristales:
            datos_tabla = [['Código', 'Descripción', 'Dimensiones', 'Cantidad', 'm²']]

            for cristal in cristales:
                datos_tabla.append([
                    cristal['codigo'],
                    cristal['descripcion'],
                    f"{cristal['alto']}x{cristal['ancho']}",
                    str(cristal['cantidad_total']),
                    f"{cristal['metros_cuadrados_total']:.2f}"
                ])

            tabla = Table(datos_tabla)
            tabla.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.blue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            elementos.append(tabla)
        else:
            elementos.append(Paragraph("No hay cristales en esta obra.", estilos['Normal']))

    def _agregar_seccion_accesorios_pdf(self, elementos, estilos):
        """Agrega la sección de accesorios al PDF."""
        elementos.append(PageBreak())
        elementos.append(Paragraph("LISTA DE ACCESORIOS", estilos['Heading2']))
        elementos.append(Spacer(1, 12))

        accesorios = self.datos_procesados['materiales_agrupados']['accesorios']

        if accesorios:
            datos_tabla = [['Código', 'Descripción', 'Cantidad', 'Unidad']]

            for accesorio in accesorios:
                datos_tabla.append([
                    accesorio['codigo'],
                    accesorio['descripcion'],
                    str(accesorio['cantidad_total']),
                    accesorio['unidad']
                ])

            tabla = Table(datos_tabla)
            tabla.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.green),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightgreen),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            elementos.append(tabla)
        else:
            elementos.append(Paragraph("No hay accesorios en esta obra.", estilos['Normal']))

    def _agregar_seccion_pedido_pdf(self, elementos, estilos):
        """Agrega la sección de pedido individual al PDF."""
        elementos.append(PageBreak())
        elementos.append(Paragraph("PEDIDO INDIVIDUAL", estilos['Heading2']))
        elementos.append(Spacer(1, 12))

        if 'pedido_individual' in self.datos_procesados:
            pedido = self.datos_procesados['pedido_individual']

            # Información del pedido
            info_pedido = f"""
            <b>Número de Pedido:</b> {pedido['numero_pedido']}<br/>
            <b>Fecha:</b> {pedido['fecha_pedido'].strftime('%d/%m/%Y')}<br/>
            <b>Proveedor Sugerido:</b> {pedido['proveedor_sugerido']}
            """
            elementos.append(Paragraph(info_pedido, estilos['Normal']))
            elementos.append(Spacer(1, 12))

            # Tabla de materiales
            datos_tabla = [['Tipo', 'Código', 'Descripción', 'Cantidad', 'Observaciones']]

            for material in pedido['materiales']:
                observaciones = material.get('observaciones', '')
                if 'medida' in material:
                    observaciones += f" M:{material['medida']}mm"
                if 'dimensiones' in material:
                    observaciones += f" {material['dimensiones']}"

                datos_tabla.append([
                    material['tipo'],
                    material['codigo'],
                    material['descripcion'],
                    f"{material['cantidad']} {material['unidad']}",
                    observaciones
                ])

            tabla = Table(datos_tabla)
            tabla.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.purple),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lavender),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            elementos.append(tabla)

    def _imprimir_informe(self):
        """Imprime el informe."""
        QMessageBox.information(
            self,
            "Función de Impresión",
            "La función de impresión estará disponible en la próxima versión.\n"
            "Por ahora, puede exportar a PDF e imprimir desde su visor de PDF."
        )

    def _generar_pedido_individual(self):
        """Genera un archivo de pedido individual separado."""
        if 'pedido_individual' not in self.datos_procesados:
            QMessageBox.warning(self, "Error", "Genere el informe completo primero.")
            return

        archivo, _ = QFileDialog.getSaveFileName(
            self,
            "Guardar Pedido Individual",
            f"Pedido_{self.obra.codigo}_{datetime.now().strftime('%Y%m%d')}.pdf",
            "Archivos PDF (*.pdf)"
        )

        if not archivo:
            return

        try:
            self._generar_pdf_pedido_individual(archivo)
            QMessageBox.information(
                self,
                "✅ Pedido Generado",
                f"El pedido individual se ha generado correctamente:\n{archivo}"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ Error",
                f"Error al generar pedido: {str(e)}"
            )

    def _generar_pdf_pedido_individual(self, archivo):
        """Genera un PDF específico para el pedido individual."""
        doc = SimpleDocTemplate(archivo, pagesize=A4)
        elementos = []

        estilos = getSampleStyleSheet()

        # Solo la sección de pedido
        self._agregar_seccion_pedido_pdf(elementos, estilos)

        doc.build(elementos)
