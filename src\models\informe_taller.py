"""
Modelos para informes de taller y optimización de materiales.
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from datetime import datetime
from .base import Base


class InformeTaller(Base):
    """Modelo para informes de taller."""
    
    __tablename__ = 'informes_taller'
    
    id = Column(Integer, primary_key=True)
    codigo = Column(String(50), unique=True, nullable=False)
    nombre = Column(String(200), nullable=False)
    obra_id = Column(Integer, ForeignKey('obras.id'), nullable=False)
    fecha_creacion = Column(DateTime, default=datetime.now)
    fecha_impresion = Column(DateTime)
    estado = Column(String(20), default='pendiente')  # pendiente, impreso, fabricado
    notas = Column(Text)
    
    # Configuración del informe
    incluir_perfiles = Column(Boolean, default=True)
    incluir_accesorios = Column(Boolean, default=True)
    incluir_cristales = Column(Boolean, default=True)
    incluir_persianas = Column(Boolean, default=True)
    optimizar_cortes = Column(Boolean, default=True)
    longitud_barra_estandar = Column(Float, default=6000.0)  # mm
    
    # Relaciones
    obra = relationship("Obra")
    cortes_perfiles = relationship("CortePerfilTaller", back_populates="informe", cascade="all, delete-orphan")
    lista_accesorios = relationship("AccesorioTaller", back_populates="informe", cascade="all, delete-orphan")
    lista_cristales = relationship("CristalTaller", back_populates="informe", cascade="all, delete-orphan")


class CortePerfilTaller(Base):
    """Modelo para cortes de perfiles optimizados."""
    
    __tablename__ = 'cortes_perfiles_taller'
    
    id = Column(Integer, primary_key=True)
    informe_id = Column(Integer, ForeignKey('informes_taller.id'), nullable=False)
    perfil_id = Column(Integer, ForeignKey('perfiles.id'), nullable=False)
    
    # Información del corte
    numero_barra = Column(Integer, nullable=False)
    longitud_corte = Column(Float, nullable=False)  # mm
    cantidad = Column(Integer, nullable=False)
    articulo_codigo = Column(String(20))
    posicion_articulo = Column(String(50))  # ej: "Marco superior", "Lateral izquierdo"
    
    # Información de fabricación
    angulo_inicio = Column(Float, default=90.0)  # grados
    angulo_fin = Column(Float, default=90.0)     # grados
    mecanizado = Column(String(100))             # operaciones especiales
    observaciones = Column(String(200))
    
    # Relaciones
    informe = relationship("InformeTaller", back_populates="cortes_perfiles")
    perfil = relationship("Perfil")


class AccesorioTaller(Base):
    """Modelo para lista de accesorios del taller."""
    
    __tablename__ = 'accesorios_taller'
    
    id = Column(Integer, primary_key=True)
    informe_id = Column(Integer, ForeignKey('informes_taller.id'), nullable=False)
    accesorio_id = Column(Integer, ForeignKey('accesorios.id'), nullable=False)
    
    # Información del accesorio
    cantidad_total = Column(Integer, nullable=False)
    articulo_codigo = Column(String(20))
    posicion_articulo = Column(String(50))
    observaciones = Column(String(200))
    
    # Relaciones
    informe = relationship("InformeTaller", back_populates="lista_accesorios")
    accesorio = relationship("Accesorio")


class CristalTaller(Base):
    """Modelo para lista de cristales del taller."""
    
    __tablename__ = 'cristales_taller'
    
    id = Column(Integer, primary_key=True)
    informe_id = Column(Integer, ForeignKey('informes_taller.id'), nullable=False)
    cristal_id = Column(Integer, ForeignKey('cristales.id'), nullable=False)
    
    # Información del cristal
    altura = Column(Float, nullable=False)  # mm
    anchura = Column(Float, nullable=False)  # mm
    cantidad = Column(Integer, nullable=False)
    metros_cuadrados = Column(Float, nullable=False)
    articulo_codigo = Column(String(20))
    observaciones = Column(String(200))
    
    # Relaciones
    informe = relationship("InformeTaller", back_populates="lista_cristales")
    cristal = relationship("Cristal")


class OptimizadorCortes:
    """Clase para optimizar cortes de perfiles."""
    
    def __init__(self, longitud_barra=6000.0):
        """
        Inicializa el optimizador.
        
        Args:
            longitud_barra (float): Longitud de barra estándar en mm
        """
        self.longitud_barra = longitud_barra
        self.tolerancia_corte = 5.0  # mm de tolerancia para el corte
    
    def optimizar_cortes(self, lista_cortes):
        """
        Optimiza una lista de cortes para minimizar el desperdicio.
        
        Args:
            lista_cortes (list): Lista de diccionarios con 'longitud' y 'cantidad'
            
        Returns:
            list: Lista de barras optimizadas con sus cortes
        """
        # Expandir la lista de cortes
        cortes_expandidos = []
        for corte in lista_cortes:
            for _ in range(corte['cantidad']):
                cortes_expandidos.append({
                    'longitud': corte['longitud'],
                    'perfil_id': corte.get('perfil_id'),
                    'articulo_codigo': corte.get('articulo_codigo'),
                    'posicion': corte.get('posicion'),
                    'angulo_inicio': corte.get('angulo_inicio', 90.0),
                    'angulo_fin': corte.get('angulo_fin', 90.0),
                    'mecanizado': corte.get('mecanizado', ''),
                    'observaciones': corte.get('observaciones', '')
                })
        
        # Ordenar por longitud descendente (algoritmo First Fit Decreasing)
        cortes_expandidos.sort(key=lambda x: x['longitud'], reverse=True)
        
        barras_optimizadas = []
        numero_barra = 1
        
        while cortes_expandidos:
            barra_actual = {
                'numero': numero_barra,
                'longitud_total': self.longitud_barra,
                'longitud_usada': 0,
                'longitud_desperdicio': self.longitud_barra,
                'cortes': []
            }
            
            # Intentar colocar cortes en la barra actual
            i = 0
            while i < len(cortes_expandidos):
                corte = cortes_expandidos[i]
                longitud_necesaria = corte['longitud'] + self.tolerancia_corte
                
                if barra_actual['longitud_usada'] + longitud_necesaria <= self.longitud_barra:
                    # El corte cabe en la barra
                    barra_actual['cortes'].append(corte)
                    barra_actual['longitud_usada'] += longitud_necesaria
                    barra_actual['longitud_desperdicio'] = self.longitud_barra - barra_actual['longitud_usada']
                    
                    # Remover el corte de la lista
                    cortes_expandidos.pop(i)
                else:
                    i += 1
            
            barras_optimizadas.append(barra_actual)
            numero_barra += 1
        
        return barras_optimizadas
    
    def calcular_estadisticas(self, barras_optimizadas):
        """
        Calcula estadísticas de optimización.
        
        Args:
            barras_optimizadas (list): Lista de barras optimizadas
            
        Returns:
            dict: Estadísticas de optimización
        """
        total_barras = len(barras_optimizadas)
        total_longitud = total_barras * self.longitud_barra
        total_usado = sum(barra['longitud_usada'] for barra in barras_optimizadas)
        total_desperdicio = total_longitud - total_usado
        
        eficiencia = (total_usado / total_longitud * 100) if total_longitud > 0 else 0
        
        return {
            'total_barras': total_barras,
            'total_longitud_mm': total_longitud,
            'total_longitud_m': total_longitud / 1000,
            'total_usado_mm': total_usado,
            'total_usado_m': total_usado / 1000,
            'total_desperdicio_mm': total_desperdicio,
            'total_desperdicio_m': total_desperdicio / 1000,
            'eficiencia_porcentaje': eficiencia,
            'desperdicio_porcentaje': 100 - eficiencia
        }


class GeneradorInformeTaller:
    """Clase para generar informes de taller."""
    
    def __init__(self, obra_id):
        """
        Inicializa el generador.
        
        Args:
            obra_id (int): ID de la obra
        """
        self.obra_id = obra_id
        self.optimizador = OptimizadorCortes()
    
    def generar_informe_completo(self, incluir_perfiles=True, incluir_accesorios=True, 
                                incluir_cristales=True, optimizar_cortes=True):
        """
        Genera un informe completo de taller.
        
        Args:
            incluir_perfiles (bool): Incluir lista de perfiles
            incluir_accesorios (bool): Incluir lista de accesorios
            incluir_cristales (bool): Incluir lista de cristales
            optimizar_cortes (bool): Optimizar cortes de perfiles
            
        Returns:
            dict: Informe completo con todas las listas
        """
        from .base import get_db
        from .obra import Obra
        from .articulo import ObraArticulo
        
        db = next(get_db())
        
        try:
            # Obtener la obra
            obra = db.query(Obra).filter(Obra.id == self.obra_id).first()
            if not obra:
                raise ValueError(f"No se encontró la obra con ID {self.obra_id}")
            
            # Obtener artículos de la obra
            obra_articulos = db.query(ObraArticulo).filter(
                ObraArticulo.obra_id == self.obra_id
            ).all()
            
            # Crear diccionario con datos de la obra para evitar problemas de sesión
            obra_data = {
                'id': obra.id,
                'codigo': obra.codigo,
                'nombre': obra.nombre,
                'cliente_nombre': obra.cliente.nombre if obra.cliente else 'N/A'
            }

            informe = {
                'obra': obra_data,
                'fecha_generacion': datetime.now(),
                'perfiles': [],
                'accesorios': [],
                'cristales': [],
                'estadisticas': {}
            }
            
            # Generar listas según configuración
            if incluir_perfiles:
                informe['perfiles'] = self._generar_lista_perfiles(obra_articulos, optimizar_cortes)
            
            if incluir_accesorios:
                informe['accesorios'] = self._generar_lista_accesorios(obra_articulos)
            
            if incluir_cristales:
                informe['cristales'] = self._generar_lista_cristales(obra_articulos)
            
            return informe
            
        finally:
            db.close()
    
    def _generar_lista_perfiles(self, obra_articulos, optimizar=True):
        """Genera la lista de perfiles con optimización opcional."""
        lista_cortes = []
        
        for obra_articulo in obra_articulos:
            materiales = obra_articulo.articulo.calcular_materiales(
                obra_articulo.altura, obra_articulo.anchura
            )
            
            for material in materiales['perfiles']:
                for _ in range(obra_articulo.cantidad):
                    lista_cortes.append({
                        'perfil_id': material['perfil'].id,
                        'perfil': material['perfil'],
                        'longitud': material['medida'],
                        'cantidad': int(material['cantidad']),
                        'articulo_codigo': obra_articulo.articulo.codigo,
                        'posicion': 'Perfil',
                        'angulo_inicio': 90.0,
                        'angulo_fin': 90.0
                    })
        
        if optimizar:
            # Agrupar por perfil y optimizar
            perfiles_agrupados = {}
            for corte in lista_cortes:
                perfil_id = corte['perfil_id']
                if perfil_id not in perfiles_agrupados:
                    perfiles_agrupados[perfil_id] = {
                        'perfil': corte['perfil'],
                        'cortes': []
                    }
                perfiles_agrupados[perfil_id]['cortes'].append(corte)
            
            resultado = []
            for perfil_id, datos in perfiles_agrupados.items():
                barras_optimizadas = self.optimizador.optimizar_cortes(datos['cortes'])
                estadisticas = self.optimizador.calcular_estadisticas(barras_optimizadas)
                
                resultado.append({
                    'perfil': datos['perfil'],
                    'barras': barras_optimizadas,
                    'estadisticas': estadisticas
                })
            
            return resultado
        else:
            return lista_cortes
    
    def _generar_lista_accesorios(self, obra_articulos):
        """Genera la lista de accesorios agrupada."""
        accesorios_agrupados = {}
        
        for obra_articulo in obra_articulos:
            materiales = obra_articulo.articulo.calcular_materiales(
                obra_articulo.altura, obra_articulo.anchura
            )
            
            for material in materiales['accesorios']:
                accesorio_id = material['accesorio'].id
                cantidad_total = int(material['cantidad']) * obra_articulo.cantidad
                
                if accesorio_id not in accesorios_agrupados:
                    accesorios_agrupados[accesorio_id] = {
                        'accesorio': material['accesorio'],
                        'cantidad_total': 0,
                        'articulos': []
                    }
                
                accesorios_agrupados[accesorio_id]['cantidad_total'] += cantidad_total
                accesorios_agrupados[accesorio_id]['articulos'].append({
                    'codigo': obra_articulo.articulo.codigo,
                    'cantidad': int(material['cantidad']),
                    'unidades_obra': obra_articulo.cantidad
                })
        
        return list(accesorios_agrupados.values())
    
    def _generar_lista_cristales(self, obra_articulos):
        """Genera la lista de cristales con medidas exactas."""
        lista_cristales = []
        
        for obra_articulo in obra_articulos:
            materiales = obra_articulo.articulo.calcular_materiales(
                obra_articulo.altura, obra_articulo.anchura
            )
            
            for material in materiales['cristales']:
                # Calcular medidas del cristal
                superficie_mm2 = material['medida']
                # Asumir que es rectangular y calcular dimensiones aproximadas
                altura_cristal = obra_articulo.altura - 60  # Menos marcos
                anchura_cristal = obra_articulo.anchura - 60  # Menos marcos
                
                for _ in range(obra_articulo.cantidad):
                    lista_cristales.append({
                        'cristal': material['cristal'],
                        'altura': altura_cristal,
                        'anchura': anchura_cristal,
                        'cantidad': int(material['cantidad']),
                        'metros_cuadrados': material['metros_cuadrados'],
                        'articulo_codigo': obra_articulo.articulo.codigo
                    })
        
        return lista_cristales
