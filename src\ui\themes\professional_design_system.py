"""
Sistema de Diseño Profesional Unificado para PRO-2000
Define todos los elementos visuales, colores, tipografía y componentes
"""

from typing import Dict, Any
from enum import Enum

class DesignTokens:
    """Tokens de diseño - Valores base del sistema"""
    
    # 🎨 PALETA DE COLORES PROFESIONAL
    COLORS = {
        # Colores primarios
        'primary': '#2563eb',        # Azul principal
        'primary_hover': '#1d4ed8',  # Azul hover
        'primary_pressed': '#1e40af', # Azul presionado
        'primary_light': '#dbeafe',   # Azul claro
        'primary_dark': '#1e3a8a',    # Azul oscuro
        
        # Colores secundarios
        'secondary': '#64748b',       # Gris azulado
        'secondary_hover': '#475569', # Gris azulado hover
        'secondary_light': '#f1f5f9', # Gris azulado claro
        
        # Colores de estado
        'success': '#059669',         # Verde éxito
        'success_light': '#dcfce7',   # Verde claro
        'warning': '#d97706',         # Naranja advertencia
        'warning_light': '#fef3c7',   # Naranja claro
        'error': '#dc2626',           # Rojo error
        'error_light': '#fee2e2',     # Rojo claro
        'info': '#2563eb',            # Azul información
        'info_light': '#dbeafe',      # Azul información claro
        
        # Colores de superficie
        'surface': '#ffffff',         # Blanco superficie
        'surface_secondary': '#f8fafc', # Gris muy claro
        'surface_tertiary': '#f1f5f9', # Gris claro
        'background': '#ffffff',      # Fondo principal
        'background_alt': '#f8fafc',  # Fondo alternativo
        
        # Colores de texto
        'text_primary': '#0f172a',    # Texto principal
        'text_secondary': '#475569',  # Texto secundario
        'text_tertiary': '#64748b',   # Texto terciario
        'text_disabled': '#94a3b8',   # Texto deshabilitado
        'text_inverse': '#ffffff',    # Texto inverso
        
        # Colores de borde
        'border': '#e2e8f0',          # Borde normal
        'border_hover': '#cbd5e1',    # Borde hover
        'border_focus': '#2563eb',    # Borde focus
        'border_error': '#dc2626',    # Borde error
        
        # Sombras
        'shadow_light': 'rgba(0, 0, 0, 0.05)',
        'shadow_medium': 'rgba(0, 0, 0, 0.1)',
        'shadow_heavy': 'rgba(0, 0, 0, 0.15)',
    }
    
    # 📏 ESPACIADO
    SPACING = {
        'xs': '4px',    # Extra pequeño
        'sm': '8px',    # Pequeño
        'md': '12px',   # Medio
        'lg': '16px',   # Grande
        'xl': '20px',   # Extra grande
        'xxl': '24px',  # Doble extra grande
        '3xl': '32px',  # Triple extra grande
        '4xl': '48px',  # Cuádruple extra grande
    }
    
    # 🔄 BORDES REDONDEADOS
    RADIUS = {
        'none': '0px',
        'sm': '4px',
        'md': '6px',
        'lg': '8px',
        'xl': '12px',
        'full': '9999px',
    }
    
    # 📝 TIPOGRAFÍA
    TYPOGRAPHY = {
        'font_family': "'Segoe UI', 'Inter', 'SF Pro Display', system-ui, sans-serif",
        'font_sizes': {
            'xs': '11px',
            'sm': '12px',
            'base': '14px',
            'lg': '16px',
            'xl': '18px',
            '2xl': '20px',
            '3xl': '24px',
            '4xl': '32px',
        },
        'font_weights': {
            'normal': '400',
            'medium': '500',
            'semibold': '600',
            'bold': '700',
        },
        'line_heights': {
            'tight': '1.2',
            'normal': '1.4',
            'relaxed': '1.6',
        }
    }
    
    # 🌊 SOMBRAS
    SHADOWS = {
        'none': 'none',
        'sm': '0 1px 2px rgba(0, 0, 0, 0.05)',
        'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
        'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
    }

class ComponentStyles:
    """Estilos para componentes específicos"""
    
    @staticmethod
    def button_primary():
        """Estilo para botón primario"""
        return f"""
        QPushButton {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {DesignTokens.COLORS['primary']}, 
                stop:1 {DesignTokens.COLORS['primary_hover']});
            color: {DesignTokens.COLORS['text_inverse']};
            border: none;
            border-radius: {DesignTokens.RADIUS['lg']};
            padding: {DesignTokens.SPACING['md']} {DesignTokens.SPACING['xl']};
            font-family: {DesignTokens.TYPOGRAPHY['font_family']};
            font-size: {DesignTokens.TYPOGRAPHY['font_sizes']['base']};
            font-weight: {DesignTokens.TYPOGRAPHY['font_weights']['semibold']};
            min-height: 40px;
            min-width: 120px;
        }}
        QPushButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {DesignTokens.COLORS['primary_hover']}, 
                stop:1 {DesignTokens.COLORS['primary_pressed']});
        }}
        QPushButton:pressed {{
            background: {DesignTokens.COLORS['primary_pressed']};
        }}
        QPushButton:disabled {{
            background: {DesignTokens.COLORS['text_disabled']};
            color: {DesignTokens.COLORS['surface']};
        }}
        """
    
    @staticmethod
    def button_secondary():
        """Estilo para botón secundario"""
        return f"""
        QPushButton {{
            background: {DesignTokens.COLORS['surface']};
            color: {DesignTokens.COLORS['text_primary']};
            border: 2px solid {DesignTokens.COLORS['border']};
            border-radius: {DesignTokens.RADIUS['lg']};
            padding: {DesignTokens.SPACING['md']} {DesignTokens.SPACING['xl']};
            font-family: {DesignTokens.TYPOGRAPHY['font_family']};
            font-size: {DesignTokens.TYPOGRAPHY['font_sizes']['base']};
            font-weight: {DesignTokens.TYPOGRAPHY['font_weights']['semibold']};
            min-height: 40px;
            min-width: 120px;
        }}
        QPushButton:hover {{
            background: {DesignTokens.COLORS['surface_secondary']};
            border-color: {DesignTokens.COLORS['border_hover']};
        }}
        QPushButton:pressed {{
            background: {DesignTokens.COLORS['surface_tertiary']};
        }}
        """
    
    @staticmethod
    def button_success():
        """Estilo para botón de éxito"""
        return f"""
        QPushButton {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {DesignTokens.COLORS['success']}, 
                stop:1 #047857);
            color: {DesignTokens.COLORS['text_inverse']};
            border: none;
            border-radius: {DesignTokens.RADIUS['lg']};
            padding: {DesignTokens.SPACING['md']} {DesignTokens.SPACING['xl']};
            font-family: {DesignTokens.TYPOGRAPHY['font_family']};
            font-size: {DesignTokens.TYPOGRAPHY['font_sizes']['base']};
            font-weight: {DesignTokens.TYPOGRAPHY['font_weights']['semibold']};
            min-height: 40px;
            min-width: 120px;
        }}
        QPushButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #047857, stop:1 #065f46);
        }}
        """
    
    @staticmethod
    def button_danger():
        """Estilo para botón de peligro"""
        return f"""
        QPushButton {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {DesignTokens.COLORS['error']}, 
                stop:1 #b91c1c);
            color: {DesignTokens.COLORS['text_inverse']};
            border: none;
            border-radius: {DesignTokens.RADIUS['lg']};
            padding: {DesignTokens.SPACING['md']} {DesignTokens.SPACING['xl']};
            font-family: {DesignTokens.TYPOGRAPHY['font_family']};
            font-size: {DesignTokens.TYPOGRAPHY['font_sizes']['base']};
            font-weight: {DesignTokens.TYPOGRAPHY['font_weights']['semibold']};
            min-height: 40px;
            min-width: 120px;
        }}
        QPushButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #b91c1c, stop:1 #991b1b);
        }}
        """
    
    @staticmethod
    def input_field():
        """Estilo para campos de entrada"""
        return f"""
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background: {DesignTokens.COLORS['surface']};
            color: {DesignTokens.COLORS['text_primary']};
            border: 2px solid {DesignTokens.COLORS['border']};
            border-radius: {DesignTokens.RADIUS['lg']};
            padding: {DesignTokens.SPACING['md']};
            font-family: {DesignTokens.TYPOGRAPHY['font_family']};
            font-size: {DesignTokens.TYPOGRAPHY['font_sizes']['base']};
            min-height: 40px;
        }}
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {DesignTokens.COLORS['border_focus']};
            background: {DesignTokens.COLORS['primary_light']};
            outline: none;
        }}
        QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover {{
            border-color: {DesignTokens.COLORS['border_hover']};
        }}
        """
    
    @staticmethod
    def combo_box():
        """Estilo para combo boxes"""
        return f"""
        QComboBox {{
            background: {DesignTokens.COLORS['surface']};
            color: {DesignTokens.COLORS['text_primary']};
            border: 2px solid {DesignTokens.COLORS['border']};
            border-radius: {DesignTokens.RADIUS['lg']};
            padding: {DesignTokens.SPACING['md']};
            font-family: {DesignTokens.TYPOGRAPHY['font_family']};
            font-size: {DesignTokens.TYPOGRAPHY['font_sizes']['base']};
            min-height: 40px;
            min-width: 150px;
        }}
        QComboBox:focus {{
            border-color: {DesignTokens.COLORS['border_focus']};
        }}
        QComboBox:hover {{
            border-color: {DesignTokens.COLORS['border_hover']};
        }}
        QComboBox::drop-down {{
            border: none;
            width: 30px;
            background: transparent;
        }}
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 6px solid {DesignTokens.COLORS['text_secondary']};
            margin-right: {DesignTokens.SPACING['md']};
        }}
        QComboBox QAbstractItemView {{
            background: {DesignTokens.COLORS['surface']};
            color: {DesignTokens.COLORS['text_primary']};
            border: 2px solid {DesignTokens.COLORS['border']};
            border-radius: {DesignTokens.RADIUS['lg']};
            selection-background-color: {DesignTokens.COLORS['primary']};
            selection-color: {DesignTokens.COLORS['text_inverse']};
            padding: {DesignTokens.SPACING['sm']};
        }}
        """
    
    @staticmethod
    def table_widget():
        """Estilo para tablas"""
        return f"""
        QTableWidget, QTableView {{
            background: {DesignTokens.COLORS['surface']};
            color: {DesignTokens.COLORS['text_primary']};
            border: 2px solid {DesignTokens.COLORS['border']};
            border-radius: {DesignTokens.RADIUS['lg']};
            gridline-color: {DesignTokens.COLORS['border']};
            selection-background-color: {DesignTokens.COLORS['primary']};
            selection-color: {DesignTokens.COLORS['text_inverse']};
            alternate-background-color: {DesignTokens.COLORS['surface_secondary']};
            font-family: {DesignTokens.TYPOGRAPHY['font_family']};
            font-size: {DesignTokens.TYPOGRAPHY['font_sizes']['base']};
        }}
        QTableWidget::item, QTableView::item {{
            color: {DesignTokens.COLORS['text_primary']};
            padding: {DesignTokens.SPACING['md']} {DesignTokens.SPACING['sm']};
            border: none;
            border-bottom: 1px solid {DesignTokens.COLORS['border']};
        }}
        QTableWidget::item:selected, QTableView::item:selected {{
            background: {DesignTokens.COLORS['primary']};
            color: {DesignTokens.COLORS['text_inverse']};
        }}
        QTableWidget::item:hover, QTableView::item:hover {{
            background: {DesignTokens.COLORS['surface_secondary']};
        }}
        QHeaderView::section {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {DesignTokens.COLORS['surface_tertiary']}, 
                stop:1 {DesignTokens.COLORS['surface_secondary']});
            color: {DesignTokens.COLORS['text_primary']};
            border: none;
            border-bottom: 2px solid {DesignTokens.COLORS['border']};
            border-right: 1px solid {DesignTokens.COLORS['border']};
            padding: {DesignTokens.SPACING['lg']} {DesignTokens.SPACING['sm']};
            font-weight: {DesignTokens.TYPOGRAPHY['font_weights']['bold']};
            font-size: {DesignTokens.TYPOGRAPHY['font_sizes']['sm']};
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }}
        QHeaderView::section:hover {{
            background: {DesignTokens.COLORS['surface_secondary']};
        }}
        """
    
    @staticmethod
    def group_box():
        """Estilo para group boxes"""
        return f"""
        QGroupBox {{
            color: {DesignTokens.COLORS['text_primary']};
            border: 2px solid {DesignTokens.COLORS['border']};
            border-radius: {DesignTokens.RADIUS['lg']};
            margin-top: {DesignTokens.SPACING['lg']};
            padding-top: {DesignTokens.SPACING['lg']};
            font-family: {DesignTokens.TYPOGRAPHY['font_family']};
            font-size: {DesignTokens.TYPOGRAPHY['font_sizes']['lg']};
            font-weight: {DesignTokens.TYPOGRAPHY['font_weights']['semibold']};
        }}
        QGroupBox::title {{
            color: {DesignTokens.COLORS['text_primary']};
            subcontrol-origin: margin;
            left: {DesignTokens.SPACING['lg']};
            padding: 0 {DesignTokens.SPACING['md']} 0 {DesignTokens.SPACING['md']};
            background: {DesignTokens.COLORS['surface']};
        }}
        """
    
    @staticmethod
    def tab_widget():
        """Estilo para pestañas"""
        return f"""
        QTabWidget::pane {{
            border: 2px solid {DesignTokens.COLORS['border']};
            border-radius: {DesignTokens.RADIUS['lg']};
            background: {DesignTokens.COLORS['surface']};
            margin-top: 2px;
        }}
        QTabBar::tab {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {DesignTokens.COLORS['surface_tertiary']}, 
                stop:1 {DesignTokens.COLORS['surface_secondary']});
            color: {DesignTokens.COLORS['text_secondary']};
            border: 2px solid {DesignTokens.COLORS['border']};
            padding: {DesignTokens.SPACING['md']} {DesignTokens.SPACING['xxl']};
            margin-right: 2px;
            border-top-left-radius: {DesignTokens.RADIUS['lg']};
            border-top-right-radius: {DesignTokens.RADIUS['lg']};
            font-family: {DesignTokens.TYPOGRAPHY['font_family']};
            font-size: {DesignTokens.TYPOGRAPHY['font_sizes']['base']};
            font-weight: {DesignTokens.TYPOGRAPHY['font_weights']['semibold']};
            min-width: 120px;
        }}
        QTabBar::tab:selected {{
            background: {DesignTokens.COLORS['surface']};
            color: {DesignTokens.COLORS['text_primary']};
            border-bottom-color: {DesignTokens.COLORS['surface']};
            font-weight: {DesignTokens.TYPOGRAPHY['font_weights']['bold']};
        }}
        QTabBar::tab:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {DesignTokens.COLORS['surface_secondary']}, 
                stop:1 {DesignTokens.COLORS['border']});
            color: {DesignTokens.COLORS['text_primary']};
        }}
        """
    
    @staticmethod
    def dialog_base():
        """Estilo base para diálogos"""
        return f"""
        QDialog {{
            background: {DesignTokens.COLORS['background']};
            color: {DesignTokens.COLORS['text_primary']};
            font-family: {DesignTokens.TYPOGRAPHY['font_family']};
        }}
        QLabel {{
            color: {DesignTokens.COLORS['text_primary']};
            font-family: {DesignTokens.TYPOGRAPHY['font_family']};
            font-size: {DesignTokens.TYPOGRAPHY['font_sizes']['base']};
        }}
        """
    
    @staticmethod
    def scrollbar():
        """Estilo para scrollbars"""
        return f"""
        QScrollBar:vertical {{
            background: {DesignTokens.COLORS['surface_secondary']};
            width: 12px;
            border-radius: 6px;
            margin: 0;
        }}
        QScrollBar::handle:vertical {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {DesignTokens.COLORS['border_hover']}, 
                stop:1 {DesignTokens.COLORS['text_disabled']});
            border-radius: 6px;
            min-height: 20px;
            margin: 2px;
        }}
        QScrollBar::handle:vertical:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {DesignTokens.COLORS['text_disabled']}, 
                stop:1 {DesignTokens.COLORS['text_tertiary']});
        }}
        QScrollBar:horizontal {{
            background: {DesignTokens.COLORS['surface_secondary']};
            height: 12px;
            border-radius: 6px;
            margin: 0;
        }}
        QScrollBar::handle:horizontal {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {DesignTokens.COLORS['border_hover']}, 
                stop:1 {DesignTokens.COLORS['text_disabled']});
            border-radius: 6px;
            min-width: 20px;
            margin: 2px;
        }}
        QScrollBar::add-line, QScrollBar::sub-line {{
            width: 0px;
            height: 0px;
        }}
        """

class ProfessionalTheme:
    """Tema profesional unificado"""
    
    @staticmethod
    def get_complete_stylesheet():
        """Obtiene la hoja de estilos completa"""
        return f"""
        /* Sistema de Diseño Profesional PRO-2000 v2.1.0 */
        
        /* Estilos base */
        QWidget {{
            font-family: {DesignTokens.TYPOGRAPHY['font_family']};
            font-size: {DesignTokens.TYPOGRAPHY['font_sizes']['base']};
            color: {DesignTokens.COLORS['text_primary']};
            background: {DesignTokens.COLORS['background']};
        }}
        
        /* Botones */
        {ComponentStyles.button_primary()}
        
        /* Botones secundarios */
        QPushButton[class="secondary"] {{
            {ComponentStyles.button_secondary().replace('QPushButton {', '').replace('}', '')}
        }}
        
        /* Botones de éxito */
        QPushButton[class="success"] {{
            {ComponentStyles.button_success().replace('QPushButton {', '').replace('}', '')}
        }}
        
        /* Botones de peligro */
        QPushButton[class="danger"] {{
            {ComponentStyles.button_danger().replace('QPushButton {', '').replace('}', '')}
        }}
        
        /* Campos de entrada */
        {ComponentStyles.input_field()}
        
        /* Combo boxes */
        {ComponentStyles.combo_box()}
        
        /* Tablas */
        {ComponentStyles.table_widget()}
        
        /* Group boxes */
        {ComponentStyles.group_box()}
        
        /* Pestañas */
        {ComponentStyles.tab_widget()}
        
        /* Scrollbars */
        {ComponentStyles.scrollbar()}
        
        /* Diálogos */
        {ComponentStyles.dialog_base()}
        
        /* Menús */
        QMenuBar {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {DesignTokens.COLORS['surface']}, 
                stop:1 {DesignTokens.COLORS['surface_secondary']});
            color: {DesignTokens.COLORS['text_primary']};
            border-bottom: 2px solid {DesignTokens.COLORS['border']};
            padding: {DesignTokens.SPACING['sm']};
            font-weight: {DesignTokens.TYPOGRAPHY['font_weights']['semibold']};
        }}
        QMenuBar::item {{
            background: transparent;
            color: {DesignTokens.COLORS['text_primary']};
            padding: {DesignTokens.SPACING['md']} {DesignTokens.SPACING['lg']};
            border-radius: {DesignTokens.RADIUS['md']};
        }}
        QMenuBar::item:selected {{
            background: {DesignTokens.COLORS['surface_tertiary']};
            color: {DesignTokens.COLORS['text_primary']};
        }}
        QMenu {{
            background: {DesignTokens.COLORS['surface']};
            color: {DesignTokens.COLORS['text_primary']};
            border: 2px solid {DesignTokens.COLORS['border']};
            border-radius: {DesignTokens.RADIUS['lg']};
            padding: {DesignTokens.SPACING['sm']};
        }}
        QMenu::item {{
            color: {DesignTokens.COLORS['text_primary']};
            padding: {DesignTokens.SPACING['md']} {DesignTokens.SPACING['xxl']};
            border-radius: {DesignTokens.RADIUS['md']};
        }}
        QMenu::item:selected {{
            background: {DesignTokens.COLORS['primary']};
            color: {DesignTokens.COLORS['text_inverse']};
        }}
        
        /* Tooltips */
        QToolTip {{
            background: {DesignTokens.COLORS['text_primary']};
            color: {DesignTokens.COLORS['text_inverse']};
            border: none;
            border-radius: {DesignTokens.RADIUS['md']};
            padding: {DesignTokens.SPACING['md']};
            font-size: {DesignTokens.TYPOGRAPHY['font_sizes']['sm']};
            font-weight: {DesignTokens.TYPOGRAPHY['font_weights']['medium']};
        }}
        
        /* Checkboxes y Radio buttons */
        QCheckBox, QRadioButton {{
            color: {DesignTokens.COLORS['text_primary']};
            spacing: {DesignTokens.SPACING['md']};
            font-family: {DesignTokens.TYPOGRAPHY['font_family']};
        }}
        QCheckBox::indicator, QRadioButton::indicator {{
            width: 20px;
            height: 20px;
            border: 2px solid {DesignTokens.COLORS['border']};
            border-radius: {DesignTokens.RADIUS['sm']};
            background: {DesignTokens.COLORS['surface']};
        }}
        QCheckBox::indicator:checked, QRadioButton::indicator:checked {{
            background: {DesignTokens.COLORS['primary']};
            border-color: {DesignTokens.COLORS['primary']};
        }}
        QRadioButton::indicator {{
            border-radius: 10px;
        }}
        
        /* Spinboxes */
        QSpinBox, QDoubleSpinBox {{
            background: {DesignTokens.COLORS['surface']};
            color: {DesignTokens.COLORS['text_primary']};
            border: 2px solid {DesignTokens.COLORS['border']};
            border-radius: {DesignTokens.RADIUS['lg']};
            padding: {DesignTokens.SPACING['md']};
            font-family: {DesignTokens.TYPOGRAPHY['font_family']};
            min-height: 40px;
        }}
        QSpinBox:focus, QDoubleSpinBox:focus {{
            border-color: {DesignTokens.COLORS['border_focus']};
        }}
        
        /* Progress bars */
        QProgressBar {{
            background: {DesignTokens.COLORS['surface_secondary']};
            border: 2px solid {DesignTokens.COLORS['border']};
            border-radius: {DesignTokens.RADIUS['lg']};
            text-align: center;
            color: {DesignTokens.COLORS['text_primary']};
            font-weight: {DesignTokens.TYPOGRAPHY['font_weights']['semibold']};
            min-height: 24px;
        }}
        QProgressBar::chunk {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {DesignTokens.COLORS['primary']}, 
                stop:1 {DesignTokens.COLORS['primary_hover']});
            border-radius: {DesignTokens.RADIUS['md']};
        }}
        """

# Instancia global del tema
professional_theme = ProfessionalTheme()

def get_professional_theme():
    """Obtiene el tema profesional"""
    return professional_theme

def apply_professional_theme(app):
    """Aplica el tema profesional a la aplicación"""
    app.setStyleSheet(professional_theme.get_complete_stylesheet())
