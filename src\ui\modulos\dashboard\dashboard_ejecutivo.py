"""
Dashboard ejecutivo para PRO-2000.
Muestra KPIs y métricas importantes del negocio.
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QGridLayout, QScrollArea, QPushButton, QComboBox,
    QProgressBar, QTableWidget, QTableWidgetItem, QHeaderView,
    QGroupBox, QTabWidget
)
from PyQt6.QtCore import Qt, QTimer, QDate
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor, QBrush
# Intentar importar QtChart, si no está disponible usar gráficos básicos
try:
    from PyQt6.QtChart import QChart, QChartView, QPieSeries, QBarSeries, QBarSet, QValueAxis, QBarCategoryAxis
    QTCHART_AVAILABLE = True
except ImportError:
    QTCHART_AVAILABLE = False

from models.base import get_db
from models.obra import Obra
from models.articulo import Articulo, ObraArticulo
from models.perfil import Perfil
from models.accesorio import Accesorio
from models.cristal import Cristal
from models.cliente import Cliente
from datetime import datetime, timedelta
import calendar


class DashboardEjecutivo(QWidget):
    """Widget del dashboard ejecutivo."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Dashboard Ejecutivo - PRO-2000")
        
        # Timer para actualización automática
        self.timer_actualizacion = QTimer()
        self.timer_actualizacion.timeout.connect(self._actualizar_datos)
        self.timer_actualizacion.start(300000)  # Actualizar cada 5 minutos
        
        self._inicializar_ui()
        self._cargar_datos_iniciales()
    
    def _inicializar_ui(self):
        """Configura la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)
        
        # Título y controles
        self._crear_header(layout_principal)
        
        # Crear pestañas
        self.tabs = QTabWidget()
        
        # Pestaña 1: Resumen General
        self._crear_tab_resumen()
        
        # Pestaña 2: Obras
        self._crear_tab_obras()
        
        # Pestaña 3: Inventario
        self._crear_tab_inventario()
        
        # Pestaña 4: Rendimiento
        self._crear_tab_rendimiento()
        
        layout_principal.addWidget(self.tabs)
    
    def _crear_header(self, layout_principal):
        """Crea el header con título y controles."""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #2c3e50;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        layout_header = QHBoxLayout(header_frame)
        
        # Título
        titulo = QLabel("📊 Dashboard Ejecutivo PRO-2000")
        titulo.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        titulo.setStyleSheet("color: white;")
        layout_header.addWidget(titulo)
        
        layout_header.addStretch()
        
        # Fecha y hora actual
        self.label_fecha_hora = QLabel()
        self.label_fecha_hora.setFont(QFont("Arial", 12))
        self.label_fecha_hora.setStyleSheet("color: #ecf0f1;")
        self._actualizar_fecha_hora()
        layout_header.addWidget(self.label_fecha_hora)
        
        # Timer para actualizar fecha/hora
        timer_fecha = QTimer()
        timer_fecha.timeout.connect(self._actualizar_fecha_hora)
        timer_fecha.start(1000)  # Actualizar cada segundo
        
        # Botón de actualizar
        btn_actualizar = QPushButton("🔄 Actualizar")
        btn_actualizar.clicked.connect(self._actualizar_datos)
        btn_actualizar.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout_header.addWidget(btn_actualizar)
        
        layout_principal.addWidget(header_frame)
    
    def _crear_tab_resumen(self):
        """Crea la pestaña de resumen general."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # KPIs principales
        self._crear_kpis_principales(layout)
        
        # Gráficos de resumen
        self._crear_graficos_resumen(layout)
        
        self.tabs.addTab(tab, "📈 Resumen")
    
    def _crear_kpis_principales(self, layout):
        """Crea los KPIs principales."""
        grupo_kpis = QGroupBox("KPIs Principales")
        layout_kpis = QGridLayout(grupo_kpis)
        
        # Crear tarjetas de KPI
        self.kpi_obras_activas = self._crear_tarjeta_kpi("Obras Activas", "0", "#3498db")
        self.kpi_obras_mes = self._crear_tarjeta_kpi("Obras Este Mes", "0", "#2ecc71")
        self.kpi_facturacion_mes = self._crear_tarjeta_kpi("Facturación Mes", "0 €", "#e74c3c")
        self.kpi_articulos_total = self._crear_tarjeta_kpi("Artículos Catálogo", "0", "#f39c12")
        
        # Añadir a layout
        layout_kpis.addWidget(self.kpi_obras_activas, 0, 0)
        layout_kpis.addWidget(self.kpi_obras_mes, 0, 1)
        layout_kpis.addWidget(self.kpi_facturacion_mes, 0, 2)
        layout_kpis.addWidget(self.kpi_articulos_total, 0, 3)
        
        # Segunda fila de KPIs
        self.kpi_perfiles_stock = self._crear_tarjeta_kpi("Perfiles en Stock", "0", "#9b59b6")
        self.kpi_eficiencia_cortes = self._crear_tarjeta_kpi("Eficiencia Cortes", "0%", "#1abc9c")
        self.kpi_tiempo_promedio = self._crear_tarjeta_kpi("Tiempo Promedio", "0h", "#34495e")
        self.kpi_margen_beneficio = self._crear_tarjeta_kpi("Margen Beneficio", "0%", "#e67e22")
        
        layout_kpis.addWidget(self.kpi_perfiles_stock, 1, 0)
        layout_kpis.addWidget(self.kpi_eficiencia_cortes, 1, 1)
        layout_kpis.addWidget(self.kpi_tiempo_promedio, 1, 2)
        layout_kpis.addWidget(self.kpi_margen_beneficio, 1, 3)
        
        layout.addWidget(grupo_kpis)
    
    def _crear_tarjeta_kpi(self, titulo, valor, color):
        """Crea una tarjeta de KPI."""
        frame = QFrame()
        frame.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }}
        """)
        frame.setMinimumHeight(100)
        
        layout = QVBoxLayout(frame)
        
        # Título
        label_titulo = QLabel(titulo)
        label_titulo.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        label_titulo.setStyleSheet("color: white;")
        label_titulo.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label_titulo)
        
        # Valor
        label_valor = QLabel(valor)
        label_valor.setFont(QFont("Arial", 20, QFont.Weight.Bold))
        label_valor.setStyleSheet("color: white;")
        label_valor.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label_valor)
        
        # Guardar referencia al label del valor para actualizarlo
        frame.label_valor = label_valor
        
        return frame
    
    def _crear_graficos_resumen(self, layout):
        """Crea los gráficos de resumen."""
        layout_graficos = QHBoxLayout()

        if QTCHART_AVAILABLE:
            # Gráfico de obras por estado
            self.grafico_obras_estado = self._crear_grafico_pie("Obras por Estado")
            layout_graficos.addWidget(self.grafico_obras_estado)

            # Gráfico de facturación mensual
            self.grafico_facturacion = self._crear_grafico_barras("Facturación Últimos 6 Meses")
            layout_graficos.addWidget(self.grafico_facturacion)
        else:
            # Gráficos básicos sin QtChart
            self.grafico_obras_estado = self._crear_grafico_basico("Obras por Estado")
            layout_graficos.addWidget(self.grafico_obras_estado)

            self.grafico_facturacion = self._crear_grafico_basico("Facturación Últimos 6 Meses")
            layout_graficos.addWidget(self.grafico_facturacion)

        layout.addLayout(layout_graficos)
    
    def _crear_grafico_pie(self, titulo):
        """Crea un gráfico de pastel."""
        chart = QChart()
        chart.setTitle(titulo)
        chart.setAnimationOptions(QChart.AnimationOption.SeriesAnimations)
        
        # Serie de datos (se llenará con datos reales)
        series = QPieSeries()
        series.append("Activas", 0)
        series.append("Completadas", 0)
        series.append("Pausadas", 0)
        
        chart.addSeries(series)
        chart.legend().setAlignment(Qt.AlignmentFlag.AlignBottom)
        
        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.RenderHint.Antialiasing)
        chart_view.setMinimumHeight(300)
        
        return chart_view
    
    def _crear_grafico_barras(self, titulo):
        """Crea un gráfico de barras."""
        chart = QChart()
        chart.setTitle(titulo)
        chart.setAnimationOptions(QChart.AnimationOption.SeriesAnimations)
        
        # Serie de datos
        series = QBarSeries()
        bar_set = QBarSet("Facturación")
        
        # Datos de ejemplo (se actualizarán con datos reales)
        bar_set.append([0, 0, 0, 0, 0, 0])
        series.append(bar_set)
        
        chart.addSeries(series)
        
        # Configurar ejes
        categories = ["Ene", "Feb", "Mar", "Abr", "May", "Jun"]
        axis_x = QBarCategoryAxis()
        axis_x.append(categories)
        chart.addAxis(axis_x, Qt.AlignmentFlag.AlignBottom)
        series.attachAxis(axis_x)
        
        axis_y = QValueAxis()
        axis_y.setRange(0, 100000)
        chart.addAxis(axis_y, Qt.AlignmentFlag.AlignLeft)
        series.attachAxis(axis_y)
        
        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.RenderHint.Antialiasing)
        chart_view.setMinimumHeight(300)
        
        return chart_view

    def _crear_grafico_basico(self, titulo):
        """Crea un gráfico básico sin QtChart."""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                background-color: #ecf0f1;
                padding: 10px;
            }
        """)
        frame.setMinimumHeight(300)

        layout = QVBoxLayout(frame)

        # Título
        label_titulo = QLabel(titulo)
        label_titulo.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        label_titulo.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label_titulo)

        # Contenido del gráfico (texto informativo)
        if "Estado" in titulo:
            contenido = QLabel("""
            📊 Distribución de Obras:

            🟢 Activas: 15 obras
            🔵 Completadas: 8 obras
            🟡 Pausadas: 2 obras

            Total: 25 obras
            """)
        else:
            contenido = QLabel("""
            💰 Facturación Mensual:

            Enero: 25,000 €
            Febrero: 32,000 €
            Marzo: 28,000 €
            Abril: 45,000 €
            Mayo: 38,000 €
            Junio: 42,000 €
            """)

        contenido.setAlignment(Qt.AlignmentFlag.AlignCenter)
        contenido.setStyleSheet("color: #2c3e50; font-size: 11px;")
        layout.addWidget(contenido)

        return frame

    def _crear_tab_obras(self):
        """Crea la pestaña de obras."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Tabla de obras recientes
        grupo_obras = QGroupBox("Obras Recientes")
        layout_obras = QVBoxLayout(grupo_obras)
        
        self.tabla_obras = QTableWidget()
        self.tabla_obras.setColumnCount(6)
        self.tabla_obras.setHorizontalHeaderLabels([
            "Código", "Nombre", "Cliente", "Estado", "Fecha Inicio", "Valor"
        ])
        
        # Configurar tabla
        header = self.tabla_obras.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        
        layout_obras.addWidget(self.tabla_obras)
        layout.addWidget(grupo_obras)
        
        self.tabs.addTab(tab, "🏗️ Obras")
    
    def _crear_tab_inventario(self):
        """Crea la pestaña de inventario."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Resumen de inventario
        layout_resumen = QHBoxLayout()
        
        self.kpi_perfiles_tipos = self._crear_tarjeta_kpi("Tipos de Perfiles", "0", "#3498db")
        self.kpi_accesorios_tipos = self._crear_tarjeta_kpi("Tipos de Accesorios", "0", "#2ecc71")
        self.kpi_cristales_tipos = self._crear_tarjeta_kpi("Tipos de Cristales", "0", "#e74c3c")
        
        layout_resumen.addWidget(self.kpi_perfiles_tipos)
        layout_resumen.addWidget(self.kpi_accesorios_tipos)
        layout_resumen.addWidget(self.kpi_cristales_tipos)
        
        layout.addLayout(layout_resumen)
        
        # Tabla de inventario crítico
        grupo_critico = QGroupBox("Inventario Crítico (Stock Bajo)")
        layout_critico = QVBoxLayout(grupo_critico)
        
        self.tabla_inventario_critico = QTableWidget()
        self.tabla_inventario_critico.setColumnCount(4)
        self.tabla_inventario_critico.setHorizontalHeaderLabels([
            "Tipo", "Código", "Descripción", "Stock Actual"
        ])
        
        layout_critico.addWidget(self.tabla_inventario_critico)
        layout.addWidget(grupo_critico)
        
        self.tabs.addTab(tab, "📦 Inventario")
    
    def _crear_tab_rendimiento(self):
        """Crea la pestaña de rendimiento."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Métricas de rendimiento
        layout_metricas = QHBoxLayout()
        
        self.kpi_obras_completadas_mes = self._crear_tarjeta_kpi("Obras Completadas", "0", "#27ae60")
        self.kpi_tiempo_promedio_obra = self._crear_tarjeta_kpi("Tiempo Promedio", "0 días", "#8e44ad")
        self.kpi_satisfaccion_cliente = self._crear_tarjeta_kpi("Satisfacción", "0%", "#f39c12")
        
        layout_metricas.addWidget(self.kpi_obras_completadas_mes)
        layout_metricas.addWidget(self.kpi_tiempo_promedio_obra)
        layout_metricas.addWidget(self.kpi_satisfaccion_cliente)
        
        layout.addLayout(layout_metricas)
        
        # Gráfico de tendencias
        if QTCHART_AVAILABLE:
            self.grafico_tendencias = self._crear_grafico_barras("Tendencias de Producción")
        else:
            self.grafico_tendencias = self._crear_grafico_basico("Tendencias de Producción")
        layout.addWidget(self.grafico_tendencias)
        
        self.tabs.addTab(tab, "⚡ Rendimiento")
    
    def _actualizar_fecha_hora(self):
        """Actualiza la fecha y hora actual."""
        ahora = datetime.now()
        fecha_hora = ahora.strftime("%d/%m/%Y %H:%M:%S")
        self.label_fecha_hora.setText(fecha_hora)
    
    def _cargar_datos_iniciales(self):
        """Carga los datos iniciales del dashboard."""
        self._actualizar_datos()
    
    def _actualizar_datos(self):
        """Actualiza todos los datos del dashboard."""
        try:
            self._actualizar_kpis_principales()
            self._actualizar_tabla_obras()
            self._actualizar_inventario()
            self._actualizar_graficos()
        except Exception as e:
            print(f"Error al actualizar dashboard: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def _actualizar_kpis_principales(self):
        """Actualiza los KPIs principales."""
        db = next(get_db())

        try:
            # Obras activas (en curso)
            obras_activas = db.query(Obra).filter(Obra.estado == 'En curso').count()
            self.kpi_obras_activas.label_valor.setText(str(obras_activas))

            # Obras este mes
            inicio_mes = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            obras_mes = db.query(Obra).filter(
                Obra.fecha_inicio >= inicio_mes
            ).count()
            self.kpi_obras_mes.label_valor.setText(str(obras_mes))

            # Artículos en catálogo
            articulos_total = db.query(Articulo).count()
            self.kpi_articulos_total.label_valor.setText(str(articulos_total))
            
            # Perfiles en stock
            perfiles_stock = db.query(Perfil).count()
            self.kpi_perfiles_stock.label_valor.setText(str(perfiles_stock))
            
            # Valores simulados para otros KPIs
            self.kpi_facturacion_mes.label_valor.setText("45,230 €")
            self.kpi_eficiencia_cortes.label_valor.setText("89.2%")
            self.kpi_tiempo_promedio.label_valor.setText("12.5h")
            self.kpi_margen_beneficio.label_valor.setText("23.8%")
            
        finally:
            db.close()
    
    def _actualizar_tabla_obras(self):
        """Actualiza la tabla de obras."""
        db = next(get_db())

        try:
            # Obtener obras con información del cliente
            obras = db.query(Obra).join(Cliente, isouter=True).order_by(Obra.fecha_inicio.desc()).limit(10).all()

            self.tabla_obras.setRowCount(len(obras))

            for i, obra in enumerate(obras):
                self.tabla_obras.setItem(i, 0, QTableWidgetItem(obra.codigo or ""))
                self.tabla_obras.setItem(i, 1, QTableWidgetItem(obra.nombre or ""))

                # Obtener nombre del cliente
                cliente_nombre = obra.cliente.nombre if obra.cliente else "Sin cliente"
                self.tabla_obras.setItem(i, 2, QTableWidgetItem(cliente_nombre))

                self.tabla_obras.setItem(i, 3, QTableWidgetItem(obra.estado or "Pendiente"))

                fecha_str = obra.fecha_inicio.strftime("%d/%m/%Y") if obra.fecha_inicio else "N/A"
                self.tabla_obras.setItem(i, 4, QTableWidgetItem(fecha_str))

                # Usar presupuesto como valor
                valor = obra.presupuesto if obra.presupuesto else 0.0
                self.tabla_obras.setItem(i, 5, QTableWidgetItem(f"{valor:.2f} €"))

        finally:
            db.close()
    
    def _actualizar_inventario(self):
        """Actualiza los datos de inventario."""
        db = next(get_db())
        
        try:
            # Contar tipos de materiales
            perfiles_count = db.query(Perfil).count()
            accesorios_count = db.query(Accesorio).count()
            cristales_count = db.query(Cristal).count()

            # Verificar si los KPIs existen antes de actualizarlos
            if hasattr(self, 'kpi_perfiles_tipos'):
                self.kpi_perfiles_tipos.label_valor.setText(str(perfiles_count))
            if hasattr(self, 'kpi_accesorios_tipos'):
                self.kpi_accesorios_tipos.label_valor.setText(str(accesorios_count))
            if hasattr(self, 'kpi_cristales_tipos'):
                self.kpi_cristales_tipos.label_valor.setText(str(cristales_count))
            
            # Simular inventario crítico solo si la tabla existe
            if hasattr(self, 'tabla_inventario_critico'):
                self.tabla_inventario_critico.setRowCount(3)

                items_criticos = [
                    ("Perfil", "PERF-001", "Marco 60x40", "5 uds"),
                    ("Accesorio", "ACC-015", "Bisagra reforzada", "12 uds"),
                    ("Cristal", "CRIS-003", "Doble 4+4", "8 m²")
                ]

                for i, (tipo, codigo, desc, stock) in enumerate(items_criticos):
                    self.tabla_inventario_critico.setItem(i, 0, QTableWidgetItem(tipo))
                    self.tabla_inventario_critico.setItem(i, 1, QTableWidgetItem(codigo))
                    self.tabla_inventario_critico.setItem(i, 2, QTableWidgetItem(desc))
                    self.tabla_inventario_critico.setItem(i, 3, QTableWidgetItem(stock))
                
        finally:
            db.close()
    
    def _actualizar_graficos(self):
        """Actualiza los gráficos."""
        if QTCHART_AVAILABLE:
            try:
                # Actualizar gráfico de obras por estado
                chart_obras = self.grafico_obras_estado.chart()
                series_obras = chart_obras.series()[0]
                series_obras.clear()

                # Datos simulados
                series_obras.append("Activas", 15)
                series_obras.append("Completadas", 8)
                series_obras.append("Pausadas", 2)

                # Actualizar gráfico de facturación
                chart_facturacion = self.grafico_facturacion.chart()
                series_facturacion = chart_facturacion.series()[0]
                bar_set = series_facturacion.barSets()[0]

                # Datos simulados de facturación
                bar_set.replace(0, [25000, 32000, 28000, 45000, 38000, 42000])
            except Exception as e:
                print(f"Error actualizando gráficos QtChart: {str(e)}")
        else:
            # Los gráficos básicos no necesitan actualización dinámica
            pass
    
    def _calcular_valor_obra(self, obra):
        """Calcula el valor estimado de una obra."""
        db = next(get_db())
        
        try:
            obra_articulos = db.query(ObraArticulo).filter(
                ObraArticulo.obra_id == obra.id
            ).all()
            
            valor_total = 0.0
            for obra_articulo in obra_articulos:
                if hasattr(obra_articulo, 'precio_total') and obra_articulo.precio_total:
                    valor_total += obra_articulo.precio_total
                else:
                    # Estimación básica si no hay precio calculado
                    valor_total += 150.0 * obra_articulo.cantidad  # 150€ por artículo estimado
            
            return valor_total
            
        finally:
            db.close()
