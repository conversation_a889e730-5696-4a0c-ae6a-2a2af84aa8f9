"""
Editor de artículos SIMPLE que SÍ FUNCIONA
"""

import sys
import os
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QWidget
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPainter, QColor, QBrush, QPen, QFont, QFontMetrics
from PyQt6.QtCore import QRect, QPoint

from src.ui.utils.window_utils import setup_maximized_dialog


class CanvasSimple(QWidget):
    """Canvas simple que SÍ FUNCIONA"""
    
    def __init__(self):
        super().__init__()
        self.setMinimumSize(700, 500)
        self.setStyleSheet("background-color: #f8f9fa; border: 2px solid #e0e0e0;")
        print("🎨 CANVAS SIMPLE: Inicializado correctamente")
    
    def paintEvent(self, event):
        """Dibuja elementos GARANTIZADOS"""
        print("🎨 CANVAS SIMPLE: paintEvent ejecutándose...")
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Fondo blanco
        painter.fillRect(self.rect(), QBrush(QColor(255, 255, 255)))
        
        # VENTANA BÁSICA - GARANTIZADA
        self._dibujar_ventana_simple(painter)
        
        # ELEMENTOS DE PRUEBA - GARANTIZADOS
        self._dibujar_elementos_prueba(painter)
        
        print("🎨 CANVAS SIMPLE: paintEvent completado")
    
    def _dibujar_ventana_simple(self, painter):
        """Dibuja una ventana simple GARANTIZADA"""
        x, y, w, h = 50, 50, 300, 200
        
        # Marco exterior - MARRÓN
        painter.fillRect(QRect(x, y, w, 20), QBrush(QColor(139, 69, 19)))  # Superior
        painter.fillRect(QRect(x, y + h - 20, w, 20), QBrush(QColor(139, 69, 19)))  # Inferior
        painter.fillRect(QRect(x, y, 20, h), QBrush(QColor(139, 69, 19)))  # Izquierdo
        painter.fillRect(QRect(x + w - 20, y, 20, h), QBrush(QColor(139, 69, 19)))  # Derecho
        
        # Travesaño central - NARANJA
        painter.fillRect(QRect(x + w//2 - 10, y + 20, 20, h - 40), QBrush(QColor(255, 140, 0)))
        
        # Hojas - AZUL
        painter.fillRect(QRect(x + 20, y + 20, (w - 60)//2, h - 40), QBrush(QColor(70, 130, 180, 200)))
        painter.fillRect(QRect(x + 30 + (w - 60)//2, y + 20, (w - 60)//2, h - 40), QBrush(QColor(70, 130, 180, 200)))
        
        # Cristales - AZUL CLARO
        painter.fillRect(QRect(x + 25, y + 25, (w - 70)//2, h - 50), QBrush(QColor(173, 216, 230, 150)))
        painter.fillRect(QRect(x + 35 + (w - 70)//2, y + 25, (w - 70)//2, h - 50), QBrush(QColor(173, 216, 230, 150)))
        
        # Bordes negros
        painter.setPen(QPen(QColor(0, 0, 0), 2))
        painter.drawRect(QRect(x, y, w, h))
        painter.drawRect(QRect(x + w//2 - 10, y + 20, 20, h - 40))
        
        # Texto
        painter.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        painter.drawText(QRect(x, y, w, 20), Qt.AlignmentFlag.AlignCenter, "VENTANA SIMPLE")
    
    def _dibujar_elementos_prueba(self, painter):
        """Dibuja elementos de prueba GARANTIZADOS"""
        # Rectángulo ROJO
        painter.fillRect(QRect(400, 100, 100, 80), QBrush(QColor(255, 0, 0)))
        painter.setPen(QPen(QColor(0, 0, 0), 3))
        painter.drawRect(QRect(400, 100, 100, 80))
        
        # Texto en rectángulo
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        painter.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        painter.drawText(QRect(400, 100, 100, 80), Qt.AlignmentFlag.AlignCenter, "PRUEBA")
        
        # Círculo VERDE
        painter.setBrush(QBrush(QColor(0, 255, 0)))
        painter.drawEllipse(QRect(520, 100, 60, 60))
        
        # Texto de estado
        painter.setPen(QPen(QColor(0, 0, 0), 2))
        painter.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        painter.drawText(QRect(50, 300, 500, 50), Qt.AlignmentFlag.AlignLeft, "✅ CANVAS SIMPLE FUNCIONANDO CORRECTAMENTE")


class EditorArticuloSimple(QDialog):
    """Editor de artículos SIMPLE que SÍ FUNCIONA"""
    
    def __init__(self, parent=None, obra=None, obra_articulo=None):
        print("🚀 EDITOR SIMPLE: Iniciando constructor...")
        super().__init__(parent)
        
        self.obra = obra
        self.obra_articulo = obra_articulo
        
        self.setWindowTitle("Editor Simple de Artículos - FUNCIONAL")
        setup_maximized_dialog(self, "Editor Simple de Artículos")
        
        print("🚀 EDITOR SIMPLE: Configuración básica completada")
        
        self.init_ui()
        
        print("🚀 EDITOR SIMPLE: Constructor completado exitosamente")
    
    def init_ui(self):
        """Inicializa la interfaz SIMPLE"""
        print("🎨 EDITOR SIMPLE: Iniciando init_ui...")
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Header
        header = QLabel("🎯 EDITOR SIMPLE DE ARTÍCULOS - FUNCIONANDO")
        header.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                padding: 15px;
                border-radius: 8px;
                font-size: 18px;
                font-weight: bold;
            }
        """)
        layout.addWidget(header)
        
        # Canvas SIMPLE
        print("🎨 EDITOR SIMPLE: Creando canvas simple...")
        self.canvas = CanvasSimple()
        layout.addWidget(self.canvas)
        print("🎨 EDITOR SIMPLE: Canvas agregado al layout")
        
        # Botones
        botones_layout = QHBoxLayout()
        
        btn_cerrar = QPushButton("Cerrar")
        btn_cerrar.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        btn_cerrar.clicked.connect(self.reject)
        
        botones_layout.addStretch()
        botones_layout.addWidget(btn_cerrar)
        
        layout.addLayout(botones_layout)
        
        print("🎨 EDITOR SIMPLE: init_ui completado")
