"""
Editor de artículos SIMPLE que SÍ FUNCIONA
"""

import sys
import os
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QWidget
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPainter, QColor, QBrush, QPen, QFont, QFontMetrics
from PyQt6.QtCore import QRect, QPoint

from src.ui.utils.window_utils import setup_maximized_dialog


class ElementoInteractivo:
    """Elemento interactivo simple"""
    def __init__(self, tipo, nombre, rect, color):
        self.tipo = tipo
        self.nombre = nombre
        self.rect = rect
        self.color = color
        self.seleccionado = False
        self.hover = False

    def dibujar(self, painter):
        """Dibuja el elemento"""
        # Color base
        color = QColor(self.color)
        if self.hover:
            color = color.lighter(120)
        if self.seleccionado:
            color = color.lighter(140)

        # Dibujar elemento
        painter.fillRect(self.rect, QBrush(color))

        # Borde
        borde_color = QColor(0, 0, 0) if not self.seleccionado else QColor(255, 0, 0)
        borde_grosor = 2 if not self.seleccionado else 3
        painter.setPen(QPen(borde_color, borde_grosor))
        painter.drawRect(self.rect)

        # Texto
        painter.setPen(QPen(QColor(255, 255, 255), 1))
        painter.setFont(QFont("Arial", 8, QFont.Weight.Bold))
        painter.drawText(self.rect, Qt.AlignmentFlag.AlignCenter, self.nombre)


class CanvasSimple(QWidget):
    """Canvas simple que SÍ FUNCIONA con elementos interactivos"""

    elemento_seleccionado = pyqtSignal(object)

    def __init__(self):
        super().__init__()
        self.setMinimumSize(700, 500)
        self.setStyleSheet("background-color: #f8f9fa; border: 2px solid #e0e0e0;")

        # Elementos interactivos
        self.elementos = []
        self.elemento_hover = None
        self.elemento_seleccionado_actual = None

        self._crear_elementos_ventana()

        print("🎨 CANVAS SIMPLE: Inicializado correctamente con elementos interactivos")

    def _crear_elementos_ventana(self):
        """Crea los elementos interactivos de la ventana"""
        x, y, w, h = 50, 50, 300, 200

        # Marco superior
        self.elementos.append(ElementoInteractivo(
            "marco", "Marco Superior",
            QRect(x, y, w, 20),
            QColor(139, 69, 19)
        ))

        # Marco inferior
        self.elementos.append(ElementoInteractivo(
            "marco", "Marco Inferior",
            QRect(x, y + h - 20, w, 20),
            QColor(139, 69, 19)
        ))

        # Marco izquierdo
        self.elementos.append(ElementoInteractivo(
            "marco", "Marco Izquierdo",
            QRect(x, y, 20, h),
            QColor(139, 69, 19)
        ))

        # Marco derecho
        self.elementos.append(ElementoInteractivo(
            "marco", "Marco Derecho",
            QRect(x + w - 20, y, 20, h),
            QColor(139, 69, 19)
        ))

        # Travesaño central
        self.elementos.append(ElementoInteractivo(
            "travesano_v", "Travesaño Central",
            QRect(x + w//2 - 10, y + 20, 20, h - 40),
            QColor(255, 140, 0)
        ))

        # Hoja izquierda
        self.elementos.append(ElementoInteractivo(
            "hoja", "Hoja Izquierda",
            QRect(x + 20, y + 20, (w - 60)//2, h - 40),
            QColor(70, 130, 180, 200)
        ))

        # Hoja derecha
        self.elementos.append(ElementoInteractivo(
            "hoja", "Hoja Derecha",
            QRect(x + 30 + (w - 60)//2, y + 20, (w - 60)//2, h - 40),
            QColor(70, 130, 180, 200)
        ))

        # Cristal izquierdo
        self.elementos.append(ElementoInteractivo(
            "cristal", "Cristal Izquierdo",
            QRect(x + 25, y + 25, (w - 70)//2, h - 50),
            QColor(173, 216, 230, 150)
        ))

        # Cristal derecho
        self.elementos.append(ElementoInteractivo(
            "cristal", "Cristal Derecho",
            QRect(x + 35 + (w - 70)//2, y + 25, (w - 70)//2, h - 50),
            QColor(173, 216, 230, 150)
        ))

        print(f"🎨 CANVAS: Creados {len(self.elementos)} elementos interactivos")
    
    def paintEvent(self, event):
        """Dibuja elementos GARANTIZADOS"""
        print("🎨 CANVAS SIMPLE: paintEvent ejecutándose...")

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Fondo blanco
        painter.fillRect(self.rect(), QBrush(QColor(255, 255, 255)))

        # ELEMENTOS INTERACTIVOS
        for elemento in self.elementos:
            elemento.dibujar(painter)

        # ELEMENTOS DE PRUEBA - GARANTIZADOS
        self._dibujar_elementos_prueba(painter)

        # Información de estado
        self._dibujar_info_estado(painter)

        print("🎨 CANVAS SIMPLE: paintEvent completado")
    
    def _dibujar_ventana_simple(self, painter):
        """Dibuja una ventana simple GARANTIZADA"""
        x, y, w, h = 50, 50, 300, 200
        
        # Marco exterior - MARRÓN
        painter.fillRect(QRect(x, y, w, 20), QBrush(QColor(139, 69, 19)))  # Superior
        painter.fillRect(QRect(x, y + h - 20, w, 20), QBrush(QColor(139, 69, 19)))  # Inferior
        painter.fillRect(QRect(x, y, 20, h), QBrush(QColor(139, 69, 19)))  # Izquierdo
        painter.fillRect(QRect(x + w - 20, y, 20, h), QBrush(QColor(139, 69, 19)))  # Derecho
        
        # Travesaño central - NARANJA
        painter.fillRect(QRect(x + w//2 - 10, y + 20, 20, h - 40), QBrush(QColor(255, 140, 0)))
        
        # Hojas - AZUL
        painter.fillRect(QRect(x + 20, y + 20, (w - 60)//2, h - 40), QBrush(QColor(70, 130, 180, 200)))
        painter.fillRect(QRect(x + 30 + (w - 60)//2, y + 20, (w - 60)//2, h - 40), QBrush(QColor(70, 130, 180, 200)))
        
        # Cristales - AZUL CLARO
        painter.fillRect(QRect(x + 25, y + 25, (w - 70)//2, h - 50), QBrush(QColor(173, 216, 230, 150)))
        painter.fillRect(QRect(x + 35 + (w - 70)//2, y + 25, (w - 70)//2, h - 50), QBrush(QColor(173, 216, 230, 150)))
        
        # Bordes negros
        painter.setPen(QPen(QColor(0, 0, 0), 2))
        painter.drawRect(QRect(x, y, w, h))
        painter.drawRect(QRect(x + w//2 - 10, y + 20, 20, h - 40))
        
        # Texto
        painter.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        painter.drawText(QRect(x, y, w, 20), Qt.AlignmentFlag.AlignCenter, "VENTANA SIMPLE")
    
    def _dibujar_elementos_prueba(self, painter):
        """Dibuja elementos de prueba GARANTIZADOS"""
        # Rectángulo ROJO
        painter.fillRect(QRect(400, 100, 100, 80), QBrush(QColor(255, 0, 0)))
        painter.setPen(QPen(QColor(0, 0, 0), 3))
        painter.drawRect(QRect(400, 100, 100, 80))
        
        # Texto en rectángulo
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        painter.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        painter.drawText(QRect(400, 100, 100, 80), Qt.AlignmentFlag.AlignCenter, "PRUEBA")
        
        # Círculo VERDE
        painter.setBrush(QBrush(QColor(0, 255, 0)))
        painter.drawEllipse(QRect(520, 100, 60, 60))
        
        # Texto de estado
        painter.setPen(QPen(QColor(0, 0, 0), 2))
        painter.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        painter.drawText(QRect(50, 300, 500, 50), Qt.AlignmentFlag.AlignLeft, "✅ CANVAS SIMPLE FUNCIONANDO CORRECTAMENTE")

    def _dibujar_info_estado(self, painter):
        """Dibuja información del estado actual"""
        painter.setPen(QPen(QColor(0, 0, 0), 1))
        painter.setFont(QFont("Arial", 12))

        info_text = f"Elementos: {len(self.elementos)}"
        if self.elemento_seleccionado_actual:
            info_text += f" | Seleccionado: {self.elemento_seleccionado_actual.nombre}"
        if self.elemento_hover:
            info_text += f" | Hover: {self.elemento_hover.nombre}"

        painter.drawText(QRect(50, 350, 600, 30), Qt.AlignmentFlag.AlignLeft, info_text)

    def mousePressEvent(self, event):
        """Maneja clics del mouse"""
        if event.button() == Qt.MouseButton.LeftButton:
            punto = event.position().toPoint()

            # Buscar elemento clickeado
            elemento_clickeado = None
            for elemento in reversed(self.elementos):  # Revisar desde arriba
                if elemento.rect.contains(punto):
                    elemento_clickeado = elemento
                    break

            # Actualizar selección
            if self.elemento_seleccionado_actual:
                self.elemento_seleccionado_actual.seleccionado = False

            if elemento_clickeado:
                elemento_clickeado.seleccionado = True
                self.elemento_seleccionado_actual = elemento_clickeado
                self.elemento_seleccionado.emit(elemento_clickeado)
                print(f"🎯 CLICK: Seleccionado {elemento_clickeado.nombre}")
            else:
                self.elemento_seleccionado_actual = None
                print("🎯 CLICK: Deseleccionado")

            self.update()

    def mouseMoveEvent(self, event):
        """Maneja movimiento del mouse"""
        punto = event.position().toPoint()

        # Buscar elemento bajo el mouse
        elemento_hover = None
        for elemento in reversed(self.elementos):
            if elemento.rect.contains(punto):
                elemento_hover = elemento
                break

        # Actualizar hover
        if self.elemento_hover != elemento_hover:
            if self.elemento_hover:
                self.elemento_hover.hover = False

            self.elemento_hover = elemento_hover

            if self.elemento_hover:
                self.elemento_hover.hover = True

            self.update()


class EditorArticuloSimple(QDialog):
    """Editor de artículos SIMPLE que SÍ FUNCIONA"""
    
    def __init__(self, parent=None, obra=None, obra_articulo=None):
        print("🚀 EDITOR SIMPLE: Iniciando constructor...")
        super().__init__(parent)
        
        self.obra = obra
        self.obra_articulo = obra_articulo
        
        self.setWindowTitle("Editor Simple de Artículos - FUNCIONAL")
        setup_maximized_dialog(self, "Editor Simple de Artículos")
        
        print("🚀 EDITOR SIMPLE: Configuración básica completada")
        
        self.init_ui()
        
        print("🚀 EDITOR SIMPLE: Constructor completado exitosamente")
    
    def init_ui(self):
        """Inicializa la interfaz SIMPLE"""
        print("🎨 EDITOR SIMPLE: Iniciando init_ui...")
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Header
        header = QLabel("🎯 EDITOR SIMPLE DE ARTÍCULOS - FUNCIONANDO")
        header.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                padding: 15px;
                border-radius: 8px;
                font-size: 18px;
                font-weight: bold;
            }
        """)
        layout.addWidget(header)
        
        # Layout principal con canvas y panel
        contenido_layout = QHBoxLayout()

        # Canvas SIMPLE
        print("🎨 EDITOR SIMPLE: Creando canvas simple...")
        self.canvas = CanvasSimple()
        self.canvas.elemento_seleccionado.connect(self.on_elemento_seleccionado)
        contenido_layout.addWidget(self.canvas, 2)  # 2/3 del espacio
        print("🎨 EDITOR SIMPLE: Canvas agregado al layout")

        # Panel de información
        self.panel_info = self._crear_panel_info()
        contenido_layout.addWidget(self.panel_info, 1)  # 1/3 del espacio

        layout.addLayout(contenido_layout)
        
        # Botones
        botones_layout = QHBoxLayout()
        
        btn_cerrar = QPushButton("Cerrar")
        btn_cerrar.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        btn_cerrar.clicked.connect(self.reject)
        
        botones_layout.addStretch()
        botones_layout.addWidget(btn_cerrar)
        
        layout.addLayout(botones_layout)
        
        print("🎨 EDITOR SIMPLE: init_ui completado")

    def _crear_panel_info(self):
        """Crea el panel de información del elemento seleccionado"""
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 8px;
            }
        """)
        panel.setMaximumWidth(300)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # Título
        titulo = QLabel("🔧 Elemento Seleccionado")
        titulo.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                background-color: #e3f2fd;
                padding: 10px;
                border-radius: 5px;
            }
        """)
        layout.addWidget(titulo)

        # Información del elemento
        self.info_elemento = QLabel("Selecciona un elemento en el canvas")
        self.info_elemento.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #666;
                background-color: white;
                padding: 15px;
                border-radius: 5px;
                border: 1px solid #ddd;
            }
        """)
        self.info_elemento.setWordWrap(True)
        layout.addWidget(self.info_elemento)

        # Instrucciones
        instrucciones = QLabel("""
        <b>Instrucciones:</b><br/>
        • Haz clic en cualquier elemento para seleccionarlo<br/>
        • Mueve el mouse sobre los elementos para resaltarlos<br/>
        • Los elementos seleccionados se muestran con borde rojo<br/>
        • Los elementos en hover se muestran más claros
        """)
        instrucciones.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #555;
                background-color: #fff3e0;
                padding: 10px;
                border-radius: 5px;
                border: 1px solid #ffcc02;
            }
        """)
        instrucciones.setWordWrap(True)
        layout.addWidget(instrucciones)

        layout.addStretch()
        return panel

    def on_elemento_seleccionado(self, elemento):
        """Maneja la selección de un elemento"""
        if elemento:
            info_text = f"""
            <b>Tipo:</b> {elemento.tipo}<br/>
            <b>Nombre:</b> {elemento.nombre}<br/>
            <b>Posición:</b> ({elemento.rect.x()}, {elemento.rect.y()})<br/>
            <b>Tamaño:</b> {elemento.rect.width()} x {elemento.rect.height()}<br/>
            <b>Color:</b> {elemento.color.name()}
            """
            self.info_elemento.setText(info_text)
            print(f"📋 INFO: Elemento seleccionado - {elemento.nombre}")
        else:
            self.info_elemento.setText("Selecciona un elemento en el canvas")
            print("📋 INFO: Ningún elemento seleccionado")
