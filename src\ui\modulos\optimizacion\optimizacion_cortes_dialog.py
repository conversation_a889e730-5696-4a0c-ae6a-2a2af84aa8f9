"""
Diálogo para optimización visual de cortes de perfiles.
Muestra gráficamente cómo optimizar el corte de barras.
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QFormLayout, QDialogButtonBox, QComboBox, QDoubleSpinBox, QSpinBox,
    QMessageBox, QGroupBox, QTextEdit, QTableWidget, QTableWidgetItem,
    QHeaderView, QTabWidget, QWidget, QScrollArea, QProgressBar,
    QCheckBox, QFileDialog
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont, QPainter, QPen, QBrush, QColor, QPixmap
from PyQt6.QtWidgets import QGraphicsView, QGraphicsScene, QGraphicsRectItem, QGraphicsTextItem

from models.base import get_db
from models.obra import Obra
from models.articulo import ObraArticulo
from .optimizador_cortes import OptimizadorCortes
from ui.utils.window_utils import apply_smart_geometry
import os
from datetime import datetime

# Verificar disponibilidad de ReportLab para PDFs
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.units import cm, mm
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False


class OptimizacionCortesDialog(QDialog):
    """Diálogo para optimización visual de cortes."""
    
    def __init__(self, parent=None, obra=None):
        super().__init__(parent)
        self.obra = obra
        self.resultados_optimizacion = None
        self.materiales_manuales = []  # Lista de materiales agregados manualmente

        self.setWindowTitle("Optimización de Cortes - PRO-2000")
        self.setModal(True)

        # Aplicar geometría inteligente
        apply_smart_geometry(self, "Optimización de Cortes")

        self._inicializar_ui()

        if obra:
            self._cargar_datos_obra()
        else:
            self._cargar_obras_disponibles()
    
    def _inicializar_ui(self):
        """Configura la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)
        
        # Título
        titulo = QLabel("Optimización de Cortes de Perfiles")
        titulo.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        titulo.setAlignment(Qt.AlignmentFlag.AlignCenter)
        titulo.setStyleSheet("color: #2c3e50; padding: 10px;")
        layout_principal.addWidget(titulo)
        
        # Crear pestañas
        self.tabs = QTabWidget()

        # Pestaña 1: Selección de Obra y Materiales
        self._crear_tab_seleccion()

        # Pestaña 2: Configuración
        self._crear_tab_configuracion()

        # Pestaña 3: Resultados
        self._crear_tab_resultados()

        # Pestaña 4: Visualización
        self._crear_tab_visualizacion()
        
        layout_principal.addWidget(self.tabs)
        
        # Botones de acción
        self._crear_botones_accion(layout_principal)

    def _crear_tab_seleccion(self):
        """Crea la pestaña de selección de obra y materiales."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Selección de obra
        grupo_obra = QGroupBox("Selección de Obra")
        layout_obra = QVBoxLayout(grupo_obra)

        # Combo de obras
        layout_combo = QHBoxLayout()
        layout_combo.addWidget(QLabel("Obra:"))

        self.combo_obras = QComboBox()
        self.combo_obras.currentIndexChanged.connect(self._obra_seleccionada)
        layout_combo.addWidget(self.combo_obras)

        self.btn_actualizar_obras = QPushButton("🔄 Actualizar")
        self.btn_actualizar_obras.clicked.connect(self._cargar_obras_disponibles)
        layout_combo.addWidget(self.btn_actualizar_obras)

        layout_obra.addLayout(layout_combo)

        # Información de la obra seleccionada
        self.label_info_obra_sel = QLabel("Seleccione una obra para ver información")
        self.label_info_obra_sel.setWordWrap(True)
        layout_obra.addWidget(self.label_info_obra_sel)

        layout.addWidget(grupo_obra)

        # Materiales adicionales
        grupo_materiales = QGroupBox("Materiales Adicionales (Opcional)")
        layout_materiales = QVBoxLayout(grupo_materiales)

        # Controles para agregar material
        layout_agregar = QHBoxLayout()

        layout_agregar.addWidget(QLabel("Perfil:"))
        self.combo_perfiles = QComboBox()
        self._cargar_perfiles_disponibles()
        layout_agregar.addWidget(self.combo_perfiles)

        layout_agregar.addWidget(QLabel("Longitud:"))
        self.campo_longitud_manual = QSpinBox()
        self.campo_longitud_manual.setRange(50, 6000)
        self.campo_longitud_manual.setValue(1000)
        self.campo_longitud_manual.setSuffix(" mm")
        layout_agregar.addWidget(self.campo_longitud_manual)

        layout_agregar.addWidget(QLabel("Cantidad:"))
        self.campo_cantidad_manual = QSpinBox()
        self.campo_cantidad_manual.setRange(1, 100)
        self.campo_cantidad_manual.setValue(1)
        layout_agregar.addWidget(self.campo_cantidad_manual)

        self.btn_agregar_material = QPushButton("➕ Agregar")
        self.btn_agregar_material.clicked.connect(self._agregar_material_manual)
        layout_agregar.addWidget(self.btn_agregar_material)

        layout_materiales.addLayout(layout_agregar)

        # Tabla de materiales agregados
        self.tabla_materiales_manuales = QTableWidget()
        self.tabla_materiales_manuales.setColumnCount(4)
        self.tabla_materiales_manuales.setHorizontalHeaderLabels([
            "Perfil", "Longitud", "Cantidad", "Acciones"
        ])

        # Configurar tabla
        header = self.tabla_materiales_manuales.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)

        layout_materiales.addWidget(self.tabla_materiales_manuales)

        layout.addWidget(grupo_materiales)

        layout.addStretch()

        self.tabs.addTab(tab, "🎯 Selección")

    def _crear_tab_configuracion(self):
        """Crea la pestaña de configuración."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Información de la obra (se actualizará dinámicamente)
        self.grupo_obra_config = QGroupBox("Información de la Obra")
        layout_obra = QFormLayout(self.grupo_obra_config)

        self.label_info_obra = QLabel("Seleccione una obra en la pestaña de Selección")
        layout_obra.addRow("Información:", self.label_info_obra)

        layout.addWidget(self.grupo_obra_config)
        
        # Configuración de optimización
        grupo_config = QGroupBox("Configuración de Optimización")
        layout_config = QFormLayout(grupo_config)
        
        # Longitud de barra estándar
        self.campo_longitud_barra = QDoubleSpinBox()
        self.campo_longitud_barra.setRange(1000, 12000)
        self.campo_longitud_barra.setValue(6000)
        self.campo_longitud_barra.setSuffix(" mm")
        self.campo_longitud_barra.setToolTip("Longitud estándar de las barras de perfil")
        layout_config.addRow("Longitud de barra:", self.campo_longitud_barra)
        
        # Desperdicio mínimo
        self.campo_desperdicio_min = QDoubleSpinBox()
        self.campo_desperdicio_min.setRange(0, 500)
        self.campo_desperdicio_min.setValue(50)
        self.campo_desperdicio_min.setSuffix(" mm")
        self.campo_desperdicio_min.setToolTip("Longitud mínima de desperdicio aprovechable")
        layout_config.addRow("Desperdicio mínimo:", self.campo_desperdicio_min)
        
        # Grosor de corte
        self.campo_grosor_corte = QDoubleSpinBox()
        self.campo_grosor_corte.setRange(0, 10)
        self.campo_grosor_corte.setValue(3)
        self.campo_grosor_corte.setSuffix(" mm")
        self.campo_grosor_corte.setToolTip("Grosor de la sierra (pérdida por corte)")
        layout_config.addRow("Grosor de corte:", self.campo_grosor_corte)
        
        # Algoritmo de optimización
        self.combo_algoritmo = QComboBox()
        self.combo_algoritmo.addItems([
            "First Fit Decreasing (FFD)",
            "Best Fit Decreasing (BFD)",
            "Next Fit Decreasing (NFD)"
        ])
        self.combo_algoritmo.setCurrentIndex(1)  # BFD por defecto
        layout_config.addRow("Algoritmo:", self.combo_algoritmo)
        
        # Opciones avanzadas
        self.check_agrupar_perfiles = QCheckBox("Agrupar por tipo de perfil")
        self.check_agrupar_perfiles.setChecked(True)
        layout_config.addRow("", self.check_agrupar_perfiles)
        
        self.check_considerar_angulos = QCheckBox("Considerar ángulos de corte")
        self.check_considerar_angulos.setChecked(True)
        layout_config.addRow("", self.check_considerar_angulos)
        
        layout.addWidget(grupo_config)
        
        # Botón de optimizar
        self.btn_optimizar = QPushButton("🔧 Ejecutar Optimización")
        self.btn_optimizar.clicked.connect(self._ejecutar_optimizacion)
        self.btn_optimizar.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        layout.addWidget(self.btn_optimizar)
        
        # Barra de progreso
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        layout.addStretch()
        
        self.tabs.addTab(tab, "⚙️ Configuración")
    
    def _crear_tab_resultados(self):
        """Crea la pestaña de resultados."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Resumen de resultados
        grupo_resumen = QGroupBox("Resumen de Optimización")
        layout_resumen = QFormLayout(grupo_resumen)
        
        self.label_barras_necesarias = QLabel("No calculado")
        layout_resumen.addRow("Barras necesarias:", self.label_barras_necesarias)
        
        self.label_eficiencia = QLabel("No calculado")
        layout_resumen.addRow("Eficiencia:", self.label_eficiencia)
        
        self.label_desperdicio_total = QLabel("No calculado")
        layout_resumen.addRow("Desperdicio total:", self.label_desperdicio_total)
        
        self.label_coste_estimado = QLabel("No calculado")
        layout_resumen.addRow("Coste estimado:", self.label_coste_estimado)
        
        layout.addWidget(grupo_resumen)
        
        # Tabla de resultados detallados
        grupo_detalle = QGroupBox("Detalle de Cortes por Barra")
        layout_detalle = QVBoxLayout(grupo_detalle)
        
        self.tabla_resultados = QTableWidget()
        self.tabla_resultados.setColumnCount(5)
        self.tabla_resultados.setHorizontalHeaderLabels([
            "Barra", "Perfil", "Cortes", "Aprovechamiento", "Desperdicio"
        ])
        
        # Configurar tabla
        header = self.tabla_resultados.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        
        layout_detalle.addWidget(self.tabla_resultados)
        layout.addWidget(grupo_detalle)
        
        self.tabs.addTab(tab, "📊 Resultados")
    
    def _crear_tab_visualizacion(self):
        """Crea la pestaña de visualización gráfica."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Controles de visualización
        grupo_controles = QGroupBox("Controles de Visualización")
        layout_controles = QHBoxLayout(grupo_controles)
        
        self.combo_barra_visualizar = QComboBox()
        self.combo_barra_visualizar.currentTextChanged.connect(self._actualizar_visualizacion)
        layout_controles.addWidget(QLabel("Barra:"))
        layout_controles.addWidget(self.combo_barra_visualizar)
        
        self.check_mostrar_medidas = QCheckBox("Mostrar medidas")
        self.check_mostrar_medidas.setChecked(True)
        self.check_mostrar_medidas.toggled.connect(self._actualizar_visualizacion)
        layout_controles.addWidget(self.check_mostrar_medidas)
        
        self.check_mostrar_colores = QCheckBox("Colores por perfil")
        self.check_mostrar_colores.setChecked(True)
        self.check_mostrar_colores.toggled.connect(self._actualizar_visualizacion)
        layout_controles.addWidget(self.check_mostrar_colores)
        
        layout_controles.addStretch()
        
        layout.addWidget(grupo_controles)
        
        # Área de visualización
        self.scroll_visualizacion = QScrollArea()
        self.scroll_visualizacion.setWidgetResizable(True)
        self.scroll_visualizacion.setMinimumHeight(400)
        
        self.widget_visualizacion = QWidget()
        self.widget_visualizacion.setMinimumSize(800, 200)
        self.scroll_visualizacion.setWidget(self.widget_visualizacion)
        
        layout.addWidget(self.scroll_visualizacion)
        
        # Leyenda
        grupo_leyenda = QGroupBox("Leyenda")
        layout_leyenda = QVBoxLayout(grupo_leyenda)
        
        self.label_leyenda = QLabel("Ejecute la optimización para ver la leyenda")
        self.label_leyenda.setWordWrap(True)
        layout_leyenda.addWidget(self.label_leyenda)
        
        layout.addWidget(grupo_leyenda)
        
        self.tabs.addTab(tab, "👁️ Visualización")
    
    def _crear_botones_accion(self, layout_principal):
        """Crea los botones de acción."""
        layout_botones = QHBoxLayout()
        
        # Botón de exportar
        self.btn_exportar = QPushButton("📄 Exportar Resultados")
        self.btn_exportar.clicked.connect(self._exportar_resultados)
        self.btn_exportar.setEnabled(False)
        layout_botones.addWidget(self.btn_exportar)
        
        # Botón de imprimir
        self.btn_imprimir = QPushButton("🖨️ Imprimir")
        self.btn_imprimir.clicked.connect(self._imprimir_resultados)
        self.btn_imprimir.setEnabled(False)
        layout_botones.addWidget(self.btn_imprimir)
        
        layout_botones.addStretch()
        
        # Botones estándar
        botones = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        botones.button(QDialogButtonBox.StandardButton.Close).setText("❌ Cerrar")
        botones.rejected.connect(self.reject)
        
        layout_botones.addWidget(botones)
        layout_principal.addLayout(layout_botones)

    def _cargar_obras_disponibles(self):
        """Carga las obras disponibles en el combo."""
        db = next(get_db())
        try:
            obras = db.query(Obra).order_by(Obra.fecha_inicio.desc()).all()

            self.combo_obras.clear()
            self.combo_obras.addItem("-- Seleccionar obra --", None)

            for obra in obras:
                texto = f"{obra.codigo} - {obra.nombre}"
                if obra.estado:
                    texto += f" ({obra.estado})"
                self.combo_obras.addItem(texto, obra)

        except Exception as e:
            QMessageBox.warning(self, "Error", f"Error al cargar obras: {str(e)}")
        finally:
            db.close()

    def _cargar_perfiles_disponibles(self):
        """Carga los perfiles disponibles."""
        from models.perfil import Perfil

        db = next(get_db())
        try:
            perfiles = db.query(Perfil).all()

            self.combo_perfiles.clear()
            for perfil in perfiles:
                self.combo_perfiles.addItem(f"{perfil.codigo} - {perfil.descripcion}", perfil)

        except Exception as e:
            print(f"Error al cargar perfiles: {str(e)}")
        finally:
            db.close()

    def _obra_seleccionada(self):
        """Se ejecuta cuando se selecciona una obra."""
        index = self.combo_obras.currentIndex()
        if index > 0:  # Ignorar el primer elemento "-- Seleccionar obra --"
            obra_data = self.combo_obras.currentData()
            if obra_data:
                self.obra = obra_data
                print(f"DEBUG: Obra seleccionada: {self.obra.codigo} - {getattr(self.obra, 'nombre', 'Sin nombre')}")
                self._mostrar_info_obra_seleccionada()
                # IMPORTANTE: También actualizar la información de configuración
                self._cargar_datos_obra()
            else:
                self.obra = None
                self.label_info_obra_sel.setText("Seleccione una obra para ver información")
                self.label_info_obra.setText("Seleccione una obra para ver información")
        else:
            self.obra = None
            self.label_info_obra_sel.setText("Seleccione una obra para ver información")
            self.label_info_obra.setText("Seleccione una obra para ver información")

    def _mostrar_info_obra_seleccionada(self):
        """Muestra información de la obra seleccionada."""
        if not self.obra:
            return

        db = next(get_db())
        try:
            # Obtener artículos de la obra
            obra_articulos = db.query(ObraArticulo).filter(
                ObraArticulo.obra_id == self.obra.id
            ).all()

            total_articulos = len(obra_articulos)

            info_texto = f"""
            <b>Código:</b> {self.obra.codigo}<br>
            <b>Nombre:</b> {self.obra.nombre}<br>
            <b>Estado:</b> {self.obra.estado or 'Pendiente'}<br>
            <b>Artículos:</b> {total_articulos}<br>
            <b>Presupuesto:</b> {self.obra.presupuesto or 0:.2f} €
            """

            # Cargar cliente de forma segura
            try:
                if hasattr(self.obra, 'cliente_id') and self.obra.cliente_id:
                    from models.cliente import Cliente
                    cliente = db.query(Cliente).filter(Cliente.id == self.obra.cliente_id).first()
                    if cliente:
                        info_texto += f"<br><b>Cliente:</b> {cliente.nombre}"
                    else:
                        info_texto += f"<br><b>Cliente:</b> Sin cliente"
                else:
                    info_texto += f"<br><b>Cliente:</b> Sin cliente"
            except Exception:
                info_texto += f"<br><b>Cliente:</b> Sin cliente"

            self.label_info_obra_sel.setText(info_texto)

        except Exception as e:
            self.label_info_obra_sel.setText(f"Error al cargar información: {str(e)}")
        finally:
            db.close()

    def _agregar_material_manual(self):
        """Agrega un material manualmente a la lista."""
        perfil_data = self.combo_perfiles.currentData()
        if not perfil_data:
            QMessageBox.warning(self, "Error", "Seleccione un perfil")
            return

        longitud = self.campo_longitud_manual.value()
        cantidad = self.campo_cantidad_manual.value()

        material = {
            'perfil': perfil_data,
            'longitud': longitud,
            'cantidad': cantidad
        }

        self.materiales_manuales.append(material)
        self._actualizar_tabla_materiales_manuales()

    def _actualizar_tabla_materiales_manuales(self):
        """Actualiza la tabla de materiales manuales."""
        self.tabla_materiales_manuales.setRowCount(len(self.materiales_manuales))

        for i, material in enumerate(self.materiales_manuales):
            # Perfil
            perfil_texto = f"{material['perfil'].codigo} - {material['perfil'].descripcion}"
            self.tabla_materiales_manuales.setItem(i, 0, QTableWidgetItem(perfil_texto))

            # Longitud
            self.tabla_materiales_manuales.setItem(i, 1, QTableWidgetItem(f"{material['longitud']} mm"))

            # Cantidad
            self.tabla_materiales_manuales.setItem(i, 2, QTableWidgetItem(str(material['cantidad'])))

            # Botón eliminar
            btn_eliminar = QPushButton("🗑️")
            btn_eliminar.clicked.connect(lambda checked, idx=i: self._eliminar_material_manual(idx))
            btn_eliminar.setMaximumWidth(40)
            self.tabla_materiales_manuales.setCellWidget(i, 3, btn_eliminar)

    def _eliminar_material_manual(self, indice):
        """Elimina un material manual de la lista."""
        if 0 <= indice < len(self.materiales_manuales):
            del self.materiales_manuales[indice]
            self._actualizar_tabla_materiales_manuales()

    def _cargar_datos_obra(self):
        """Carga los datos de la obra."""
        if not self.obra:
            # Actualizar título del grupo
            self.grupo_obra_config.setTitle("Información de la Obra")
            self.label_info_obra.setText("Seleccione una obra en la pestaña de Selección")
            self.btn_optimizar.setEnabled(False)
            return

        # Actualizar título del grupo con la obra seleccionada
        self.grupo_obra_config.setTitle(f"Obra: {self.obra.codigo} - {self.obra.nombre}")

        db = next(get_db())
        try:
            # Obtener artículos de la obra
            obra_articulos = db.query(ObraArticulo).filter(
                ObraArticulo.obra_id == self.obra.id
            ).all()

            total_articulos = len(obra_articulos)
            total_perfiles = 0

            for obra_articulo in obra_articulos:
                if obra_articulo.articulo and obra_articulo.articulo.perfiles:
                    total_perfiles += len(obra_articulo.articulo.perfiles)

            # Obtener cliente de forma segura
            cliente_nombre = "No especificado"
            try:
                if hasattr(self.obra, 'cliente_id') and self.obra.cliente_id:
                    from models.cliente import Cliente
                    cliente = db.query(Cliente).filter(Cliente.id == self.obra.cliente_id).first()
                    if cliente:
                        cliente_nombre = cliente.nombre
            except Exception:
                pass

            info_texto = f"""
            <b>Código:</b> {self.obra.codigo}<br>
            <b>Nombre:</b> {self.obra.nombre}<br>
            <b>Artículos en obra:</b> {total_articulos}<br>
            <b>Tipos de perfil:</b> {total_perfiles}<br>
            <b>Cliente:</b> {cliente_nombre}<br>
            <b>Estado:</b> {self.obra.estado or 'Pendiente'}
            """

            self.label_info_obra.setText(info_texto)

            # Habilitar botón de optimizar si hay datos
            self.btn_optimizar.setEnabled(total_perfiles > 0)

            if total_perfiles == 0:
                self.label_info_obra.setText(
                    self.label_info_obra.text() +
                    "<br><span style='color: red;'><b>⚠️ No hay perfiles para optimizar</b></span>"
                )

        except Exception as e:
            self.label_info_obra.setText(f"Error al cargar datos: {str(e)}")
            self.btn_optimizar.setEnabled(False)
        finally:
            db.close()

    def _ejecutar_optimizacion(self):
        """Ejecuta la optimización de cortes."""
        if not self.obra:
            QMessageBox.warning(self, "Error", "No hay obra seleccionada.")
            return

        try:
            # Mostrar progreso
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # Indeterminado
            self.btn_optimizar.setEnabled(False)

            # Configuración
            config = {
                'longitud_barra': self.campo_longitud_barra.value(),
                'desperdicio_minimo': self.campo_desperdicio_min.value(),
                'grosor_corte': self.campo_grosor_corte.value(),
                'algoritmo': self.combo_algoritmo.currentText(),
                'agrupar_perfiles': self.check_agrupar_perfiles.isChecked(),
                'considerar_angulos': self.check_considerar_angulos.isChecked(),
                'materiales_manuales': self.materiales_manuales
            }

            # Ejecutar optimización
            optimizador = OptimizadorCortes(self.obra, config)
            self.resultados_optimizacion = optimizador.optimizar()

            # Mostrar resultados
            self._mostrar_resultados()

            # Habilitar botones
            self.btn_exportar.setEnabled(True)
            self.btn_imprimir.setEnabled(True)

            # Cambiar a pestaña de resultados
            self.tabs.setCurrentIndex(1)

            QMessageBox.information(
                self,
                "✅ Optimización Completada",
                f"Optimización ejecutada correctamente.\n\n"
                f"Barras necesarias: {len(self.resultados_optimizacion['barras'])}\n"
                f"Eficiencia: {self.resultados_optimizacion['eficiencia']:.1f}%"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ Error",
                f"Error durante la optimización: {str(e)}"
            )
        finally:
            self.progress_bar.setVisible(False)
            self.btn_optimizar.setEnabled(True)

    def _simular_optimizacion(self):
        """Simula una optimización básica para demostración."""
        import random

        # Generar datos de ejemplo
        longitud_barra = self.campo_longitud_barra.value()

        # Simular cortes necesarios
        cortes_necesarios = []
        for i in range(random.randint(15, 25)):
            cortes_necesarios.append({
                'longitud': random.randint(200, 1500),
                'perfil_codigo': f'PERFIL-{random.randint(1, 5):02d}',
                'cantidad': 1
            })

        # Algoritmo simple First Fit Decreasing
        cortes_necesarios.sort(key=lambda x: x['longitud'], reverse=True)

        barras = []
        grosor_corte = self.campo_grosor_corte.value()

        for corte in cortes_necesarios:
            # Buscar barra que pueda contener el corte
            barra_encontrada = False

            for barra in barras:
                espacio_usado = sum(c['longitud'] for c in barra['cortes'])
                espacio_cortes = len(barra['cortes']) * grosor_corte
                espacio_disponible = longitud_barra - espacio_usado - espacio_cortes

                if espacio_disponible >= corte['longitud'] + grosor_corte:
                    barra['cortes'].append(corte)
                    barra['longitud_usada'] = espacio_usado + corte['longitud'] + grosor_corte
                    barra_encontrada = True
                    break

            # Si no se encontró barra, crear nueva
            if not barra_encontrada:
                nueva_barra = {
                    'cortes': [corte],
                    'longitud_usada': corte['longitud']
                }
                barras.append(nueva_barra)

        # Calcular estadísticas
        longitud_total_usada = sum(barra['longitud_usada'] for barra in barras)
        longitud_total_disponible = len(barras) * longitud_barra
        eficiencia = (longitud_total_usada / longitud_total_disponible) * 100
        desperdicio_total = longitud_total_disponible - longitud_total_usada

        self.resultados_optimizacion = {
            'barras': barras,
            'eficiencia': eficiencia,
            'desperdicio_total': desperdicio_total,
            'coste_estimado': len(barras) * 25.0  # 25€ por barra estimado
        }

        # Mostrar resultados
        self._mostrar_resultados()

    def _mostrar_resultados(self):
        """Muestra los resultados de la optimización."""
        if not self.resultados_optimizacion:
            return

        resultados = self.resultados_optimizacion

        # Actualizar resumen
        self.label_barras_necesarias.setText(f"{len(resultados['barras'])} barras")
        self.label_eficiencia.setText(f"{resultados['eficiencia']:.1f}%")
        self.label_desperdicio_total.setText(f"{resultados['desperdicio_total']:.0f} mm")
        self.label_coste_estimado.setText(f"{resultados['coste_estimado']:.2f} €")

        # Llenar tabla de resultados
        self.tabla_resultados.setRowCount(len(resultados['barras']))

        for i, barra in enumerate(resultados['barras']):
            # Número de barra
            self.tabla_resultados.setItem(i, 0, QTableWidgetItem(f"Barra {i+1}"))

            # Tipo de perfil
            if barra['cortes']:
                perfil_tipo = barra['cortes'][0].get('perfil_codigo', 'Mixto')
            else:
                perfil_tipo = "Vacía"
            self.tabla_resultados.setItem(i, 1, QTableWidgetItem(perfil_tipo))

            # Cortes
            cortes_texto = f"{len(barra['cortes'])} cortes"
            if barra['cortes']:
                medidas = [f"{corte['longitud']:.0f}mm" for corte in barra['cortes']]
                cortes_texto += f": {', '.join(medidas[:3])}"
                if len(medidas) > 3:
                    cortes_texto += "..."
            self.tabla_resultados.setItem(i, 2, QTableWidgetItem(cortes_texto))

            # Aprovechamiento
            aprovechamiento = ((barra['longitud_usada'] / self.campo_longitud_barra.value()) * 100)
            self.tabla_resultados.setItem(i, 3, QTableWidgetItem(f"{aprovechamiento:.1f}%"))

            # Desperdicio
            desperdicio = self.campo_longitud_barra.value() - barra['longitud_usada']
            self.tabla_resultados.setItem(i, 4, QTableWidgetItem(f"{desperdicio:.0f} mm"))

        # Actualizar combo de visualización
        self.combo_barra_visualizar.clear()
        for i in range(len(resultados['barras'])):
            self.combo_barra_visualizar.addItem(f"Barra {i+1}")

        # Actualizar visualización
        self._actualizar_visualizacion()

    def _actualizar_visualizacion(self):
        """Actualiza la visualización gráfica."""
        if not self.resultados_optimizacion:
            return

        barra_index = self.combo_barra_visualizar.currentIndex()
        if barra_index < 0 or barra_index >= len(self.resultados_optimizacion['barras']):
            return

        barra = self.resultados_optimizacion['barras'][barra_index]

        # Crear imagen de la barra
        self._dibujar_barra(barra)

    def _dibujar_barra(self, barra):
        """Dibuja la visualización de una barra."""
        # Dimensiones del dibujo
        ancho_total = 800
        alto_barra = 80
        margen = 50

        # Crear pixmap
        pixmap = QPixmap(ancho_total, alto_barra + 2 * margen)
        pixmap.fill(QColor(255, 255, 255))

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Configurar colores
        colores_perfil = [
            QColor(52, 152, 219),   # Azul
            QColor(46, 204, 113),   # Verde
            QColor(231, 76, 60),    # Rojo
            QColor(241, 196, 15),   # Amarillo
            QColor(155, 89, 182),   # Púrpura
            QColor(230, 126, 34),   # Naranja
        ]

        # Dibujar barra completa (contorno)
        longitud_barra = self.campo_longitud_barra.value()
        painter.setPen(QPen(QColor(0, 0, 0), 2))
        painter.drawRect(margen, margen, ancho_total - 2 * margen, alto_barra)

        # Dibujar cortes
        x_actual = margen
        escala = (ancho_total - 2 * margen) / longitud_barra

        for i, corte in enumerate(barra['cortes']):
            ancho_corte = corte['longitud'] * escala
            color = colores_perfil[i % len(colores_perfil)]

            # Dibujar rectángulo del corte
            painter.fillRect(
                int(x_actual), margen,
                int(ancho_corte), alto_barra,
                QBrush(color)
            )

            # Dibujar borde
            painter.setPen(QPen(QColor(0, 0, 0), 1))
            painter.drawRect(int(x_actual), margen, int(ancho_corte), alto_barra)

            # Mostrar medidas si está habilitado
            if self.check_mostrar_medidas.isChecked():
                painter.setPen(QPen(QColor(255, 255, 255), 2))
                texto = f"{corte['longitud']:.0f}"
                painter.drawText(
                    int(x_actual + ancho_corte/2 - 15), margen + alto_barra/2 + 5,
                    texto
                )

            x_actual += ancho_corte + (self.campo_grosor_corte.value() * escala)

        # Dibujar desperdicio
        if x_actual < ancho_total - margen:
            painter.fillRect(
                int(x_actual), margen,
                int(ancho_total - margen - x_actual), alto_barra,
                QBrush(QColor(200, 200, 200))
            )

            # Texto de desperdicio
            desperdicio = longitud_barra - barra['longitud_usada']
            painter.setPen(QPen(QColor(100, 100, 100), 1))
            painter.drawText(
                int(x_actual + 10), margen + alto_barra/2 + 5,
                f"Desperdicio: {desperdicio:.0f}mm"
            )

        painter.end()

        # Mostrar en el widget
        label_imagen = QLabel()
        label_imagen.setPixmap(pixmap)

        # Limpiar layout anterior
        layout = self.widget_visualizacion.layout()
        if layout:
            while layout.count():
                child = layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
        else:
            layout = QVBoxLayout(self.widget_visualizacion)

        layout.addWidget(label_imagen)

        # Actualizar leyenda
        self._actualizar_leyenda(barra)

    def _actualizar_leyenda(self, barra):
        """Actualiza la leyenda de colores."""
        leyenda_texto = "<b>Leyenda de colores:</b><br>"

        colores_html = [
            "#3498db", "#2ecc71", "#e74c3c", "#f1c40f", "#9b59b6", "#e67e22"
        ]

        perfiles_mostrados = set()

        for i, corte in enumerate(barra['cortes']):
            perfil_codigo = corte.get('perfil_codigo', f'Perfil {i+1}')
            if perfil_codigo not in perfiles_mostrados:
                color = colores_html[i % len(colores_html)]
                leyenda_texto += f'<span style="color: {color};">■</span> {perfil_codigo}<br>'
                perfiles_mostrados.add(perfil_codigo)

        leyenda_texto += '<span style="color: #c8c8c8;">■</span> Desperdicio'

        self.label_leyenda.setText(leyenda_texto)

    def _exportar_resultados(self):
        """Exporta los resultados a un archivo (TXT o PDF)."""
        if not self.resultados_optimizacion:
            QMessageBox.warning(self, "Error", "No hay resultados para exportar.")
            return

        # Determinar filtros de archivo según disponibilidad
        if REPORTLAB_AVAILABLE:
            filtros = "PDF (*.pdf);;Archivos de texto (*.txt);;Todos los archivos (*)"
            extension_defecto = ".pdf"
        else:
            filtros = "Archivos de texto (*.txt);;Todos los archivos (*)"
            extension_defecto = ".txt"

        # Obtener nombre de archivo
        fecha = datetime.now()
        nombre_sugerido = f"Optimizacion_Cortes_{self.obra.codigo}_{fecha.strftime('%Y%m%d_%H%M')}{extension_defecto}"

        archivo, filtro_seleccionado = QFileDialog.getSaveFileName(
            self,
            "Exportar Resultados de Optimización",
            nombre_sugerido,
            filtros
        )

        if not archivo:
            return

        try:
            # Determinar tipo de archivo por extensión
            if archivo.lower().endswith('.pdf') and REPORTLAB_AVAILABLE:
                self._exportar_pdf(archivo)
            else:
                self._exportar_txt(archivo)

            QMessageBox.information(
                self,
                "✅ Exportación Exitosa",
                f"Resultados exportados correctamente a:\n{archivo}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ Error",
                f"Error al exportar resultados: {str(e)}"
            )

    def _exportar_txt(self, archivo):
        """Exporta los resultados a un archivo de texto."""
        fecha = datetime.now()

        with open(archivo, 'w', encoding='utf-8') as f:
            f.write("OPTIMIZACIÓN DE CORTES - PRO-2000\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Obra: {self.obra.codigo} - {self.obra.nombre}\n")
            f.write(f"Fecha: {fecha.strftime('%d/%m/%Y %H:%M')}\n")
            f.write(f"Algoritmo: {self.combo_algoritmo.currentText()}\n")
            f.write(f"Longitud de barra: {self.campo_longitud_barra.value():.0f} mm\n\n")

            # Resumen
            f.write("RESUMEN:\n")
            f.write(f"Barras necesarias: {len(self.resultados_optimizacion['barras'])}\n")
            f.write(f"Eficiencia: {self.resultados_optimizacion['eficiencia']:.1f}%\n")
            f.write(f"Desperdicio total: {self.resultados_optimizacion['desperdicio_total']:.0f} mm\n")
            f.write(f"Coste estimado: {self.resultados_optimizacion['coste_estimado']:.2f} €\n\n")

            # Detalle por barra
            f.write("DETALLE POR BARRA:\n")
            f.write("-" * 50 + "\n")

            for i, barra in enumerate(self.resultados_optimizacion['barras']):
                f.write(f"\nBarra {i+1}:\n")
                f.write(f"  Cortes: {len(barra['cortes'])}\n")

                for j, corte in enumerate(barra['cortes']):
                    perfil = corte.get('perfil_codigo', 'N/A')
                    descripcion = corte.get('descripcion', '')
                    f.write(f"    {j+1}. {corte['longitud']:.0f}mm - {perfil}")
                    if descripcion:
                        f.write(f" ({descripcion})")
                    f.write("\n")

                desperdicio = self.campo_longitud_barra.value() - barra['longitud_usada']
                eficiencia_barra = (barra['longitud_usada'] / self.campo_longitud_barra.value()) * 100
                f.write(f"  Longitud usada: {barra['longitud_usada']:.0f}mm\n")
                f.write(f"  Desperdicio: {desperdicio:.0f}mm\n")
                f.write(f"  Eficiencia: {eficiencia_barra:.1f}%\n")

    def _exportar_pdf(self, archivo):
        """Exporta los resultados a un archivo PDF."""
        if not REPORTLAB_AVAILABLE:
            raise Exception("ReportLab no está disponible para generar PDFs")

        fecha = datetime.now()

        # Crear documento PDF
        doc = SimpleDocTemplate(archivo, pagesize=A4)
        story = []
        styles = getSampleStyleSheet()

        # Título
        titulo = Paragraph("OPTIMIZACIÓN DE CORTES - PRO-2000", styles['Title'])
        story.append(titulo)
        story.append(Spacer(1, 12))

        # Información de la obra
        info_obra = f"""
        <b>Obra:</b> {self.obra.codigo} - {self.obra.nombre}<br/>
        <b>Fecha:</b> {fecha.strftime('%d/%m/%Y %H:%M')}<br/>
        <b>Algoritmo:</b> {self.combo_algoritmo.currentText()}<br/>
        <b>Longitud de barra:</b> {self.campo_longitud_barra.value():.0f} mm
        """
        story.append(Paragraph(info_obra, styles['Normal']))
        story.append(Spacer(1, 12))

        # Resumen
        resumen_titulo = Paragraph("RESUMEN", styles['Heading2'])
        story.append(resumen_titulo)

        resumen_data = [
            ['Concepto', 'Valor'],
            ['Barras necesarias', f"{len(self.resultados_optimizacion['barras'])}"],
            ['Eficiencia', f"{self.resultados_optimizacion['eficiencia']:.1f}%"],
            ['Desperdicio total', f"{self.resultados_optimizacion['desperdicio_total']:.0f} mm"],
            ['Coste estimado', f"{self.resultados_optimizacion['coste_estimado']:.2f} €"]
        ]

        resumen_table = Table(resumen_data, colWidths=[8*cm, 4*cm])
        resumen_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(resumen_table)
        story.append(Spacer(1, 20))

        # Detalle por barra
        detalle_titulo = Paragraph("DETALLE POR BARRA", styles['Heading2'])
        story.append(detalle_titulo)

        for i, barra in enumerate(self.resultados_optimizacion['barras']):
            # Título de la barra
            barra_titulo = Paragraph(f"Barra {i+1}", styles['Heading3'])
            story.append(barra_titulo)

            # Datos de la barra
            barra_data = [['Corte', 'Longitud (mm)', 'Perfil', 'Descripción']]

            for j, corte in enumerate(barra['cortes']):
                perfil = corte.get('perfil_codigo', 'N/A')
                descripcion = corte.get('descripcion', '')
                barra_data.append([
                    f"{j+1}",
                    f"{corte['longitud']:.0f}",
                    perfil,
                    descripcion
                ])

            # Agregar fila de resumen
            desperdicio = self.campo_longitud_barra.value() - barra['longitud_usada']
            eficiencia_barra = (barra['longitud_usada'] / self.campo_longitud_barra.value()) * 100

            barra_data.append(['', '', 'TOTAL USADO:', f"{barra['longitud_usada']:.0f} mm"])
            barra_data.append(['', '', 'DESPERDICIO:', f"{desperdicio:.0f} mm"])
            barra_data.append(['', '', 'EFICIENCIA:', f"{eficiencia_barra:.1f}%"])

            barra_table = Table(barra_data, colWidths=[1.5*cm, 2.5*cm, 3*cm, 5*cm])
            barra_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
                ('BACKGROUND', (0, -3), (-1, -1), colors.lightyellow),
                ('FONTNAME', (0, -3), (-1, -1), 'Helvetica-Bold'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(barra_table)
            story.append(Spacer(1, 12))

        # Generar PDF
        doc.build(story)

    def _imprimir_resultados(self):
        """Imprime los resultados."""
        if not self.resultados_optimizacion:
            QMessageBox.warning(self, "Error", "No hay resultados para imprimir.")
            return

        # Crear un archivo PDF temporal para imprimir
        try:
            import tempfile
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
                temp_path = temp_file.name

            if REPORTLAB_AVAILABLE:
                self._exportar_pdf(temp_path)

                # Abrir el PDF con el visor predeterminado
                import subprocess
                import platform

                if platform.system() == 'Windows':
                    os.startfile(temp_path)
                elif platform.system() == 'Darwin':  # macOS
                    subprocess.call(['open', temp_path])
                else:  # Linux
                    subprocess.call(['xdg-open', temp_path])

                QMessageBox.information(
                    self,
                    "Impresión",
                    "Se ha abierto el PDF en el visor predeterminado.\nPuede imprimirlo desde allí."
                )
            else:
                QMessageBox.information(
                    self,
                    "Funcionalidad limitada",
                    "Para imprimir directamente, instale ReportLab:\npip install reportlab\n\nPor ahora, puede exportar a TXT e imprimir manualmente."
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "Error de impresión",
                f"Error al preparar la impresión: {str(e)}"
            )



    def _ejecutar_optimizacion(self):
        """Ejecuta la optimización de cortes."""
        if not self.obra:
            QMessageBox.warning(self, "Error", "No hay obra seleccionada.")
            return

        try:
            # Mostrar progreso
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # Indeterminado
            self.btn_optimizar.setEnabled(False)

            # Configuración
            config = {
                'longitud_barra': self.campo_longitud_barra.value(),
                'desperdicio_minimo': self.campo_desperdicio_min.value(),
                'grosor_corte': self.campo_grosor_corte.value(),
                'algoritmo': self.combo_algoritmo.currentText(),
                'agrupar_perfiles': self.check_agrupar_perfiles.isChecked(),
                'considerar_angulos': self.check_considerar_angulos.isChecked(),
                'materiales_manuales': self.materiales_manuales
            }

            # Ejecutar optimización
            optimizador = OptimizadorCortes(self.obra, config)
            self.resultados_optimizacion = optimizador.optimizar()

            # Mostrar resultados
            self._mostrar_resultados()

            # Habilitar botones
            self.btn_exportar.setEnabled(True)
            self.btn_imprimir.setEnabled(True)

            # Cambiar a pestaña de resultados
            self.tabs.setCurrentIndex(1)

            QMessageBox.information(
                self,
                "✅ Optimización Completada",
                f"Optimización ejecutada correctamente.\n\n"
                f"Barras necesarias: {len(self.resultados_optimizacion['barras'])}\n"
                f"Eficiencia: {self.resultados_optimizacion['eficiencia']:.1f}%"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ Error",
                f"Error durante la optimización: {str(e)}"
            )
        finally:
            self.progress_bar.setVisible(False)
            self.btn_optimizar.setEnabled(True)

    def _mostrar_resultados(self):
        """Muestra los resultados de la optimización."""
        if not self.resultados_optimizacion:
            return

        resultados = self.resultados_optimizacion

        # Actualizar resumen
        self.label_barras_necesarias.setText(f"{len(resultados['barras'])} barras")
        self.label_eficiencia.setText(f"{resultados['eficiencia']:.1f}%")
        self.label_desperdicio_total.setText(f"{resultados['desperdicio_total']:.0f} mm")

        if 'coste_estimado' in resultados:
            self.label_coste_estimado.setText(f"{resultados['coste_estimado']:.2f} €")
        else:
            self.label_coste_estimado.setText("No calculado")

        # Llenar tabla de resultados
        self.tabla_resultados.setRowCount(len(resultados['barras']))

        for i, barra in enumerate(resultados['barras']):
            # Número de barra
            self.tabla_resultados.setItem(i, 0, QTableWidgetItem(f"Barra {i+1}"))

            # Tipo de perfil
            if barra['cortes']:
                perfil_tipo = barra['cortes'][0].get('perfil_codigo', 'Mixto')
            else:
                perfil_tipo = "Vacía"
            self.tabla_resultados.setItem(i, 1, QTableWidgetItem(perfil_tipo))

            # Cortes
            cortes_texto = f"{len(barra['cortes'])} cortes"
            if barra['cortes']:
                medidas = [f"{corte['longitud']:.0f}mm" for corte in barra['cortes']]
                cortes_texto += f": {', '.join(medidas)}"
            self.tabla_resultados.setItem(i, 2, QTableWidgetItem(cortes_texto))

            # Aprovechamiento
            aprovechamiento = ((barra['longitud_usada'] / self.campo_longitud_barra.value()) * 100)
            self.tabla_resultados.setItem(i, 3, QTableWidgetItem(f"{aprovechamiento:.1f}%"))

            # Desperdicio
            desperdicio = self.campo_longitud_barra.value() - barra['longitud_usada']
            self.tabla_resultados.setItem(i, 4, QTableWidgetItem(f"{desperdicio:.0f} mm"))

        # Actualizar combo de visualización
        self.combo_barra_visualizar.clear()
        for i in range(len(resultados['barras'])):
            self.combo_barra_visualizar.addItem(f"Barra {i+1}")

        # Actualizar visualización
        self._actualizar_visualizacion()

    def _actualizar_visualizacion(self):
        """Actualiza la visualización gráfica."""
        if not self.resultados_optimizacion:
            return

        barra_index = self.combo_barra_visualizar.currentIndex()
        if barra_index < 0 or barra_index >= len(self.resultados_optimizacion['barras']):
            return

        barra = self.resultados_optimizacion['barras'][barra_index]

        # Crear imagen de la barra
        self._dibujar_barra(barra)

    def _dibujar_barra(self, barra):
        """Dibuja la visualización de una barra."""
        # Dimensiones del dibujo
        ancho_total = 800
        alto_barra = 80
        margen = 50

        # Crear pixmap
        pixmap = QPixmap(ancho_total, alto_barra + 2 * margen)
        pixmap.fill(QColor(255, 255, 255))

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Configurar colores
        colores_perfil = [
            QColor(52, 152, 219),   # Azul
            QColor(46, 204, 113),   # Verde
            QColor(231, 76, 60),    # Rojo
            QColor(241, 196, 15),   # Amarillo
            QColor(155, 89, 182),   # Púrpura
            QColor(230, 126, 34),   # Naranja
        ]

        # Dibujar barra completa
        longitud_barra = self.campo_longitud_barra.value()
        rect_barra = painter.drawRect(margen, margen, ancho_total - 2 * margen, alto_barra)

        # Dibujar cortes
        x_actual = margen
        escala = (ancho_total - 2 * margen) / longitud_barra

        for i, corte in enumerate(barra['cortes']):
            ancho_corte = corte['longitud'] * escala
            color = colores_perfil[i % len(colores_perfil)]

            # Dibujar rectángulo del corte
            painter.fillRect(
                int(x_actual), margen,
                int(ancho_corte), alto_barra,
                QBrush(color)
            )

            # Dibujar borde
            painter.setPen(QPen(QColor(0, 0, 0), 1))
            painter.drawRect(int(x_actual), margen, int(ancho_corte), alto_barra)

            # Mostrar medidas si está habilitado
            if self.check_mostrar_medidas.isChecked():
                painter.setPen(QPen(QColor(0, 0, 0), 1))
                texto = f"{corte['longitud']:.0f}"
                painter.drawText(
                    int(x_actual + ancho_corte/2 - 15), int(margen + alto_barra/2 + 5),
                    texto
                )

            x_actual += ancho_corte + (self.campo_grosor_corte.value() * escala)

        # Dibujar desperdicio
        if x_actual < ancho_total - margen:
            painter.fillRect(
                int(x_actual), margen,
                int(ancho_total - margen - x_actual), alto_barra,
                QBrush(QColor(200, 200, 200))
            )

            # Texto de desperdicio
            desperdicio = longitud_barra - barra['longitud_usada']
            painter.setPen(QPen(QColor(100, 100, 100), 1))
            painter.drawText(
                int(x_actual + 10), int(margen + alto_barra/2 + 5),
                f"Desperdicio: {desperdicio:.0f}mm"
            )

        painter.end()

        # Mostrar en el widget
        label_imagen = QLabel()
        label_imagen.setPixmap(pixmap)

        # Limpiar layout anterior
        layout = self.widget_visualizacion.layout()
        if layout:
            while layout.count():
                child = layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
        else:
            layout = QVBoxLayout(self.widget_visualizacion)

        layout.addWidget(label_imagen)

        # Actualizar leyenda
        self._actualizar_leyenda(barra)

    def _actualizar_leyenda(self, barra):
        """Actualiza la leyenda de colores."""
        leyenda_texto = "<b>Leyenda de colores:</b><br>"

        colores_html = [
            "#3498db", "#2ecc71", "#e74c3c", "#f1c40f", "#9b59b6", "#e67e22"
        ]

        perfiles_mostrados = set()

        for i, corte in enumerate(barra['cortes']):
            perfil_codigo = corte.get('perfil_codigo', f'Perfil {i+1}')
            if perfil_codigo not in perfiles_mostrados:
                color = colores_html[i % len(colores_html)]
                leyenda_texto += f'<span style="color: {color};">■</span> {perfil_codigo}<br>'
                perfiles_mostrados.add(perfil_codigo)

        leyenda_texto += '<span style="color: #c8c8c8;">■</span> Desperdicio'

        self.label_leyenda.setText(leyenda_texto)
