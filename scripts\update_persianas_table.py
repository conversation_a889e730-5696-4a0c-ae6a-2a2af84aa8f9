"""
Script para actualizar la tabla persianas en la base de datos.
"""
import os
import sqlite3
from sqlite3 import <PERSON><PERSON><PERSON>

def create_connection(db_file):
    """Crear una conexión a la base de datos SQLite."""
    conn = None
    try:
        conn = sqlite3.connect(db_file)
        print(f"Conexión exitosa a SQLite versión {sqlite3.version}")
        return conn
    except Error as e:
        print(f"Error al conectar a la base de datos: {e}")
        return None

def update_persianas_table(conn):
    """Actualizar la tabla persianas con la columna referencia."""
    try:
        # Verificar si la columna ya existe
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(persianas)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'referencia' not in columns:
            print("Añadiendo columna 'referencia' a la tabla 'persianas'...")
            
            # Añadir la columna referencia
            cursor.execute("""
                ALTER TABLE persianas 
                ADD COLUMN referencia TEXT NOT NULL DEFAULT ''
            """)
            
            # Actualizar registros existentes con valores únicos
            cursor.execute("""
                UPDATE persianas 
                SET referencia = 'PERS-' || id 
                WHERE referencia = '' OR referencia IS NULL
            """)
            
            # Crear índice único
            cursor.execute("""
                CREATE UNIQUE INDEX IF NOT EXISTS uq_persianas_referencia 
                ON persianas (referencia)
            """)
            
            conn.commit()
            print("Tabla 'persianas' actualizada exitosamente.")
        else:
            print("La columna 'referencia' ya existe en la tabla 'persianas'.")
            
    except Error as e:
        print(f"Error al actualizar la tabla 'persianas': {e}")
        if conn:
            conn.rollback()

def main():
    # Ruta a la base de datos
    db_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'data', 'database.db'))
    
    # Crear conexión
    conn = create_connection(db_path)
    if conn is not None:
        # Actualizar tabla persianas
        update_persianas_table(conn)
        conn.close()
        print("Conexión cerrada.")
    else:
        print("No se pudo establecer la conexión a la base de datos.")

if __name__ == "__main__":
    main()
