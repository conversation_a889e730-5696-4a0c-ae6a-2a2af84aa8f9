#!/usr/bin/env python3
"""
Script para crear datos iniciales en PRO-2000
"""

import sys
import os

# Añadir el directorio src al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from models.base import init_db, get_db
from models.usuario import Usuario

def crear_usuario_admin():
    """Crea un usuario administrador por defecto"""
    try:
        # Inicializar BD
        init_db()
        
        # Obtener sesión
        db = next(get_db())
        
        # Verificar si ya existe el usuario admin
        admin_existente = Usuario.buscar_por_nombre(db, "admin")
        
        if admin_existente:
            print("✅ Usuario admin ya existe")
            return
        
        # Crear usuario admin
        admin = Usuario(
            nombre="admin",
            tipo=1,  # Administrador
            clave="admin123",  # En producción debería estar hasheada
            activo=1
        )
        
        db.add(admin)
        db.commit()
        
        print("✅ Usuario administrador creado:")
        print("   Usuario: admin")
        print("   Contraseña: admin123")
        print("   Tipo: Administrador")
        
    except Exception as e:
        print(f"❌ Error creando usuario admin: {e}")
        db.rollback()
    finally:
        db.close()

def crear_usuario_normal():
    """Crea un usuario normal por defecto"""
    try:
        db = next(get_db())
        
        # Verificar si ya existe el usuario
        usuario_existente = Usuario.buscar_por_nombre(db, "usuario")
        
        if usuario_existente:
            print("✅ Usuario normal ya existe")
            return
        
        # Crear usuario normal
        usuario = Usuario(
            nombre="usuario",
            tipo=2,  # Usuario normal
            clave="123",
            activo=1
        )
        
        db.add(usuario)
        db.commit()
        
        print("✅ Usuario normal creado:")
        print("   Usuario: usuario")
        print("   Contraseña: 123")
        print("   Tipo: Usuario")
        
    except Exception as e:
        print(f"❌ Error creando usuario normal: {e}")
        db.rollback()
    finally:
        db.close()

def main():
    """Función principal"""
    print("🔧 Creando datos iniciales para PRO-2000...")
    
    try:
        crear_usuario_admin()
        crear_usuario_normal()
        
        print("\n🎉 Datos iniciales creados exitosamente!")
        print("\n📋 Usuarios disponibles:")
        print("   👤 admin / admin123 (Administrador)")
        print("   👤 usuario / 123 (Usuario normal)")
        print("\n🚀 Ahora puede ejecutar: python src/main_minimal.py")
        
    except Exception as e:
        print(f"❌ Error general: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
