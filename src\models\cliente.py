from sqlalchemy import Column, Integer, String, Boolean, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime
from .base import Base

class Cliente(Base):
    """
    Modelo que representa un cliente en el sistema.
    """
    __tablename__ = 'clientes'
    
    id = Column(Integer, primary_key=True)
    codigo = Column(String(20), unique=True, nullable=False)
    nombre = Column(String(100), nullable=False)
    tipo = Column(String(50))  # Particular, Empresa, etc.
    nif = Column(String(20))
    direccion = Column(String(200))
    codigo_postal = Column(String(10))
    ciudad = Column(String(100))  # Cambiar localidad por ciudad
    provincia = Column(String(100))
    telefono = Column(String(20))
    movil = Column(String(20))  # Teléfono móvil
    email = Column(String(100))
    web = Column(String(200))  # Página web
    persona_contacto = Column(String(100))  # Persona de contacto
    telefono_contacto = Column(String(20))  # Teléfono de contacto
    pais = Column(String(100), default='España')  # País
    notas = Column(String(500))  # Notas adicionales
    fecha_alta = Column(DateTime, default=datetime.utcnow)
    activo = Column(Boolean, default=True)
    
    # Relaciones
    obras = relationship("Obra", back_populates="cliente")
    
    def __repr__(self):
        return f"<Cliente(codigo='{self.codigo}', nombre='{self.nombre}')>"
    
    def to_dict(self):
        """Convierte el objeto a un diccionario."""
        return {
            'id': self.id,
            'codigo': self.codigo,
            'nombre': self.nombre,
            'tipo': self.tipo,
            'nif': self.nif,
            'direccion': self.direccion,
            'codigo_postal': self.codigo_postal,
            'ciudad': self.ciudad,
            'provincia': self.provincia,
            'telefono': self.telefono,
            'movil': self.movil,
            'email': self.email,
            'web': self.web,
            'persona_contacto': self.persona_contacto,
            'telefono_contacto': self.telefono_contacto,
            'pais': self.pais,
            'notas': self.notas,
            'fecha_alta': self.fecha_alta.isoformat() if self.fecha_alta else None,
            'activo': self.activo
        }
