"""
Modelo de datos para persianas.
"""
from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON>an, Enum
from sqlalchemy.orm import validates
from .base import Base


class Persiana(Base):
    """Modelo para representar una persiana con sus características específicas."""
    
    __tablename__ = 'persianas'
    
    # Alturas de cajón disponibles (en mm)
    ALTURAS_CAJON = [155, 185, 200, 220]
    
    # Tipos de cajón
    TIPO_NORMAL = 'normal'
    TIPO_DECORATIVO = 'decorativo'
    TIPOS_CAJON = [TIPO_NORMAL, TIPO_DECORATIVO]
    
    # Tipos de operación
    OPERACION_MOTORIZADO = 'motorizado'
    OPERACION_RECOGEDOR = 'recogedor'
    TIPOS_OPERACION = [OPERACION_MOTORIZADO, OPERACION_RECOGEDOR]
    
    # Posiciones del mando
    POSICION_IZQUIERDA = 'izquierda'
    POSICION_DERECHA = 'derecha'
    POSICIONES_MANDO = [POSICION_IZQUIERDA, POSICION_DERECHA]
    
    id = Column(Integer, primary_key=True)
    referencia = Column(String(50), unique=True, nullable=False, comment='Referencia única de la persiana')
    nombre = Column(String(100), nullable=False, comment='Nombre descriptivo de la persiana')
    
    # Características del cajón
    altura_cajon = Column(Integer, nullable=False, comment='Altura del cajón en mm')
    tipo_cajon = Column(String(20), nullable=False, default=TIPO_NORMAL, comment='Tipo de cajón (normal/decorativo)')
    
    # Configuración de operación
    tipo_operacion = Column(String(20), nullable=False, comment='Tipo de operación (motorizado/recogedor)')
    posicion_mando = Column(String(20), nullable=True, comment='Posición del mando (izquierda/derecha)')
    
    # Apariencia
    color = Column(String(50), nullable=False, comment='Color de la persiana')
    
    # Estado
    activo = Column(Boolean, default=True, comment='Indica si la persiana está activa en el sistema')
    
    def __repr__(self):
        """Representación en cadena del objeto."""
        return f"<Persiana(referencia='{self.referencia}', nombre='{self.nombre}')>"
    
    def to_dict(self):
        """Convierte el objeto a un diccionario."""
        return {
            'id': self.id,
            'referencia': self.referencia,
            'nombre': self.nombre,
            'altura_cajon': self.altura_cajon,
            'tipo_cajon': self.tipo_cajon,
            'tipo_operacion': self.tipo_operacion,
            'posicion_mando': self.posicion_mando,
            'color': self.color,
            'activo': self.activo
        }
        
    @validates('altura_cajon')
    def validate_altura_cajon(self, key, altura):
        """Valida que la altura del cajón sea una de las permitidas."""
        if altura not in self.ALTURAS_CAJON:
            raise ValueError(f'La altura del cajón debe ser una de: {self.ALTURAS_CAJON}')
        return altura
        
    @validates('tipo_cajon')
    def validate_tipo_cajon(self, key, tipo):
        """Valida que el tipo de cajón sea uno de los permitidos."""
        if tipo not in self.TIPOS_CAJON:
            raise ValueError(f'El tipo de cajón debe ser uno de: {self.TIPOS_CAJON}')
        return tipo
        
    @validates('tipo_operacion')
    def validate_tipo_operacion(self, key, operacion):
        """Valida que el tipo de operación sea uno de los permitidos."""
        if operacion not in self.TIPOS_OPERACION:
            raise ValueError(f'El tipo de operación debe ser uno de: {self.TIPOS_OPERACION}')
        return operacion
        
    @validates('posicion_mando')
    def validate_posicion_mando(self, key, posicion):
        """Valida que la posición del mando sea una de las permitidas."""
        if posicion is not None and posicion not in self.POSICIONES_MANDO:
            raise ValueError(f'La posición del mando debe ser una de: {self.POSICIONES_MANDO}')
        return posicion
