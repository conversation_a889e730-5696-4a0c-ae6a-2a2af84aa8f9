"""
Diálogo para gestionar tipos de perfiles
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QLineEdit, QTableWidget, QTableWidgetItem, QMessageBox, QHeaderView,
    QDialogButtonBox, QFormLayout, QTextEdit, QCheckBox, QWidget, QComboBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from datetime import datetime

from models.base import get_db
from models.perfil import TipoPerfil
from ui.utils.window_utils import smart_dialog_setup, force_dialog_maximized


class TipoDialog(QDialog):
    """Diálogo para gestionar tipos de perfiles."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Gestión de Tipos de Perfiles")

        # ✅ FORZAR MAXIMIZACIÓN COMPLETA
        force_dialog_maximized(self, "Gestión de Tipos de Perfiles")

        self.tipo_actual = None
        self._configurar_ui()
        self._cargar_tipos()
        
    def _configurar_ui(self):
        """Configura la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)
        layout_principal.setContentsMargins(16, 16, 16, 16)
        layout_principal.setSpacing(16)
        
        # Título
        titulo = QLabel("🏷️ Gestión de Tipos de Perfiles")
        titulo.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        titulo.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout_principal.addWidget(titulo)
        
        # Layout horizontal para tabla y formulario
        layout_horizontal = QHBoxLayout()
        
        # Panel izquierdo - Lista de tipos
        panel_izquierdo = self._crear_panel_lista()
        layout_horizontal.addWidget(panel_izquierdo, 2)
        
        # Panel derecho - Formulario
        panel_derecho = self._crear_panel_formulario()
        layout_horizontal.addWidget(panel_derecho, 1)
        
        layout_principal.addLayout(layout_horizontal)
        
        # Botones de diálogo
        botones = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        botones.rejected.connect(self.accept)
        layout_principal.addWidget(botones)

    def _cargar_categorias(self):
        """Carga todas las categorías únicas de la base de datos en el combo."""
        db = next(get_db())
        try:
            categorias = db.query(TipoPerfil.categoria).distinct().all()
            valores = sorted(set(c[0] for c in categorias if c[0]))
            self.categoria.blockSignals(True)
            self.categoria.clear()
            self.categoria.addItems(valores)
            self.categoria.blockSignals(False)
        except Exception as e:
            pass
        finally:
            db.close()
        
    def _crear_panel_lista(self):
        """Crea el panel de lista de tipos."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(0, 0, 8, 0)
        
        # Título y botones
        header_layout = QHBoxLayout()
        
        titulo_lista = QLabel("Lista de Tipos")
        titulo_lista.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        header_layout.addWidget(titulo_lista)
        
        header_layout.addStretch()
        
        self.btn_actualizar = QPushButton("🔄 Actualizar")
        self.btn_actualizar.clicked.connect(self._cargar_tipos)
        header_layout.addWidget(self.btn_actualizar)
        
        layout.addLayout(header_layout)
        
        # Tabla
        self.tabla_tipos = QTableWidget()
        self.tabla_tipos.setColumnCount(5)
        self.tabla_tipos.setHorizontalHeaderLabels([
            "Código", "Nombre", "Categoría", "Descripción", "Estado"
        ])
        
        # Configurar tabla
        self.tabla_tipos.setAlternatingRowColors(False)  # Desactivar filas alternadas
        self.tabla_tipos.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        header = self.tabla_tipos.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        
        self.tabla_tipos.itemSelectionChanged.connect(self._on_seleccion_cambiada)
        
        layout.addWidget(self.tabla_tipos)
        
        return panel
        
    def _crear_panel_formulario(self):
        """Crea el panel del formulario."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(8, 0, 0, 0)
        
        # Título
        titulo_form = QLabel("Datos del Tipo")
        titulo_form.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(titulo_form)
        
        # Formulario
        form_layout = QFormLayout()
        form_layout.setSpacing(12)
        
        # Código
        self.codigo = QLineEdit()
        self.codigo.setMaxLength(20)
        self.codigo.setPlaceholderText("Ej: MARCO")
        form_layout.addRow("Código*:", self.codigo)
        
        # Nombre
        self.nombre = QLineEdit()
        self.nombre.setMaxLength(100)
        self.nombre.setPlaceholderText("Nombre del tipo")
        form_layout.addRow("Nombre*:", self.nombre)
        
        # Categoría
        self.categoria = QComboBox()
        self.categoria.setEditable(True)
        self._cargar_categorias()
        form_layout.addRow("Categoría*:", self.categoria)
        
        # Descripción
        self.descripcion = QTextEdit()
        self.descripcion.setMaximumHeight(100)
        self.descripcion.setPlaceholderText("Descripción detallada del tipo")
        form_layout.addRow("Descripción:", self.descripcion)
        
        # Activo
        self.activo = QCheckBox("Tipo activo")
        self.activo.setChecked(True)
        form_layout.addRow("Estado:", self.activo)
        
        layout.addLayout(form_layout)
        

        # Mensaje de error
        self.label_error = QLabel()
        self.label_error.setStyleSheet("color: #e74c3c; font-weight: bold;")
        self.label_error.setVisible(False)
        layout.addWidget(self.label_error)
        
        # Botones de acción
        botones_layout = QVBoxLayout()
        botones_layout.setSpacing(8)
        
        self.btn_nuevo = QPushButton("➕ Nuevo Tipo")
        self.btn_nuevo.setStyleSheet(self._get_button_style("#27ae60"))
        self.btn_nuevo.clicked.connect(self._nuevo_tipo)
        botones_layout.addWidget(self.btn_nuevo)
        
        self.btn_guardar = QPushButton("💾 Guardar")
        self.btn_guardar.setStyleSheet(self._get_button_style("#3498db"))
        self.btn_guardar.setEnabled(False)
        self.btn_guardar.clicked.connect(self._guardar_tipo)
        botones_layout.addWidget(self.btn_guardar)
        
        self.btn_eliminar = QPushButton("🗑️ Eliminar")
        self.btn_eliminar.setStyleSheet(self._get_button_style("#e74c3c"))
        self.btn_eliminar.setEnabled(False)
        self.btn_eliminar.clicked.connect(self._eliminar_tipo)
        botones_layout.addWidget(self.btn_eliminar)
        
        layout.addLayout(botones_layout)
        layout.addStretch()
        
        return panel
    
    def _get_button_style(self, color):
        """Obtiene el estilo para botones."""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 10px 16px;
                border-radius: 6px;
                font-weight: bold;
                min-height: 35px;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
            }}
            QPushButton:pressed {{
                background-color: {color}aa;
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
        """
    
    def _cargar_tipos(self):
        """Carga la lista de tipos."""
        db = next(get_db())
        try:
            tipos = db.query(TipoPerfil).order_by(TipoPerfil.categoria, TipoPerfil.nombre).all()
            
            self.tabla_tipos.setRowCount(len(tipos))
            
            for fila, tipo in enumerate(tipos):
                # Código
                self.tabla_tipos.setItem(fila, 0, QTableWidgetItem(tipo.codigo))
                
                # Nombre
                self.tabla_tipos.setItem(fila, 1, QTableWidgetItem(tipo.nombre))
                
                # Categoría
                categoria = tipo.categoria or ""
                self.tabla_tipos.setItem(fila, 2, QTableWidgetItem(categoria))
                
                # Descripción
                descripcion = (tipo.descripcion or "")[:40] + "..." if tipo.descripcion and len(tipo.descripcion) > 40 else (tipo.descripcion or "")
                self.tabla_tipos.setItem(fila, 3, QTableWidgetItem(descripcion))
                
                # Estado
                estado = "✅ Activo" if tipo.activo else "❌ Inactivo"
                estado_item = QTableWidgetItem(estado)
                estado_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.tabla_tipos.setItem(fila, 4, estado_item)
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error cargando tipos: {str(e)}")
        finally:
            db.close()
    
    def _on_seleccion_cambiada(self):
        """Maneja el cambio de selección."""
        fila = self.tabla_tipos.currentRow()
        if fila >= 0:
            codigo = self.tabla_tipos.item(fila, 0).text()
            self._cargar_tipo(codigo)
            self.btn_guardar.setEnabled(True)
            self.btn_eliminar.setEnabled(True)
        else:
            self._limpiar_formulario()
            self.btn_guardar.setEnabled(False)
            self.btn_eliminar.setEnabled(False)
    
    def _cargar_tipo(self, codigo):
        """Carga un tipo en el formulario."""
        db = next(get_db())
        try:
            tipo = db.query(TipoPerfil).filter(TipoPerfil.codigo == codigo).first()
            if tipo:
                self.tipo_actual = tipo
                self.codigo.setText(tipo.codigo)
                self.codigo.setReadOnly(True)
                self.nombre.setText(tipo.nombre)
                # Si la categoría no está en el combo, añadirla
                if tipo.categoria and self.categoria.findText(tipo.categoria) == -1:
                    self.categoria.addItem(tipo.categoria)
                self.categoria.setCurrentText(tipo.categoria or "")
                self.descripcion.setPlainText(tipo.descripcion or "")
                self.activo.setChecked(bool(tipo.activo) if tipo.activo is not None else True)
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error cargando tipo: {str(e)}")
        finally:
            db.close()
    
    def _nuevo_tipo(self):
        """Prepara el formulario para un nuevo tipo."""
        self._limpiar_formulario()
        self.tipo_actual = None
        self.codigo.setReadOnly(False)
        self.btn_guardar.setEnabled(True)
        self.btn_eliminar.setEnabled(False)
        self.codigo.setFocus()
        # Refrescar categorías al crear nuevo tipo
        self._cargar_categorias()
    
    def _limpiar_formulario(self):
        """Limpia el formulario."""
        self.codigo.clear()
        self.nombre.clear()
        self.categoria.setCurrentText("")
        self.descripcion.clear()
        self.activo.setChecked(True)
        self.label_error.setVisible(False)
        # Refrescar categorías siempre que se limpie el formulario
        self._cargar_categorias()
    
    def _validar_formulario(self):
        """Valida el formulario."""
        if not self.codigo.text().strip():
            self._mostrar_error("El código es obligatorio")
            return False
        
        if not self.nombre.text().strip():
            self._mostrar_error("El nombre es obligatorio")
            return False
        
        if not self.categoria.currentText().strip():
            self._mostrar_error("La categoría es obligatoria")
            return False
        
        self.label_error.setVisible(False)
        return True
    
    def _mostrar_error(self, mensaje):
        """Muestra un mensaje de error."""
        self.label_error.setText(mensaje)
        self.label_error.setVisible(True)
    
    def _guardar_tipo(self):
        """Guarda el tipo."""
        if not self._validar_formulario():
            return
        
        db = next(get_db())
        try:
            if self.tipo_actual:
                # Actualizar existente
                self.tipo_actual.nombre = self.nombre.text().strip()
                self.tipo_actual.categoria = self.categoria.currentText().strip()
                self.tipo_actual.descripcion = self.descripcion.toPlainText().strip() or None
                self.tipo_actual.activo = self.activo.isChecked()
            else:
                # Crear nuevo
                # Verificar que no exista el código
                existe = db.query(TipoPerfil).filter(TipoPerfil.codigo == self.codigo.text().strip()).first()
                if existe:
                    self._mostrar_error("Ya existe un tipo con este código")
                    return
                
                nuevo_tipo = TipoPerfil(
                    codigo=self.codigo.text().strip(),
                    nombre=self.nombre.text().strip(),
                    categoria=self.categoria.currentText().strip(),
                    descripcion=self.descripcion.toPlainText().strip() or None,
                    activo=self.activo.isChecked(),
                    fecha_creacion=datetime.now()
                )
                db.add(nuevo_tipo)
            
            db.commit()
            # Si la categoría no está en el combo, añadirla
            nueva_categoria = self.categoria.currentText().strip()
            if nueva_categoria and self.categoria.findText(nueva_categoria) == -1:
                self.categoria.addItem(nueva_categoria)
            QMessageBox.information(self, "Éxito", "Tipo guardado correctamente")
            self._cargar_tipos()
            
        except Exception as e:
            db.rollback()
            QMessageBox.critical(self, "Error", f"Error guardando tipo: {str(e)}")
        finally:
            db.close()
    
    def _eliminar_tipo(self):
        """Elimina el tipo actual."""
        if not self.tipo_actual:
            return
        
        respuesta = QMessageBox.question(
            self,
            "Confirmar eliminación",
            f"¿Está seguro de eliminar el tipo '{self.tipo_actual.nombre}'?\n"
            "Esta acción no se puede deshacer.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if respuesta == QMessageBox.StandardButton.Yes:
            db = next(get_db())
            try:
                db.delete(self.tipo_actual)
                db.commit()
                QMessageBox.information(self, "Éxito", "Tipo eliminado correctamente")
                self._cargar_tipos()
                self._limpiar_formulario()
                self.tipo_actual = None
                self.btn_guardar.setEnabled(False)
                self.btn_eliminar.setEnabled(False)
                
            except Exception as e:
                db.rollback()
                QMessageBox.critical(self, "Error", f"Error eliminando tipo: {str(e)}")
            finally:
                db.close()
