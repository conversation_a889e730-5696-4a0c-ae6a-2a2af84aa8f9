#!/usr/bin/env python3
"""
Sistema de Manejo de Errores Centralizado para PRO-2000
Proporciona manejo robusto y logging de errores
"""

import logging
import traceback
import functools
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Callable, Any
from enum import Enum

from PyQt6.QtWidgets import QMessageBox, QWidget
from PyQt6.QtCore import QObject, pyqtSignal

class ErrorLevel(Enum):
    """Niveles de severidad de error"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """Categorías de errores"""
    DATABASE = "database"
    UI = "ui"
    FILE_OPERATION = "file_operation"
    VALIDATION = "validation"
    IMPORT_MODULE = "import_module"
    NETWORK = "network"
    CONFIGURATION = "configuration"
    CALCULATION = "calculation"
    GENERAL = "general"

class ErrorHandler(QObject):
    """Manejador centralizado de errores"""
    
    # Señales para notificar errores
    error_occurred = pyqtSignal(str, str, str)  # level, category, message
    
    def __init__(self):
        super().__init__()
        self.logger = self._setup_logger()
        self.error_count = {level: 0 for level in ErrorLevel}
        self.last_errors = []
        self.max_recent_errors = 100
        
    def _setup_logger(self) -> logging.Logger:
        """Configura el logger para errores"""
        logger = logging.getLogger("PRO2000_ErrorHandler")
        logger.setLevel(logging.DEBUG)
        
        # Evitar duplicar handlers
        if logger.handlers:
            return logger
        
        # Crear directorio de logs
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Handler para archivo
        log_file = log_dir / "errors.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.WARNING)
        
        # Handler para consola
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.ERROR)
        
        # Formato
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def handle_error(
        self, 
        error: Exception, 
        category: ErrorCategory = ErrorCategory.GENERAL,
        level: ErrorLevel = ErrorLevel.ERROR,
        context: str = "",
        show_user: bool = True,
        parent_widget: Optional[QWidget] = None
    ) -> bool:
        """
        Maneja un error de forma centralizada
        """
        try:
            # Crear mensaje de error
            error_msg = str(error)
            if context:
                error_msg = f"{context}: {error_msg}"
            
            # Obtener traceback
            tb_str = traceback.format_exc()
            
            # Registrar en log
            log_msg = f"[{category.value.upper()}] {error_msg}\n{tb_str}"
            
            if level == ErrorLevel.CRITICAL:
                self.logger.critical(log_msg)
            elif level == ErrorLevel.ERROR:
                self.logger.error(log_msg)
            elif level == ErrorLevel.WARNING:
                self.logger.warning(log_msg)
            else:
                self.logger.info(log_msg)
            
            # Actualizar estadísticas
            self.error_count[level] += 1
            
            # Guardar en errores recientes
            error_info = {
                'timestamp': datetime.now(),
                'level': level,
                'category': category,
                'message': error_msg,
                'traceback': tb_str
            }
            self.last_errors.append(error_info)
            
            # Mantener solo los errores más recientes
            if len(self.last_errors) > self.max_recent_errors:
                self.last_errors.pop(0)
            
            # Emitir señal
            self.error_occurred.emit(level.value, category.value, error_msg)
            
            # Mostrar al usuario si es necesario
            if show_user:
                self._show_user_error(error_msg, level, category, parent_widget)
            
            return True
            
        except Exception as handler_error:
            # Error en el manejador de errores - situación crítica
            print(f"❌ CRITICAL: Error en ErrorHandler: {handler_error}")
            return False
    
    def _show_user_error(
        self, 
        message: str, 
        level: ErrorLevel, 
        category: ErrorCategory,
        parent: Optional[QWidget] = None
    ):
        """Muestra el error al usuario mediante QMessageBox"""
        try:
            # Configurar título e icono según el nivel
            if level == ErrorLevel.CRITICAL:
                title = "❌ Error Crítico"
                icon = QMessageBox.Icon.Critical
            elif level == ErrorLevel.ERROR:
                title = "⚠️ Error"
                icon = QMessageBox.Icon.Warning
            elif level == ErrorLevel.WARNING:
                title = "⚠️ Advertencia"
                icon = QMessageBox.Icon.Warning
            else:
                title = "ℹ️ Información"
                icon = QMessageBox.Icon.Information
            
            # Crear mensaje más amigable
            user_message = self._make_user_friendly_message(message, category)
            
            # Mostrar diálogo
            msg_box = QMessageBox(parent)
            msg_box.setIcon(icon)
            msg_box.setWindowTitle(title)
            msg_box.setText(user_message)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.exec()
                
        except Exception as e:
            print(f"Error mostrando diálogo de error: {e}")
    
    def _make_user_friendly_message(self, message: str, category: ErrorCategory) -> str:
        """Convierte mensajes técnicos en mensajes amigables"""
        user_messages = {
            ErrorCategory.DATABASE: "Error en la base de datos. Los datos pueden no estar disponibles temporalmente.",
            ErrorCategory.FILE_OPERATION: "Error al acceder a un archivo. Verifique los permisos y que el archivo exista.",
            ErrorCategory.IMPORT_MODULE: "Error al cargar un componente de la aplicación. Algunas funciones pueden no estar disponibles.",
            ErrorCategory.VALIDATION: "Los datos ingresados no son válidos. Por favor revise la información.",
            ErrorCategory.NETWORK: "Error de conexión de red. Verifique su conexión a internet.",
            ErrorCategory.CONFIGURATION: "Error en la configuración de la aplicación. Se usarán valores por defecto."
        }
        
        friendly_msg = user_messages.get(category, "Ha ocurrido un error inesperado.")
        return f"{friendly_msg}\n\nDetalle técnico: {message}"

# Instancia global del manejador de errores
error_handler = ErrorHandler()

def handle_exception(
    category: ErrorCategory = ErrorCategory.GENERAL,
    level: ErrorLevel = ErrorLevel.ERROR,
    context: str = "",
    show_user: bool = True,
    fallback_return=None
):
    """Decorador para manejo automático de excepciones"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Determinar contexto si no se proporciona
                func_context = context or f"Función: {func.__name__}"
                
                # Manejar el error
                error_handler.handle_error(
                    error=e,
                    category=category,
                    level=level,
                    context=func_context,
                    show_user=show_user
                )
                
                # Retornar valor por defecto
                return fallback_return
        return wrapper
    return decorator

def handle_database_errors(show_user: bool = True, fallback_return=None):
    """Decorador específico para errores de base de datos"""
    return handle_exception(
        category=ErrorCategory.DATABASE,
        level=ErrorLevel.ERROR,
        context="Operación de base de datos",
        show_user=show_user,
        fallback_return=fallback_return
    )

def handle_file_errors(show_user: bool = True, fallback_return=None):
    """Decorador específico para errores de archivo"""
    return handle_exception(
        category=ErrorCategory.FILE_OPERATION,
        level=ErrorLevel.ERROR,
        context="Operación de archivo",
        show_user=show_user,
        fallback_return=fallback_return
    )

def handle_ui_errors(show_user: bool = False, fallback_return=None):
    """Decorador específico para errores de UI"""
    return handle_exception(
        category=ErrorCategory.UI,
        level=ErrorLevel.WARNING,
        context="Interfaz de usuario",
        show_user=show_user,
        fallback_return=fallback_return
    )

def safe_import(module_name: str, fallback_class=None):
    """Importa un módulo de forma segura"""
    try:
        import importlib
        return importlib.import_module(module_name)
    except ImportError as e:
        error_handler.handle_error(
            error=e,
            category=ErrorCategory.IMPORT_MODULE,
            level=ErrorLevel.WARNING,
            context=f"Importando módulo: {module_name}",
            show_user=False
        )
        return fallback_class

class ValidationError(Exception):
    """Excepción personalizada para errores de validación"""
    def __init__(self, field_name: str, message: str, value=None):
        self.field_name = field_name
        self.value = value
        super().__init__(f"Error en {field_name}: {message}")

# Funciones de conveniencia para logging
def log_info(message: str, context: str = ""):
    """Log de información"""
    error_handler.logger.info(f"{context}: {message}" if context else message)

def log_warning(message: str, context: str = ""):
    """Log de advertencia"""
    error_handler.logger.warning(f"{context}: {message}" if context else message)

def log_error(message: str, context: str = ""):
    """Log de error"""
    error_handler.logger.error(f"{context}: {message}" if context else message)
