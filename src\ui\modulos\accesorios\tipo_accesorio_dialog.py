"""
<PERSON><PERSON><PERSON><PERSON> que define el diálogo para gestionar tipos de accesorios.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QLineEdit, QTableWidget, QTableWidgetItem, QMessageBox, QHeaderView,
    QDialogButtonBox, QFormLayout, QCheckBox, QTextEdit
)
from PyQt6.QtCore import Qt, pyqtSlot
from PyQt6.QtGui import QIcon

from models.base import get_db
from models.tipo_accesorio import TipoAccesorio
from ui.utils.window_utils import setup_maximized_dialog


class TipoAccesorioDialog(QDialog):
    """Diálogo para gestionar tipos de accesorios."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Gestión de Tipos de Accesorios")

        # ✅ CONFIGURACIÓN SIMPLE Y EFECTIVA
        setup_maximized_dialog(self, "Gestión de Tipos de Accesorios")
        
        self._configurar_ui()
        self._cargar_tipos()

    def _configurar_ui(self):
        """Configura la interfaz de usuario."""
        layout = QVBoxLayout(self)

        # Título
        titulo = QLabel("Gestión de Tipos de Accesorios")
        titulo.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(titulo)

        # Botones de acción
        layout_botones = QHBoxLayout()
        
        self.btn_nuevo = QPushButton("Nuevo Tipo")
        self.btn_nuevo.clicked.connect(self._nuevo_tipo)
        layout_botones.addWidget(self.btn_nuevo)
        
        self.btn_editar = QPushButton("Editar")
        self.btn_editar.clicked.connect(self._editar_tipo)
        layout_botones.addWidget(self.btn_editar)
        
        self.btn_eliminar = QPushButton("Eliminar")
        self.btn_eliminar.clicked.connect(self._eliminar_tipo)
        layout_botones.addWidget(self.btn_eliminar)
        
        layout_botones.addStretch()
        layout.addLayout(layout_botones)

        # Tabla de tipos
        self.tabla_tipos = QTableWidget()
        self.tabla_tipos.setColumnCount(4)
        self.tabla_tipos.setHorizontalHeaderLabels([
            "Código", "Nombre", "Descripción", "Activo"
        ])
        
        # Configurar tabla
        header = self.tabla_tipos.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        
        self.tabla_tipos.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.tabla_tipos.setAlternatingRowColors(True)
        self.tabla_tipos.doubleClicked.connect(self._editar_tipo)
        
        layout.addWidget(self.tabla_tipos)

        # Botones de diálogo
        botones = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        botones.rejected.connect(self.reject)
        layout.addWidget(botones)

    def _cargar_tipos(self):
        """Carga los tipos de accesorios en la tabla."""
        db = next(get_db())
        try:
            tipos = db.query(TipoAccesorio).order_by(TipoAccesorio.nombre).all()
            
            self.tabla_tipos.setRowCount(len(tipos))
            
            for fila, tipo in enumerate(tipos):
                # Código
                item_codigo = QTableWidgetItem(tipo.codigo)
                item_codigo.setData(Qt.ItemDataRole.UserRole, tipo.id)
                self.tabla_tipos.setItem(fila, 0, item_codigo)
                
                # Nombre
                self.tabla_tipos.setItem(fila, 1, QTableWidgetItem(tipo.nombre))
                
                # Descripción
                self.tabla_tipos.setItem(fila, 2, QTableWidgetItem(tipo.descripcion or ""))
                
                # Activo
                activo_text = "Sí" if tipo.activo else "No"
                self.tabla_tipos.setItem(fila, 3, QTableWidgetItem(activo_text))
                
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Error al cargar tipos de accesorios: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        finally:
            db.close()

    @pyqtSlot()
    def _nuevo_tipo(self):
        """Abre el diálogo para crear un nuevo tipo."""
        dialogo = TipoAccesorioEditarDialog(self)
        if dialogo.exec() == QDialog.DialogCode.Accepted:
            self._cargar_tipos()

    @pyqtSlot()
    def _editar_tipo(self):
        """Abre el diálogo para editar el tipo seleccionado."""
        fila_actual = self.tabla_tipos.currentRow()
        if fila_actual < 0:
            QMessageBox.information(
                self,
                "Información",
                "Por favor, selecciona un tipo para editar.",
                QMessageBox.StandardButton.Ok
            )
            return

        tipo_id = self.tabla_tipos.item(fila_actual, 0).data(Qt.ItemDataRole.UserRole)
        
        db = next(get_db())
        try:
            tipo = db.query(TipoAccesorio).filter(TipoAccesorio.id == tipo_id).first()
            if tipo:
                dialogo = TipoAccesorioEditarDialog(self, tipo)
                if dialogo.exec() == QDialog.DialogCode.Accepted:
                    self._cargar_tipos()
        finally:
            db.close()

    @pyqtSlot()
    def _eliminar_tipo(self):
        """Elimina el tipo seleccionado."""
        fila_actual = self.tabla_tipos.currentRow()
        if fila_actual < 0:
            QMessageBox.information(
                self,
                "Información",
                "Por favor, selecciona un tipo para eliminar.",
                QMessageBox.StandardButton.Ok
            )
            return

        codigo = self.tabla_tipos.item(fila_actual, 0).text()
        
        respuesta = QMessageBox.question(
            self,
            "Confirmar eliminación",
            f"¿Estás seguro de que quieres eliminar el tipo '{codigo}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if respuesta == QMessageBox.StandardButton.Yes:
            tipo_id = self.tabla_tipos.item(fila_actual, 0).data(Qt.ItemDataRole.UserRole)
            
            db = next(get_db())
            try:
                tipo = db.query(TipoAccesorio).filter(TipoAccesorio.id == tipo_id).first()
                if tipo:
                    db.delete(tipo)
                    db.commit()
                    self._cargar_tipos()
            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self,
                    "Error",
                    f"No se pudo eliminar el tipo: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )
            finally:
                db.close()


class TipoAccesorioEditarDialog(QDialog):
    """Diálogo para editar o crear un tipo de accesorio."""
    
    def __init__(self, parent=None, tipo=None):
        super().__init__(parent)
        self.tipo = tipo
        self.setWindowTitle("Nuevo Tipo de Accesorio" if tipo is None else f"Editar Tipo: {tipo.codigo}")
        
        # Configurar tamaño del diálogo
        self.resize(600, 400)
        
        self._configurar_ui()
        
        if tipo:
            self._cargar_datos_tipo()

    def _configurar_ui(self):
        """Configura la interfaz de usuario."""
        layout = QVBoxLayout(self)

        # Formulario
        layout_formulario = QFormLayout()

        # Código
        self.codigo = QLineEdit()
        self.codigo.setMaxLength(10)
        layout_formulario.addRow("Código*:", self.codigo)

        # Nombre
        self.nombre = QLineEdit()
        self.nombre.setMaxLength(50)
        layout_formulario.addRow("Nombre*:", self.nombre)

        # Descripción
        self.descripcion = QTextEdit()
        self.descripcion.setMaximumHeight(100)
        layout_formulario.addRow("Descripción:", self.descripcion)

        # Activo
        self.activo = QCheckBox("Activo")
        self.activo.setChecked(True)
        layout_formulario.addRow("Estado:", self.activo)

        layout.addLayout(layout_formulario)

        # Botones
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        botones.accepted.connect(self._guardar)
        botones.rejected.connect(self.reject)
        layout.addWidget(botones)

    def _cargar_datos_tipo(self):
        """Carga los datos del tipo en el formulario."""
        if self.tipo:
            self.codigo.setText(self.tipo.codigo)
            self.nombre.setText(self.tipo.nombre)
            self.descripcion.setPlainText(self.tipo.descripcion or "")
            self.activo.setChecked(self.tipo.activo)

    def _guardar(self):
        """Guarda el tipo de accesorio."""
        # Validar campos obligatorios
        if not self.codigo.text().strip():
            QMessageBox.warning(self, "Error", "El código es obligatorio.")
            return
        
        if not self.nombre.text().strip():
            QMessageBox.warning(self, "Error", "El nombre es obligatorio.")
            return

        db = next(get_db())
        try:
            if self.tipo:
                # Editar tipo existente
                self.tipo.codigo = self.codigo.text().strip()
                self.tipo.nombre = self.nombre.text().strip()
                self.tipo.descripcion = self.descripcion.toPlainText().strip() or None
                self.tipo.activo = self.activo.isChecked()
            else:
                # Crear nuevo tipo
                nuevo_tipo = TipoAccesorio(
                    codigo=self.codigo.text().strip(),
                    nombre=self.nombre.text().strip(),
                    descripcion=self.descripcion.toPlainText().strip() or None,
                    activo=self.activo.isChecked()
                )
                db.add(nuevo_tipo)
            
            db.commit()
            self.accept()
            
        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self,
                "Error",
                f"Error al guardar el tipo: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        finally:
            db.close()
