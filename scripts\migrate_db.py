"""
Script para ejecutar migraciones manualmente.
"""
import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from alembic.config import Config
from alembic import command

def run_migrations():
    # Configurar la ruta a la base de datos
    db_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'data', 'database.db'))
    sqlalchemy_url = f'sqlite:///{db_path}'
    
    # Configurar Alembic
    config = Config()
    config.set_main_option('script_location', os.path.join(os.path.dirname(__file__), '..', 'src', 'migrations'))
    config.set_main_option('sqlalchemy.url', sqlalchemy_url)
    
    # Ejecutar la migración
    print("Ejecutando migraciones...")
    command.upgrade(config, 'head')
    print("Migraciones completadas exitosamente.")

if __name__ == "__main__":
    run_migrations()
