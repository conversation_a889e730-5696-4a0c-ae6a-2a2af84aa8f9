"""
<PERSON><PERSON><PERSON><PERSON> que define el diálogo para gestionar persianas.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QGroupBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox, QFormLayout,
    QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox, QDialogButtonBox,
    QSizePolicy, QColorDialog, QScrollArea, QWidget, QFrame, QTabWidget, QTextEdit,
    QGridLayout, QSlider
)
from PyQt6.QtGui import QColor
from PyQt6.QtCore import Qt, pyqtSlot, pyqtSignal
from PyQt6.QtGui import QIcon

from models.base import get_db
from models.persiana import Persiana
from ui.utils.window_utils import setup_maximized_dialog

class PersianaDialog(QDialog):
    """Diálogo para gestionar persianas."""
    persiana_actualizada = pyqtSignal()
    
    def __init__(self, parent=None):
        """
        Inicializa el diálogo de gestión de persianas.

        Args:
            parent: Widget padre
        """
        super().__init__(parent)
        self.setWindowTitle("Gestión de Persianas")

        # ✅ CONFIGURACIÓN SIMPLE Y EFECTIVA
        setup_maximized_dialog(self, "Gestión de Persianas")

        # Variables
        self.persiana_actual = None

        # Configurar interfaz
        self._configurar_ui()

        # Cargar datos
        self._cargar_persianas()
    
    def _configurar_ui(self):
        """Configura la interfaz de usuario del diálogo."""
        # Configuración básica del diálogo
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowType.WindowContextHelpButtonHint)
        
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(12, 12, 12, 12)
        main_layout.setSpacing(12)
        
        # Barra de herramientas
        tool_bar = QWidget()
        tool_bar.setStyleSheet("""
            QWidget {
                background-color: #f5f5f5;
                border-radius: 4px;
                padding: 6px;
            }
        """)
        
        layout_botones = QHBoxLayout(tool_bar)
        layout_botones.setContentsMargins(4, 4, 4, 4)
        
        # Botones con iconos
        self.boton_nuevo = QPushButton("Nueva")
        self.boton_editar = QPushButton("Editar")
        self.boton_eliminar = QPushButton("Eliminar")
        self.boton_actualizar = QPushButton("Actualizar")
        
        # Estilo para los botones
        button_style = """
            QPushButton {
                background-color: #4a90e2;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                min-width: 80px;
                margin-right: 6px;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
            QPushButton:hover {
                background-color: #3a80d2;
            }
            QPushButton:pressed {
                background-color: #2a70c2;
            }
        """
        
        self.boton_nuevo.setStyleSheet(button_style)
        self.boton_editar.setStyleSheet(button_style)
        self.boton_eliminar.setStyleSheet(button_style)
        self.boton_actualizar.setStyleSheet(button_style)
        
        # Deshabilitar botones hasta que se seleccione una persiana
        self.boton_editar.setEnabled(False)
        self.boton_eliminar.setEnabled(False)
        
        layout_botones.addWidget(self.boton_nuevo)
        layout_botones.addWidget(self.boton_editar)
        layout_botones.addWidget(self.boton_eliminar)
        layout_botones.addStretch(1)
        layout_botones.addWidget(self.boton_actualizar)
        
        # Tabla de persianas
        self.tabla_persianas = QTableWidget()
        self.tabla_persianas.setColumnCount(8)
        self.tabla_persianas.setHorizontalHeaderLabels([
            "Referencia", "Nombre", "Altura (mm)", "Tipo Cajón", 
            "Operación", "Mando", "Color", "Activo"
        ])
        
        # Configurar la tabla
        self.tabla_persianas.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.tabla_persianas.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.tabla_persianas.setAlternatingRowColors(True)
        self.tabla_persianas.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #dddddd;
                border-radius: 4px;
                gridline-color: #eeeeee;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                padding: 6px;
                border: none;
                border-right: 1px solid #dddddd;
                border-bottom: 1px solid #dddddd;
            }
            QHeaderView::section:last {
                border-right: none;
            }
            QTableWidget::item {
                padding: 4px;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #000000;
            }
        """)
        
        # Ajustar el ancho de las columnas
        header = self.tabla_persianas.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # Referencia
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Nombre
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Altura
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Tipo Cajón
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Operación
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # Mando
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # Color
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)  # Activo
        
        # Conectar señales
        self.boton_nuevo.clicked.connect(self._on_nueva_persiana)
        self.boton_editar.clicked.connect(self._on_editar_persiana)
        self.boton_eliminar.clicked.connect(self._on_eliminar_persiana)
        self.boton_actualizar.clicked.connect(self._cargar_persianas)
        self.tabla_persianas.itemSelectionChanged.connect(self._on_seleccion_cambiada)
        self.tabla_persianas.doubleClicked.connect(self._on_editar_persiana)
        
        # Añadir widgets al layout principal
        main_layout.addWidget(tool_bar)
        main_layout.addWidget(self.tabla_persianas, 1)  # El 1 hace que la tabla se expanda
        
        # Botones de diálogo
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        button_box.rejected.connect(self.reject)
        button_box.setStyleSheet("""
            QDialogButtonBox {
                border-top: 1px solid #dddddd;
                padding-top: 10px;
            }
        """)
        main_layout.addWidget(button_box)
    
    def _cargar_persianas(self):
        """Carga la lista de persianas desde la base de datos."""
        db = next(get_db())
        
        try:
            # Obtener todas las persianas
            persianas = db.query(Persiana).order_by(Persiana.referencia).all()
            
            # Configurar la tabla
            self.tabla_persianas.setRowCount(len(persianas))
            
            for fila, persiana in enumerate(persianas):
                # Referencia
                self.tabla_persianas.setItem(fila, 0, QTableWidgetItem(persiana.referencia or ""))
                
                # Nombre
                self.tabla_persianas.setItem(fila, 1, QTableWidgetItem(persiana.nombre or ""))
                
                # Altura del cajón (mm)
                altura_item = QTableWidgetItem(str(persiana.altura_cajon) if persiana.altura_cajon is not None else "")
                altura_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.tabla_persianas.setItem(fila, 2, altura_item)
                
                # Tipo de cajón
                self.tabla_persianas.setItem(fila, 3, QTableWidgetItem(persiana.tipo_cajon or ""))
                
                # Tipo de operación
                self.tabla_persianas.setItem(fila, 4, QTableWidgetItem(persiana.tipo_operacion or ""))
                
                # Posición del mando
                self.tabla_persianas.setItem(fila, 5, QTableWidgetItem(persiana.posicion_mando or ""))
                
                # Color
                self.tabla_persianas.setItem(fila, 6, QTableWidgetItem(persiana.color or ""))
                
                # Estado (Activo/Inactivo)
                activo_item = QTableWidgetItem()
                activo_item.setFlags(Qt.ItemFlag.ItemIsUserCheckable | Qt.ItemFlag.ItemIsEnabled)
                activo_item.setCheckState(
                    Qt.CheckState.Checked if persiana.activo else Qt.CheckState.Unchecked
                )
                activo_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.tabla_persianas.setItem(fila, 7, activo_item)
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudieron cargar las persianas: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        finally:
            db.close()
    
    def _on_seleccion_cambiada(self):
        """Se ejecuta cuando cambia la selección en la tabla."""
        seleccion = self.tabla_persianas.selectedItems()
        habilitar = len(seleccion) > 0
        
        self.boton_editar.setEnabled(habilitar)
        self.boton_eliminar.setEnabled(habilitar)
        
        if habilitar:
            fila = seleccion[0].row()
            self.persiana_actual = self.tabla_persianas.item(fila, 0).data(Qt.ItemDataRole.UserRole)
        else:
            self.persiana_actual = None
            
    def _on_nueva_persiana(self):
        """Abre el diálogo para crear una nueva persiana."""
        dialogo = PersianaEditarDialog(self)
        if dialogo.exec() == QDialog.DialogCode.Accepted:
            datos = dialogo.get_datos()
            session = next(get_db())
            try:
                existe = session.query(Persiana).filter_by(referencia=datos['referencia']).first()
                if existe:
                    QMessageBox.warning(
                        self, 
                        "Error", 
                        f"Ya existe una persiana con la referencia: {datos['referencia']}"
                    )
                    return
                # Crear la nueva persiana
                persiana = Persiana(
                    referencia=datos['referencia'],
                    nombre=datos['nombre'],
                    altura_cajon=datos['altura_cajon'],
                    tipo_cajon=datos['tipo_cajon'],
                    tipo_operacion=datos['tipo_operacion'],
                    posicion_mando=datos['posicion_mando'],
                    color=datos['color'],
                    activo=datos['activo']
                )
                session.add(persiana)
                session.commit()
                # Mostrar mensaje de éxito
                referencia = datos['referencia']
                QMessageBox.information(
                    self,
                    "Éxito",
                    f"Persiana '{referencia}' creada correctamente.",
                    QMessageBox.StandardButton.Ok
                )
                # Actualizar la tabla
                self._cargar_persianas()
                # Notificar que se ha actualizado
                self.persiana_actualizada.emit()
            except Exception as e:
                session.rollback()
                QMessageBox.critical(
                    self, 
                    "Error al guardar", 
                    f"No se pudo guardar la persiana: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )
                return
            finally:
                session.close()
    
    def _on_editar_persiana(self):
        """Maneja el evento de editar una persiana existente."""
        fila = self.tabla_persianas.currentRow()
        if fila < 0:
            return

        referencia = self.tabla_persianas.item(fila, 0).text()
        db = next(get_db())

        try:
            # Buscar la persiana por referencia en lugar de código
            persiana = db.query(Persiana).filter(Persiana.referencia == referencia).first()
            if not persiana:
                raise ValueError("Persiana no encontrada")

            dialogo = PersianaEditarDialog(self, persiana)
            if dialogo.exec() == QDialog.DialogCode.Accepted:
                # Actualizar la persiana existente
                datos = dialogo.get_datos()

                # Actualizar solo los campos que existen en el modelo actual
                persiana.referencia = datos.get('referencia', '')
                persiana.nombre = datos.get('nombre', '')
                persiana.altura_cajon = datos.get('altura_cajon', 0)
                persiana.tipo_cajon = datos.get('tipo_cajon', 'normal')
                persiana.tipo_operacion = datos.get('tipo_operacion', 'recogedor')
                persiana.posicion_mando = datos.get('posicion_mando')
                persiana.color = datos.get('color', '')
                persiana.activo = datos.get('activo', True)
                
                db.commit()
                
                # Actualizar la tabla
                self._cargar_persianas()
                
                # Notificar que se ha actualizado
                self.persiana_actualizada.emit()
                
        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self,
                "Error al guardar",
                f"No se pudo guardar la persiana: {str(e)}"
            )
        finally:
            db.close()
    
    def _on_eliminar_persiana(self):
        """Elimina la persiana seleccionada después de confirmación."""
        if not self.persiana_actual:
            return
            
        # Obtener la referencia de la persiana para mostrarla en el mensaje
        with get_db() as session:
            persiana = session.get(Persiana, self.persiana_actual)
            if not persiana:
                return
                
            # Pedir confirmación
            respuesta = QMessageBox.question(
                self,
                "Confirmar eliminación",
                f"¿Está seguro de que desea eliminar la persiana '{persiana.referencia}'?\n"
                "Esta acción no se puede deshacer.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if respuesta == QMessageBox.StandardButton.Yes:
                try:
                    session.delete(persiana)
                    session.commit()
                    
                    # Actualizar la tabla
                    self._cargar_persianas()
                    
                    # Notificar que se ha actualizado
                    self.persiana_actualizada.emit()
                    
                except Exception as e:
                    session.rollback()
                    QMessageBox.critical(
                        self,
                        "Error al eliminar",
                        f"No se pudo eliminar la persiana: {str(e)}"
                    )


class PersianaEditarDialog(QDialog):
    """Diálogo para editar o crear una persiana."""
    
    def __init__(self, parent=None, persiana=None):
        """
        Inicializa el diálogo de edición de persiana.
        
        Args:
            parent: Widget padre
            persiana: Instancia de Persiana a editar (None para nueva)
        """
        super().__init__(parent, 
                       Qt.WindowType.Window | 
                       Qt.WindowType.WindowMinimizeButtonHint | 
                       Qt.WindowType.WindowMaximizeButtonHint | 
                       Qt.WindowType.WindowCloseButtonHint)
        
        self.persiana = persiana
        self.setWindowTitle("Editar Persiana" if persiana else "Nueva Persiana")
        
        # Configurar interfaz
        self._configurar_ui()
        
        # Si se está editando una persiana, cargar sus datos
        if self.persiana:
            self._cargar_datos_persiana()
    
    def _configurar_ui(self):
        """Configura la interfaz de usuario del diálogo (versión moderna, columnas y scroll)."""
        self.setWindowFlags(Qt.WindowType.Dialog |
                            Qt.WindowType.WindowTitleHint |
                            Qt.WindowType.WindowCloseButtonHint |
                            Qt.WindowType.WindowMinMaxButtonsHint)

        # Layout principal con scroll
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(15)

        # Crear área de scroll
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)

        # Widget contenedor para el área de scroll
        scroll_widget = QWidget()
        scroll_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # Layout principal con dos columnas
        scroll_layout = QHBoxLayout(scroll_widget)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(20)

        # Columna izquierda
        col_izq = QVBoxLayout()
        col_izq.setContentsMargins(0, 0, 0, 0)
        col_izq.setSpacing(15)

        # Columna derecha
        col_der = QVBoxLayout()
        col_der.setContentsMargins(0, 0, 0, 0)
        col_der.setSpacing(15)

        # --- COLUMNA IZQUIERDA ---
        # Grupo de datos básicos
        grupo_datos = QGroupBox("Datos Básicos")
        form_layout = QFormLayout(grupo_datos)
        form_layout.setContentsMargins(20, 30, 20, 20)
        form_layout.setHorizontalSpacing(15)
        form_layout.setVerticalSpacing(12)
        form_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        form_layout.setFormAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)

        # Campo de referencia
        label_referencia = QLabel("Referencia:")
        self.campo_referencia = QLineEdit()
        self.campo_referencia.setPlaceholderText("Ej: PER-001")
        form_layout.addRow(label_referencia, self.campo_referencia)

        # Campo de nombre
        label_nombre = QLabel("Nombre:")
        self.campo_nombre = QLineEdit()
        self.campo_nombre.setPlaceholderText("Ej: Persiana de aluminio con motor")
        form_layout.addRow(label_nombre, self.campo_nombre)

        # Grupo de características del cajón
        grupo_cajon = QGroupBox("Características del Cajón")
        cajon_layout = QFormLayout(grupo_cajon)
        cajon_layout.setContentsMargins(20, 30, 20, 20)
        cajon_layout.setHorizontalSpacing(15)
        cajon_layout.setVerticalSpacing(12)
        cajon_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        cajon_layout.setFormAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)

        # Altura del cajón
        label_altura = QLabel("Altura (mm):")
        self.combo_altura = QComboBox()
        self.combo_altura.addItems([str(h) for h in Persiana.ALTURAS_CAJON])
        cajon_layout.addRow(label_altura, self.combo_altura)

        # Tipo de cajón
        label_tipo = QLabel("Tipo:")
        self.combo_tipo = QComboBox()
        self.combo_tipo.addItems(["Normal", "Decorativo"])
        cajon_layout.addRow(label_tipo, self.combo_tipo)

        # Añadir grupos a la columna izquierda
        col_izq.addWidget(grupo_datos)
        col_izq.addWidget(grupo_cajon)
        col_izq.addStretch()

        # --- COLUMNA DERECHA ---
        # Grupo de operación
        grupo_operacion = QGroupBox("Tipo de Operación")
        operacion_layout = QFormLayout(grupo_operacion)
        operacion_layout.setContentsMargins(20, 30, 20, 20)
        operacion_layout.setHorizontalSpacing(15)
        operacion_layout.setVerticalSpacing(12)
        operacion_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        operacion_layout.setFormAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)

        # Tipo de operación
        label_operacion = QLabel("Tipo:")
        self.combo_operacion = QComboBox()
        self.combo_operacion.addItems(["Motorizado", "Con Recogedor"])
        self.combo_operacion.currentTextChanged.connect(self._actualizar_visibilidad_posicion)
        operacion_layout.addRow(label_operacion, self.combo_operacion)

        # Posición del mando
        label_posicion = QLabel("Posición del mando:")
        self.combo_posicion = QComboBox()
        self.combo_posicion.addItems(["Izquierda", "Derecha"])
        operacion_layout.addRow(label_posicion, self.combo_posicion)

        # Grupo de apariencia
        grupo_apariencia = QGroupBox("Apariencia")
        apariencia_layout = QFormLayout(grupo_apariencia)
        apariencia_layout.setContentsMargins(20, 30, 20, 20)
        apariencia_layout.setHorizontalSpacing(15)
        apariencia_layout.setVerticalSpacing(12)
        apariencia_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        apariencia_layout.setFormAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)

        # Color
        self.color_combo = QComboBox()
        self.color_combo.addItems(["Blanco", "Negro", "Plata", "Bronce", "Personalizado"])
        apariencia_layout.addRow("Color*:", self.color_combo)

        # Grupo de estado
        grupo_estado = QGroupBox("Estado")
        estado_layout = QVBoxLayout(grupo_estado)
        estado_layout.setContentsMargins(15, 25, 15, 20)
        self.check_activo = QCheckBox("Activo")
        self.check_activo.setChecked(True)
        estado_layout.addWidget(self.check_activo)

        # Añadir grupos a la columna derecha
        col_der.addWidget(grupo_operacion)
        col_der.addWidget(grupo_apariencia)
        col_der.addWidget(grupo_estado)
        col_der.addStretch()

        # Añadir columnas al layout principal
        scroll_layout.addLayout(col_izq, 1)
        scroll_layout.addLayout(col_der, 1)

        # Configurar el widget en el área de desplazamiento
        scroll_area.setWidget(scroll_widget)
        main_layout.addWidget(scroll_area, 1)

        # Botones de diálogo
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel
        )
        botones.accepted.connect(self._validar_y_aceptar)
        botones.rejected.connect(self.reject)
        main_layout.addWidget(botones)

        # Conectar señales para validación en tiempo real
        self.campo_referencia.textChanged.connect(self._validar_formulario)
        self.campo_nombre.textChanged.connect(self._validar_formulario)
        self.color_combo.currentTextChanged.connect(self._validar_formulario)
        self.combo_posicion.currentTextChanged.connect(self._validar_formulario)

        # Aplicar estilos unificados
        self.setStyleSheet(self._cargar_estilos())

    def _cargar_estilos(self):
        """Carga los estilos CSS para el diálogo."""
        return """
            QDialog {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 14px;
            }
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
                background: white;
            }
            QTabBar::tab {
                padding: 8px 15px;
                border: 1px solid #dee2e6;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                margin-right: 2px;
                background: #f1f1f1;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom: 2px solid #0d6efd;
                color: #0d6efd;
            }
            QGroupBox {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                margin-top: 15px;
                padding: 10px 15px 15px 15px;
                background: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
                color: #495057;
                font-weight: bold;
            }
            QLabel {
                color: #495057;
                padding: 2px;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QTextEdit {
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 6px;
                min-height: 28px;
                background: white;
            }
            QLineEdit:focus, QComboBox:focus,
            QSpinBox:focus, QDoubleSpinBox:focus {
                border: 2px solid #86b7fe;
                background-color: #f0f7ff;
            }
            QLineEdit[error="true"] {
                border: 2px solid #dc3545;
                background-color: #fff5f5;
            }
            QLabel[error="true"] {
                color: #dc3545;
            }
        """

    def _configurar_tab_espec(self, parent):
        """Configura la pestaña de especificaciones técnicas."""
        layout = QVBoxLayout(parent)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)

        # Grupo: Materiales
        grupo_materiales = QGroupBox("Materiales")
        form_materiales = QFormLayout()
        form_materiales.setContentsMargins(15, 15, 15, 15)
        form_materiales.setSpacing(10)
        
        self.material_laminas = QComboBox()
        self.material_laminas.addItems(["Aluminio", "PVC", "Madera", "Composite"])
        form_materiales.addRow("Material de las láminas:", self.material_laminas)
        
        self.color_laminas = QPushButton("Seleccionar color")
        self.color_laminas.clicked.connect(self._seleccionar_color)
        form_materiales.addRow("Color:", self.color_laminas)
        
        self.acabado = QComboBox()
        self.acabado.addItems(["Mate", "Brillante", "Texturizado", "Madera"])
        form_materiales.addRow("Acabado:", self.acabado)
        
        grupo_materiales.setLayout(form_materiales)
        layout.addWidget(grupo_materiales)

        # Grupo: Características técnicas
        grupo_tecnico = QGroupBox("Características Técnicas")
        form_tecnico = QFormLayout()
        form_tecnico.setContentsMargins(15, 15, 15, 15)
        form_tecnico.setSpacing(10)
        
        self.aislamiento = QComboBox()
        self.aislamiento.addItems(["Básico", "Reforzado", "Térmico", "Acústico"])
        form_tecnico.addRow("Nivel de aislamiento:", self.aislamiento)
        
        self.orientacion = QComboBox()
        self.orientacion.addItems(["Interior", "Exterior"])
        form_tecnico.addRow("Orientación:", self.orientacion)
        
        self.resistencia_viento = QComboBox()
        self.resistencia_viento.addItems(["Baja", "Media", "Alta", "Muy alta"])
        form_tecnico.addRow("Resistencia al viento:", self.resistencia_viento)
        
        grupo_tecnico.setLayout(form_tecnico)
        layout.addWidget(grupo_tecnico)
        
        # Espaciador para empujar todo hacia arriba
        layout.addStretch()

    def _configurar_tab_info(self, parent):
        """Configura la pestaña de información básica."""
        layout = QVBoxLayout(parent)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)

        # Grupo: Identificación y apariencia
        grupo_id = QGroupBox("Identificación y Apariencia")
        form_id = QFormLayout()
        form_id.setContentsMargins(15, 15, 15, 15)
        form_id.setSpacing(10)
        
        self.campo_referencia = QLineEdit()
        self.campo_referencia.setPlaceholderText("Ej: PER-001")
        form_id.addRow("Referencia*:", self.campo_referencia)
        
        # Usar self.campo_nombre en vez de self.nombre_edit
        self.campo_nombre = QLineEdit()
        self.campo_nombre.setPlaceholderText("Nombre descriptivo")
        form_id.addRow("Nombre*:", self.campo_nombre)
        
        self.descripcion_edit = QTextEdit()
        self.descripcion_edit.setMaximumHeight(100)
        self.descripcion_edit.setPlaceholderText("Descripción detallada")
        form_id.addRow("Descripción:", self.descripcion_edit)

        # Añadir selector de color
        self.color_combo = QComboBox()
        self.color_combo.addItems(["Blanco", "Negro", "Plata", "Bronce", "Personalizado"])
        form_id.addRow("Color*:", self.color_combo)
        
        grupo_id.setLayout(form_id)
        layout.addWidget(grupo_id)

        # Grupo: Dimensiones
        grupo_dim = QGroupBox("Dimensiones (metros)")
        form_dim = QFormLayout()
        form_dim.setContentsMargins(15, 15, 15, 15)
        form_dim.setSpacing(10)
        
        self.ancho_spin = QDoubleSpinBox()
        self.ancho_spin.setRange(0.1, 10.0)
        self.ancho_spin.setSuffix(" m")
        self.ancho_spin.setSingleStep(0.1)
        form_dim.addRow("Ancho*:", self.ancho_spin)
        
        self.alto_spin = QDoubleSpinBox()
        self.alto_spin.setRange(0.1, 10.0)
        self.alto_spin.setSuffix(" m")
        self.alto_spin.setSingleStep(0.1)
        form_dim.addRow("Alto*:", self.alto_spin)
        
        grupo_dim.setLayout(form_dim)
        layout.addWidget(grupo_dim)
        
        # Grupo: Estado
        grupo_estado = QGroupBox("Estado")
        hbox = QHBoxLayout()
        hbox.setContentsMargins(15, 10, 15, 15)
        
        self.activo_check = QCheckBox("Activo")
        self.activo_check.setChecked(True)
        hbox.addWidget(self.activo_check)
        
        grupo_estado.setLayout(hbox)
        layout.addWidget(grupo_estado)
        
        layout.addStretch(1)

    def _configurar_tab_tecnica(self, parent):
        """Configura la pestaña de características técnicas."""
        layout = QVBoxLayout(parent)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(15)

        # Grupo: Materiales (requerido)
        grupo_material = QGroupBox("Materiales *")
        grupo_material.setProperty("required", True)
        form_material = QFormLayout()
        form_material.setContentsMargins(12, 15, 12, 15)
        form_material.setSpacing(12)
        form_material.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        
        self.material_combo = QComboBox()
        self.material_combo.addItems(["Aluminio", "PVC", "Madera", "Composite"])
        form_material.addRow(QLabel("<span style='color: #dc3545'>*</span> Material:"), self.material_combo)
        
        self.color_combo = QComboBox()
        self.color_combo.addItems(["Blanco", "Negro", "Madera", "Gris", "Personalizado"])
        form_material.addRow("Color:", self.color_combo)
        
        grupo_material.setLayout(form_material)
        layout.addWidget(grupo_material)

        # Grupo: Mecanismo (requerido)
        grupo_mecanismo = QGroupBox("Mecanismo *")
        grupo_mecanismo.setProperty("required", True)
        form_mecanismo = QFormLayout()
        form_mecanismo.setContentsMargins(12, 15, 12, 15)
        form_mecanismo.setSpacing(12)
        form_mecanismo.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        
        self.tipo_mecanismo_combo = QComboBox()
        self.tipo_mecanismo_combo.addItems(["Manual", "Motorizado", "Automático"])
        form_mecanismo.addRow(QLabel("<span style='color: #dc3545'>*</span> Tipo:"), self.tipo_mecanismo_combo)
        
        self.sistema_control_combo = QComboBox()
        self.sistema_control_combo.addItems(["Cinta", "Cadena", "Mando", "App"])
        form_mecanismo.addRow("Control:", self.sistema_control_combo)
        
        grupo_mecanismo.setLayout(form_mecanismo)
        layout.addWidget(grupo_mecanismo)
        
        layout.addStretch()

    def _configurar_tab_config(self, parent):
        """Configura la pestaña de configuración."""
        layout = QVBoxLayout(parent)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        # Grupo: Opciones
        grupo_opciones = QGroupBox("Opciones")
        form_opciones = QFormLayout()
        form_opciones.setContentsMargins(10, 15, 10, 15)
        form_opciones.setSpacing(10)
        
        self.opacidad_slider = QSlider(Qt.Orientation.Horizontal)
        self.opacidad_slider.setRange(0, 100)
        self.opacidad_slider.setToolTip("Controla la cantidad de luz que deja pasar la persiana")
        form_opciones.addRow("Opacidad:", self.opacidad_slider)
        
        self.orientacion_combo = QComboBox()
        self.orientacion_combo.addItems(["Interior", "Exterior"])
        self.orientacion_combo.setToolTip("Determina si la persiana se instala interior o exteriormente")
        form_opciones.addRow("Orientación:", self.orientacion_combo)
        
        self.aislamiento_check = QCheckBox("Refuerzo de aislamiento")
        self.aislamiento_check.setToolTip("Activa el refuerzo térmico y acústico")
        form_opciones.addRow(self.aislamiento_check)
        
        grupo_opciones.setLayout(form_opciones)
        layout.addWidget(grupo_opciones)

        # Grupo: Personalización
        grupo_personal = QGroupBox("Personalización")
        form_personal = QFormLayout()
        form_personal.setContentsMargins(10, 15, 10, 15)
        form_personal.setSpacing(10)
        
        self.patron_combo = QComboBox()
        self.patron_combo.addItems(["Liso", "Rayas", "Geométrico", "Personalizado"])
        self.patron_combo.setToolTip("Selecciona el patrón visual de la persiana")
        form_personal.addRow("Patrón:", self.patron_combo)
        
        self.textura_combo = QComboBox()
        self.textura_combo.addItems(["Lisa", "Relieve", "Madera", "Metal"])
        self.textura_combo.setToolTip("Define la textura superficial del material")
        form_personal.addRow("Textura:", self.textura_combo)
        
        grupo_personal.setLayout(form_personal)
        layout.addWidget(grupo_personal)

        # Conexión de señales para validación
        self.codigo_edit.textChanged.connect(self._validar_codigo)
        self.nombre_edit.textChanged.connect(self._validar_nombre)
        self.ancho_spin.valueChanged.connect(self._validar_dimensiones)
        self.alto_spin.valueChanged.connect(self._validar_dimensiones)
        
        layout.addStretch()

    def _validar_codigo(self):
        """Valida que la referencia tenga el formato correcto."""
        codigo = self.campo_referencia.text().strip()
        valido = len(codigo) >= 3 and codigo.isalnum()
        self.campo_referencia.setProperty("error", not valido)
        self.campo_referencia.style().polish(self.campo_referencia)
        return valido

    def _validar_nombre(self):
        """Valida que el nombre no esté vacío."""
        nombre = self.campo_nombre.text().strip()
        valido = len(nombre) > 0
        self.campo_nombre.setProperty("error", not valido)
        self.campo_nombre.style().polish(self.campo_nombre)
        return valido

    def _guardar_datos(self):
        """Guarda los datos del formulario en el modelo."""
        if not all([
            self._validar_codigo(),
            self._validar_nombre(),
            # Si tienes validación de dimensiones, agrégala aquí
        ]):
            return None
        # Aquí podrías crear y devolver una instancia de Persiana si lo necesitas
        return True

    def _cargar_datos(self, persiana):
        """Carga los datos de la persiana en el formulario."""
        if persiana:
            self.campo_referencia.setText(persiana.referencia)
            self.campo_nombre.setText(persiana.nombre)
            # ... resto de campos

    def accept(self):
        """Sobreescribe el método accept para validar antes de cerrar."""
        if self._guardar_datos():
            super().accept()

    def get_datos(self):
        """Retorna los datos del formulario como un diccionario completo."""
        # Mapeo para tipo_operacion
        operacion_map = {
            "Motorizado": "motorizado",
            "Con Recogedor": "recogedor"
        }
        return {
            'referencia': self.campo_referencia.text().strip(),
            'nombre': self.campo_nombre.text().strip(),
            'altura_cajon': int(self.combo_altura.currentText()) if self.combo_altura.currentText().isdigit() else None,
            'tipo_cajon': self.combo_tipo.currentText().strip().lower(),
            'tipo_operacion': operacion_map.get(self.combo_operacion.currentText().strip(), "motorizado"),
            'posicion_mando': self.combo_posicion.currentText().strip() if self.combo_posicion.isVisible() else None,
            'color': self.color_combo.currentText().strip(),
            'activo': self.check_activo.isChecked()
        }

    def _cargar_datos_persiana(self):
        """Carga los datos de la persiana en el formulario para edición."""
        p = self.persiana
        if p:
            self.campo_referencia.setText(p.referencia or "")
            self.campo_nombre.setText(p.nombre or "")
            if p.altura_cajon and str(p.altura_cajon) in [self.combo_altura.itemText(i) for i in range(self.combo_altura.count())]:
                self.combo_altura.setCurrentText(str(p.altura_cajon))
            if p.tipo_cajon:
                self.combo_tipo.setCurrentText(p.tipo_cajon)
            if p.tipo_operacion:
                self.combo_operacion.setCurrentText(p.tipo_operacion)
            if p.posicion_mando:
                self.combo_posicion.setCurrentText(p.posicion_mando)
            if p.color:
                self.color_combo.setCurrentText(p.color)
            self.check_activo.setChecked(p.activo if p.activo is not None else True)

    def _actualizar_visibilidad_posicion(self):
        """Muestra u oculta el campo de posición del mando según el tipo de operación."""
        if hasattr(self, 'combo_operacion') and hasattr(self, 'combo_posicion'):
            if self.combo_operacion.currentText() in ["Motorizado", "Con Recogedor"]:
                self.combo_posicion.setVisible(True)
            else:
                self.combo_posicion.setVisible(False)

    def _validar_formulario(self):
        """Valida que todos los campos obligatorios estén completos y correctos."""
        referencia = self.campo_referencia.text().strip()
        nombre = self.campo_nombre.text().strip()
        color = self.color_combo.currentText().strip()
        valido = True
        errores = []
        if not referencia:
            valido = False
            errores.append("La referencia es obligatoria.")
        if not nombre:
            valido = False
            errores.append("El nombre es obligatorio.")
        if not color:
            valido = False
            errores.append("El color es obligatorio.")
        # Puedes agregar más validaciones aquí si lo necesitas
        if not valido:
            QMessageBox.warning(self, "Campos obligatorios", "\n".join(errores))
        return valido

    def _validar_y_aceptar(self):
        """Valida el formulario y acepta el diálogo si todo está correcto."""
        if self._validar_formulario():
            self.accept()
