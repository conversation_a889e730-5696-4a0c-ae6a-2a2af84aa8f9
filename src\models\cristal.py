from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON><PERSON>, ForeignKey
from sqlalchemy.orm import relationship
from .base import Base

class Cristal(Base):
    """
    Modelo que representa un tipo de cristal en el sistema.
    """
    __tablename__ = 'cristales'
    
    id = Column(Integer, primary_key=True)
    codigo = Column(String(20), unique=True, nullable=False)
    descripcion = Column(String(100), nullable=False)
    espesor = Column(Float, nullable=False)  # en milímetros
    tipo = Column(String(50))  # Tipo de cristal
    precio_metro_cuadrado = Column(Float, nullable=False, default=0.0)
    stock = Column(Integer, default=0)  # Stock disponible
    activo = Column(Boolean, default=True)
    
    # Relaciones
    obras = relationship("Obra", back_populates="cristal")
    
    def __repr__(self):
        return f"<Cristal(codigo='{self.codigo}', descripcion='{self.descripcion}', espesor={self.espesor}mm)>"
    
    def to_dict(self):
        """Convierte el objeto a un diccionario."""
        return {
            'id': self.id,
            'codigo': self.codigo,
            'descripcion': self.descripcion,
            'espesor': self.espesor,
            'tipo': self.tipo,
            'precio_metro_cuadrado': self.precio_metro_cuadrado,
            'stock': self.stock,
            'activo': self.activo
        }
