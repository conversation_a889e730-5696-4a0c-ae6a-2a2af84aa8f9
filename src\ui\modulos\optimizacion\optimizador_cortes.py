"""
Optimizador de cortes para perfiles.
"""

import random
from models.base import get_db
from models.articulo import ObraArticulo


class OptimizadorCortes:
    """Clase para optimizar cortes de perfiles."""
    
    def __init__(self, obra, config):
        self.obra = obra
        self.config = config
        self.materiales_manuales = config.get('materiales_manuales', [])
    
    def optimizar(self):
        """Ejecuta la optimización de cortes."""
        # Obtener cortes necesarios
        cortes_necesarios = self._obtener_cortes_necesarios()
        
        if not cortes_necesarios:
            return {
                'barras': [],
                'eficiencia': 0.0,
                'desperdicio_total': 0.0,
                'coste_estimado': 0.0
            }
        
        # Ejecutar algoritmo de optimización
        barras = self._ejecutar_algoritmo(cortes_necesarios)
        
        # Calcular estadísticas
        estadisticas = self._calcular_estadisticas(barras)
        
        return {
            'barras': barras,
            'eficiencia': estadisticas['eficiencia'],
            'desperdicio_total': estadisticas['desperdicio_total'],
            'coste_estimado': estadisticas['coste_estimado']
        }
    
    def _obtener_cortes_necesarios(self):
        """Obtiene la lista de cortes necesarios."""
        cortes = []
        
        # Agregar materiales manuales si existen
        if self.materiales_manuales:
            for material in self.materiales_manuales:
                for _ in range(int(material['cantidad'])):
                    cortes.append({
                        'longitud': material['longitud'],
                        'perfil_codigo': material['perfil'].codigo,
                        'cantidad': 1
                    })
        else:
            # Obtener cortes de la obra
            db = next(get_db())
            try:
                obra_articulos = db.query(ObraArticulo).filter(
                    ObraArticulo.obra_id == self.obra.id
                ).all()
                
                for obra_articulo in obra_articulos:
                    if obra_articulo.articulo:
                        # Simular cortes basados en las dimensiones del artículo
                        altura = obra_articulo.altura
                        anchura = obra_articulo.anchura
                        cantidad = obra_articulo.cantidad
                        
                        # Generar cortes típicos para una ventana
                        cortes_articulo = [
                            {'longitud': altura, 'perfil_codigo': 'MARCO-V', 'descripcion': 'Marco vertical'},
                            {'longitud': altura, 'perfil_codigo': 'MARCO-V', 'descripcion': 'Marco vertical'},
                            {'longitud': anchura, 'perfil_codigo': 'MARCO-H', 'descripcion': 'Marco horizontal'},
                            {'longitud': anchura, 'perfil_codigo': 'MARCO-H', 'descripcion': 'Marco horizontal'},
                            {'longitud': altura - 50, 'perfil_codigo': 'HOJA-V', 'descripcion': 'Hoja vertical'},
                            {'longitud': altura - 50, 'perfil_codigo': 'HOJA-V', 'descripcion': 'Hoja vertical'},
                            {'longitud': anchura - 50, 'perfil_codigo': 'HOJA-H', 'descripcion': 'Hoja horizontal'},
                            {'longitud': anchura - 50, 'perfil_codigo': 'HOJA-H', 'descripcion': 'Hoja horizontal'},
                        ]
                        
                        # Multiplicar por la cantidad de artículos
                        for _ in range(cantidad):
                            cortes.extend(cortes_articulo)
                            
            finally:
                db.close()
        
        return cortes
    
    def _ejecutar_algoritmo(self, cortes_necesarios):
        """Ejecuta el algoritmo de optimización seleccionado."""
        algoritmo = self.config.get('algoritmo', 'Best Fit Decreasing (BFD)')
        
        if 'First Fit' in algoritmo:
            return self._first_fit_decreasing(cortes_necesarios)
        elif 'Best Fit' in algoritmo:
            return self._best_fit_decreasing(cortes_necesarios)
        elif 'Next Fit' in algoritmo:
            return self._next_fit_decreasing(cortes_necesarios)
        else:
            return self._best_fit_decreasing(cortes_necesarios)
    
    def _best_fit_decreasing(self, cortes):
        """Algoritmo Best Fit Decreasing."""
        # Ordenar cortes por longitud descendente
        cortes_ordenados = sorted(cortes, key=lambda x: x['longitud'], reverse=True)
        
        barras = []
        longitud_barra = self.config['longitud_barra']
        grosor_corte = self.config['grosor_corte']
        
        for corte in cortes_ordenados:
            mejor_barra = None
            mejor_desperdicio = float('inf')
            
            # Buscar la mejor barra (menor desperdicio)
            for barra in barras:
                espacio_usado = sum(c['longitud'] for c in barra['cortes'])
                espacio_cortes = len(barra['cortes']) * grosor_corte
                espacio_disponible = longitud_barra - espacio_usado - espacio_cortes
                
                if espacio_disponible >= corte['longitud'] + grosor_corte:
                    desperdicio = espacio_disponible - corte['longitud'] - grosor_corte
                    if desperdicio < mejor_desperdicio:
                        mejor_desperdicio = desperdicio
                        mejor_barra = barra
            
            if mejor_barra:
                mejor_barra['cortes'].append(corte)
                mejor_barra['longitud_usada'] = sum(c['longitud'] for c in mejor_barra['cortes'])
            else:
                # Crear nueva barra
                nueva_barra = {
                    'cortes': [corte],
                    'longitud_usada': corte['longitud']
                }
                barras.append(nueva_barra)
        
        return barras
    
    def _first_fit_decreasing(self, cortes):
        """Algoritmo First Fit Decreasing."""
        cortes_ordenados = sorted(cortes, key=lambda x: x['longitud'], reverse=True)
        
        barras = []
        longitud_barra = self.config['longitud_barra']
        grosor_corte = self.config['grosor_corte']
        
        for corte in cortes_ordenados:
            barra_encontrada = False
            
            # Buscar primera barra que pueda contener el corte
            for barra in barras:
                espacio_usado = sum(c['longitud'] for c in barra['cortes'])
                espacio_cortes = len(barra['cortes']) * grosor_corte
                espacio_disponible = longitud_barra - espacio_usado - espacio_cortes
                
                if espacio_disponible >= corte['longitud'] + grosor_corte:
                    barra['cortes'].append(corte)
                    barra['longitud_usada'] = sum(c['longitud'] for c in barra['cortes'])
                    barra_encontrada = True
                    break
            
            if not barra_encontrada:
                nueva_barra = {
                    'cortes': [corte],
                    'longitud_usada': corte['longitud']
                }
                barras.append(nueva_barra)
        
        return barras
    
    def _next_fit_decreasing(self, cortes):
        """Algoritmo Next Fit Decreasing."""
        cortes_ordenados = sorted(cortes, key=lambda x: x['longitud'], reverse=True)
        
        barras = []
        longitud_barra = self.config['longitud_barra']
        grosor_corte = self.config['grosor_corte']
        
        for corte in cortes_ordenados:
            # Solo intentar con la última barra
            if barras:
                ultima_barra = barras[-1]
                espacio_usado = sum(c['longitud'] for c in ultima_barra['cortes'])
                espacio_cortes = len(ultima_barra['cortes']) * grosor_corte
                espacio_disponible = longitud_barra - espacio_usado - espacio_cortes
                
                if espacio_disponible >= corte['longitud'] + grosor_corte:
                    ultima_barra['cortes'].append(corte)
                    ultima_barra['longitud_usada'] = sum(c['longitud'] for c in ultima_barra['cortes'])
                    continue
            
            # Crear nueva barra
            nueva_barra = {
                'cortes': [corte],
                'longitud_usada': corte['longitud']
            }
            barras.append(nueva_barra)
        
        return barras
    
    def _calcular_estadisticas(self, barras):
        """Calcula las estadísticas de la optimización."""
        if not barras:
            return {
                'eficiencia': 0.0,
                'desperdicio_total': 0.0,
                'coste_estimado': 0.0
            }
        
        longitud_barra = self.config['longitud_barra']
        longitud_total_usada = sum(barra['longitud_usada'] for barra in barras)
        longitud_total_disponible = len(barras) * longitud_barra
        
        eficiencia = (longitud_total_usada / longitud_total_disponible) * 100
        desperdicio_total = longitud_total_disponible - longitud_total_usada
        coste_estimado = len(barras) * 25.0  # 25€ por barra estimado
        
        return {
            'eficiencia': eficiencia,
            'desperdicio_total': desperdicio_total,
            'coste_estimado': coste_estimado
        }
