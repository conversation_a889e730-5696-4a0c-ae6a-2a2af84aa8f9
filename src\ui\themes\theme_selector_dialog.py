"""
Diálogo para seleccionar temas de la aplicación.
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QListWidget, QListWidgetItem, QDialogButtonBox, QGroupBox,
    QTextEdit, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor

from .theme_manager import ThemeManager


class ThemeSelectorDialog(QDialog):
    """Diálogo para seleccionar temas."""
    
    theme_changed = pyqtSignal(str)  # Señal emitida cuando cambia el tema
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = ThemeManager()
        
        self.setWindowTitle("Selector de Temas - PRO-2000")
        self.setMinimumSize(600, 500)
        self.setModal(True)
        
        self._inicializar_ui()
        self._cargar_temas()
    
    def _inicializar_ui(self):
        """Configura la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)
        
        # Título
        titulo = QLabel("🎨 Personalización de Temas")
        titulo.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        titulo.setAlignment(Qt.AlignmentFlag.AlignCenter)
        titulo.setStyleSheet("padding: 15px; color: #2c3e50;")
        layout_principal.addWidget(titulo)
        
        # Layout horizontal para lista y preview
        layout_contenido = QHBoxLayout()
        
        # Panel izquierdo: Lista de temas
        self._crear_panel_temas(layout_contenido)
        
        # Panel derecho: Preview y descripción
        self._crear_panel_preview(layout_contenido)
        
        layout_principal.addLayout(layout_contenido)
        
        # Botones de acción
        self._crear_botones_accion(layout_principal)
    
    def _crear_panel_temas(self, layout_contenido):
        """Crea el panel de selección de temas."""
        grupo_temas = QGroupBox("Temas Disponibles")
        layout_temas = QVBoxLayout(grupo_temas)
        
        # Lista de temas
        self.lista_temas = QListWidget()
        self.lista_temas.currentItemChanged.connect(self._tema_seleccionado)
        layout_temas.addWidget(self.lista_temas)
        
        # Información del tema actual
        self.label_tema_actual = QLabel()
        self.label_tema_actual.setStyleSheet("""
            background-color: #e8f4f8;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 10px;
            font-weight: bold;
        """)
        layout_temas.addWidget(self.label_tema_actual)
        
        grupo_temas.setMaximumWidth(250)
        layout_contenido.addWidget(grupo_temas)
    
    def _crear_panel_preview(self, layout_contenido):
        """Crea el panel de preview del tema."""
        grupo_preview = QGroupBox("Vista Previa")
        layout_preview = QVBoxLayout(grupo_preview)
        
        # Descripción del tema
        self.texto_descripcion = QTextEdit()
        self.texto_descripcion.setMaximumHeight(80)
        self.texto_descripcion.setReadOnly(True)
        layout_preview.addWidget(self.texto_descripcion)
        
        # Preview visual
        self.frame_preview = QFrame()
        self.frame_preview.setMinimumHeight(300)
        self.frame_preview.setStyleSheet("""
            QFrame {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
        """)
        layout_preview.addWidget(self.frame_preview)
        
        # Crear elementos de preview dentro del frame
        self._crear_elementos_preview()
        
        layout_contenido.addWidget(grupo_preview)
    
    def _crear_elementos_preview(self):
        """Crea elementos de preview dentro del frame."""
        layout_frame = QVBoxLayout(self.frame_preview)
        
        # Título de preview
        titulo_preview = QLabel("Vista Previa del Tema")
        titulo_preview.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        titulo_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout_frame.addWidget(titulo_preview)
        
        # Botones de ejemplo
        layout_botones = QHBoxLayout()
        
        btn_ejemplo1 = QPushButton("Botón Principal")
        btn_ejemplo1.setEnabled(True)
        layout_botones.addWidget(btn_ejemplo1)
        
        btn_ejemplo2 = QPushButton("Botón Secundario")
        btn_ejemplo2.setEnabled(False)
        layout_botones.addWidget(btn_ejemplo2)
        
        layout_frame.addLayout(layout_botones)
        
        # Texto de ejemplo
        texto_ejemplo = QLabel("""
        Este es un ejemplo de cómo se verá el texto
        con el tema seleccionado. Los colores y estilos
        se aplicarán automáticamente a toda la aplicación.
        """)
        texto_ejemplo.setWordWrap(True)
        texto_ejemplo.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout_frame.addWidget(texto_ejemplo)
        
        layout_frame.addStretch()
    
    def _crear_botones_accion(self, layout_principal):
        """Crea los botones de acción."""
        layout_botones = QHBoxLayout()
        
        # Botón de aplicar
        self.btn_aplicar = QPushButton("🎨 Aplicar Tema")
        self.btn_aplicar.clicked.connect(self._aplicar_tema)
        self.btn_aplicar.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        layout_botones.addWidget(self.btn_aplicar)
        
        # Botón de vista previa
        self.btn_preview = QPushButton("👁️ Vista Previa")
        self.btn_preview.clicked.connect(self._vista_previa)
        layout_botones.addWidget(self.btn_preview)
        
        layout_botones.addStretch()
        
        # Botones estándar
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        botones.button(QDialogButtonBox.StandardButton.Ok).setText("✅ Aceptar")
        botones.button(QDialogButtonBox.StandardButton.Cancel).setText("❌ Cancelar")
        
        botones.accepted.connect(self.accept)
        botones.rejected.connect(self.reject)
        
        layout_botones.addWidget(botones)
        layout_principal.addLayout(layout_botones)
    
    def _cargar_temas(self):
        """Carga los temas disponibles en la lista."""
        temas_disponibles = self.theme_manager.get_available_themes()
        tema_actual = self.theme_manager.get_current_theme()
        
        for tema_nombre in temas_disponibles:
            tema_info = self.theme_manager.get_theme_info(tema_nombre)
            
            item = QListWidgetItem()
            item.setText(tema_info.get("name", tema_nombre.title()))
            item.setData(Qt.ItemDataRole.UserRole, tema_nombre)
            
            # Marcar el tema actual
            if tema_nombre == tema_actual:
                item.setText(f"✓ {item.text()}")
                self.lista_temas.setCurrentItem(item)
            
            self.lista_temas.addItem(item)
        
        # Actualizar información del tema actual
        self._actualizar_tema_actual()
    
    def _actualizar_tema_actual(self):
        """Actualiza la información del tema actual."""
        tema_actual = self.theme_manager.get_current_theme()
        tema_info = self.theme_manager.get_theme_info(tema_actual)
        
        self.label_tema_actual.setText(
            f"🎨 Tema Actual:\n{tema_info.get('name', tema_actual.title())}"
        )
    
    def _tema_seleccionado(self, current, previous):
        """Maneja la selección de un tema."""
        if current:
            tema_nombre = current.data(Qt.ItemDataRole.UserRole)
            tema_info = self.theme_manager.get_theme_info(tema_nombre)
            
            # Actualizar descripción
            descripcion = tema_info.get("description", "Sin descripción disponible.")
            self.texto_descripcion.setPlainText(descripcion)
            
            # Habilitar botones
            self.btn_aplicar.setEnabled(True)
            self.btn_preview.setEnabled(True)
    
    def _vista_previa(self):
        """Muestra una vista previa temporal del tema."""
        current_item = self.lista_temas.currentItem()
        if current_item:
            tema_nombre = current_item.data(Qt.ItemDataRole.UserRole)
            
            # Aplicar tema temporalmente
            self.theme_manager.apply_theme(tema_nombre)
            
            # Mostrar mensaje
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(
                self,
                "Vista Previa",
                f"Vista previa del tema '{current_item.text()}' aplicada.\n\n"
                "Los cambios son temporales. Use 'Aplicar Tema' para guardar."
            )
    
    def _aplicar_tema(self):
        """Aplica el tema seleccionado permanentemente."""
        current_item = self.lista_temas.currentItem()
        if current_item:
            tema_nombre = current_item.data(Qt.ItemDataRole.UserRole)
            
            # Aplicar tema
            if self.theme_manager.apply_theme(tema_nombre):
                # Emitir señal
                self.theme_changed.emit(tema_nombre)
                
                # Actualizar lista
                self._actualizar_lista_temas()
                self._actualizar_tema_actual()
                
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.information(
                    self,
                    "✅ Tema Aplicado",
                    f"El tema '{current_item.text()}' se ha aplicado correctamente.\n\n"
                    "Los cambios se han guardado y se aplicarán en futuros inicios."
                )
    
    def _actualizar_lista_temas(self):
        """Actualiza la lista de temas marcando el actual."""
        tema_actual = self.theme_manager.get_current_theme()
        
        for i in range(self.lista_temas.count()):
            item = self.lista_temas.item(i)
            tema_nombre = item.data(Qt.ItemDataRole.UserRole)
            tema_info = self.theme_manager.get_theme_info(tema_nombre)
            
            if tema_nombre == tema_actual:
                item.setText(f"✓ {tema_info.get('name', tema_nombre.title())}")
            else:
                item.setText(tema_info.get('name', tema_nombre.title()))
    
    def closeEvent(self, event):
        """Restaura el tema original al cerrar sin aplicar."""
        # Restaurar tema guardado
        self.theme_manager.load_saved_theme()
        event.accept()
