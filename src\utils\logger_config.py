"""
Configuración de logging para PRO-2000
Sistema centralizado de registro de eventos y errores
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional


class CustomFormatter(logging.Formatter):
    """Formateador personalizado con colores para consola"""
    
    # Códigos de color ANSI
    COLORS = {
        'DEBUG': '\033[36m',      # Cyan
        'INFO': '\033[32m',       # Verde
        'WARNING': '\033[33m',    # Amarillo
        'ERROR': '\033[31m',      # Rojo
        'CRITICAL': '\033[35m',   # Magenta
        'RESET': '\033[0m'        # Reset
    }
    
    def format(self, record):
        # Formato base
        log_format = "%(asctime)s | %(name)s | %(levelname)s | %(message)s"
        
        # Agregar información de archivo y línea para errores
        if record.levelno >= logging.ERROR:
            log_format = "%(asctime)s | %(name)s | %(levelname)s | %(filename)s:%(lineno)d | %(message)s"
        
        # Aplicar color si es para consola
        if hasattr(self, 'use_colors') and self.use_colors:
            color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
            log_format = f"{color}{log_format}{self.COLORS['RESET']}"
        
        formatter = logging.Formatter(
            log_format,
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        return formatter.format(record)


def setup_logger(
    name: str = 'PRO2000',
    level: str = 'INFO',
    log_to_file: bool = True,
    log_to_console: bool = True,
    log_dir: Optional[str] = None,
    max_file_size_mb: int = 10,
    backup_count: int = 5
) -> logging.Logger:
    """
    Configura y retorna un logger personalizado para PRO-2000
    
    Args:
        name: Nombre del logger
        level: Nivel de logging (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_to_file: Si guardar logs en archivo
        log_to_console: Si mostrar logs en consola
        log_dir: Directorio para archivos de log (None = directorio por defecto)
        max_file_size_mb: Tamaño máximo del archivo de log en MB
        backup_count: Número de archivos de backup a mantener
        
    Returns:
        Logger configurado
    """
    
    # Crear logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper(), logging.INFO))
    
    # Limpiar handlers existentes
    logger.handlers.clear()
    
    # Configurar directorio de logs
    if log_dir is None:
        log_dir = Path.home() / ".pro2000" / "logs"
    else:
        log_dir = Path(log_dir)
    
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Handler para archivo
    if log_to_file:
        log_file = log_dir / f"{name.lower()}_{datetime.now().strftime('%Y%m%d')}.log"
        
        # Usar RotatingFileHandler para manejar el tamaño del archivo
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size_mb * 1024 * 1024,  # Convertir a bytes
            backupCount=backup_count,
            encoding='utf-8'
        )
        
        file_formatter = CustomFormatter()
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    # Handler para consola
    if log_to_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_formatter = CustomFormatter()
        console_formatter.use_colors = True  # Habilitar colores para consola
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
    
    # Configurar logging para capturar excepciones no manejadas
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        logger.critical(
            "Excepción no manejada",
            exc_info=(exc_type, exc_value, exc_traceback)
        )
    
    sys.excepthook = handle_exception
    
    # Log inicial
    logger.info(f"Sistema de logging inicializado para {name}")
    logger.info(f"Nivel de logging: {level.upper()}")
    logger.info(f"Archivo de log: {'Habilitado' if log_to_file else 'Deshabilitado'}")
    logger.info(f"Consola: {'Habilitada' if log_to_console else 'Deshabilitada'}")
    
    return logger


def get_logger(name: str = 'PRO2000') -> logging.Logger:
    """
    Obtiene un logger existente o crea uno nuevo con configuración por defecto
    
    Args:
        name: Nombre del logger
        
    Returns:
        Logger configurado
    """
    logger = logging.getLogger(name)
    
    # Si el logger no tiene handlers, configurarlo
    if not logger.handlers:
        return setup_logger(name)
    
    return logger


def log_performance(func):
    """
    Decorador para medir y registrar el tiempo de ejecución de funciones
    
    Usage:
        @log_performance
        def mi_funcion():
            # código
    """
    import time
    import functools
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = get_logger()
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info(f"⏱️ {func.__name__} ejecutada en {execution_time:.4f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ {func.__name__} falló después de {execution_time:.4f}s: {str(e)}")
            raise
    
    return wrapper


def log_method_calls(cls):
    """
    Decorador de clase para registrar llamadas a métodos
    
    Usage:
        @log_method_calls
        class MiClase:
            def mi_metodo(self):
                # código
    """
    logger = get_logger()
    
    for attr_name in dir(cls):
        attr = getattr(cls, attr_name)
        if callable(attr) and not attr_name.startswith('_'):
            def logged_method(original_method):
                def wrapper(self, *args, **kwargs):
                    logger.debug(f"🔧 Llamando {cls.__name__}.{original_method.__name__}")
                    return original_method(self, *args, **kwargs)
                return wrapper
            
            setattr(cls, attr_name, logged_method(attr))
    
    return cls


# Configuración por defecto para toda la aplicación
def configure_app_logging():
    """Configura el logging para toda la aplicación PRO-2000"""
    
    # Configuración principal
    main_logger = setup_logger(
        name='PRO2000',
        level='INFO',
        log_to_file=True,
        log_to_console=True
    )
    
    # Loggers específicos para diferentes módulos
    ui_logger = setup_logger(
        name='PRO2000.UI',
        level='DEBUG',
        log_to_file=True,
        log_to_console=False  # Solo a archivo para UI
    )
    
    db_logger = setup_logger(
        name='PRO2000.DB',
        level='INFO',
        log_to_file=True,
        log_to_console=False
    )
    
    error_logger = setup_logger(
        name='PRO2000.ERROR',
        level='ERROR',
        log_to_file=True,
        log_to_console=True
    )
    
    # Configurar logging para bibliotecas externas
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    
    main_logger.info("🚀 Sistema de logging de PRO-2000 configurado")
    return main_logger


if __name__ == "__main__":
    # Ejemplo de uso
    print("🔧 Probando sistema de logging...")
    
    # Configurar logger
    logger = setup_logger('TEST', level='DEBUG')
    
    # Probar diferentes niveles
    logger.debug("🐛 Mensaje de debug")
    logger.info("ℹ️ Mensaje informativo")
    logger.warning("⚠️ Mensaje de advertencia")
    logger.error("❌ Mensaje de error")
    logger.critical("🚨 Mensaje crítico")
    
    # Probar decorador de performance
    @log_performance
    def test_function():
        import time
        time.sleep(0.1)
        return "Resultado"
    
    result = test_function()
    
    print("✅ Sistema de logging funcionando correctamente")
