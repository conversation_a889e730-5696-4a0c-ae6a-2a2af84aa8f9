"""
Diálogo profesional mejorado para agregar/editar perfiles en artículos.
Incluye tipos de perfil, ángulos de corte y variables múltiples.
"""

import datetime

from PyQt6.QtCore import Qt, QRegularExpression, QTimer
from PyQt6.QtGui import (
    QTextCharFormat, QFont, QSyntaxHighlighter, QColor, QTextCursor,
    QTextDocument, QAction, QKeySequence, QIcon, QPixmap, QPainter
)
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, QLineEdit,
    QTextEdit, QPushButton, QTabWidget, QWidget, QFormLayout, QSpinBox,
    QDoubleSpinBox, QMessageBox, QFileDialog, QFrame, QListWidget,
    QListWidgetItem, QMenu, QInputDialog, QDialogButtonBox, QToolButton
)

# Clase para resaltado de sintaxis
class FormulaHighlighter(QSyntaxHighlighter):
    """Resaltador de sintaxis para fórmulas."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.highlighting_rules = []
        
        # Palabras clave
        keyword_format = QTextCharFormat()
        keyword_format.setForeground(QColor('#7B1FA2'))  # Púrpura
        keyword_format.setFontWeight(QFont.Weight.Bold)
        keywords = [
            'IF', 'MIN', 'MAX', 'ROUND', 'CEIL', 'FLOOR', 'ABS', 'SQRT',
            'AND', 'OR', 'NOT', 'TRUE', 'FALSE'
        ]
        for word in keywords:
            pattern = r'\b' + word + r'\b'
            self.highlighting_rules.append((pattern, keyword_format))
        
        # Números
        number_format = QTextCharFormat()
        number_format.setForeground(QColor('#2E7D32'))  # Verde oscuro
        self.highlighting_rules.append((r'\b\d+(\.\d+)?\b', number_format))
        
        # Variables
        variable_format = QTextCharFormat()
        variable_format.setForeground(QColor('#1565C0'))  # Azul oscuro
        variable_format.setFontWeight(QFont.Weight.Bold)
        variables = ['H', 'A', 'H1', 'H2', 'H3', 'H4', 'A1', 'A2', 'A3', 'A4', 'L', 'P', 'S']
        for var in variables:
            pattern = r'\b' + var + r'\b'
            self.highlighting_rules.append((pattern, variable_format))
        
        # Operadores
        operator_format = QTextCharFormat()
        operator_format.setForeground(QColor('#D81B60'))  # Rosa
        operator_format.setFontWeight(QFont.Weight.Bold)
        operators = [r'\+', r'\-', r'\*', r'/', '=', '>', '<', '!']
        for op in operators:
            self.highlighting_rules.append((op, operator_format))
        
        # Paréntesis
        parenthesis_format = QTextCharFormat()
        parenthesis_format.setForeground(QColor('#FF8F00'))  # Naranja
        parenthesis_format.setFontWeight(QFont.Weight.Bold)
        self.highlighting_rules.append((r'[\(\)]', parenthesis_format))
    
    def highlightBlock(self, text):
        """Aplica el resaltado al bloque de texto."""
        for pattern, format in self.highlighting_rules:
            import re
            for match in re.finditer(pattern, text, re.IGNORECASE):
                start = match.start()
                length = match.end() - start
                self.setFormat(start, length, format)


from PyQt6.QtGui import QShortcut

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLineEdit, QLabel,
    QFormLayout, QDialogButtonBox, QComboBox, QDoubleSpinBox, QSpinBox,
    QMessageBox, QGroupBox, QTextEdit, QTabWidget, QWidget, QGridLayout,
    QFileDialog, QCheckBox
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QKeySequence, QTextCharFormat, QColor, QSyntaxHighlighter
from PyQt6.QtGui import QFont, QPixmap
from PyQt6.QtWidgets import QScrollArea

from models.base import get_db
from models.perfil import Perfil
from models.articulo import ArticuloPerfil
from ui.utils.window_utils import setup_maximized_dialog


class PerfilProfesionalDialog(QDialog):
    """Diálogo profesional para agregar/editar perfiles con funcionalidades avanzadas.
    
    Este diálogo permite configurar perfiles profesionales con soporte para:
    - Fórmulas personalizadas para medidas y cantidades
    - Validación en tiempo real
    - Resaltado de sintaxis
    - Atajos de teclado
    - Optimizaciones de rendimiento
    
    Args:
        parent: Widget padre (opcional)
        articulo: Objeto de artículo al que pertenece el perfil (opcional)
        componente: Componente existente para edición (opcional)
    """
    
    # Cache para cálculos de fórmulas
    _formula_cache = {}
    _formula_cache_max_size = 100
    
    def __init__(self, parent=None, articulo=None, componente=None):
        super().__init__(parent)
        self.articulo = articulo
        self.componente = componente  # Para edición
        self._profiles_loaded = False  # Para carga diferida
        self._setup_keyboard_shortcuts()
        self._setup_validation_timer()
        
        # Inicializar caché de cálculos
        self._calculation_cache = {}
        
        self.setWindowTitle("Configuración Profesional de Perfil" if not componente else "Editar Perfil Profesional")
        self.setModal(True)

        # ✅ CONFIGURACIÓN SIMPLE Y EFECTIVA
        setup_maximized_dialog(self, "Configuración Profesional de Perfil" if not componente else "Editar Perfil Profesional")
        
        # Tipos de perfil profesionales
        self.tipos_perfil = {
            'marco': 'Marco - Perímetro exterior',
            'hoja': 'Hoja - Parte móvil de la ventana',
            'inversor': 'Inversor - Perfil de cambio de dirección',
            'travesaño': 'Travesaño - División horizontal',
            'junquillo': 'Junquillo - Sujeción de cristal',
            'alargadera': 'Alargadera - Extensión de perfil',
            'guia': 'Guía - Carril de deslizamiento',
            'union': 'Unión - Conexión entre perfiles',
            'jamba': 'Jamba - Perfil lateral de marco'
        }
        
        # Ángulos de corte estándar
        self.angulos_corte = [0.0, 22.5, 45.0, 67.5, 90.0]
        
        # Variables de medidas disponibles
        self.variables_medidas = {
            'H': 'Altura principal',
            'A': 'Anchura principal',
            'H1': 'Segunda altura',
            'H2': 'Tercera altura',
            'H3': 'Cuarta altura',
            'H4': 'Quinta altura',
            'A1': 'Segunda anchura',
            'A2': 'Tercera anchura',
            'A3': 'Cuarta anchura',
            'A4': 'Quinta anchura'
        }
        
        # Inicializar la interfaz de usuario
        self._inicializar_ui()
        
        # Cargar perfiles
        self._cargar_perfiles()
        
        # Cargar datos del componente si se está editando uno existente
        if componente:
            # Asegurarse de que la interfaz esté completamente cargada
            QTimer.singleShot(100, self._cargar_datos_componente)
    
    def _inicializar_ui(self):
        """Configura la interfaz de usuario con pestañas."""
        layout_principal = QVBoxLayout(self)
        
        # Título
        titulo = QLabel("Configuración Profesional de Perfil")
        titulo.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        titulo.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout_principal.addWidget(titulo)
        
        # Crear pestañas
        self.tabs = QTabWidget()
        
        # Pestaña 1: Datos Básicos
        self._crear_tab_datos_basicos()
        
        # Pestaña 2: Configuración Profesional
        self._crear_tab_configuracion_profesional()
        
        # Pestaña 3: Fórmulas y Condiciones
        self._crear_tab_formulas()
        
        # Pestaña 4: Ayuda y Ejemplos
        self._crear_tab_ayuda()
        
        layout_principal.addWidget(self.tabs)
        
        # Botones de acción
        self._crear_botones_accion(layout_principal)
        
        # Configurar resaltado de sintaxis
        self._setup_syntax_highlighting()
    
    def _crear_tab_datos_basicos(self):
        """Crea la pestaña de datos básicos."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Grupo: Selección de Perfil
        grupo_perfil = QGroupBox("Selección de Perfil")
        layout_perfil = QFormLayout(grupo_perfil)
        
        self.combo_perfil = QComboBox()
        self.combo_perfil.currentTextChanged.connect(self._actualizar_info_perfil)
        layout_perfil.addRow("Perfil*:", self.combo_perfil)
        
        # Información del perfil seleccionado
        self.label_info_perfil = QLabel("Seleccione un perfil para ver su información")
        self.label_info_perfil.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        self.label_info_perfil.setWordWrap(True)
        layout_perfil.addRow("Información:", self.label_info_perfil)
        
        layout.addWidget(grupo_perfil)
        
        # Grupo: Tipo y Posición
        grupo_tipo = QGroupBox("Tipo y Posición del Perfil")
        layout_tipo = QFormLayout(grupo_tipo)
        
        self.combo_tipo_perfil = QComboBox()
        for codigo, descripcion in self.tipos_perfil.items():
            self.combo_tipo_perfil.addItem(descripcion, codigo)
        layout_tipo.addRow("Tipo de Perfil*:", self.combo_tipo_perfil)
        
        self.campo_descripcion_posicion = QLineEdit()
        self.campo_descripcion_posicion.setPlaceholderText("Ej: Marco superior, Hoja izquierda, Travesaño central")
        layout_tipo.addRow("Descripción Posición:", self.campo_descripcion_posicion)
        
        layout.addWidget(grupo_tipo)
        
        # Grupo: Orden de Fabricación
        grupo_orden = QGroupBox("Orden de Fabricación")
        layout_orden = QFormLayout(grupo_orden)
        
        self.campo_orden = QSpinBox()
        self.campo_orden.setRange(1, 999)
        self.campo_orden.setValue(1)
        self.campo_orden.setToolTip("Orden en que se fabricará este perfil")
        layout_orden.addRow("Orden:", self.campo_orden)
        
        layout.addWidget(grupo_orden)
        
        # Grupo: Observaciones
        grupo_obs = QGroupBox("Observaciones")
        layout_obs = QVBoxLayout(grupo_obs)
        
        self.campo_observaciones = QTextEdit()
        self.campo_observaciones.setPlaceholderText("Ingrese cualquier observación o nota sobre este perfil...")
        self.campo_observaciones.setMaximumHeight(100)
        layout_obs.addWidget(self.campo_observaciones)
        
        layout.addWidget(grupo_obs)
        
        self.tabs.addTab(tab, "📋 Datos Básicos")
    
    def _crear_tab_configuracion_profesional(self):
        """Crea la pestaña de configuración profesional."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Grupo: Ángulos de Corte
        grupo_angulos = QGroupBox("Ángulos de Corte")
        layout_angulos = QGridLayout(grupo_angulos)
        
        layout_angulos.addWidget(QLabel("Ángulo Izquierdo:"), 0, 0)
        self.combo_angulo_izquierdo = QComboBox()
        for angulo in self.angulos_corte:
            self.combo_angulo_izquierdo.addItem(f"{angulo}°", angulo)
        self.combo_angulo_izquierdo.setCurrentText("90.0°")
        layout_angulos.addWidget(self.combo_angulo_izquierdo, 0, 1)
        
        layout_angulos.addWidget(QLabel("Ángulo Derecho:"), 0, 2)
        self.combo_angulo_derecho = QComboBox()
        for angulo in self.angulos_corte:
            self.combo_angulo_derecho.addItem(f"{angulo}°", angulo)
        self.combo_angulo_derecho.setCurrentText("90.0°")
        layout_angulos.addWidget(self.combo_angulo_derecho, 0, 3)
        
        # Información sobre ángulos
        info_angulos = QLabel(
            "Los ángulos de corte son fundamentales para la optimización. "
            "90° = corte recto, 45° = inglete, 22.5° = corte especial."
        )
        info_angulos.setWordWrap(True)
        info_angulos.setStyleSheet("color: #666; font-size: 10px; padding: 5px;")
        layout_angulos.addWidget(info_angulos, 1, 0, 1, 4)
        
        layout.addWidget(grupo_angulos)
        
        # Grupo: Variable de Medida
        grupo_variable = QGroupBox("Variable de Medida")
        layout_variable = QFormLayout(grupo_variable)
        
        self.combo_variable_medida = QComboBox()
        for codigo, descripcion in self.variables_medidas.items():
            self.combo_variable_medida.addItem(f"{codigo} - {descripcion}", codigo)
        layout_variable.addRow("Variable Principal*:", self.combo_variable_medida)
        
        # Información sobre variables
        info_variables = QLabel(
            "Seleccione qué variable de medida utilizará este perfil. "
            "H/A son las medidas principales, H1-H4/A1-A4 son medidas adicionales."
        )
        info_variables.setWordWrap(True)
        info_variables.setStyleSheet("color: #666; font-size: 10px;")
        layout_variable.addRow("", info_variables)
        
        layout.addWidget(grupo_variable)
        
        self.tabs.addTab(tab, "⚙️ Configuración")

    def _crear_tab_formulas(self):
        """Crea la pestaña de fórmulas y condiciones con desplazamiento."""
        # Crear el widget principal y el layout
        tab = QWidget()
        layout_principal = QVBoxLayout(tab)
        layout_principal.setContentsMargins(5, 5, 5, 5)
        
        # Crear el área de desplazamiento
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        
        # Widget contenedor para el área de desplazamiento
        contenido = QWidget()
        scroll_area.setWidget(contenido)
        
        # Layout del contenido
        layout = QVBoxLayout(contenido)
        layout.setContentsMargins(10, 10, 20, 10)  # Margen derecho más ancho para la barra de desplazamiento
        layout.setSpacing(15)

        # Grupo: Fórmula de Medida
        grupo_medida = QGroupBox("Fórmula de Medida")
        grupo_medida.setStyleSheet("""
            QGroupBox {
                margin-top: 10px;
                padding-top: 15px;
                border: 1px solid #c0c0c0;
                border-radius: 4px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px;
            }
        """)
        
        layout_medida = QVBoxLayout(grupo_medida)
        layout_medida.setContentsMargins(10, 15, 10, 15)
        layout_medida.setSpacing(10)

        # Fila para la fórmula con botón de ayuda
        layout_formula = QHBoxLayout()
        layout_formula.setSpacing(10)
        
        self.campo_medida = QLineEdit()
        self.campo_medida.setPlaceholderText("Ej: H-25, 2*H+2*A, IF(H>1500,H-50,H-40)")
        self.campo_medida.setMinimumHeight(35)
        self.campo_medida.setStyleSheet("""
            QLineEdit {
                padding: 5px 8px;
                border: 1px solid #a0a0a0;
                border-radius: 3px;
                font-family: 'Consolas', 'Monospace';
                font-size: 12px;
            }
            QLineEdit:focus {
                border: 1px solid #4b8bf4;
            }
        """)
        layout_formula.addWidget(self.campo_medida, stretch=1)
        
        # Botón para sugerir fórmula según perfil
        btn_sugerir = QPushButton("Sugerir Fórmula")
        btn_sugerir.setToolTip("Sugerir fórmula según el tipo de perfil seleccionado")
        btn_sugerir.setMinimumHeight(35)
        btn_sugerir.clicked.connect(self._aplicar_formula_perfil)
        btn_sugerir.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #a0a0a0;
                border-radius: 3px;
                padding: 5px 10px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)
        layout_formula.addWidget(btn_sugerir)
        
        layout_medida.addLayout(layout_formula)

        # Grupo de botones de fórmulas comunes
        grupo_botones = QGroupBox("Fórmulas Comunes")
        grupo_botones.setStyleSheet("""
            QGroupBox {
                margin-top: 5px;
                padding-top: 15px;
                border: 1px solid #c0c0c0;
                border-radius: 4px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px;
            }
        """)
        
        layout_botones = QGridLayout(grupo_botones)
        layout_botones.setContentsMargins(10, 15, 10, 15)
        layout_botones.setVerticalSpacing(8)
        layout_botones.setHorizontalSpacing(8)

        # Función para crear botones con estilo consistente
        def crear_boton_formula(texto, formula):
            btn = QPushButton(texto)
            btn.setToolTip(f"Insertar: {formula}")
            btn.setMinimumHeight(30)
            btn.clicked.connect(lambda: self.campo_medida.setText(formula))
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #f8f8f8;
                    border: 1px solid #c0c0c0;
                    border-radius: 3px;
                    padding: 4px 8px;
                    text-align: left;
                }
                QPushButton:hover {
                    background-color: #e8f0fe;
                    border-color: #4b8bf4;
                }
                QPushButton:pressed {
                    background-color: #d0e0ff;
                }
            """)
            return btn

        # Fila 1
        layout_botones.addWidget(crear_boton_formula("Perímetro (2H+2A)", "2*H+2*A"), 0, 0)
        layout_botones.addWidget(crear_boton_formula("Altura -50mm", "H-50"), 0, 1)
        layout_botones.addWidget(crear_boton_formula("Ancho -30mm", "A-30"), 0, 2)
        
        # Fila 2
        layout_botones.addWidget(crear_boton_formula("Superficie (H*A)", "H*A"), 1, 0)
        layout_botones.addWidget(crear_boton_formula("Condicional (IF)", "IF(H>1500,H-100,H-50)"), 1, 1)
        layout_botones.addWidget(crear_boton_formula("Mínimo (MIN)", "MIN(H,A)"), 1, 2)

        # Añadir los grupos al layout principal
        layout.addWidget(grupo_medida)
        layout.addWidget(grupo_botones)
        
        # Grupo: Fórmula de Cantidad
        grupo_cantidad = QGroupBox("Fórmula de Cantidad")
        grupo_cantidad.setStyleSheet(grupo_medida.styleSheet())
        layout_cantidad = QVBoxLayout(grupo_cantidad)
        layout_cantidad.setContentsMargins(10, 15, 10, 15)
        
        # Fila para cantidad base y fórmula
        layout_cantidad_superior = QHBoxLayout()
        
        # Cantidad base
        layout_cantidad_base = QVBoxLayout()
        lbl_cantidad_base = QLabel("Cantidad Base:")
        self.campo_cantidad_base = QDoubleSpinBox()
        self.campo_cantidad_base.setMinimum(0.01)
        self.campo_cantidad_base.setMaximum(999999.99)
        self.campo_cantidad_base.setValue(1.0)
        self.campo_cantidad_base.setDecimals(2)
        self.campo_cantidad_base.setSingleStep(0.5)
        layout_cantidad_base.addWidget(lbl_cantidad_base)
        layout_cantidad_base.addWidget(self.campo_cantidad_base)
        
        # Fórmula de cantidad
        layout_formula_cantidad = QVBoxLayout()
        lbl_formula_cantidad = QLabel("Fórmula de Cantidad:")
        self.campo_cantidad_formula = QLineEdit()
        self.campo_cantidad_formula.setPlaceholderText("Ej: 1, 2, CEIL(H/1000)")
        self.campo_cantidad_formula.setMinimumHeight(35)
        self.campo_cantidad_formula.setStyleSheet(self.campo_medida.styleSheet())
        layout_formula_cantidad.addWidget(lbl_formula_cantidad)
        layout_formula_cantidad.addWidget(self.campo_cantidad_formula)
        
        # Agregar a la fila superior
        layout_cantidad_superior.addLayout(layout_cantidad_base, 1)
        layout_cantidad_superior.addSpacing(10)
        layout_cantidad_superior.addLayout(layout_formula_cantidad, 3)
        
        # Agregar etiqueta informativa
        lbl_info_cantidad = QLabel("Deje la fórmula vacía para usar solo la cantidad base")
        lbl_info_cantidad.setStyleSheet("color: #666; font-size: 10px; font-style: italic;")
        
        # Agregar al layout principal de cantidad
        layout_cantidad.addLayout(layout_cantidad_superior)
        layout_cantidad.addWidget(lbl_info_cantidad)
        layout_cantidad.addWidget(lbl_info_cantidad)
        
        # Grupo: Condición de Aplicación
        grupo_condicion = QGroupBox("Condición de Aplicación (opcional)")
        grupo_condicion.setStyleSheet(grupo_medida.styleSheet())
        layout_condicion = QVBoxLayout(grupo_condicion)
        layout_condicion.setContentsMargins(10, 15, 10, 15)
        
        self.campo_condicion = QLineEdit()
        self.campo_condicion.setPlaceholderText("Ej: H>1000, A>500, H>A")
        self.campo_condicion.setMinimumHeight(35)
        self.campo_condicion.setStyleSheet(self.campo_medida.styleSheet())
        
        # Agregar etiqueta informativa
        lbl_info_condicion = QLabel(
            "La condición debe evaluarse como VERDADERO o FALSO. "
            "Deje vacío para aplicar siempre."
        )
        lbl_info_condicion.setWordWrap(True)
        lbl_info_condicion.setStyleSheet("color: #666; font-size: 10px; font-style: italic;")
        
        layout_condicion.addWidget(self.campo_condicion)
        layout_condicion.addWidget(lbl_info_condicion)
        
        # Añadir grupos adicionales al layout principal
        layout.addWidget(grupo_cantidad)
        layout.addWidget(grupo_condicion)
        
        # Agregar espacio elástico para empujar todo hacia arriba
        layout.addStretch()
        
        # Añadir el área de desplazamiento al layout principal
        layout_principal.addWidget(scroll_area)
        
        # Configurar la pestaña
        self.tabs.addTab(tab, "🧮 Fórmulas")
        
        # Asegurar que el área de desplazamiento se expanda correctamente
        layout_principal.setContentsMargins(0, 0, 0, 0)
        
        return tab

    def _aplicar_formula_perfil(self):
        """Aplica una fórmula sugerida según el tipo de perfil seleccionado."""
        # Obtener el tipo de perfil seleccionado
        tipo_perfil = self.combo_tipo_perfil.currentText().lower()
        
        # Diccionario de fórmulas sugeridas por tipo de perfil
        formulas = {
            'marco': 'H-25',
            'hoja': 'H-50',
            'inversor': 'IF(H>1500,H-100,H-50)',
            'travesaño': 'A-30',
            'junquillo': '2*H+2*A-100',
            'alargadera': 'MAX(0,H-2000)',
            'guia': 'A+20',
            'union': 'MIN(H,A)/10',
            'jamba': 'H'
        }
        
        if tipo_perfil and tipo_perfil in formulas:
            self.campo_medida.setText(formulas[tipo_perfil])
            QMessageBox.information(
                self,
                "Fórmula aplicada",
                f"Se ha aplicado la fórmula sugerida para perfil tipo '{tipo_perfil}'.\n"
                "Puedes modificarla según sea necesario."
            )
        else:
            QMessageBox.information(
                self,
                "Sin fórmula predefinida",
                "No hay una fórmula predefinida para este tipo de perfil.\n"
                "Por favor, ingresa la fórmula manualmente."
            )

    def _crear_tab_ayuda(self):
        """Crea la pestaña de ayuda y ejemplos con un diseño mejorado."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Crear área de scroll para la ayuda
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setFrameShape(QFrame.Shape.NoFrame)
        
        # Widget contenedor principal
        widget_ayuda = QWidget()
        layout_ayuda = QVBoxLayout(widget_ayuda)
        layout_ayuda.setSpacing(15)
        layout_ayuda.setContentsMargins(5, 5, 15, 5)

        # Estilo común para los grupos
        estilo_grupo = """
            QGroupBox {
                font-weight: bold;
                border: 1px solid #6f42c1;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """

        # 1. Guía Rápida
        grupo_guia = QGroupBox("📋 Guía Rápida")
        grupo_guia.setStyleSheet(estilo_grupo)
        layout_guia = QVBoxLayout(grupo_guia)
        
        texto_guia = """
        <div style='margin: 5px; line-height: 1.6;'>
            <p>Este asistente le permite configurar fórmulas avanzadas para el cálculo de medidas de perfiles.</p>
            <p><b>Flujo de trabajo recomendado:</b></p>
            <ol>
                <li>Seleccione el tipo de perfil en la pestaña de configuración</li>
                <li>Utilice el botón <b>"Sugerir Fórmula"</b> para obtener una fórmula predefinida</li>
                <li>Ajuste la fórmula según sea necesario</li>
                <li>Valide las fórmulas antes de guardar</li>
            </ol>
        </div>
        """
        label_guia = QLabel(texto_guia)
        label_guia.setWordWrap(True)
        label_guia.setOpenExternalLinks(True)
        layout_guia.addWidget(label_guia)
        layout_ayuda.addWidget(grupo_guia)

        # 2. Variables disponibles
        grupo_variables = QGroupBox("📊 Variables Disponibles")
        grupo_variables.setStyleSheet(estilo_grupo)
        layout_variables = QVBoxLayout(grupo_variables)
        
        # Tabla de variables
        texto_variables = """
        <div style='margin: 5px;'>
            <table border='1' cellspacing='0' cellpadding='5' style='border-collapse: collapse; width: 100%;'>
                <tr style='background-color: #f0f0f0;'>
                    <th style='text-align: left; padding: 8px;'>Variable</th>
                    <th style='text-align: left; padding: 8px;'>Descripción</th>
                    <th style='text-align: left; padding: 8px;'>Ejemplo</th>
                </tr>
                <tr><td><b>H</b></td><td>Altura principal (mm)</td><td>1500</td></tr>
                <tr><td><b>A</b></td><td>Anchura principal (mm)</td><td>800</td></tr>
                <tr><td><b>H1-H4</b></td><td>Alturas adicionales</td><td>1200, 900, 600, 300</td></tr>
                <tr><td><b>A1-A4</b></td><td>Anchuras adicionales</td><td>600, 400, 300, 200</td></tr>
                <tr><td><b>L</b></td><td>Lado mayor (MAX(H,A))</td><td>1500</td></tr>
                <tr><td><b>P</b></td><td>Perímetro (2*H + 2*A)</td><td>4600</td></tr>
                <tr><td><b>S</b></td><td>Superficie (H*A)</td><td>1200000</td></tr>
            </table>
        </div>
        """
        label_variables = QLabel(texto_variables)
        label_variables.setTextFormat(Qt.TextFormat.RichText)
        label_variables.setWordWrap(True)
        layout_variables.addWidget(label_variables)
        layout_ayuda.addWidget(grupo_variables)

        # 3. Funciones disponibles
        grupo_funciones = QGroupBox("🧮 Funciones Disponibles")
        grupo_funciones.setStyleSheet(estilo_grupo)
        layout_funciones = QVBoxLayout(grupo_funciones)
        
        texto_funciones = """
        <div style='margin: 5px;'>
            <h4 style='color: #6f42c1; margin-top: 0;'>Matemáticas Básicas</h4>
            <table border='0' cellspacing='5' cellpadding='3' style='margin-bottom: 15px;'>
                <tr><td><b>+ - * /</b></td><td>Operaciones aritméticas básicas</td></tr>
                <tr><td><b>^</b></td><td>Potencia (ej: 2^3 = 8)</td></tr>
                <tr><td><b>%</b></td><td>Módulo (resto de la división)</td></tr>
            </table>
            
            <h4 style='color: #6f42c1;'>Funciones Avanzadas</h4>
            <table border='0' cellspacing='5' cellpadding='3'>
                <tr><td><b>MIN(a,b,...)</b></td><td>Menor valor</td><td>MIN(100, 200) → 100</td></tr>
                <tr><td><b>MAX(a,b,...)</b></td><td>Mayor valor</td><td>MAX(100, 200) → 200</td></tr>
                <tr><td><b>ROUND(x)</b></td><td>Redondeo al entero más cercano</td><td>ROUND(1.6) → 2</td></tr>
                <tr><td><b>CEIL(x)</b></td><td>Redondeo hacia arriba</td><td>CEIL(1.2) → 2</td></tr>
                <tr><td><b>FLOOR(x)</b></td><td>Redondeo hacia abajo</td><td>FLOOR(1.9) → 1</td></tr>
                <tr><td><b>ABS(x)</b></td><td>Valor absoluto</td><td>ABS(-5) → 5</td></tr>
                <tr><td><b>SQRT(x)</b></td><td>Raíz cuadrada</td><td>SQRT(16) → 4</td></tr>
                <tr><td><b>IF(cond,si,no)</b></td><td>Condicional</td><td>IF(H>1000,"Grande","Pequeño")</td></tr>
            </table>
        </div>
        """
        label_funciones = QLabel(texto_funciones)
        label_funciones.setTextFormat(Qt.TextFormat.RichText)
        label_funciones.setWordWrap(True)
        layout_funciones.addWidget(label_funciones)
        layout_ayuda.addWidget(grupo_funciones)

        # 4. Ejemplos por tipo de perfil
        grupo_ejemplos = QGroupBox("🔧 Ejemplos por Tipo de Perfil")
        grupo_ejemplos.setStyleSheet(estilo_grupo)
        layout_ejemplos = QVBoxLayout(grupo_ejemplos)
        
        texto_ejemplos = """
        <div style='margin: 5px;'>
            <table border='0' cellspacing='5' cellpadding='3' width='100%'>
                <tr style='background-color: #f9f9f9;'>
                    <th style='text-align: left; padding: 8px;'>Tipo</th>
                    <th style='text-align: left; padding: 8px;'>Fórmula</th>
                    <th style='text-align: left; padding: 8px;'>Explicación</th>
                </tr>
                <tr><td><b>Marco</b></td><td><code>H-25</code></td><td>Altura menos 25mm para holgura</td></tr>
                <tr><td><b>Hoja</b></td><td><code>H-50</code></td><td>Altura menos marco superior e inferior</td></tr>
                <tr><td><b>Travesaño</b></td><td><code>A-30</code></td><td>Anchura menos marcos laterales</td></tr>
                <tr><td><b>Junquillo</b></td><td><code>2*H+2*A-100</code></td><td>Perímetro menos esquinas</td></tr>
                <tr><td><b>Guía</b></td><td><code>A+20</code></td><td>Anchura más holgura de funcionamiento</td></tr>
                <tr><td><b>Inversor</b></td><td><code>IF(H>1500,H-100,H-50)</code></td><td>Según altura</td></tr>
                <tr><td><b>Unión</b></td><td><code>MIN(H,A)/10</code></td><td>Proporcional a menor medida</td></tr>
                <tr><td><b>Jamba</b></td><td><code>H</code></td><td>Altura completa</td></tr>
                <tr><td><b>Alargadera</b></td><td><code>MAX(0,H-2000)</code></td><td>Solo si excede 2m</td></tr>
            </table>
        </div>
        """
        label_ejemplos = QLabel(texto_ejemplos)
        label_ejemplos.setTextFormat(Qt.TextFormat.RichText)
        label_ejemplos.setWordWrap(True)
        layout_ejemplos.addWidget(label_ejemplos)
        layout_ayuda.addWidget(grupo_ejemplos)

        # 5. Consejos y trucos
        grupo_consejos = QGroupBox("💡 Consejos y Trucos")
        grupo_consejos.setStyleSheet(estilo_grupo)
        layout_consejos = QVBoxLayout(grupo_consejos)
        
        texto_consejos = """
        <div style='margin: 5px; line-height: 1.6;'>
            <ul>
                <li>Use el botón <b>"Sugerir Fórmula"</b> para obtener fórmulas predefinidas según el tipo de perfil</li>
                <li>Las fórmulas pueden incluir cualquier combinación de variables y operadores matemáticos</li>
                <li>Utilice paréntesis para agrupar operaciones cuando sea necesario</li>
                <li>Puede probar sus fórmulas con el botón <b>"Probar con Medidas"</b></li>
                <li>Guarde configuraciones comunes como plantillas para reutilizarlas</li>
            </ul>
        </div>
        """
        label_consejos = QLabel(texto_consejos)
        label_consejos.setTextFormat(Qt.TextFormat.RichText)
        label_consejos.setWordWrap(True)
        layout_consejos.addWidget(label_consejos)
        layout_ayuda.addWidget(grupo_consejos)

        # Añadir espaciador para empujar todo hacia arriba
        layout_ayuda.addStretch()

        # Configurar el scroll
        scroll.setWidget(widget_ayuda)
        layout.addWidget(scroll)

        self.tabs.addTab(tab, "❓ Ayuda")

    def _crear_botones_accion(self, layout_principal):
        """Crea los botones de acción."""
        layout_botones = QHBoxLayout()
        
        # Botón de plantillas
        btn_plantillas = QToolButton()
        btn_plantillas.setPopupMode(QToolButton.ToolButtonPopupMode.InstantPopup)
        btn_plantillas.setText("📋 Plantillas")
        btn_plantillas.setToolTip("Gestionar plantillas de perfiles")
        btn_plantillas.setStyleSheet("""
            QToolButton {
                padding: 5px 10px;
                border: 1px solid #6f42c1;
                border-radius: 4px;
                background-color: #6f42c1;
                color: white;
                text-align: left;
                padding-right: 20px;
            }
            QToolButton:hover {
                background-color: #5a32a3;
            }
            QToolButton::menu-indicator {
                width: 0px;
            }
        """)
        
        # Menú desplegable de plantillas
        menu_plantillas = QMenu(self)
        
        # Acción para guardar plantilla
        accion_guardar_plantilla = QAction("💾 Guardar como plantilla", self)
        accion_guardar_plantilla.triggered.connect(self._guardar_como_plantilla)
        menu_plantillas.addAction(accion_guardar_plantilla)
        
        # Acción para cargar plantilla
        accion_cargar_plantilla = QAction("📂 Cargar plantilla...", self)
        accion_cargar_plantilla.triggered.connect(self._gestionar_plantillas)
        menu_plantillas.addAction(accion_cargar_plantilla)
        
        menu_plantillas.addSeparator()
        
        # Acción para importar configuración
        accion_importar = QAction("📥 Importar configuración...", self)
        accion_importar.triggered.connect(self._importar_configuracion)
        menu_plantillas.addAction(accion_importar)
        
        # Acción para exportar configuración
        accion_exportar = QAction("📤 Exportar configuración...", self)
        accion_exportar.triggered.connect(self._exportar_configuracion)
        menu_plantillas.addAction(accion_exportar)
        
        btn_plantillas.setMenu(menu_plantillas)
        layout_botones.addWidget(btn_plantillas)
        
        # Separador
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.VLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        layout_botones.addWidget(separator)

        # Botón de validar fórmulas
        btn_validar = QPushButton("🔍 Validar Fórmulas")
        btn_validar.clicked.connect(self._validar_formulas)
        btn_validar.setToolTip("Valida que las fórmulas sean correctas")
        layout_botones.addWidget(btn_validar)

        # Botón de probar con medidas
        btn_probar = QPushButton("🧪 Probar con Medidas")
        btn_probar.clicked.connect(self._probar_con_medidas)
        btn_probar.setToolTip("Prueba las fórmulas con medidas de ejemplo")
        layout_botones.addWidget(btn_probar)

        layout_botones.addStretch()

        # Botones estándar
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )

        if self.componente:
            botones.button(QDialogButtonBox.StandardButton.Ok).setText("💾 Guardar Cambios")
        else:
            botones.button(QDialogButtonBox.StandardButton.Ok).setText("➕ Agregar Perfil")

        botones.button(QDialogButtonBox.StandardButton.Cancel).setText("❌ Cancelar")

        botones.accepted.connect(self._aceptar)
        botones.rejected.connect(self.reject)

        layout_botones.addWidget(botones)
        layout_principal.addLayout(layout_botones)

    def _actualizar_info_perfil(self):
        """Actualiza la información del perfil seleccionado."""
        # Obtener el perfil seleccionado
        perfil_id = self.combo_perfil.currentData()
        if not perfil_id:
            return
            
        # Aquí iría la lógica para cargar la información del perfil
        # Por ahora, solo actualizamos el campo de observaciones
        try:
            from models.base import SessionLocal as Session
            from models.perfil import Perfil
            
            session = Session()
            perfil = session.query(Perfil).get(perfil_id)
            
            if perfil:
                # Actualizar campos con la información del perfil
                self.campo_observaciones.setPlainText(perfil.observaciones or "")
                
                # Si hay un componente, actualizamos sus datos
                if hasattr(self, 'componente'):
                    self.componente.perfil_id = perfil.id
                    
            session.close()
            
        except Exception as e:
            print(f"Error al cargar información del perfil: {e}")
    
    def _cargar_perfiles(self, force=False):
        """Carga los perfiles disponibles de forma diferida.
        
        Args:
            force: Si es True, fuerza la recarga aunque ya se hayan cargado antes
            
        Returns:
            bool: True si la carga fue exitosa, False en caso de error
        """
        if self._profiles_loaded and not force:
            return True
            
        # Mostrar indicador de carga
        self.combo_perfil.clear()
        self.combo_perfil.addItem("Cargando perfiles...")
        self.combo_perfil.setEnabled(False)  # Deshabilitar mientras se carga
        self.combo_perfil.repaint()  # Forzar actualización de la UI
        
        db = next(get_db())
        try:
            # Cargar solo los campos necesarios
            perfiles = db.query(
                Perfil.id,
                Perfil.codigo,
                Perfil.descripcion,
                Perfil.precio_metro
            ).filter(
                Perfil.activo == True
            ).order_by(
                Perfil.codigo
            ).all()

            # Limpiar el combo
            self.combo_perfil.clear()
            
            # Agregar opción vacía
            self.combo_perfil.addItem("Seleccione un perfil...", None)
            
            # Llenar con los perfiles
            for perfil in perfiles:
                texto = f"{perfil.codigo} - {perfil.descripcion}"
                if perfil.precio_metro:
                    texto += f" ({perfil.precio_metro:.2f}€/m)"
                self.combo_perfil.addItem(texto, perfil.id)
                
            self._profiles_loaded = True
            # Habilitar el combo después de cargar
            self.combo_perfil.setEnabled(True)
            return True

        except Exception as e:
            QMessageBox.critical(
                self, 
                "Error al cargar perfiles",
                f"No se pudieron cargar los perfiles: {str(e)}\n\n"
                "Por favor, verifique la conexión a la base de datos e intente nuevamente."
            )
            self.combo_perfil.clear()
            self.combo_perfil.addItem("Error al cargar perfiles")
            return False
            
        finally:
            db.close()
            if not self._profiles_loaded:
                self.combo_perfil.addItem("Error al cargar perfiles")
            return self._profiles_loaded
            
    def _exportar_configuracion(self):
        """Exporta la configuración actual del perfil a un archivo JSON."""
        # Obtener la configuración actual
        config = {
            'profile_id': self.combo_perfil.currentData(),
            'medida_formula': self.campo_medida.text().strip(),
            'cantidad_formula': self.campo_cantidad_formula.text().strip(),
            'condicion': self.campo_condicion.text().strip(),
            'angulo_izquierdo': self.campo_angulo_izq.value(),
            'angulo_derecho': self.campo_angulo_der.value(),
            'observaciones': self.campo_observaciones.toPlainText().strip()
        }
        
        # Mostrar diálogo para guardar archivo
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Exportar Configuración",
            "",
            "Archivos JSON (*.json);;Todos los archivos (*)"
        )
        
        if not file_path:
            return  # Usuario canceló
            
        # Asegurar extensión .json
        if not file_path.lower().endswith('.json'):
            file_path += '.json'
            
        try:
            import json
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
            QMessageBox.information(
                self,
                "Exportación Exitosa",
                f"La configuración se ha exportado correctamente a:\n{file_path}"
            )
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error al Exportar",
                f"No se pudo exportar la configuración:\n{str(e)}"
            )
    
    def _importar_configuracion(self, template_mode=False):
        """Importa una configuración de perfil desde un archivo JSON.
        
        Args:
            template_mode: Si es True, no muestra mensaje de éxito ni actualiza la interfaz
        """
        # Mostrar diálogo para seleccionar archivo
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Importar Configuración",
            "",
            "Archivos JSON (*.json);;Todos los archivos (*)"
        )
        
        if not file_path:
            return None  # Usuario canceló
            
        try:
            import json
            with open(file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            # Validar estructura del archivo
            required_fields = [
                'profile_id', 'medida_formula', 'cantidad_formula',
                'condicion', 'angulo_izquierdo', 'angulo_derecho'
            ]
            
            if not all(field in config for field in required_fields):
                raise ValueError("El archivo de configuración no tiene el formato correcto.")
                
            # Cargar perfiles si no están cargados
            self._cargar_perfiles()
            
            # Buscar y seleccionar el perfil
            index = self.combo_perfil.findData(config['profile_id'])
            if index >= 0:
                if not template_mode:
                    self.combo_perfil.setCurrentIndex(index)
                    
                    # Aplicar configuración
                    self.campo_medida.setText(config['medida_formula'])
                    self.campo_cantidad_formula.setText(config['cantidad_formula'])
                    self.campo_condicion.setText(config['condicion'])
                    self.campo_angulo_izq.setValue(float(config['angulo_izquierdo']))
                    self.campo_angulo_der.setValue(float(config['angulo_derecho']))
                    self.campo_observaciones.setPlainText(config.get('observaciones', ''))
                    
                    QMessageBox.information(
                        self,
                        "Importación Exitosa",
                        "La configuración se ha importado correctamente."
                    )
                return config
            else:
                QMessageBox.warning(
                    self,
                    "Perfil no Encontrado",
                    "El perfil de esta configuración no está disponible en el sistema."
                )
                return None
                
        except json.JSONDecodeError:
            QMessageBox.critical(
                self,
                "Error de Formato",
                "El archivo seleccionado no es un archivo JSON válido."
            )
            return None
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error al Importar",
                f"No se pudo importar la configuración:\n{str(e)}"
            )
            return None
            
    def _gestionar_plantillas(self):
        """Muestra un diálogo para gestionar plantillas guardadas."""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QListWidget, QDialogButtonBox, QHBoxLayout, QPushButton
        
        class GestionarPlantillasDialog(QDialog):
            def __init__(self, parent=None):
                super().__init__(parent)
                self.setWindowTitle("Gestionar Plantillas")
                
                layout = QVBoxLayout(self)
                
                # Lista de plantillas
                self.lista_plantillas = QListWidget()
                self._cargar_plantillas()
                layout.addWidget(self.lista_plantillas)
                
                # Botones de acción
                btn_layout = QHBoxLayout()
                
                self.btn_cargar = QPushButton("Cargar")
                self.btn_cargar.clicked.connect(self.accept)
                self.btn_eliminar = QPushButton("Eliminar")
                self.btn_eliminar.clicked.connect(self._eliminar_plantilla)
                
                btn_layout.addWidget(self.btn_cargar)
                btn_layout.addWidget(self.btn_eliminar)
                btn_layout.addStretch()
                
                # Botón de cancelar
                btn_cancelar = QPushButton("Cerrar")
                btn_cancelar.clicked.connect(self.reject)
                btn_layout.addWidget(btn_cancelar)
                
                layout.addLayout(btn_layout)
                
                # Actualizar estado de los botones
                self.lista_plantillas.itemSelectionChanged.connect(self._actualizar_botones)
                self._actualizar_botones()
            
            def _cargar_plantillas(self):
                """Carga la lista de plantillas guardadas."""
                import os
                from pathlib import Path
                
                self.lista_plantillas.clear()
                templates_dir = self._get_templates_dir()
                
                if templates_dir.exists():
                    for file in templates_dir.glob("*.json"):
                        self.lista_plantillas.addItem(file.stem)
            
            def _get_templates_dir(self):
                """Obtiene el directorio de plantillas, creándolo si no existe."""
                from pathlib import Path
                import os
                
                app_data = Path.home() / ".pro2000" / "templates"
                app_data.mkdir(parents=True, exist_ok=True)
                return app_data
            
            def _actualizar_botones(self):
                """Actualiza el estado de los botones según la selección."""
                seleccionado = bool(self.lista_plantillas.currentItem())
                self.btn_cargar.setEnabled(seleccionado)
                self.btn_eliminar.setEnabled(seleccionado)
            
            def _eliminar_plantilla(self):
                """Elimina la plantilla seleccionada."""
                item = self.lista_plantillas.currentItem()
                if not item:
                    return
                    
                from pathlib import Path
                template_file = self._get_templates_dir() / f"{item.text()}.json"
                
                try:
                    template_file.unlink()
                    self.lista_plantillas.takeItem(self.lista_plantillas.row(item))
                except Exception as e:
                    QMessageBox.critical(
                        self,
                        "Error",
                        f"No se pudo eliminar la plantilla:\n{str(e)}"
                    )
            
            def get_selected_template(self):
                """Devuelve el nombre de la plantilla seleccionada."""
                item = self.lista_plantillas.currentItem()
                return item.text() if item else None
        
        # Mostrar diálogo de gestión de plantillas
        dialog = GestionarPlantillasDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            template_name = dialog.get_selected_template()
            if template_name:
                self._cargar_plantilla(template_name)
    
    def _cargar_plantilla(self, template_name):
        """Carga una plantilla guardada."""
        from pathlib import Path
        templates_dir = Path.home() / ".pro2000" / "templates"
        template_file = templates_dir / f"{template_name}.json"
        
        if not template_file.exists():
            QMessageBox.warning(
                self,
                "Plantilla no Encontrada",
                f"No se encontró la plantilla: {template_name}"
            )
            return
            
        try:
            import json
            with open(template_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            # Aplicar configuración
            index = self.combo_perfil.findData(config['profile_id'])
            if index >= 0:
                self.combo_perfil.setCurrentIndex(index)
                self.campo_medida.setText(config['medida_formula'])
                self.campo_cantidad_formula.setText(config['cantidad_formula'])
                self.campo_condicion.setText(config['condicion'])
                # Set the combo box index based on the angle value
                angulo_izq = float(config['angulo_izquierdo'])
                index = self.combo_angulo_izquierdo.findData(angulo_izq)
                if index >= 0:
                    self.combo_angulo_izquierdo.setCurrentIndex(index)
                
                angulo_der = float(config['angulo_derecho'])
                index = self.combo_angulo_derecho.findData(angulo_der)
                if index >= 0:
                    self.combo_angulo_derecho.setCurrentIndex(index)
                    
                self.campo_observaciones.setPlainText(config.get('observaciones', ''))
                
                QMessageBox.information(
                    self,
                    "Plantilla Cargada",
                    f"Se ha cargado la plantilla: {template_name}"
                )
            else:
                QMessageBox.warning(
                    self,
                    "Perfil no Encontrado",
                    "El perfil de esta plantilla no está disponible en el sistema."
                )
                
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error al Cargar Plantilla",
                f"No se pudo cargar la plantilla:\n{str(e)}"
            )
    
    def _guardar_como_plantilla(self):
        """Guarda la configuración actual como una plantilla."""
        from PyQt6.QtWidgets import QInputDialog
        
        # Obtener nombre de la plantilla
        nombre, ok = QInputDialog.getText(
            self,
            "Guardar como Plantilla",
            "Nombre de la plantilla:",
            text=f"Plantilla_{self.combo_perfil.currentText()}"
        )
        
        if not ok or not nombre.strip():
            return  # Usuario canceló o nombre vacío
            
        # Validar nombre del archivo
        import re
        nombre_valido = re.sub(r'[^\w\-_. ]', '_', nombre.strip())
        
        # Obtener configuración actual
        config = self._obtener_configuracion_actual()
        
        # Guardar plantilla
        from pathlib import Path
        templates_dir = Path.home() / ".pro2000" / "templates"
        templates_dir.mkdir(parents=True, exist_ok=True)
        
        template_file = templates_dir / f"{nombre_valido}.json"
        
        try:
            import json
            with open(template_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
            QMessageBox.information(
                self,
                "Plantilla Guardada",
                f"La plantilla se ha guardado correctamente como:\n{nombre_valido}"
            )
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error al Guardar",
                f"No se pudo guardar la plantilla:\n{str(e)}"
            )
    
    def _obtener_configuracion_actual(self):
        """Obtiene la configuración actual del diálogo."""
        return {
            'profile_id': self.combo_perfil.currentData(),
            'profile_name': self.combo_perfil.currentText(),
            'medida_formula': self.campo_medida.text().strip(),
            'cantidad_formula': self.campo_cantidad_formula.text().strip(),
            'condicion': self.campo_condicion.text().strip(),
            'angulo_izquierdo': float(self.combo_angulo_izquierdo.currentData() or 90.0),
            'angulo_derecho': float(self.combo_angulo_derecho.currentData() or 90.0),
            'observaciones': self.campo_observaciones.toPlainText().strip(),
            'fecha_creacion': datetime.datetime.now().isoformat()
        }
    
    def _setup_syntax_highlighting(self):
        """Configura el resaltado de sintaxis para los campos de fórmula."""
        # Solo aplicar resaltado a campos QTextEdit que tengan el método document()
        if hasattr(self, 'campo_medida') and hasattr(self.campo_medida, 'document'):
            self.highlighter_medida = FormulaHighlighter(self.campo_medida.document())
        
        if hasattr(self, 'campo_cantidad_formula') and hasattr(self.campo_cantidad_formula, 'document'):
            self.highlighter_cantidad = FormulaHighlighter(self.campo_cantidad_formula.document())
        
        if hasattr(self, 'campo_condicion') and hasattr(self.campo_condicion, 'document'):
            self.highlighter_condicion = FormulaHighlighter(self.campo_condicion.document())
        
        # Conectar la validación en tiempo real
        for campo in [self.campo_medida, self.campo_cantidad_formula, self.campo_condicion]:
            if hasattr(campo, 'textChanged'):
                campo.textChanged.connect(self._iniciar_validacion_temporizada)
    
    def _setup_keyboard_shortcuts(self):
        """Configura los atajos de teclado para el diálogo."""
        # Guardar con Ctrl+S
        self._shortcut_guardar = QShortcut(QKeySequence.StandardKey.Save, self)
        self._shortcut_guardar.activated.connect(self._aceptar)
        
        # Cancelar con Esc
        self._shortcut_cancelar = QShortcut(QKeySequence.StandardKey.Cancel, self)
        self._shortcut_cancelar.activated.connect(self.reject)
        
        # Validar fórmulas con F5
        self._shortcut_validar = QShortcut(QKeySequence("F5"), self)
        self._shortcut_validar.activated.connect(self._validar_formulas)
        
        # Cambiar entre pestañas con Ctrl+Tab
        self._shortcut_siguiente_tab = QShortcut(QKeySequence("Ctrl+Tab"), self)
        self._shortcut_siguiente_tab.activated.connect(self._siguiente_tab)
        
        # Cambiar a pestaña anterior con Ctrl+Shift+Tab
        self._shortcut_tab_anterior = QShortcut(QKeySequence("Ctrl+Shift+Tab"), self)
        self._shortcut_tab_anterior.activated.connect(self._tab_anterior)
    
    def _siguiente_tab(self):
        """Cambia a la siguiente pestaña."""
        if hasattr(self, 'tab_widget'):
            current = self.tab_widget.currentIndex()
            total = self.tab_widget.count()
            self.tab_widget.setCurrentIndex((current + 1) % total)
    
    def _tab_anterior(self):
        """Cambia a la pestaña anterior."""
        if hasattr(self, 'tab_widget'):
            current = self.tab_widget.currentIndex()
            total = self.tab_widget.count()
            self.tab_widget.setCurrentIndex((current - 1) % total)
    
    def _probar_con_medidas(self):
        """Muestra un diálogo para probar las fórmulas con valores de ejemplo."""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QFormLayout, QDialogButtonBox, QLineEdit, QMessageBox
        from PyQt6.QtCore import Qt
        
        class ProbarMedidasDialog(QDialog):
            def __init__(self, parent=None):
                super().__init__(parent)
                self.setWindowTitle("Probar con Medidas")
                self.setModal(True)
                
                layout = QVBoxLayout(self)
                form_layout = QFormLayout()
                
                # Campos para las variables de entrada
                self.campos = {}
                for var, desc in parent.variables_medidas.items():
                    campo = QLineEdit()
                    campo.setPlaceholderText(f"Ingrese valor para {var} ({desc})")
                    form_layout.addRow(f"{var} ({desc}):", campo)
                    self.campos[var] = campo
                
                layout.addLayout(form_layout)
                
                # Botones
                btn_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
                btn_box.accepted.connect(self.accept)
                btn_box.rejected.connect(self.reject)
                layout.addWidget(btn_box)
                
                self.setLayout(layout)
            
            def get_valores(self):
                """Obtiene los valores ingresados por el usuario."""
                valores = {}
                for var, campo in self.campos.items():
                    try:
                        valor = float(campo.text().replace(',', '.')) if campo.text() else 0.0
                        valores[var] = valor
                    except ValueError:
                        valores[var] = 0.0
                return valores
        
        # Mostrar diálogo para ingresar valores
        dialog = ProbarMedidasDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            valores = dialog.get_valores()
            
            # Crear entorno seguro para evaluación
            safe_dict = {
                '__builtins__': {
                    'min': min,
                    'max': max,
                    'round': round,
                    'abs': abs,
                    'int': int,
                    'float': float
                }
            }
            
            # Agregar variables al entorno seguro
            safe_dict.update(valores)
            
            # Evaluar fórmulas
            resultados = []
            
            # 1. Evaluar fórmula de medida
            if hasattr(self, 'campo_medida') and self.campo_medida.text().strip():
                try:
                    formula_medida = self.campo_medida.text()
                    resultado = eval(formula_medida, {"__builtins__": {}}, safe_dict)
                    resultados.append(f"<b>Fórmula de medida:</b> {formula_medida}")
                    resultados.append(f"<b>Resultado:</b> {resultado:.2f} mm")
                except Exception as e:
                    resultados.append(f"<b>Error en fórmula de medida:</b> {str(e)}")
            
            # 2. Evaluar fórmula de cantidad
            if hasattr(self, 'campo_cantidad_formula') and self.campo_cantidad_formula.text().strip():
                try:
                    formula_cantidad = self.campo_cantidad_formula.text()
                    resultado = eval(formula_cantidad, {"__builtins__": {}}, safe_dict)
                    resultados.append(f"<b>Fórmula de cantidad:</b> {formula_cantidad}")
                    resultados.append(f"<b>Resultado:</b> {resultado:.2f} unidades")
                except Exception as e:
                    resultados.append(f"<b>Error en fórmula de cantidad:</b> {str(e)}")
            
            # 3. Mostrar valores utilizados
            valores_utilizados = ["<b>Valores utilizados:</b>"]
            for var, valor in valores.items():
                if valor != 0:  # Solo mostrar valores no nulos
                    valores_utilizados.append(f"{var} = {valor}")
            
            # Mostrar resultados
            if resultados:
                mensaje = "<b>Resultados de la prueba:</b><br><br>"
                mensaje += "<br>".join(resultados)
                mensaje += "<br><br>" + "<br>".join(valores_utilizados)
                
                msg = QMessageBox(self)
                msg.setIcon(QMessageBox.Icon.Information)
                msg.setWindowTitle("Prueba de Fórmulas")
                msg.setText("Resultados de la prueba")
                msg.setInformativeText(mensaje)
                msg.setStandardButtons(QMessageBox.StandardButton.Ok)
                msg.setTextFormat(Qt.TextFormat.RichText)
                msg.exec()
            else:
                QMessageBox.warning(
                    self,
                    "Sin fórmulas para probar",
                    "No hay fórmulas definidas para probar. Por favor, ingrese al menos una fórmula en la pestaña de Fórmulas."
                )
    
    def _validar_formulas(self, show_success=True):
        """Valida las fórmulas ingresadas.
        
        Args:
            show_success: Si es True, muestra un mensaje de éxito cuando la validación es exitosa.
        """
        # Implementación básica de validación
        valido = True
        mensajes = []
        
        # Validar fórmula de medida
        if not self.campo_medida.text().strip():
            mensajes.append("La fórmula de medida no puede estar vacía")
            valido = False
            
        # Validar fórmula de cantidad
        if not self.campo_cantidad_formula.text().strip():
            mensajes.append("La fórmula de cantidad no puede estar vacía")
            valido = False
            
        # Mostrar mensaje de validación
        if valido:
            if show_success:
                QMessageBox.information(
                    self,
                    "Validación exitosa",
                    "Todas las fórmulas son válidas."
                )
            return True
        else:
            QMessageBox.warning(
                self,
                "Error de validación",
                "Se encontraron los siguientes errores:\n• " + "\n• ".join(mensajes)
            )
            return False

    def _iniciar_validacion_temporizada(self):
        """Inicia la validación de los campos del formulario."""
        # Validar fórmula de medida
        medida = self.campo_medida.text().strip()
        if medida:
            # Aquí iría la lógica de validación específica
            pass
            
        # Validar fórmula de cantidad
        cantidad = self.campo_cantidad_formula.text().strip()
        if cantidad:
            # Aquí iría la lógica de validación específica
            pass
            
        # Validar condición
        condicion = self.campo_condicion.text().strip()
        if condicion:
            # Aquí iría la lógica de validación específica
            pass
    
    def _setup_validation_timer(self):
        """Configura el temporizador para la validación en tiempo real."""
        self._validation_timer = QTimer(self)
        self._validation_timer.setSingleShot(True)
        self._validation_timer.timeout.connect(self._iniciar_validacion_temporizada)

    def _aceptar(self):
        """Valida y guarda el componente."""
        if self.combo_perfil.currentData() is None:
            QMessageBox.warning(self, "Error", "Debe seleccionar un perfil.")
            self.tabs.setCurrentIndex(0)  # Ir a la primera pestaña
            self.combo_perfil.setFocus()
            return False

        if not self.campo_medida.text().strip():
            QMessageBox.warning(self, "Error", "Debe especificar una fórmula de medida.")
            self.tabs.setCurrentIndex(2)  # Ir a la pestaña de fórmulas
            self.campo_medida.setFocus()
            return False
            
        # Validar fórmulas antes de guardar
        if not self._validar_formulas(show_success=False):
            QMessageBox.warning(
                self,
                "Error de Validación",
                "No se puede guardar hasta que se corrijan los errores en las fórmulas."
            )
            self.tabs.setCurrentIndex(2)  # Ir a la pestaña de fórmulas
            return False

        if self._guardar_componente():
            self.accept()
    def _cargar_datos_componente(self):
        """Carga los datos del componente para edición."""
        try:
            if not self.componente:
                print("No se proporcionó componente para cargar")
                return
                
            print(f"Cargando datos del componente: {self.componente}")
            
            # Buscar el perfil en el combo
            if hasattr(self.componente, 'perfil_id') and self.componente.perfil_id:
                index = self.combo_perfil.findData(self.componente.perfil_id)
                if index >= 0:
                    print(f"Estableciendo perfil en índice {index} con ID {self.componente.perfil_id}")
                    self.combo_perfil.setCurrentIndex(index)
                else:
                    print(f"No se encontró el perfil con ID {self.componente.perfil_id} en el combo")
            
            # Establecer valores de los campos básicos
            if hasattr(self.componente, 'medida_formula'):
                self.campo_medida.setText(self.componente.medida_formula or "")
                print(f"Establecida fórmula de medida: {self.componente.medida_formula}")
                
            if hasattr(self.componente, 'condicion'):
                self.campo_condicion.setText(self.componente.condicion or "")
                print(f"Establecida condición: {self.componente.condicion}")
                
            if hasattr(self.componente, 'cantidad_formula'):
                self.campo_cantidad_formula.setText(self.componente.cantidad_formula or "")
                print(f"Establecida fórmula de cantidad: {self.componente.cantidad_formula}")
            
            # Establecer ángulos si existen
            if hasattr(self.componente, 'angulo_izquierdo') and self.componente.angulo_izquierdo is not None:
                try:
                    angulo_izq = float(self.componente.angulo_izquierdo)
                    index = self.combo_angulo_izquierdo.findData(angulo_izq)
                    if index >= 0:
                        self.combo_angulo_izquierdo.setCurrentIndex(index)
                        print(f"Establecido ángulo izquierdo: {angulo_izq}° en índice {index}")
                    else:
                        print(f"No se encontró el ángulo izquierdo {angulo_izq}° en el combo")
                except (ValueError, TypeError) as e:
                    print(f"Error al establecer ángulo izquierdo: {e}")
                    
            if hasattr(self.componente, 'angulo_derecho') and self.componente.angulo_derecho is not None:
                try:
                    angulo_der = float(self.componente.angulo_derecho)
                    index = self.combo_angulo_derecho.findData(angulo_der)
                    if index >= 0:
                        self.combo_angulo_derecho.setCurrentIndex(index)
                        print(f"Establecido ángulo derecho: {angulo_der}° en índice {index}")
                    else:
                        print(f"No se encontró el ángulo derecho {angulo_der}° en el combo")
                except (ValueError, TypeError) as e:
                    print(f"Error al establecer ángulo derecho: {e}")
            
            # Establecer variable de medida si existe
            if hasattr(self.componente, 'variable_medida') and self.componente.variable_medida:
                index = self.combo_variable_medida.findData(self.componente.variable_medida)
                if index >= 0:
                    self.combo_variable_medida.setCurrentIndex(index)
                    print(f"Establecida variable de medida: {self.componente.variable_medida} en índice {index}")
                else:
                    print(f"No se encontró la variable de medida {self.componente.variable_medida} en el combo")
            
            # Establecer tipo de perfil si existe
            if hasattr(self.componente, 'tipo_perfil') and self.componente.tipo_perfil:
                # Buscar por el código del tipo de perfil (user data)
                index = self.combo_tipo_perfil.findData(self.componente.tipo_perfil)
                if index >= 0:
                    self.combo_tipo_perfil.setCurrentIndex(index)
                    print(f"Establecido tipo de perfil: {self.componente.tipo_perfil} en índice {index}")
                else:
                    # Si no se encuentra por código, intentar buscar por texto
                    index = self.combo_tipo_perfil.findText(self.componente.tipo_perfil)
                    if index >= 0:
                        self.combo_tipo_perfil.setCurrentIndex(index)
                        print(f"Establecido tipo de perfil por texto: {self.componente.tipo_perfil} en índice {index}")
                    else:
                        print(f"No se encontró el tipo de perfil {self.componente.tipo_perfil} en el combo")
                    
            # Establecer posición si existe
            if hasattr(self.componente, 'posicion') and self.componente.posicion:
                index = self.combo_posicion.findText(self.componente.posicion)
                if index >= 0:
                    self.combo_posicion.setCurrentIndex(index)
                    print(f"Establecida posición: {self.componente.posicion} en índice {index}")
                else:
                    print(f"No se encontró la posición {self.componente.posicion} en el combo")
                    
            # Establecer observaciones si existe
            if hasattr(self.componente, 'observaciones'):
                self.campo_observaciones.setPlainText(self.componente.observaciones or "")
                print(f"Establecidas observaciones: {self.componente.observaciones}")
                
            print("Carga de datos del componente completada")
            
        except Exception as e:
            print(f"Error en _cargar_datos_componente: {str(e)}")

    def _guardar_componente(self):
        """Guarda el componente en la base de datos."""
        db = next(get_db())

        try:
            if self.componente:
                # Editar componente existente
                componente_db = db.query(ArticuloPerfil).filter(
                    ArticuloPerfil.id == self.componente.id
                ).first()
                if not componente_db:
                    QMessageBox.critical(self, "Error", "No se encontró el componente a editar.")
                    return False
            else:
                # Crear nuevo componente
                componente_db = ArticuloPerfil()
                componente_db.articulo_id = self.articulo.id
                db.add(componente_db)

            # Asignar valores básicos
            componente_db.perfil_id = self.combo_perfil.currentData()
            componente_db.medida_formula = self.campo_medida.text().strip() or None
            componente_db.condicion = self.campo_condicion.text().strip() or None
            componente_db.cantidad_base = self.campo_cantidad_base.value()
            componente_db.cantidad_formula = self.campo_cantidad_formula.text().strip() or None
            componente_db.orden = self.campo_orden.value()

            # Asignar valores profesionales
            componente_db.tipo_perfil = self.combo_tipo_perfil.currentData()
            componente_db.angulo_izquierdo = float(self.combo_angulo_izquierdo.currentData())
            componente_db.angulo_derecho = float(self.combo_angulo_derecho.currentData())
            componente_db.variable_medida = self.combo_variable_medida.currentData()
            componente_db.descripcion_posicion = self.campo_descripcion_posicion.text().strip() or None

            db.commit()

            # Mensaje de éxito
            tipo_perfil_desc = self.combo_tipo_perfil.currentText().split(' - ')[0]
            perfil_desc = self.combo_perfil.currentText().split(' - ')[0]

            QMessageBox.information(
                self,
                "✅ Éxito",
                f"Perfil {tipo_perfil_desc} '{perfil_desc}' guardado correctamente.\n\n"
                f"Configuración:\n"
                f"• Tipo: {tipo_perfil_desc}\n"
                f"• Ángulos: {self.combo_angulo_izquierdo.currentText()} / {self.combo_angulo_derecho.currentText()}\n"
                f"• Variable: {self.combo_variable_medida.currentText().split(' - ')[0]}\n"
                f"• Fórmula: {self.campo_medida.text() or 'Sin fórmula'}"
            )

            return True

        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self,
                "❌ Error",
                f"No se pudo guardar el componente: {str(e)}"
            )
            return False
        finally:
            db.close()
