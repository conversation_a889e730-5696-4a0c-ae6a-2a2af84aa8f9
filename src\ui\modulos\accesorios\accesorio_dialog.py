"""
<PERSON><PERSON><PERSON><PERSON> que define el diálogo para gestionar accesorios.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QLineEdit, QTableWidget, QTableWidgetItem, QMessageBox, QHeaderView,
    QDialogButtonBox, QFormLayout, QDoubleSpinBox, QCheckBox, QComboBox, QSpinBox
)
from PyQt6.QtCore import Qt, pyqtSlot
from PyQt6.QtGui import QIcon

from models.base import get_db
from models.accesorio import Accesorio
from models.perfil import Distribuidor
from models.tipo_accesorio import TipoAccesorio
from .tipo_accesorio_dialog import TipoAccesorioDialog
from ui.utils.window_utils import force_dialog_maximized

class AccesorioDialog(QDialog):
    """Diálogo para gestionar accesorios."""
    
    def __init__(self, parent=None):
        """
        Inicializa el diálogo de gestión de accesorios.

        Args:
            parent: Widget padre
        """
        super().__init__(parent)
        self.setWindowTitle("Gestión de Accesorios")

        # ✅ FORZAR MAXIMIZACIÓN COMPLETA
        force_dialog_maximized(self, "Gestión de Accesorios")

        # Variables
        self.accesorio_actual = None

        # Configurar interfaz
        self._configurar_ui()

        # Cargar datos
        self._cargar_accesorios()
    
    def _configurar_ui(self):
        """Configura la interfaz de usuario del diálogo."""
        # Layout principal
        layout_principal = QVBoxLayout(self)
        
        # Barra de herramientas
        layout_botones = QHBoxLayout()
        
        self.boton_nuevo = QPushButton("Nuevo")
        self.boton_editar = QPushButton("Editar")
        self.boton_eliminar = QPushButton("Eliminar")
        self.boton_actualizar = QPushButton("Actualizar")
        
        # Deshabilitar botones hasta que se seleccione un accesorio
        self.boton_editar.setEnabled(False)
        self.boton_eliminar.setEnabled(False)
        
        layout_botones.addWidget(self.boton_nuevo)
        layout_botones.addWidget(self.boton_editar)
        layout_botones.addWidget(self.boton_eliminar)
        layout_botones.addStretch()

        # Botón para gestionar tipos
        self.boton_tipos = QPushButton("Gestionar Tipos")
        self.boton_tipos.clicked.connect(self._gestionar_tipos)
        layout_botones.addWidget(self.boton_tipos)

        layout_botones.addWidget(self.boton_actualizar)
        
        # Tabla de accesorios
        self.tabla_accesorios = QTableWidget()
        self.tabla_accesorios.setColumnCount(9)
        self.tabla_accesorios.setHorizontalHeaderLabels([
            "Código", "Descripción", "Distribuidor", "Tipo", "Unidad", "Precio", "Stock", "Stock Mínimo", "Activo"
        ])
        
        # Ajustar el ancho de las columnas
        header = self.tabla_accesorios.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # Código
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Descripción
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Distribuidor
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Tipo
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Unidad
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # Precio
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # Stock
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)  # Stock Mínimo
        header.setSectionResizeMode(8, QHeaderView.ResizeMode.ResizeToContents)  # Activo
        
        # Conectar señales
        self.boton_nuevo.clicked.connect(self._on_nuevo_accesorio)
        self.boton_editar.clicked.connect(self._on_editar_accesorio)
        self.boton_eliminar.clicked.connect(self._on_eliminar_accesorio)
        self.boton_actualizar.clicked.connect(self._cargar_accesorios)
        self.tabla_accesorios.itemSelectionChanged.connect(self._on_seleccion_cambiada)
        self.tabla_accesorios.doubleClicked.connect(self._on_editar_accesorio)
        
        # Añadir widgets al layout principal
        layout_principal.addLayout(layout_botones)
        layout_principal.addWidget(self.tabla_accesorios)
        
        # Botones de diálogo
        botones_dialogo = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        botones_dialogo.rejected.connect(self.reject)
        layout_principal.addWidget(botones_dialogo)
    
    def _cargar_accesorios(self):
        """Carga la lista de accesorios desde la base de datos."""
        db = next(get_db())

        try:
            # Obtener todos los accesorios
            accesorios = db.query(Accesorio).order_by(Accesorio.codigo).all()

            # Configurar la tabla
            self.tabla_accesorios.setRowCount(len(accesorios))

            for fila, accesorio in enumerate(accesorios):
                # Código
                self.tabla_accesorios.setItem(fila, 0, QTableWidgetItem(accesorio.codigo))

                # Descripción
                self.tabla_accesorios.setItem(fila, 1, QTableWidgetItem(accesorio.descripcion))

                # Distribuidor - obtener de forma segura
                distribuidor_nombre = "Sin distribuidor"
                if accesorio.distribuidor_id:
                    try:
                        distribuidor = db.query(Distribuidor).filter(Distribuidor.id == accesorio.distribuidor_id).first()
                        if distribuidor:
                            distribuidor_nombre = distribuidor.nombre
                    except Exception as e:
                        print(f"Error obteniendo distribuidor: {e}")
                        distribuidor_nombre = "Error al cargar"

                self.tabla_accesorios.setItem(fila, 2, QTableWidgetItem(distribuidor_nombre))

                # Tipo
                self.tabla_accesorios.setItem(fila, 3, QTableWidgetItem(accesorio.tipo or ""))

                # Unidad
                self.tabla_accesorios.setItem(fila, 4, QTableWidgetItem(accesorio.unidad or ""))

                # Precio
                precio_item = QTableWidgetItem(f"{accesorio.precio:.2f}")
                precio_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.tabla_accesorios.setItem(fila, 5, precio_item)

                # Stock
                stock_item = QTableWidgetItem(str(accesorio.stock or 0))
                stock_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.tabla_accesorios.setItem(fila, 6, stock_item)

                # Stock Mínimo
                stock_min_item = QTableWidgetItem(str(accesorio.stock_minimo or 0))
                stock_min_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.tabla_accesorios.setItem(fila, 7, stock_min_item)

                # Activo
                activo_item = QTableWidgetItem()
                activo_item.setFlags(Qt.ItemFlag.ItemIsUserCheckable | Qt.ItemFlag.ItemIsEnabled)
                activo_item.setCheckState(
                    Qt.CheckState.Checked if accesorio.activo else Qt.CheckState.Unchecked
                )
                activo_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.tabla_accesorios.setItem(fila, 8, activo_item)
            
        except Exception as e:
            print(f"Error detallado cargando accesorios: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudieron cargar los accesorios: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        finally:
            db.close()
    
    def _on_seleccion_cambiada(self):
        """Maneja el evento de cambio de selección en la tabla."""
        seleccion = self.tabla_accesorios.selectedItems()
        habilitar = len(seleccion) > 0
        
        self.boton_editar.setEnabled(habilitar)
        self.boton_eliminar.setEnabled(habilitar)
    
    def _on_nuevo_accesorio(self):
        """Maneja el evento de crear un nuevo accesorio."""
        dialogo = AccesorioEditarDialog(self)
        if dialogo.exec() == QDialog.DialogCode.Accepted:
            # Guardar el nuevo accesorio
            datos = dialogo.get_datos()
            db = next(get_db())

            try:
                nuevo_accesorio = Accesorio(**datos)
                db.add(nuevo_accesorio)
                db.commit()

                QMessageBox.information(
                    self,
                    "Éxito",
                    f"Accesorio '{datos['codigo']}' creado correctamente.",
                    QMessageBox.StandardButton.Ok
                )

                self._cargar_accesorios()

            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self,
                    "Error",
                    f"No se pudo crear el accesorio: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )
            finally:
                db.close()
    
    def _on_editar_accesorio(self):
        """Maneja el evento de editar un accesorio existente."""
        fila = self.tabla_accesorios.currentRow()
        if fila < 0:
            return

        codigo = self.tabla_accesorios.item(fila, 0).text()
        db = next(get_db())

        try:
            accesorio = db.query(Accesorio).filter(Accesorio.codigo == codigo).first()
            if not accesorio:
                raise ValueError("Accesorio no encontrado")

            dialogo = AccesorioEditarDialog(self, accesorio)
            if dialogo.exec() == QDialog.DialogCode.Accepted:
                # Actualizar el accesorio existente
                datos = dialogo.get_datos()

                accesorio.descripcion = datos['descripcion']
                accesorio.distribuidor_id = datos['distribuidor_id']
                accesorio.tipo = datos['tipo']  # Legacy
                accesorio.tipo_accesorio_id = datos['tipo_accesorio_id']
                accesorio.unidad = datos['unidad']
                accesorio.precio = datos['precio']
                accesorio.stock = datos['stock']
                accesorio.stock_minimo = datos['stock_minimo']
                accesorio.activo = datos['activo']

                db.commit()

                QMessageBox.information(
                    self,
                    "Éxito",
                    f"Accesorio '{accesorio.codigo}' actualizado correctamente.",
                    QMessageBox.StandardButton.Ok
                )

                self._cargar_accesorios()

        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudo actualizar el accesorio: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        finally:
            db.close()
    
    def _on_eliminar_accesorio(self):
        """Maneja el evento de eliminar un accesorio."""
        fila = self.tabla_accesorios.currentRow()
        if fila < 0:
            return
        
        codigo = self.tabla_accesorios.item(fila, 0).text()
        descripcion = self.tabla_accesorios.item(fila, 1).text()
        
        respuesta = QMessageBox.question(
            self,
            "Confirmar eliminación",
            f"¿Está seguro de que desea eliminar el accesorio '{codigo} - {descripcion}'?\n"
            "Esta acción no se puede deshacer.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if respuesta == QMessageBox.StandardButton.Yes:
            db = next(get_db())
            try:
                accesorio = db.query(Accesorio).filter(Accesorio.codigo == codigo).first()
                if accesorio:
                    db.delete(accesorio)
                    db.commit()
                    self._cargar_accesorios()
            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self,
                    "Error",
                    f"No se pudo eliminar el accesorio: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )
            finally:
                db.close()

    def _gestionar_tipos(self):
        """Abre el diálogo de gestión de tipos de accesorios."""
        dialogo = TipoAccesorioDialog(self)
        dialogo.exec()


class AccesorioEditarDialog(QDialog):
    """Diálogo para editar o crear un accesorio."""

    UNIDADES = [
        "Unidad", "Juego", "Metro", "Paquete", "Caja", "Litro", "Kg"
    ]
    
    def __init__(self, parent=None, accesorio=None):
        """
        Inicializa el diálogo de edición de accesorio.
        
        Args:
            parent: Widget padre
            accesorio: Instancia de Accesorio a editar (None para nuevo)
        """
        super().__init__(parent)
        
        self.accesorio = accesorio
        self.setWindowTitle("Editar Accesorio" if accesorio else "Nuevo Accesorio")

        # Configuración inteligente del diálogo
        from ui.utils.window_utils import smart_dialog_setup
        smart_dialog_setup(self, "simple", self.windowTitle(), maximize=False)
        
        # Configurar interfaz
        self._configurar_ui()

        # Cargar distribuidores y tipos
        self._cargar_distribuidores()
        self._cargar_tipos_accesorios()

        # Si se está editando un accesorio, cargar sus datos
        if accesorio:
            self._cargar_datos_accesorio()
    
    def _configurar_ui(self):
        """Configura la interfaz de usuario del diálogo."""
        # Layout principal
        layout_principal = QVBoxLayout(self)
        
        # Formulario
        layout_formulario = QFormLayout()
        layout_formulario.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.AllNonFixedFieldsGrow)
        
        # Código
        self.codigo = QLineEdit()
        self.codigo.setMaxLength(10)
        layout_formulario.addRow("Código*:", self.codigo)
        
        # Descripción
        self.descripcion = QLineEdit()
        self.descripcion.setMaxLength(100)
        layout_formulario.addRow("Descripción*:", self.descripcion)

        # Distribuidor
        self.combo_distribuidor = QComboBox()
        layout_formulario.addRow("Distribuidor:", self.combo_distribuidor)

        # Tipo con botón para gestionar
        layout_tipo = QHBoxLayout()
        self.combo_tipo = QComboBox()
        layout_tipo.addWidget(self.combo_tipo)

        self.btn_gestionar_tipos = QPushButton("Gestionar")
        self.btn_gestionar_tipos.clicked.connect(self._gestionar_tipos_desde_edicion)
        layout_tipo.addWidget(self.btn_gestionar_tipos)

        layout_formulario.addRow("Tipo:", layout_tipo)
        
        # Unidad
        self.unidad = QComboBox()
        self.unidad.addItems(self.UNIDADES)
        self.unidad.setCurrentText("Unidad")  # Valor por defecto
        layout_formulario.addRow("Unidad*:", self.unidad)
        
        # Precio
        self.precio = QDoubleSpinBox()
        self.precio.setRange(0, 10000)
        self.precio.setDecimals(2)
        self.precio.setPrefix("€ ")
        self.precio.setValue(0)
        layout_formulario.addRow("Precio*:", self.precio)
        
        # Stock
        self.stock = QSpinBox()
        self.stock.setRange(0, 1000000)
        self.stock.setValue(0)
        layout_formulario.addRow("Stock:", self.stock)
        
        # Stock Mínimo
        self.stock_minimo = QSpinBox()
        self.stock_minimo.setRange(0, 1000000)
        self.stock_minimo.setValue(0)
        layout_formulario.addRow("Stock Mínimo:", self.stock_minimo)
        
        # Activo
        self.activo = QCheckBox("Accesorio activo")
        self.activo.setChecked(True)
        layout_formulario.addRow("", self.activo)
        
        # Validación
        self.etiqueta_error = QLabel()
        self.etiqueta_error.setStyleSheet("color: red;")
        self.etiqueta_error.setVisible(False)
        
        # Botones
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel
        )
        botones.accepted.connect(self._validar_y_aceptar)
        botones.rejected.connect(self.reject)
        
        # Añadir widgets al layout principal
        layout_principal.addLayout(layout_formulario)
        layout_principal.addWidget(self.etiqueta_error)
        layout_principal.addWidget(botones)
        
        # Conectar señales para validación en tiempo real
        self.codigo.textChanged.connect(self._validar_formulario)
        self.descripcion.textChanged.connect(self._validar_formulario)
        self.unidad.currentTextChanged.connect(self._validar_formulario)

    def _cargar_distribuidores(self):
        """Carga los distribuidores en el combo."""
        db = next(get_db())
        try:
            distribuidores = db.query(Distribuidor).filter(Distribuidor.activo == True).order_by(Distribuidor.nombre).all()

            # Limpiar combo
            self.combo_distribuidor.clear()
            self.combo_distribuidor.addItem("Sin distribuidor", None)

            for distribuidor in distribuidores:
                self.combo_distribuidor.addItem(distribuidor.nombre, distribuidor.id)

        except Exception as e:
            print(f"Error cargando distribuidores: {e}")
            # Asegurar que al menos tenemos la opción "Sin distribuidor"
            self.combo_distribuidor.clear()
            self.combo_distribuidor.addItem("Sin distribuidor", None)
        finally:
            db.close()

    def _cargar_tipos_accesorios(self):
        """Carga los tipos de accesorios en el combo."""
        db = next(get_db())
        try:
            tipos = db.query(TipoAccesorio).filter(TipoAccesorio.activo == True).order_by(TipoAccesorio.nombre).all()

            # Limpiar combo
            self.combo_tipo.clear()
            self.combo_tipo.addItem("Sin tipo", None)

            for tipo in tipos:
                self.combo_tipo.addItem(tipo.nombre, tipo.id)

        except Exception as e:
            print(f"Error cargando tipos de accesorios: {e}")
            # Asegurar que al menos tenemos la opción "Sin tipo"
            self.combo_tipo.clear()
            self.combo_tipo.addItem("Sin tipo", None)
        finally:
            db.close()

    def _gestionar_tipos_desde_edicion(self):
        """Abre el diálogo de gestión de tipos desde el diálogo de edición."""
        dialogo = TipoAccesorioDialog(self)
        if dialogo.exec() == QDialog.DialogCode.Accepted:
            # Recargar tipos después de gestionar
            tipo_actual = self.combo_tipo.currentData()
            self._cargar_tipos_accesorios()
            # Intentar mantener la selección actual
            if tipo_actual:
                index = self.combo_tipo.findData(tipo_actual)
                if index >= 0:
                    self.combo_tipo.setCurrentIndex(index)

    def _cargar_datos_accesorio(self):
        """Carga los datos del accesorio en el formulario."""
        if not self.accesorio:
            return
            
        self.codigo.setText(self.accesorio.codigo)
        self.codigo.setReadOnly(True)  # No permitir editar el código
        self.descripcion.setText(self.accesorio.descripcion)

        # Establecer el distribuidor
        if self.accesorio.distribuidor_id:
            index_dist = self.combo_distribuidor.findData(self.accesorio.distribuidor_id)
            if index_dist >= 0:
                self.combo_distribuidor.setCurrentIndex(index_dist)

        # Establecer el tipo de accesorio
        if self.accesorio.tipo_accesorio_id:
            index = self.combo_tipo.findData(self.accesorio.tipo_accesorio_id)
            if index >= 0:
                self.combo_tipo.setCurrentIndex(index)
        elif self.accesorio.tipo:
            # Fallback para tipos legacy (texto)
            index = self.combo_tipo.findText(self.accesorio.tipo)
            if index >= 0:
                self.combo_tipo.setCurrentIndex(index)

        # Establecer la unidad
        index_unidad = self.unidad.findText(self.accesorio.unidad or "Unidad")
        if index_unidad >= 0:
            self.unidad.setCurrentIndex(index_unidad)
        
        self.precio.setValue(float(self.accesorio.precio or 0))
        self.stock.setValue(int(self.accesorio.stock or 0))
        self.stock_minimo.setValue(int(self.accesorio.stock_minimo or 0))
        self.activo.setChecked(bool(self.accesorio.activo))
    
    def _validar_formulario(self):
        """Valida los campos del formulario."""
        codigo = self.codigo.text().strip()
        descripcion = self.descripcion.text().strip()
        unidad = self.unidad.currentText().strip()
        
        if not codigo:
            self.mostrar_error("El código es obligatorio")
            return False
            
        if not descripcion:
            self.mostrar_error("La descripción es obligatoria")
            return False
            
        if not unidad:
            self.mostrar_error("Debe seleccionar una unidad")
            return False
            
        if self.precio.value() < 0:
            self.mostrar_error("El precio no puede ser negativo")
            return False
            
        if self.stock.value() < 0:
            self.mostrar_error("El stock no puede ser negativo")
            return False
            
        if self.stock_minimo.value() < 0:
            self.mostrar_error("El stock mínimo no puede ser negativo")
            return False
            
        self.ocultar_error()
        return True
    
    def mostrar_error(self, mensaje):
        """Muestra un mensaje de error en el formulario."""
        self.etiqueta_error.setText(mensaje)
        self.etiqueta_error.setVisible(True)
    
    def ocultar_error(self):
        """Oculta el mensaje de error."""
        self.etiqueta_error.setVisible(False)
    
    def _validar_y_aceptar(self):
        """Valida el formulario y acepta el diálogo si es válido."""
        if not self._validar_formulario():
            return
            
        # Validar que el código no esté duplicado (solo para nuevos accesorios)
        if not self.accesorio:
            db = next(get_db())
            try:
                existe = db.query(Accesorio).filter(Accesorio.codigo == self.codigo.text().strip()).first()
                if existe:
                    self.mostrar_error("Ya existe un accesorio con este código")
                    return
            finally:
                db.close()
        
        self.accept()
    
    def get_datos(self):
        """
        Devuelve un diccionario con los datos del formulario.
        
        Returns:
            dict: Datos del accesorio
        """
        return {
            'codigo': self.codigo.text().strip(),
            'descripcion': self.descripcion.text().strip(),
            'distribuidor_id': self.combo_distribuidor.currentData(),
            'tipo': self.combo_tipo.currentText() if self.combo_tipo.currentText() != "Sin tipo" else None,  # Legacy
            'tipo_accesorio_id': self.combo_tipo.currentData(),
            'unidad': self.unidad.currentText(),
            'precio': float(self.precio.value()),
            'stock': int(self.stock.value() or 0),
            'stock_minimo': int(self.stock_minimo.value() or 0),
            'activo': self.activo.isChecked()
        }
