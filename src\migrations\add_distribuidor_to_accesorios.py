"""
Script de migración para añadir la columna distribuidor_id a la tabla accesorios
"""
import sqlite3
import os

def migrate_database():
    """Añade la columna distribuidor_id a la tabla accesorios si no existe."""

    # Buscar la base de datos en varias ubicaciones posibles
    possible_paths = [
        "data/pro2000.db",
        "../data/pro2000.db",
        "../../data/pro2000.db",
        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'pro2000.db')
    ]

    db_path = None
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            break

    if not db_path:
        print("❌ No se pudo encontrar la base de datos pro2000.db")
        return

    print(f"📍 Usando base de datos: {db_path}")

    conn = None
    try:
        # Conectar a la base de datos
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Verificar si la columna ya existe
        cursor.execute("PRAGMA table_info(accesorios)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'distribuidor_id' not in columns:
            print("Añadiendo columna distribuidor_id a la tabla accesorios...")

            # Añadir la columna
            cursor.execute("""
                ALTER TABLE accesorios
                ADD COLUMN distribuidor_id INTEGER
                REFERENCES distribuidores(id)
            """)

            conn.commit()
            print("✅ Columna distribuidor_id añadida exitosamente!")
        else:
            print("✅ La columna distribuidor_id ya existe en la tabla accesorios.")

    except Exception as e:
        print(f"❌ Error durante la migración: {e}")
        import traceback
        traceback.print_exc()
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    migrate_database()
