"""
Diálogo principal para la gestión de artículos.
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QTableWidget,
    QTableWidgetItem, QHeaderView, QMessageBox, QLineEdit, QLabel,
    QComboBox, QFormLayout, QDialogButtonBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon, QFont

from models.base import get_db
from models.articulo import Articulo
from ui.utils.window_utils import setup_maximized_dialog


class ArticuloDialog(QDialog):
    """Diálogo principal para gestionar artículos."""

    def __init__(self, parent=None):
        """Inicializa el diálogo de gestión de artículos."""
        super().__init__(parent)
        
        # Configuración básica
        self.setWindowTitle("Gestión de Artículos")
        
        # Variables
        self.articulos = []
        
        # Configuración inteligente
        setup_maximized_dialog(self, "Gestión de Artículos")
        
        # Inicializar la interfaz
        self._inicializar_ui()
        
        # Cargar datos
        self._cargar_articulos()
    
    def _inicializar_ui(self):
        """Configura la interfaz de usuario del diálogo."""
        layout_principal = QVBoxLayout(self)
        
        # Título
        titulo = QLabel("Gestión de Artículos")
        titulo.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout_principal.addWidget(titulo)
        
        # Barra de herramientas
        self._crear_barra_herramientas(layout_principal)
        
        # Filtros
        self._crear_filtros(layout_principal)
        
        # Tabla de artículos
        self._crear_tabla_articulos(layout_principal)
        
        # Botones de acción
        self._crear_botones_accion(layout_principal)
    
    def _crear_barra_herramientas(self, layout_principal):
        """Crea la barra de herramientas."""
        barra_herramientas = QHBoxLayout()
        
        # Botón de nuevo artículo
        self.boton_nuevo = QPushButton("Nuevo Artículo")
        self.boton_nuevo.clicked.connect(self.nuevo_articulo)
        barra_herramientas.addWidget(self.boton_nuevo)
        
        # Botón de refrescar
        boton_refrescar = QPushButton("Refrescar")
        boton_refrescar.clicked.connect(self._cargar_articulos)
        barra_herramientas.addWidget(boton_refrescar)
        
        # Espaciador
        barra_herramientas.addStretch()
        
        layout_principal.addLayout(barra_herramientas)
    
    def _crear_filtros(self, layout_principal):
        """Crea los controles de filtrado."""
        layout_filtros = QHBoxLayout()
        
        # Campo de búsqueda
        self.campo_busqueda = QLineEdit()
        self.campo_busqueda.setPlaceholderText("Buscar por código, serie o descripción...")
        self.campo_busqueda.textChanged.connect(self._aplicar_filtros)
        layout_filtros.addWidget(self.campo_busqueda)
        
        # Filtro por estado
        self.combo_activo = QComboBox()
        self.combo_activo.addItem("Todos", None)
        self.combo_activo.addItem("Activos", True)
        self.combo_activo.addItem("Inactivos", False)
        self.combo_activo.currentIndexChanged.connect(self._aplicar_filtros)
        layout_filtros.addWidget(QLabel("Estado:"))
        layout_filtros.addWidget(self.combo_activo)
        
        # Botón de limpiar filtros
        boton_limpiar = QPushButton("Limpiar")
        boton_limpiar.clicked.connect(self._limpiar_filtros)
        layout_filtros.addWidget(boton_limpiar)
        
        layout_principal.addLayout(layout_filtros)
    
    def _crear_tabla_articulos(self, layout_principal):
        """Crea la tabla de artículos."""
        self.tabla_articulos = QTableWidget()
        self.tabla_articulos.setColumnCount(6)
        self.tabla_articulos.setHorizontalHeaderLabels([
            "Código", "Serie", "Descripción", "T. Taller", "T. Obra", "Estado"
        ])
        
        # Configurar la tabla
        self.tabla_articulos.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.tabla_articulos.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.tabla_articulos.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.tabla_articulos.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        self.tabla_articulos.horizontalHeader().setStretchLastSection(True)
        self.tabla_articulos.verticalHeader().setVisible(False)
        
        # Conectar señales
        self.tabla_articulos.itemSelectionChanged.connect(self._actualizar_botones_accion)
        self.tabla_articulos.itemDoubleClicked.connect(self.editar_articulo)
        
        # Ajustar el ancho de las columnas
        self.tabla_articulos.setColumnWidth(0, 100)  # Código
        self.tabla_articulos.setColumnWidth(1, 150)  # Serie
        self.tabla_articulos.setColumnWidth(2, 300)  # Descripción
        self.tabla_articulos.setColumnWidth(3, 80)   # T. Taller
        self.tabla_articulos.setColumnWidth(4, 80)   # T. Obra
        self.tabla_articulos.setColumnWidth(5, 80)   # Estado
        
        layout_principal.addWidget(self.tabla_articulos)
    
    def _crear_botones_accion(self, layout_principal):
        """Crea los botones de acción."""
        layout_botones = QHBoxLayout()
        
        # Botón de editar
        self.boton_editar = QPushButton("Editar")
        self.boton_editar.clicked.connect(self.editar_articulo)
        self.boton_editar.setEnabled(False)
        layout_botones.addWidget(self.boton_editar)
        
        # Botón de duplicar
        self.boton_duplicar = QPushButton("Duplicar")
        self.boton_duplicar.clicked.connect(self.duplicar_articulo)
        self.boton_duplicar.setEnabled(False)
        layout_botones.addWidget(self.boton_duplicar)
        
        # Botón de eliminar
        self.boton_eliminar = QPushButton("Eliminar")
        self.boton_eliminar.clicked.connect(self.eliminar_articulo)
        self.boton_eliminar.setEnabled(False)
        layout_botones.addWidget(self.boton_eliminar)
        
        # Espaciador\n        layout_botones.addStretch()
        
        # Botón de cerrar
        boton_cerrar = QPushButton("Cerrar")
        boton_cerrar.clicked.connect(self.close)
        layout_botones.addWidget(boton_cerrar)
        
        layout_principal.addLayout(layout_botones)
    
    def _cargar_articulos(self):
        """Carga los artículos desde la base de datos."""
        db = next(get_db())
        
        try:
            # Limpiar la tabla
            self.tabla_articulos.setRowCount(0)
            
            # Obtener todos los artículos
            query = db.query(Articulo).order_by(Articulo.codigo)
            
            # Aplicar filtros si existen
            texto_busqueda = self.campo_busqueda.text().strip()
            if texto_busqueda:
                query = query.filter(
                    (Articulo.codigo.ilike(f'%{texto_busqueda}%')) |
                    (Articulo.serie.ilike(f'%{texto_busqueda}%')) |
                    (Articulo.descripcion.ilike(f'%{texto_busqueda}%'))
                )
            
            estado_filtro = self.combo_activo.currentData()
            if estado_filtro is not None:
                query = query.filter(Articulo.activo == estado_filtro)
            
            # Obtener los artículos
            self.articulos = query.all()
            
            # Llenar la tabla
            for articulo in self.articulos:
                fila = self.tabla_articulos.rowCount()
                self.tabla_articulos.insertRow(fila)
                
                # Agregar celdas
                self.tabla_articulos.setItem(fila, 0, QTableWidgetItem(articulo.codigo or ""))
                self.tabla_articulos.setItem(fila, 1, QTableWidgetItem(articulo.serie or ""))
                self.tabla_articulos.setItem(fila, 2, QTableWidgetItem(articulo.descripcion or ""))
                self.tabla_articulos.setItem(fila, 3, QTableWidgetItem(f"{articulo.tiempo_taller:.2f}h" if articulo.tiempo_taller else "0.00h"))
                self.tabla_articulos.setItem(fila, 4, QTableWidgetItem(f"{articulo.tiempo_obra:.2f}h" if articulo.tiempo_obra else "0.00h"))
                self.tabla_articulos.setItem(fila, 5, QTableWidgetItem("Activo" if articulo.activo else "Inactivo"))
                
                # Almacenar el ID del artículo en la fila
                self.tabla_articulos.item(fila, 0).setData(Qt.ItemDataRole.UserRole, articulo.id)
            
            # Actualizar el contador en el título
            self.setWindowTitle(f"Gestión de Artículos ({len(self.articulos)} artículos)")
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Error al cargar los artículos: {str(e)}"
            )
        finally:
            db.close()
    
    def _aplicar_filtros(self):
        """Aplica los filtros de búsqueda."""
        self._cargar_articulos()
    
    def _limpiar_filtros(self):
        """Limpia todos los filtros de búsqueda."""
        self.campo_busqueda.clear()
        self.combo_activo.setCurrentIndex(0)
        self._cargar_articulos()
    
    def _actualizar_botones_accion(self):
        """Actualiza el estado de los botones según la selección."""
        seleccion = self.tabla_articulos.selectedItems()
        habilitar = len(seleccion) > 0
        
        self.boton_editar.setEnabled(habilitar)
        self.boton_duplicar.setEnabled(habilitar)
        self.boton_eliminar.setEnabled(habilitar)
    
    def nuevo_articulo(self):
        """Abre el diálogo para crear un nuevo artículo."""
        try:
            from .articulo_editar_dialog import ArticuloEditarDialog
            dialog = ArticuloEditarDialog(self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self._cargar_articulos()
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al abrir nuevo artículo: {str(e)}")
    
    def editar_articulo(self):
        """Abre el diálogo para editar el artículo seleccionado."""
        fila = self.tabla_articulos.currentRow()
        if fila < 0:
            return
        
        try:
            # Obtener el ID del artículo seleccionado
            articulo_id = self.tabla_articulos.item(fila, 0).data(Qt.ItemDataRole.UserRole)
            
            # Buscar el artículo en la lista
            articulo = next((a for a in self.articulos if a.id == articulo_id), None)
            if not articulo:
                return
            
            # Abrir el diálogo de edición
            from .articulo_editar_dialog import ArticuloEditarDialog
            dialog = ArticuloEditarDialog(self, articulo)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self._cargar_articulos()
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al editar artículo: {str(e)}")
    
    def duplicar_articulo(self):
        """Duplica el artículo seleccionado."""
        fila = self.tabla_articulos.currentRow()
        if fila < 0:
            return
        
        try:
            # Obtener el ID del artículo seleccionado
            articulo_id = self.tabla_articulos.item(fila, 0).data(Qt.ItemDataRole.UserRole)
            
            # Buscar el artículo en la lista
            articulo = next((a for a in self.articulos if a.id == articulo_id), None)
            if not articulo:
                return
            
            # Abrir el diálogo de duplicación
            from .articulo_editar_dialog import ArticuloEditarDialog
            dialog = ArticuloEditarDialog(self, articulo, duplicar=True)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self._cargar_articulos()
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al duplicar artículo: {str(e)}")
    
    def eliminar_articulo(self):
        """Elimina el artículo seleccionado."""
        fila = self.tabla_articulos.currentRow()
        if fila < 0:
            return
        
        try:
            # Obtener el ID del artículo seleccionado
            articulo_id = self.tabla_articulos.item(fila, 0).data(Qt.ItemDataRole.UserRole)
            
            # Buscar el artículo en la lista
            articulo = next((a for a in self.articulos if a.id == articulo_id), None)
            if not articulo:
                return
            
            # Confirmar eliminación
            respuesta = QMessageBox.question(
                self,
                "Confirmar eliminación",
                f"¿Está seguro de que desea eliminar el artículo '{articulo.codigo}'?\\n"
                "Esta acción no se puede deshacer.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if respuesta == QMessageBox.StandardButton.Yes:
                db = next(get_db())
                try:
                    articulo_db = db.query(Articulo).filter(Articulo.id == articulo.id).first()
                    if articulo_db:
                        db.delete(articulo_db)
                        db.commit()
                        self._cargar_articulos()
                        QMessageBox.information(
                            self,
                            "Éxito",
                            "El artículo ha sido eliminado correctamente."
                        )
                except Exception as e:
                    db.rollback()
                    QMessageBox.critical(
                        self,
                        "Error",
                        f"No se pudo eliminar el artículo: {str(e)}"
                    )
                finally:
                    db.close()
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al eliminar artículo: {str(e)}")
