#!/usr/bin/env python3
"""
Sistema de Configuración Centralizado para PRO-2000
Maneja toda la configuración de la aplicación desde un solo lugar
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

class Environment(Enum):
    """Entornos de ejecución"""
    DEVELOPMENT = "development"
    PRODUCTION = "production"
    TESTING = "testing"

class ThemeType(Enum):
    """Tipos de tema disponibles"""
    PROFESSIONAL = "professional"
    MODERN = "modern"
    DARK = "dark"
    LIGHT = "light"

@dataclass
class DatabaseConfig:
    """Configuración de base de datos"""
    url: str = "sqlite:///data/pro2000.db"
    echo: bool = False
    pool_size: int = 10
    max_overflow: int = 20
    backup_interval_hours: int = 24
    
@dataclass
class UIConfig:
    """Configuración de interfaz de usuario"""
    theme: str = ThemeType.PROFESSIONAL.value
    window_width: int = 1200
    window_height: int = 800
    font_family: str = "Segoe UI"
    font_size: int = 14
    auto_save_interval: int = 300  # segundos
    
@dataclass
class PathsConfig:
    """Configuración de rutas"""
    data_dir: str = "data"
    resources_dir: str = "resources"
    logs_dir: str = "logs"
    backup_dir: str = "data/backup"
    temp_dir: str = "data/temp"
    reports_dir: str = "data/reports"
    templates_dir: str = "resources/templates"
    
@dataclass
class LoggingConfig:
    """Configuración de logging"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_max_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    console_output: bool = True
    
@dataclass
class SecurityConfig:
    """Configuración de seguridad"""
    session_timeout_minutes: int = 480  # 8 horas
    max_login_attempts: int = 3
    password_min_length: int = 6
    enable_encryption: bool = True
    
@dataclass
class PerformanceConfig:
    """Configuración de rendimiento"""
    cache_size: int = 128
    enable_lazy_loading: bool = True
    max_workers: int = 4
    query_timeout: int = 30

class AppConfig:
    """Configuración principal de la aplicación"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config/app_config.json"
        self.root_dir = Path(__file__).parent.parent.absolute()
        
        # Configuraciones por defecto
        self.app_name = "PRO-2000"
        self.version = "2.1.0"
        self.environment = Environment.PRODUCTION
        
        # Componentes de configuración
        self.database = DatabaseConfig()
        self.ui = UIConfig()
        self.paths = PathsConfig()
        self.logging = LoggingConfig()
        self.security = SecurityConfig()
        self.performance = PerformanceConfig()
        
        # Cargar configuración desde archivo
        self.load_config()
        
        # Resolver rutas absolutas
        self._resolve_paths()
    
    def _resolve_paths(self):
        """Convierte rutas relativas a absolutas"""
        for attr_name in ['data_dir', 'resources_dir', 'logs_dir', 'backup_dir', 
                         'temp_dir', 'reports_dir', 'templates_dir']:
            relative_path = getattr(self.paths, attr_name)
            absolute_path = self.root_dir / relative_path
            setattr(self.paths, attr_name, str(absolute_path))
    
    def load_config(self):
        """Carga configuración desde archivo JSON"""
        config_path = self.root_dir / self.config_file
        
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # Aplicar configuración cargada
                self._apply_config_data(config_data)
                
            except Exception as e:
                print(f"⚠️ Error cargando configuración: {e}")
                print("📋 Usando configuración por defecto")
    
    def _apply_config_data(self, config_data: Dict[str, Any]):
        """Aplica datos de configuración cargados"""
        # Configuración general
        self.app_name = config_data.get('app_name', self.app_name)
        self.version = config_data.get('version', self.version)
        
        env_str = config_data.get('environment', self.environment.value)
        try:
            self.environment = Environment(env_str)
        except ValueError:
            self.environment = Environment.PRODUCTION
        
        # Configuraciones específicas
        if 'database' in config_data:
            self._update_dataclass(self.database, config_data['database'])
        
        if 'ui' in config_data:
            self._update_dataclass(self.ui, config_data['ui'])
        
        if 'paths' in config_data:
            self._update_dataclass(self.paths, config_data['paths'])
        
        if 'logging' in config_data:
            self._update_dataclass(self.logging, config_data['logging'])
        
        if 'security' in config_data:
            self._update_dataclass(self.security, config_data['security'])
        
        if 'performance' in config_data:
            self._update_dataclass(self.performance, config_data['performance'])
    
    def _update_dataclass(self, dataclass_instance, new_data: Dict[str, Any]):
        """Actualiza un dataclass con nuevos datos"""
        for key, value in new_data.items():
            if hasattr(dataclass_instance, key):
                setattr(dataclass_instance, key, value)
    
    def save_config(self):
        """Guarda la configuración actual en archivo JSON"""
        config_data = {
            'app_name': self.app_name,
            'version': self.version,
            'environment': self.environment.value,
            'database': asdict(self.database),
            'ui': asdict(self.ui),
            'paths': asdict(self.paths),
            'logging': asdict(self.logging),
            'security': asdict(self.security),
            'performance': asdict(self.performance)
        }
        
        config_path = self.root_dir / self.config_file
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"❌ Error guardando configuración: {e}")
            return False
    
    def create_directories(self):
        """Crea los directorios necesarios"""
        directories = [
            self.paths.data_dir,
            self.paths.resources_dir,
            self.paths.logs_dir,
            self.paths.backup_dir,
            self.paths.temp_dir,
            self.paths.reports_dir,
            self.paths.templates_dir
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def get_database_url(self):
        """Obtiene la URL de base de datos con rutas absolutas"""
        if self.database.url.startswith("sqlite:///"):
            # Convertir ruta relativa a absoluta
            db_path = self.database.url.replace("sqlite:///", "")
            if not Path(db_path).is_absolute():
                db_path = self.root_dir / db_path
            return f"sqlite:///{db_path}"
        return self.database.url
    
    def is_development(self) -> bool:
        """Verifica si está en modo desarrollo"""
        return self.environment == Environment.DEVELOPMENT
    
    def is_production(self) -> bool:
        """Verifica si está en modo producción"""
        return self.environment == Environment.PRODUCTION
    
    def get_log_file_path(self) -> str:
        """Obtiene la ruta del archivo de log"""
        return str(Path(self.paths.logs_dir) / f"{self.app_name.lower()}.log")

# Instancia global de configuración
config = AppConfig()

def get_config() -> AppConfig:
    """Obtiene la instancia global de configuración"""
    return config

if __name__ == "__main__":
    # Crear configuración inicial
    print("⚙️ Configurando PRO-2000...")
    
    config = get_config()
    config.create_directories()
    
    # Guardar configuración por defecto
    if config.save_config():
        print("✅ Configuración creada correctamente")
        print(f"📁 Archivo: {config.root_dir / config.config_file}")
    else:
        print("❌ Error creando configuración")
    
    print(f"\n🎯 PRO-2000 v{config.version}")
    print(f"📊 Entorno: {config.environment.value}")
    print(f"🎨 Tema: {config.ui.theme}")
    print(f"💾 Base de datos: {config.get_database_url()}")
