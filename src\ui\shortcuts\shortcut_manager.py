"""
Gestor de atajos de teclado para PRO-2000.
Proporciona atajos consistentes en toda la aplicación.
"""

from PyQt6.QtWidgets import QWidget, QMessageBox, QDialog, QVBoxLayout, QLabel, QTableWidget, QTableWidgetItem, QDialogButtonBox, QHeaderView
from PyQt6.QtGui import QKeySequence, QFont, QShortcut
from PyQt6.QtCore import Qt, QSettings


class ShortcutManager:
    """Gestor de atajos de teclado."""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.shortcuts = {}
        self.settings = QSettings("PRO-2000", "Shortcuts")
        
        # Definir atajos por defecto
        self.default_shortcuts = {
            # Archivo
            'new_article': ('Ctrl+N', 'Nuevo Artículo'),
            'save': ('Ctrl+S', 'Guardar'),
            'open': ('Ctrl+O', 'Abrir'),
            'print': ('Ctrl+P', 'Imprimir'),
            'export': ('Ctrl+E', 'Exportar'),
            'quit': ('Ctrl+Q', 'Salir'),
            
            # Edición
            'copy': ('Ctrl+C', 'Copiar'),
            'paste': ('Ctrl+V', 'Pegar'),
            'cut': ('Ctrl+X', 'Cortar'),
            'undo': ('Ctrl+Z', 'Deshacer'),
            'redo': ('Ctrl+Y', 'Rehacer'),
            'select_all': ('Ctrl+A', 'Seleccionar Todo'),
            'find': ('Ctrl+F', 'Buscar'),
            'replace': ('Ctrl+H', 'Reemplazar'),
            
            # Navegación
            'next_tab': ('Ctrl+Tab', 'Siguiente Pestaña'),
            'prev_tab': ('Ctrl+Shift+Tab', 'Pestaña Anterior'),
            'close_tab': ('Ctrl+W', 'Cerrar Pestaña'),
            'new_tab': ('Ctrl+T', 'Nueva Pestaña'),
            
            # Módulos específicos
            'articles': ('Alt+A', 'Módulo Artículos'),
            'works': ('Alt+O', 'Módulo Obras'),
            'profiles': ('Alt+P', 'Módulo Perfiles'),
            'accessories': ('Alt+C', 'Módulo Accesorios'),
            'crystals': ('Alt+R', 'Módulo Cristales'),
            'reports': ('Alt+I', 'Informes'),
            'optimization': ('Alt+M', 'Optimización'),
            'dashboard': ('Alt+D', 'Dashboard'),
            
            # Funciones especiales
            'refresh': ('F5', 'Actualizar'),
            'fullscreen': ('F11', 'Pantalla Completa'),
            'help': ('F1', 'Ayuda'),
            'settings': ('Ctrl+,', 'Configuración'),
            'themes': ('Ctrl+Shift+T', 'Selector de Temas'),
            'shortcuts': ('Ctrl+Shift+K', 'Ver Atajos'),
            
            # Acciones rápidas
            'quick_add': ('Ctrl+Shift+N', 'Agregar Rápido'),
            'quick_search': ('Ctrl+Shift+F', 'Búsqueda Rápida'),
            'quick_export': ('Ctrl+Shift+E', 'Exportación Rápida'),
            'quick_print': ('Ctrl+Shift+P', 'Impresión Rápida'),
            
            # Desarrollo y debug
            'debug_info': ('Ctrl+Shift+D', 'Información de Debug'),
            'reload_ui': ('Ctrl+Shift+R', 'Recargar Interfaz')
        }
        
        self._setup_shortcuts()
    
    def _setup_shortcuts(self):
        """Configura todos los atajos de teclado."""
        for action_name, (key_sequence, description) in self.default_shortcuts.items():
            # Cargar atajo personalizado si existe
            saved_sequence = self.settings.value(f"shortcut_{action_name}", key_sequence)
            
            # Crear atajo
            shortcut = QShortcut(QKeySequence(saved_sequence), self.main_window)
            
            # Conectar a método si existe
            method_name = f"_shortcut_{action_name}"
            if hasattr(self, method_name):
                shortcut.activated.connect(getattr(self, method_name))
            
            # Guardar referencia
            self.shortcuts[action_name] = {
                'shortcut': shortcut,
                'sequence': saved_sequence,
                'description': description,
                'default': key_sequence
            }
    
    # Métodos para atajos específicos
    def _shortcut_new_article(self):
        """Atajo para nuevo artículo."""
        try:
            # Cambiar a pestaña de artículos y crear nuevo
            for i in range(self.main_window.tabs.count()):
                if "Artículos" in self.main_window.tabs.tabText(i):
                    self.main_window.tabs.setCurrentIndex(i)
                    # Aquí se podría llamar al método de nuevo artículo
                    break
        except Exception as e:
            print(f"Error en atajo nuevo artículo: {e}")
    
    def _shortcut_save(self):
        """Atajo para guardar."""
        # Enviar Ctrl+S al widget actual
        current_widget = self.main_window.tabs.currentWidget()
        if current_widget and hasattr(current_widget, 'save'):
            current_widget.save()
    
    def _shortcut_quit(self):
        """Atajo para salir."""
        self.main_window.close()
    
    def _shortcut_find(self):
        """Atajo para buscar."""
        current_widget = self.main_window.tabs.currentWidget()
        if current_widget and hasattr(current_widget, 'show_search'):
            current_widget.show_search()
    
    def _shortcut_next_tab(self):
        """Atajo para siguiente pestaña."""
        current_index = self.main_window.tabs.currentIndex()
        next_index = (current_index + 1) % self.main_window.tabs.count()
        self.main_window.tabs.setCurrentIndex(next_index)
    
    def _shortcut_prev_tab(self):
        """Atajo para pestaña anterior."""
        current_index = self.main_window.tabs.currentIndex()
        prev_index = (current_index - 1) % self.main_window.tabs.count()
        self.main_window.tabs.setCurrentIndex(prev_index)
    
    def _shortcut_close_tab(self):
        """Atajo para cerrar pestaña."""
        current_index = self.main_window.tabs.currentIndex()
        if current_index > 0:  # No cerrar la primera pestaña
            self.main_window.tabs.removeTab(current_index)
    
    def _shortcut_articles(self):
        """Atajo para módulo artículos."""
        self._open_module("Artículos")
    
    def _shortcut_works(self):
        """Atajo para módulo obras."""
        self._open_module("Obras")
    
    def _shortcut_profiles(self):
        """Atajo para módulo perfiles."""
        self._open_module("Perfiles")
    
    def _shortcut_accessories(self):
        """Atajo para módulo accesorios."""
        self._open_module("Accesorios")
    
    def _shortcut_crystals(self):
        """Atajo para módulo cristales."""
        self._open_module("Cristales")
    
    def _shortcut_optimization(self):
        """Atajo para optimización."""
        if hasattr(self.main_window, '_abrir_optimizacion_cortes'):
            self.main_window._abrir_optimizacion_cortes()
    
    def _shortcut_dashboard(self):
        """Atajo para dashboard."""
        if hasattr(self.main_window, '_abrir_dashboard_ejecutivo'):
            self.main_window._abrir_dashboard_ejecutivo()
    
    def _shortcut_refresh(self):
        """Atajo para actualizar."""
        current_widget = self.main_window.tabs.currentWidget()
        if current_widget and hasattr(current_widget, 'refresh'):
            current_widget.refresh()
    
    def _shortcut_fullscreen(self):
        """Atajo para pantalla completa."""
        if self.main_window.isFullScreen():
            self.main_window.showNormal()
        else:
            self.main_window.showFullScreen()
    
    def _shortcut_help(self):
        """Atajo para ayuda."""
        if hasattr(self.main_window, 'mostrar_acerca_de'):
            self.main_window.mostrar_acerca_de()
    
    def _shortcut_themes(self):
        """Atajo para selector de temas."""
        if hasattr(self.main_window, '_abrir_selector_temas'):
            self.main_window._abrir_selector_temas()
    
    def _shortcut_shortcuts(self):
        """Atajo para mostrar lista de atajos."""
        self.show_shortcuts_dialog()
    
    def _shortcut_debug_info(self):
        """Atajo para información de debug."""
        self._show_debug_info()
    
    def _shortcut_reload_ui(self):
        """Atajo para recargar interfaz."""
        if hasattr(self.main_window, 'theme_manager'):
            self.main_window.theme_manager.load_saved_theme()
        self.main_window.statusBar().showMessage("Interfaz recargada", 2000)
    
    def _open_module(self, module_name):
        """Abre un módulo específico."""
        for i in range(self.main_window.tabs.count()):
            if module_name in self.main_window.tabs.tabText(i):
                self.main_window.tabs.setCurrentIndex(i)
                return
        
        # Si no existe la pestaña, mostrar mensaje
        self.main_window.statusBar().showMessage(f"Módulo {module_name} no disponible", 2000)
    
    def _show_debug_info(self):
        """Muestra información de debug."""
        import sys
        from PyQt6.QtCore import QT_VERSION_STR, PYQT_VERSION_STR
        
        info = f"""
        PRO-2000 Debug Information
        
        Python Version: {sys.version}
        Qt Version: {QT_VERSION_STR}
        PyQt Version: {PYQT_VERSION_STR}
        
        Current Tab: {self.main_window.tabs.currentIndex()}
        Total Tabs: {self.main_window.tabs.count()}
        Window Size: {self.main_window.size().width()}x{self.main_window.size().height()}
        
        Active Shortcuts: {len(self.shortcuts)}
        Theme: {getattr(self.main_window.theme_manager, 'current_theme', 'Unknown')}
        """
        
        QMessageBox.information(self.main_window, "Debug Info", info)
    
    def show_shortcuts_dialog(self):
        """Muestra el diálogo con todos los atajos."""
        dialog = ShortcutsDialog(self.main_window, self.shortcuts)
        dialog.exec()
    
    def get_shortcut(self, action_name):
        """Obtiene un atajo específico."""
        return self.shortcuts.get(action_name)
    
    def update_shortcut(self, action_name, new_sequence):
        """Actualiza un atajo de teclado."""
        if action_name in self.shortcuts:
            self.shortcuts[action_name]['shortcut'].setKey(QKeySequence(new_sequence))
            self.shortcuts[action_name]['sequence'] = new_sequence
            self.settings.setValue(f"shortcut_{action_name}", new_sequence)
    
    def reset_shortcuts(self):
        """Restaura todos los atajos a sus valores por defecto."""
        for action_name, shortcut_info in self.shortcuts.items():
            default_sequence = shortcut_info['default']
            self.update_shortcut(action_name, default_sequence)
        
        self.settings.clear()


class ShortcutsDialog(QDialog):
    """Diálogo para mostrar todos los atajos de teclado."""
    
    def __init__(self, parent, shortcuts):
        super().__init__(parent)
        self.shortcuts = shortcuts
        
        self.setWindowTitle("Atajos de Teclado - PRO-2000")
        self.setMinimumSize(600, 500)
        self.setModal(True)
        
        self._setup_ui()
        self._load_shortcuts()
    
    def _setup_ui(self):
        """Configura la interfaz."""
        layout = QVBoxLayout(self)
        
        # Título
        title = QLabel("⌨️ Atajos de Teclado")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Tabla de atajos
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["Acción", "Atajo", "Descripción"])
        
        # Configurar tabla
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        
        layout.addWidget(self.table)
        
        # Botones
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok)
        buttons.accepted.connect(self.accept)
        layout.addWidget(buttons)
    
    def _load_shortcuts(self):
        """Carga los atajos en la tabla."""
        self.table.setRowCount(len(self.shortcuts))
        
        for row, (action_name, shortcut_info) in enumerate(self.shortcuts.items()):
            # Nombre de acción (formateado)
            action_display = action_name.replace('_', ' ').title()
            self.table.setItem(row, 0, QTableWidgetItem(action_display))
            
            # Secuencia de teclas
            sequence = shortcut_info['sequence']
            self.table.setItem(row, 1, QTableWidgetItem(sequence))
            
            # Descripción
            description = shortcut_info['description']
            self.table.setItem(row, 2, QTableWidgetItem(description))
