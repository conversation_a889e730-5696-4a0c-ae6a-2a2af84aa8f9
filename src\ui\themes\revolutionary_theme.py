"""
Tema Revolucionario para PRO-2000
Diseño completamente nuevo inspirado en aplicaciones modernas como PrefSuite, Notion, Figma
Estética: Glassmorphism + Neumorphism + Colores vibrantes
"""

from PyQt6.QtCore import QPropertyAnimation, QEasingCurve, QRect, pyqtSignal, QTimer
from PyQt6.QtWidgets import QGraphicsDropShadowEffect, QWidget
from PyQt6.QtGui import QColor, QPalette, QFont
import qtawesome as qta


class RevolutionaryTheme:
    """Tema completamente revolucionario y moderno"""
    
    # Paleta de colores COMPLETAMENTE NUEVA - Inspirada en aplicaciones modernas
    COLORS = {
        # Colores principales - Gradientes vibrantes
        'primary': '#6366f1',          # Indigo vibrante
        'primary_light': '#8b5cf6',    # Violeta
        'primary_dark': '#4f46e5',     # Indigo oscuro
        'secondary': '#06b6d4',        # <PERSON><PERSON> brillante
        'accent': '#f59e0b',           # Ámbar dorado
        
        # Colores de estado - Modernos y vibrantes
        'success': '#10b981',          # Esmeralda
        'warning': '#f59e0b',          # Ámbar
        'error': '#ef4444',            # Rojo coral
        'info': '#3b82f6',             # Azul brillante
        
        # Fondo - Glassmorphism
        'bg_primary': '#0a0a0f',       # Negro azulado
        'bg_secondary': '#1a1a2e',     # Azul oscuro
        'bg_tertiary': '#16213e',      # Azul medio
        'bg_glass': 'rgba(255, 255, 255, 0.1)',  # Efecto cristal
        'bg_glass_dark': 'rgba(0, 0, 0, 0.2)',   # Cristal oscuro
        
        # Texto - Alto contraste
        'text_primary': '#ffffff',     # Blanco puro
        'text_secondary': '#e2e8f0',   # Gris muy claro
        'text_muted': '#94a3b8',       # Gris medio
        'text_accent': '#fbbf24',      # Dorado
        
        # Bordes y superficies
        'border': 'rgba(255, 255, 255, 0.2)',
        'border_focus': '#6366f1',
        'surface': 'rgba(255, 255, 255, 0.05)',
        'surface_hover': 'rgba(255, 255, 255, 0.1)',
    }
    
    @staticmethod
    def get_revolutionary_stylesheet():
        """Retorna el stylesheet revolucionario COMPLETAMENTE DIFERENTE"""
        return f"""
        /* === TEMA REVOLUCIONARIO - GLASSMORPHISM MODERNO === */
        
        /* Aplicación base - Fondo oscuro con gradiente */
        QMainWindow {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {RevolutionaryTheme.COLORS['bg_primary']}, 
                stop:0.5 {RevolutionaryTheme.COLORS['bg_secondary']}, 
                stop:1 {RevolutionaryTheme.COLORS['bg_tertiary']});
            color: {RevolutionaryTheme.COLORS['text_primary']};
            font-family: 'Inter', 'SF Pro Display', 'Segoe UI', system-ui, sans-serif;
            font-weight: 400;
        }}
        
        /* Barra de menú - Efecto cristal */
        QMenuBar {{
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-bottom: 1px solid {RevolutionaryTheme.COLORS['border']};
            padding: 8px 16px;
            font-weight: 500;
            font-size: 14px;
        }}
        
        QMenuBar::item {{
            background: transparent;
            padding: 12px 20px;
            border-radius: 12px;
            margin: 4px 2px;
            color: {RevolutionaryTheme.COLORS['text_primary']};
            font-weight: 500;
        }}
        
        QMenuBar::item:selected {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {RevolutionaryTheme.COLORS['primary']}, 
                stop:1 {RevolutionaryTheme.COLORS['primary_light']});
            color: white;
        }}
        
        /* Menús desplegables - Glassmorphism */
        QMenu {{
            background: rgba(26, 26, 46, 0.9);
            border: 1px solid {RevolutionaryTheme.COLORS['border']};
            border-radius: 16px;
            padding: 12px;
        }}
        
        QMenu::item {{
            padding: 12px 20px;
            border-radius: 10px;
            margin: 2px;
            color: {RevolutionaryTheme.COLORS['text_primary']};
            font-weight: 400;
        }}
        
        QMenu::item:selected {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {RevolutionaryTheme.COLORS['primary']}, 
                stop:1 {RevolutionaryTheme.COLORS['secondary']});
            color: white;
        }}
        
        /* Barra de herramientas - Moderna */
        QToolBar {{
            background: rgba(255, 255, 255, 0.08);
            border: none;
            border-bottom: 1px solid {RevolutionaryTheme.COLORS['border']};
            spacing: 12px;
            padding: 16px;
        }}
        
        QToolButton {{
            background: transparent;
            border: none;
            border-radius: 12px;
            padding: 12px;
            min-width: 48px;
            min-height: 48px;
            color: {RevolutionaryTheme.COLORS['text_secondary']};
            font-weight: 500;
        }}
        
        QToolButton:hover {{
            background: {RevolutionaryTheme.COLORS['surface_hover']};
            color: {RevolutionaryTheme.COLORS['text_primary']};
        }}
        
        QToolButton:pressed {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {RevolutionaryTheme.COLORS['primary']}, 
                stop:1 {RevolutionaryTheme.COLORS['primary_light']});
            color: white;
        }}
        
        /* Botones - Diseño revolucionario con gradientes */
        QPushButton {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {RevolutionaryTheme.COLORS['primary']}, 
                stop:1 {RevolutionaryTheme.COLORS['primary_light']});
            color: white;
            border: none;
            border-radius: 14px;
            padding: 14px 28px;
            font-weight: 600;
            font-size: 14px;
            min-height: 24px;
        }}
        
        QPushButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {RevolutionaryTheme.COLORS['primary_light']}, 
                stop:1 {RevolutionaryTheme.COLORS['secondary']});
        }}
        
        QPushButton:pressed {{
            background: {RevolutionaryTheme.COLORS['primary_dark']};
        }}
        
        QPushButton:disabled {{
            background: {RevolutionaryTheme.COLORS['surface']};
            color: {RevolutionaryTheme.COLORS['text_muted']};
        }}
        
        /* Botones de éxito - Verde esmeralda */
        QPushButton[class="success"] {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {RevolutionaryTheme.COLORS['success']}, 
                stop:1 #34d399);
        }}
        
        /* Botones de peligro - Rojo coral */
        QPushButton[class="danger"] {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {RevolutionaryTheme.COLORS['error']}, 
                stop:1 #f87171);
        }}
        
        /* Botones secundarios - Efecto cristal */
        QPushButton[class="secondary"] {{
            background: rgba(255, 255, 255, 0.1);
            color: {RevolutionaryTheme.COLORS['text_primary']};
            border: 1px solid {RevolutionaryTheme.COLORS['border']};
        }}
        
        QPushButton[class="secondary"]:hover {{
            background: rgba(255, 255, 255, 0.2);
            border-color: {RevolutionaryTheme.COLORS['primary']};
        }}
        
        /* Campos de entrada - Glassmorphism */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid {RevolutionaryTheme.COLORS['border']};
            border-radius: 12px;
            padding: 14px 18px;
            font-size: 14px;
            color: {RevolutionaryTheme.COLORS['text_primary']};
            selection-background-color: {RevolutionaryTheme.COLORS['primary']};
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {RevolutionaryTheme.COLORS['border_focus']};
            background: rgba(255, 255, 255, 0.15);
        }}
        
        /* ComboBox - Moderno */
        QComboBox {{
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid {RevolutionaryTheme.COLORS['border']};
            border-radius: 12px;
            padding: 12px 18px;
            min-height: 24px;
            color: {RevolutionaryTheme.COLORS['text_primary']};
            font-weight: 500;
        }}
        
        QComboBox:focus {{
            border-color: {RevolutionaryTheme.COLORS['border_focus']};
            background: rgba(255, 255, 255, 0.15);
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 40px;
            border-top-right-radius: 12px;
            border-bottom-right-radius: 12px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 6px solid {RevolutionaryTheme.COLORS['text_secondary']};
            margin-right: 15px;
        }}
        
        QComboBox QAbstractItemView {{
            background: rgba(26, 26, 46, 0.95);
            border: 1px solid {RevolutionaryTheme.COLORS['border']};
            border-radius: 12px;
            selection-background-color: {RevolutionaryTheme.COLORS['primary']};
            outline: none;
            padding: 8px;
        }}
        """
    
    @staticmethod
    def get_table_stylesheet():
        """Stylesheet específico para tablas con diseño revolucionario"""
        return f"""
        /* Tablas - Diseño futurista */
        QTableWidget {{
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid {RevolutionaryTheme.COLORS['border']};
            border-radius: 16px;
            gridline-color: rgba(255, 255, 255, 0.1);
            selection-background-color: {RevolutionaryTheme.COLORS['primary']};
        }}
        
        QTableWidget::item {{
            padding: 12px 8px;
            border: none;
            color: {RevolutionaryTheme.COLORS['text_primary']};
        }}
        
        QTableWidget::item:selected {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {RevolutionaryTheme.COLORS['primary']}, 
                stop:1 {RevolutionaryTheme.COLORS['primary_light']});
            color: white;
        }}
        
        QTableWidget::item:hover {{
            background: rgba(255, 255, 255, 0.1);
        }}
        
        QHeaderView::section {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(99, 102, 241, 0.3), 
                stop:1 rgba(139, 92, 246, 0.3));
            border: none;
            border-bottom: 2px solid {RevolutionaryTheme.COLORS['primary']};
            padding: 16px 12px;
            font-weight: 700;
            font-size: 13px;
            color: {RevolutionaryTheme.COLORS['text_primary']};
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }}
        """
    
    @staticmethod
    def get_dialog_stylesheet():
        """Stylesheet para diálogos con efecto glassmorphism"""
        return f"""
        /* Diálogos - Glassmorphism avanzado */
        QDialog {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(15, 15, 35, 0.95),
                stop:0.5 rgba(26, 26, 46, 0.95),
                stop:1 rgba(22, 33, 62, 0.95));
            border: 1px solid {RevolutionaryTheme.COLORS['border']};
            border-radius: 20px;
            color: {RevolutionaryTheme.COLORS['text_primary']};
        }}
        
        /* GroupBox - Moderno */
        QGroupBox {{
            font-weight: 600;
            border: 2px solid {RevolutionaryTheme.COLORS['border']};
            border-radius: 16px;
            margin-top: 16px;
            padding-top: 12px;
            color: {RevolutionaryTheme.COLORS['text_primary']};
            background: rgba(255, 255, 255, 0.05);
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 16px;
            padding: 0 12px 0 12px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {RevolutionaryTheme.COLORS['primary']}, 
                stop:1 {RevolutionaryTheme.COLORS['primary_light']});
            color: white;
            border-radius: 8px;
            font-weight: 700;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }}
        """
    
    @staticmethod
    def get_scrollbar_stylesheet():
        """Scrollbars modernos y minimalistas"""
        return f"""
        /* Scrollbars - Minimalistas */
        QScrollBar:vertical {{
            background: rgba(255, 255, 255, 0.05);
            width: 8px;
            border-radius: 4px;
            margin: 0;
        }}
        
        QScrollBar::handle:vertical {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {RevolutionaryTheme.COLORS['primary']}, 
                stop:1 {RevolutionaryTheme.COLORS['primary_light']});
            border-radius: 4px;
            min-height: 30px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background: {RevolutionaryTheme.COLORS['secondary']};
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}
        
        QScrollBar:horizontal {{
            background: rgba(255, 255, 255, 0.05);
            height: 8px;
            border-radius: 4px;
            margin: 0;
        }}
        
        QScrollBar::handle:horizontal {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {RevolutionaryTheme.COLORS['primary']}, 
                stop:1 {RevolutionaryTheme.COLORS['primary_light']});
            border-radius: 4px;
            min-width: 30px;
        }}
        """
    
    @staticmethod
    def get_complete_stylesheet():
        """Retorna el stylesheet completo revolucionario"""
        return (
            RevolutionaryTheme.get_revolutionary_stylesheet() +
            RevolutionaryTheme.get_table_stylesheet() +
            RevolutionaryTheme.get_dialog_stylesheet() +
            RevolutionaryTheme.get_scrollbar_stylesheet()
        )
