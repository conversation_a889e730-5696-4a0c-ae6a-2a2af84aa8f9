#!/usr/bin/env python3
"""
Script de Limpieza Automática para PRO-2000
Elimina duplicados, reorganiza archivos y optimiza la estructura
"""

import os
import shutil
import sys
from pathlib import Path
import json
from datetime import datetime

class PRO2000Cleaner:
    """Limpiador automático para el proyecto PRO-2000"""
    
    def __init__(self, project_root=None):
        if project_root is None:
            project_root = Path(__file__).parent.absolute()
        self.root = Path(project_root)
        self.backup_dir = self.root / "backup_limpieza"
        self.log_file = self.root / "limpieza.log"
        self.results = {
            "files_removed": [],
            "files_moved": [],
            "directories_created": [],
            "errors": []
        }
    
    def log(self, message, level="INFO"):
        """Registra mensajes en el log"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        print(log_entry)
        
        with open(self.log_file, "a", encoding="utf-8") as f:
            f.write(log_entry + "\n")
    
    def create_backup(self):
        """Crea backup de seguridad antes de limpiar"""
        self.log("🔄 Creando backup de seguridad...")
        
        if self.backup_dir.exists():
            shutil.rmtree(self.backup_dir)
        
        # Crear directorio de backup
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Archivos críticos a respaldar
        critical_files = [
            "src/ui/main_window.py",
            "src/ui/main_window_funcional.py", 
            "src/ui/main_window_modern.py",
            "demo_sistema_profesional.py",
            "test_debug_import.py",
            "test_import.py",
            "test_simple_integration.py"
        ]
        
        for file_path in critical_files:
            src_file = self.root / file_path
            if src_file.exists():
                dest_file = self.backup_dir / file_path
                dest_file.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(src_file, dest_file)
                self.log(f"✅ Backup creado: {file_path}")
        
        self.log("✅ Backup completado")
    
    def remove_duplicate_files(self):
        """Elimina archivos duplicados identificados"""
        self.log("🗑️ Eliminando archivos duplicados...")
        
        files_to_remove = [
            # Ventanas principales duplicadas (mantener revolutionary)
            "src/ui/main_window.py",
            "src/ui/main_window_funcional.py",
            "src/ui/main_window_modern.py",
            
            # Demos duplicados (mantener final)
            "demo_sistema_profesional.py",
            
            # Tests de desarrollo
            "test_debug_import.py",
            "test_import.py", 
            "test_simple_integration.py",
            
            # Archivos de caché (se regeneran)
            ".venv",  # Solo si es local
        ]
        
        for file_path in files_to_remove:
            full_path = self.root / file_path
            try:
                if full_path.exists():
                    if full_path.is_file():
                        full_path.unlink()
                        self.results["files_removed"].append(str(file_path))
                        self.log(f"🗑️ Eliminado: {file_path}")
                    elif full_path.is_dir() and file_path == ".venv":
                        # Solo eliminar .venv si es local (no en producción)
                        if input(f"¿Eliminar directorio {file_path}? (s/N): ").lower() == 's':
                            shutil.rmtree(full_path)
                            self.results["files_removed"].append(str(file_path))
                            self.log(f"🗑️ Directorio eliminado: {file_path}")
            except Exception as e:
                error_msg = f"Error eliminando {file_path}: {str(e)}"
                self.results["errors"].append(error_msg)
                self.log(error_msg, "ERROR")
    
    def create_new_structure(self):
        """Crea la nueva estructura de directorios"""
        self.log("📁 Creando nueva estructura de directorios...")
        
        new_directories = [
            "data/examples/reports",
            "data/examples/templates", 
            "data/backup",
            "data/temp",
            "resources/images",
            "resources/icons",
            "logs",
            "docs/technical",
            "docs/user",
            "scripts/maintenance",
            "scripts/migration"
        ]
        
        for dir_path in new_directories:
            full_path = self.root / dir_path
            if not full_path.exists():
                full_path.mkdir(parents=True, exist_ok=True)
                self.results["directories_created"].append(str(dir_path))
                self.log(f"📁 Directorio creado: {dir_path}")
    
    def move_misplaced_files(self):
        """Mueve archivos mal ubicados a su lugar correcto"""
        self.log("📦 Reubicando archivos...")
        
        # Mapeo de archivos a mover
        file_moves = {
            # PDFs de ejemplo a data/examples/reports/
            "Informe_Taller_*.pdf": "data/examples/reports/",
            "Optimizacion_Cortes_*.pdf": "data/examples/reports/",
            "Pedido_*.pdf": "data/examples/reports/",
            
            # Imágenes técnicas a resources/images/
            "plano_tecnico_*.png": "resources/images/",
        }
        
        for pattern, dest_dir in file_moves.items():
            dest_path = self.root / dest_dir
            dest_path.mkdir(parents=True, exist_ok=True)
            
            # Buscar archivos que coincidan con el patrón
            if "*" in pattern:
                prefix = pattern.split("*")[0]
                suffix = pattern.split("*")[1] if len(pattern.split("*")) > 1 else ""
                
                for file_path in self.root.glob(f"{prefix}*{suffix}"):
                    if file_path.is_file():
                        try:
                            dest_file = dest_path / file_path.name
                            if not dest_file.exists():
                                shutil.move(str(file_path), str(dest_file))
                                self.results["files_moved"].append(f"{file_path.name} -> {dest_dir}")
                                self.log(f"📦 Movido: {file_path.name} -> {dest_dir}")
                        except Exception as e:
                            error_msg = f"Error moviendo {file_path.name}: {str(e)}"
                            self.results["errors"].append(error_msg)
                            self.log(error_msg, "ERROR")
            else:
                # Archivo específico
                file_path = self.root / pattern
                if file_path.exists():
                    try:
                        dest_file = dest_path / file_path.name
                        if not dest_file.exists():
                            shutil.move(str(file_path), str(dest_file))
                            self.results["files_moved"].append(f"{pattern} -> {dest_dir}")
                            self.log(f"📦 Movido: {pattern} -> {dest_dir}")
                    except Exception as e:
                        error_msg = f"Error moviendo {pattern}: {str(e)}"
                        self.results["errors"].append(error_msg)
                        self.log(error_msg, "ERROR")
    
    def update_main_entry_point(self):
        """Actualiza el punto de entrada principal para usar la ventana correcta"""
        self.log("🔧 Actualizando punto de entrada principal...")
        
        main_py_path = self.root / "src" / "main.py"
        if main_py_path.exists():
            try:
                with open(main_py_path, "r", encoding="utf-8") as f:
                    content = f.read()
                
                # Actualizar import para usar revolutionary window
                updated_content = content.replace(
                    "from ui.main_window_revolutionary import RevolutionaryMainWindow",
                    "from ui.main_window_revolutionary import RevolutionaryMainWindow"
                )
                
                # Asegurar que usa la ventana correcta
                if "RevolutionaryMainWindow" not in updated_content:
                    # Si no está importando revolutionary, corregir
                    updated_content = updated_content.replace(
                        "from ui.main_window import MainWindow",
                        "from ui.main_window_revolutionary import RevolutionaryMainWindow as MainWindow"
                    )
                
                with open(main_py_path, "w", encoding="utf-8") as f:
                    f.write(updated_content)
                
                self.log("✅ Punto de entrada actualizado")
                
            except Exception as e:
                error_msg = f"Error actualizando main.py: {str(e)}"
                self.results["errors"].append(error_msg)
                self.log(error_msg, "ERROR")
    
    def clean_pycache(self):
        """Limpia archivos de caché de Python"""
        self.log("🧹 Limpiando archivos de caché...")
        
        cache_patterns = ["**/__pycache__", "**/*.pyc", "**/*.pyo"]
        
        for pattern in cache_patterns:
            for cache_path in self.root.rglob(pattern):
                try:
                    if cache_path.is_file():
                        cache_path.unlink()
                    elif cache_path.is_dir():
                        shutil.rmtree(cache_path)
                    self.log(f"🧹 Limpiado: {cache_path.relative_to(self.root)}")
                except Exception as e:
                    self.log(f"Error limpiando caché {cache_path}: {str(e)}", "WARNING")
    
    def generate_report(self):
        """Genera reporte de la limpieza realizada"""
        self.log("📊 Generando reporte de limpieza...")
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "files_removed": len(self.results["files_removed"]),
                "files_moved": len(self.results["files_moved"]),
                "directories_created": len(self.results["directories_created"]),
                "errors": len(self.results["errors"])
            },
            "details": self.results
        }
        
        # Guardar reporte JSON
        report_file = self.root / "limpieza_report.json"
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Mostrar resumen
        print("\n" + "="*60)
        print("📊 REPORTE DE LIMPIEZA COMPLETADA")
        print("="*60)
        print(f"✅ Archivos eliminados: {report['summary']['files_removed']}")
        print(f"📦 Archivos movidos: {report['summary']['files_moved']}")
        print(f"📁 Directorios creados: {report['summary']['directories_created']}")
        print(f"❌ Errores: {report['summary']['errors']}")
        print("="*60)
        
        if self.results["errors"]:
            print("\n⚠️ ERRORES ENCONTRADOS:")
            for error in self.results["errors"]:
                print(f"   - {error}")
        
        print(f"\n📄 Reporte detallado guardado en: {report_file}")
        print(f"📄 Log completo guardado en: {self.log_file}")
    
    def run_cleanup(self):
        """Ejecuta el proceso completo de limpieza"""
        self.log("🚀 INICIANDO LIMPIEZA AUTOMÁTICA DE PRO-2000")
        self.log("="*50)
        
        try:
            # Fase 1: Backup de seguridad
            self.create_backup()
            
            # Fase 2: Eliminación de duplicados
            self.remove_duplicate_files()
            
            # Fase 3: Crear nueva estructura
            self.create_new_structure()
            
            # Fase 4: Reubicar archivos
            self.move_misplaced_files()
            
            # Fase 5: Actualizar configuración
            self.update_main_entry_point()
            
            # Fase 6: Limpiar caché
            self.clean_pycache()
            
            # Fase 7: Generar reporte
            self.generate_report()
            
            self.log("✅ LIMPIEZA COMPLETADA EXITOSAMENTE")
            
        except Exception as e:
            error_msg = f"❌ ERROR CRÍTICO EN LIMPIEZA: {str(e)}"
            self.log(error_msg, "CRITICAL")
            self.results["errors"].append(error_msg)
            print(f"\n{error_msg}")
            print("🔙 Los archivos de backup están disponibles en:", self.backup_dir)
            return False
        
        return True


def main():
    """Función principal del script"""
    print("🧹 SCRIPT DE LIMPIEZA AUTOMÁTICA PRO-2000")
    print("="*50)
    
    # Verificar que estamos en el directorio correcto
    current_dir = Path.cwd()
    if not (current_dir / "src" / "main.py").exists():
        print("❌ ERROR: No se encuentra el archivo src/main.py")
        print("   Asegúrese de ejecutar este script desde el directorio raíz de PRO-2000")
        return 1
    
    # Confirmar antes de proceder
    print(f"📁 Directorio de trabajo: {current_dir}")
    print("\n⚠️  ADVERTENCIA: Este script eliminará archivos duplicados.")
    print("   Se creará un backup automático en backup_limpieza/")
    
    response = input("\n¿Continuar con la limpieza? (s/N): ").lower().strip()
    if response != 's':
        print("❌ Limpieza cancelada por el usuario")
        return 0
    
    # Ejecutar limpieza
    cleaner = PRO2000Cleaner(current_dir)
    success = cleaner.run_cleanup()
    
    if success:
        print("\n🎉 ¡LIMPIEZA COMPLETADA!")
        print("📋 Próximos pasos recomendados:")
        print("   1. Revisar el reporte generado")
        print("   2. Probar que la aplicación funciona correctamente")
        print("   3. Eliminar el directorio backup_limpieza/ si todo está bien")
        print("   4. Ejecutar: python src/main.py")
        return 0
    else:
        print("\n❌ La limpieza no se completó correctamente")
        print("   Revise los errores en limpieza.log")
        print("   Los archivos originales están en backup_limpieza/")
        return 1


if __name__ == "__main__":
    sys.exit(main())"