"""
<PERSON><PERSON><PERSON><PERSON> que define el diálogo para gestionar usuarios.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox, QComboBox,
    QFormLayout, QDialogButtonBox, QTabWidget, QWidget, QCheckBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIntValidator

from models.base import get_db
from models.usuario import Usuario
from ui.utils.window_utils import setup_maximized_dialog

class FormularioUsuario(QWidget):
    """Formulario para crear o editar un usuario."""
    
    def __init__(self, usuario=None, parent=None):
        """
        Inicializa el formulario de usuario.
        
        Args:
            usuario: Instancia de Usuario a editar, o None para crear uno nuevo
            parent: Widget padre
        """
        super().__init__(parent)
        self.usuario = usuario
        self._configurar_ui()
    
    def _configurar_ui(self):
        """Configura la interfaz de usuario del formulario."""
        layout = QFormLayout(self)
        
        # Campo de nombre de usuario
        self.campo_nombre = QLineEdit()
        self.campo_nombre.setPlaceholderText("Nombre de usuario")
        
        # Campo de contraseña
        self.campo_contrasena = QLineEdit()
        self.campo_contrasena.setPlaceholderText("Dejar en blanco para no cambiar")
        self.campo_contrasena.setEchoMode(QLineEdit.EchoMode.Password)
        
        # Confirmación de contraseña
        self.campo_confirmar_contrasena = QLineEdit()
        self.campo_confirmar_contrasena.setPlaceholderText("Confirmar contraseña")
        self.campo_confirmar_contrasena.setEchoMode(QLineEdit.EchoMode.Password)
        
        # Tipo de usuario
        self.combo_tipo = QComboBox()
        self.combo_tipo.addItem("Administrador", 1)
        self.combo_tipo.addItem("Usuario normal", 2)
        
        # Estado (activo/inactivo)
        self.check_activo = QCheckBox("Usuario activo")
        self.check_activo.setChecked(True)
        
        # Añadir campos al formulario
        layout.addRow("Nombre de usuario:", self.campo_nombre)
        layout.addRow("Contraseña:", self.campo_contrasena)
        layout.addRow("Confirmar contraseña:", self.campo_confirmar_contrasena)
        layout.addRow("Tipo de usuario:", self.combo_tipo)
        layout.addRow("", self.check_activo)
        
        # Si se está editando un usuario, cargar sus datos
        if self.usuario:
            self._cargar_datos_usuario()
    
    def _cargar_datos_usuario(self):
        """Carga los datos del usuario en el formulario."""
        if not self.usuario:
            return
        
        self.campo_nombre.setText(self.usuario.nombre)
        
        # Seleccionar el tipo de usuario en el combo
        index = self.combo_tipo.findData(self.usuario.tipo)
        if index >= 0:
            self.combo_tipo.setCurrentIndex(index)
        
        # Establecer el estado del checkbox
        # Nota: Asumiendo que hay un campo 'activo' en el modelo Usuario
        # Si no existe, puedes eliminar o modificar esta línea
        if hasattr(self.usuario, 'activo'):
            self.check_activo.setChecked(bool(self.usuario.activo))
    
    def validar_formulario(self):
        """
        Valida los datos del formulario.
        
        Returns:
            tuple: (válido, mensaje) donde 'válido' es un booleano que indica
                  si el formulario es válido, y 'mensaje' contiene un mensaje
                  de error en caso de que no sea válido.
        """
        nombre = self.campo_nombre.text().strip()
        contrasena = self.campo_contrasena.text()
        confirmacion = self.campo_confirmar_contrasena.text()
        
        if not nombre:
            return False, "El nombre de usuario es obligatorio."
        
        # Si es un nuevo usuario o se está cambiando la contraseña
        if not self.usuario or contrasena:
            if len(contrasena) < 4:
                return False, "La contraseña debe tener al menos 4 caracteres."
            
            if contrasena != confirmacion:
                return False, "Las contraseñas no coinciden."
        
        return True, ""
    
    def obtener_datos(self):
        """
        Obtiene los datos del formulario.
        
        Returns:
            dict: Diccionario con los datos del formulario.
        """
        return {
            'nombre': self.campo_nombre.text().strip(),
            'contrasena': self.campo_contrasena.text() or None,
            'tipo': self.combo_tipo.currentData(),
            'activo': self.check_activo.isChecked()
        }

class UsuariosDialog(QDialog):
    """Diálogo para gestionar usuarios del sistema."""
    
    def __init__(self, parent=None):
        """Inicializa el diálogo de gestión de usuarios."""
        super().__init__(parent)
        self.setWindowTitle("Gestión de Usuarios")

        # ✅ CONFIGURACIÓN SIMPLE Y EFECTIVA
        setup_maximized_dialog(self, "Gestión de Usuarios")
        
        # Configurar la interfaz de usuario
        self._configurar_ui()
        
        # Cargar la lista de usuarios
        self._cargar_usuarios()
    
    def _configurar_ui(self):
        """Configura los elementos de la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)
        
        # Botones superiores
        layout_botones = QHBoxLayout()
        
        self.boton_nuevo = QPushButton("Nuevo Usuario")
        self.boton_editar = QPushButton("Editar Usuario")
        self.boton_eliminar = QPushButton("Eliminar Usuario")
        self.boton_actualizar = QPushButton("Actualizar Lista")
        
        # Deshabilitar botones de edición/eliminación hasta que se seleccione un usuario
        self.boton_editar.setEnabled(False)
        self.boton_eliminar.setEnabled(False)
        
        layout_botones.addWidget(self.boton_nuevo)
        layout_botones.addWidget(self.boton_editar)
        layout_botones.addWidget(self.boton_eliminar)
        layout_botones.addStretch()
        layout_botones.addWidget(self.boton_actualizar)
        
        # Tabla de usuarios
        self.tabla_usuarios = QTableWidget()
        self.tabla_usuarios.setColumnCount(4)
        self.tabla_usuarios.setHorizontalHeaderLabels(["ID", "Nombre", "Tipo", "Estado"])
        self.tabla_usuarios.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.tabla_usuarios.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.tabla_usuarios.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        
        # Conectar señales
        self.tabla_usuarios.itemSelectionChanged.connect(self._on_seleccion_cambiada)
        self.boton_nuevo.clicked.connect(self._on_nuevo_usuario)
        self.boton_editar.clicked.connect(self._on_editar_usuario)
        self.boton_eliminar.clicked.connect(self._on_eliminar_usuario)
        self.boton_actualizar.clicked.connect(self._cargar_usuarios)
        
        # Añadir widgets al layout principal
        layout_principal.addLayout(layout_botones)
        layout_principal.addWidget(self.tabla_usuarios)
        
        # Botones de diálogo
        botones_dialogo = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        botones_dialogo.rejected.connect(self.reject)
        
        layout_principal.addWidget(botones_dialogo)
    
    def _cargar_usuarios(self):
        """Carga la lista de usuarios desde la base de datos."""
        db = next(get_db())
        
        try:
            # Obtener todos los usuarios
            usuarios = db.query(Usuario).order_by(Usuario.nombre).all()
            
            # Configurar la tabla
            self.tabla_usuarios.setRowCount(len(usuarios))
            
            for fila, usuario in enumerate(usuarios):
                # ID
                self.tabla_usuarios.setItem(fila, 0, QTableWidgetItem(str(usuario.id)))
                
                # Nombre
                self.tabla_usuarios.setItem(fila, 1, QTableWidgetItem(usuario.nombre))
                
                # Tipo
                tipo = "Administrador" if usuario.tipo == 1 else "Usuario"
                self.tabla_usuarios.setItem(fila, 2, QTableWidgetItem(tipo))
                
                # Estado (asumiendo que hay un campo 'activo')
                estado = "Activo" if getattr(usuario, 'activo', True) else "Inactivo"
                self.tabla_usuarios.setItem(fila, 3, QTableWidgetItem(estado))
            
            # Ajustar el ancho de las columnas
            self.tabla_usuarios.resizeColumnsToContents()
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudieron cargar los usuarios: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        finally:
            db.close()
    
    def _on_seleccion_cambiada(self):
        """Maneja el evento de cambio de selección en la tabla."""
        seleccion = self.tabla_usuarios.selectedItems()
        habilitar = len(seleccion) > 0
        
        self.boton_editar.setEnabled(habilitar)
        self.boton_eliminar.setEnabled(habilitar)
    
    def _obtener_usuario_seleccionado(self):
        """
        Obtiene el usuario seleccionado en la tabla.
        
        Returns:
            Usuario: El usuario seleccionado, o None si no hay selección.
        """
        filas_seleccionadas = self.tabla_usuarios.selectionModel().selectedRows()
        
        if not filas_seleccionadas:
            return None
        
        # Obtener el ID del usuario de la primera columna de la fila seleccionada
        fila = filas_seleccionadas[0].row()
        id_usuario = int(self.tabla_usuarios.item(fila, 0).text())
        
        # Obtener el usuario de la base de datos
        db = next(get_db())
        try:
            return db.query(Usuario).filter(Usuario.id == id_usuario).first()
        finally:
            db.close()
    
    def _on_nuevo_usuario(self):
        """Maneja el evento de crear un nuevo usuario."""
        dialogo = QDialog(self)
        dialogo.setWindowTitle("Nuevo Usuario")
        
        layout = QVBoxLayout(dialogo)
        
        formulario = FormularioUsuario()
        
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        botones.accepted.connect(dialogo.accept)
        botones.rejected.connect(dialogo.reject)
        
        layout.addWidget(formulario)
        layout.addWidget(botones)
        
        if dialogo.exec() == QDialog.DialogCode.Accepted:
            # Validar el formulario
            valido, mensaje = formulario.validar_formulario()
            
            if not valido:
                QMessageBox.warning(self, "Validación", mensaje)
                return
            
            datos = formulario.obtener_datos()
            
            # Crear el nuevo usuario
            db = next(get_db())
            try:
                usuario = Usuario(
                    nombre=datos['nombre'],
                    tipo=datos['tipo'],
                    clave=datos['contrasena']  # En producción, esto debería estar hasheado
                )
                
                # Establecer el estado activo si existe el campo
                if hasattr(usuario, 'activo'):
                    usuario.activo = datos['activo']
                
                db.add(usuario)
                db.commit()
                
                QMessageBox.information(
                    self,
                    "Éxito",
                    f"Usuario '{usuario.nombre}' creado correctamente.",
                    QMessageBox.StandardButton.Ok
                )
                
                # Actualizar la lista de usuarios
                self._cargar_usuarios()
                
            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self,
                    "Error",
                    f"No se pudo crear el usuario: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )
            finally:
                db.close()
    
    def _on_editar_usuario(self):
        """Maneja el evento de editar un usuario existente."""
        usuario = self._obtener_usuario_seleccionado()
        
        if not usuario:
            return
        
        dialogo = QDialog(self)
        dialogo.setWindowTitle(f"Editar Usuario: {usuario.nombre}")
        
        layout = QVBoxLayout(dialogo)
        
        formulario = FormularioUsuario(usuario)
        
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        botones.accepted.connect(dialogo.accept)
        botones.rejected.connect(dialogo.reject)
        
        layout.addWidget(formulario)
        layout.addWidget(botones)
        
        if dialogo.exec() == QDialog.DialogCode.Accepted:
            # Validar el formulario
            valido, mensaje = formulario.validar_formulario()
            
            if not valido:
                QMessageBox.warning(self, "Validación", mensaje)
                return
            
            datos = formulario.obtener_datos()
            
            # Actualizar el usuario
            db = next(get_db())
            try:
                # Obtener el usuario actualizado
                usuario_actualizado = db.query(Usuario).filter(Usuario.id == usuario.id).first()
                
                if not usuario_actualizado:
                    raise ValueError("El usuario ya no existe.")
                
                # Actualizar los campos
                usuario_actualizado.nombre = datos['nombre']
                usuario_actualizado.tipo = datos['tipo']
                
                # Actualizar la contraseña solo si se proporcionó una nueva
                if datos['contrasena']:
                    usuario_actualizado.clave = datos['contrasena']  # Debería estar hasheado
                
                # Actualizar el estado activo si existe el campo
                if hasattr(usuario_actualizado, 'activo'):
                    usuario_actualizado.activo = datos['activo']
                
                db.commit()
                
                QMessageBox.information(
                    self,
                    "Éxito",
                    f"Usuario '{usuario_actualizado.nombre}' actualizado correctamente.",
                    QMessageBox.StandardButton.Ok
                )
                
                # Actualizar la lista de usuarios
                self._cargar_usuarios()
                
            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self,
                    "Error",
                    f"No se pudo actualizar el usuario: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )
            finally:
                db.close()
    
    def _on_eliminar_usuario(self):
        """Maneja el evento de eliminar un usuario."""
        usuario = self._obtener_usuario_seleccionado()
        
        if not usuario:
            return
        
        # No permitir eliminar al propio usuario actual
        # (asumiendo que hay un usuario actual en la sesión)
        usuario_actual = getattr(self.parent(), 'usuario', None)
        if usuario_actual and usuario_actual.id == usuario.id:
            QMessageBox.warning(
                self,
                "No permitido",
                "No puede eliminar su propio usuario mientras tenga la sesión iniciada.",
                QMessageBox.StandardButton.Ok
            )
            return
        
        # Confirmar eliminación
        respuesta = QMessageBox.question(
            self,
            "Confirmar eliminación",
            f"¿Está seguro de que desea eliminar al usuario '{usuario.nombre}'?\n"
            "Esta acción no se puede deshacer.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if respuesta == QMessageBox.StandardButton.Yes:
            db = next(get_db())
            try:
                # Obtener el usuario a eliminar
                usuario_eliminar = db.query(Usuario).filter(Usuario.id == usuario.id).first()
                
                if not usuario_eliminar:
                    raise ValueError("El usuario ya no existe.")
                
                # Guardar el nombre para mostrarlo en el mensaje
                nombre_usuario = usuario_eliminar.nombre
                
                # Eliminar el usuario
                db.delete(usuario_eliminar)
                db.commit()
                
                QMessageBox.information(
                    self,
                    "Usuario eliminado",
                    f"El usuario '{nombre_usuario}' ha sido eliminado correctamente.",
                    QMessageBox.StandardButton.Ok
                )
                
                # Actualizar la lista de usuarios
                self._cargar_usuarios()
                
            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self,
                    "Error",
                    f"No se pudo eliminar el usuario: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )
            finally:
                db.close()
