"""
Utilidades para ventanas de PyQt6
Funciones auxiliares para manejo de ventanas
"""

from PyQt6.QtWidgets import QWidget, QApplication
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon
import os


def setup_window_maximized(window: QWidget):
    """
    Configura una ventana para maximizarse

    Args:
        window: Widget de ventana a maximizar
    """
    try:
        window.showMaximized()
    except Exception as e:
        print(f"Error al maximizar ventana: {e}")
        # Fallback: tamaño grande
        window.resize(1200, 800)


def force_dialog_maximized(dialog: QWidget, title: str = None):
    """
    Fuerza la maximización de un diálogo con configuración completa

    Args:
        dialog: Widget del diálogo
        title: Título opcional del diálogo
    """
    try:
        # Configurar título si se proporciona
        if title:
            dialog.setWindowTitle(title)

        # Asegurar que tenga controles de ventana
        dialog.setWindowFlags(Qt.WindowType.Window |
                             Qt.WindowType.WindowCloseButtonHint |
                             Qt.WindowType.WindowMinMaxButtonsHint)

        # Configurar tamaño mínimo generoso
        dialog.setMinimumSize(800, 600)

        # MAXIMIZAR INMEDIATAMENTE
        dialog.showMaximized()

        print(f"✅ Diálogo maximizado: {title or 'Sin título'}")

    except Exception as e:
        print(f"❌ Error al maximizar diálogo: {e}")
        # Fallback: tamaño grande
        dialog.resize(1200, 800)


def execute_dialog_maximized(dialog: QWidget):
    """
    Ejecuta un diálogo asegurándose de que se maximice correctamente

    Args:
        dialog: Widget del diálogo a ejecutar
    """
    try:
        # Simplemente ejecutar el diálogo - ya está configurado para maximizarse
        print(f"✅ Ejecutando diálogo maximizado: {dialog.windowTitle()}")
        return dialog.exec()

    except Exception as e:
        print(f"❌ Error al ejecutar diálogo: {e}")
        # Fallback: ejecutar normalmente
        return dialog.exec()


def ensure_window_controls_visible(window: QWidget):
    """
    Asegura que los controles de la ventana sean visibles
    
    Args:
        window: Widget de ventana
    """
    try:
        # Remover la flag de ventana sin marco si existe
        flags = window.windowFlags()
        if flags & Qt.WindowType.FramelessWindowHint:
            flags &= ~Qt.WindowType.FramelessWindowHint
            window.setWindowFlags(flags)
    except Exception as e:
        print(f"Error al configurar controles de ventana: {e}")


def center_window(window: QWidget, parent: QWidget = None):
    """
    Centra una ventana en la pantalla o respecto a su padre
    
    Args:
        window: Ventana a centrar
        parent: Ventana padre (opcional)
    """
    try:
        if parent:
            # Centrar respecto al padre
            parent_rect = parent.geometry()
            window_rect = window.geometry()
            x = parent_rect.x() + (parent_rect.width() - window_rect.width()) // 2
            y = parent_rect.y() + (parent_rect.height() - window_rect.height()) // 2
            window.move(x, y)
        else:
            # Centrar en la pantalla
            screen = QApplication.primaryScreen()
            if screen:
                screen_rect = screen.geometry()
                window_rect = window.geometry()
                x = (screen_rect.width() - window_rect.width()) // 2
                y = (screen_rect.height() - window_rect.height()) // 2
                window.move(x, y)
    except Exception as e:
        print(f"Error al centrar ventana: {e}")


def set_window_icon(window: QWidget, icon_path: str):
    """
    Establece el icono de una ventana
    
    Args:
        window: Ventana
        icon_path: Ruta al archivo de icono
    """
    try:
        if os.path.exists(icon_path):
            window.setWindowIcon(QIcon(icon_path))
    except Exception as e:
        print(f"Error al establecer icono: {e}")


def smart_dialog_setup(dialog: QWidget, complexity: str = "simple", title: str = None, resizable: bool = True, center: bool = True, icon_path: str = None, maximize: bool = None):
    """
    Configuración inteligente para diálogos

    Args:
        dialog: Widget del diálogo
        complexity: Tipo de configuración ("simple", "complex", "management")
        title: Título del diálogo
        resizable: Si el diálogo puede redimensionarse
        center: Si centrar el diálogo
        icon_path: Ruta al icono del diálogo
        maximize: Si maximizar el diálogo (None = auto según complejidad)
    """
    try:
        # Configurar título si se proporciona
        if title:
            dialog.setWindowTitle(title)

        # Configurar banderas de ventana apropiadas
        dialog.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowCloseButtonHint | Qt.WindowType.WindowMinMaxButtonsHint)

        # Determinar si maximizar automáticamente según complejidad
        if maximize is None:
            # Auto-maximizar para diálogos complejos y de gestión
            auto_maximize = complexity in ["complex", "management"]
        else:
            auto_maximize = maximize

        # Configurar tamaño según complejidad
        if complexity == "simple":
            dialog.setMinimumSize(400, 300)
            if not auto_maximize:
                dialog.resize(600, 500)
        elif complexity == "complex":
            dialog.setMinimumSize(800, 600)
            if not auto_maximize:
                dialog.resize(1200, 800)
        elif complexity == "management":
            dialog.setMinimumSize(500, 400)
            if not auto_maximize:
                dialog.resize(800, 700)
        else:
            # Por defecto
            dialog.setMinimumSize(400, 300)
            if not auto_maximize:
                dialog.resize(600, 500)

        # Configurar redimensionamiento
        if not resizable:
            dialog.setFixedSize(dialog.size())

        # Configurar icono si se proporciona
        if icon_path:
            set_window_icon(dialog, icon_path)

        # Maximizar o centrar diálogo
        if auto_maximize:
            dialog.showMaximized()
        elif center:
            center_window(dialog)

        # Aplicar estilo moderno por defecto
        apply_window_style(dialog, "modern")

    except Exception as e:
        print(f"Error en smart_dialog_setup: {e}")


def apply_window_style(window: QWidget, style_name: str = "modern"):
    """
    Aplica un estilo predefinido a una ventana
    
    Args:
        window: Ventana
        style_name: Nombre del estilo a aplicar
    """
    styles = {
        "modern": """
            QWidget {
                background-color: #f5f5f5;
                color: #333333;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QLineEdit {
                border: 2px solid #e1e1e1;
                border-radius: 4px;
                padding: 8px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #0078d4;
            }
        """,
        "dark": """
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QLineEdit {
                border: 2px solid #555555;
                border-radius: 4px;
                padding: 8px;
                background-color: #3c3c3c;
                color: white;
            }
            QLineEdit:focus {
                border-color: #0078d4;
            }
        """
    }
    
    try:
        if style_name in styles:
            window.setStyleSheet(styles[style_name])
    except Exception as e:
        print(f"Error al aplicar estilo: {e}")
