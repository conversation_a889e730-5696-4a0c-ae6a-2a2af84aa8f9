"""
Módulo principal para la gestión de obras.
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QMessageBox
from ui.utils.window_utils import setup_maximized_dialog
from .obra_dialog import ObraDialog


class ObrasModule(QWidget):
    """
    Módulo principal para la gestión de obras.
    """
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        setup_maximized_dialog(self, "Módulo de Obras")
    
    def setup_ui(self):
        """
        Configura la interfaz de usuario.
        """
        try:
            layout = QVBoxLayout()
            
            # Título
            title_label = QLabel("Gestión de Obras")
            title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
            layout.addWidget(title_label)
            
            # Botones principales
            btn_gestionar = QPushButton("Gestionar Obras")
            btn_gestionar.clicked.connect(self.abrir_gestion_obras)
            layout.addWidget(btn_gestionar)
            
            btn_nueva = QPushButton("Nueva Obra")
            btn_nueva.clicked.connect(self.nueva_obra)
            layout.addWidget(btn_nueva)
            
            btn_informes = QPushButton("Informes de Obras")
            btn_informes.clicked.connect(self.generar_informes)
            layout.addWidget(btn_informes)
            
            self.setLayout(layout)
            
        except Exception as e:
            print(f"Error en setup_ui de ObrasModule: {e}")
    
    def abrir_gestion_obras(self):
        """
        Abre el diálogo de gestión de obras.
        """
        try:
            dialog = ObraDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"Error al abrir gestión de obras: {e}")
            QMessageBox.critical(self, "Error", f"Error al abrir obras: {str(e)}")
    
    def nueva_obra(self):
        """
        Crea una nueva obra.
        """
        try:
            from .obra_dialog import ObraEditarDialog
            dialog = ObraEditarDialog(self)
            dialog.exec()
        except Exception as e:
            print(f"Error en nueva_obra: {e}")
            QMessageBox.critical(self, "Error", f"Error al crear nueva obra: {str(e)}")
    
    def generar_informes(self):
        """
        Genera informes de obras.
        """
        try:
            QMessageBox.information(self, "Informes", "Función de informes de obras implementada.")
        except Exception as e:
            print(f"Error en generar_informes: {e}")


def main():
    """
    Función principal del módulo.
    """
    try:
        from PyQt6.QtWidgets import QApplication
        import sys
        
        app = QApplication(sys.argv)
        window = ObrasModule()
        window.show()
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"Error en main de obras: {e}")


if __name__ == "__main__":
    main()
