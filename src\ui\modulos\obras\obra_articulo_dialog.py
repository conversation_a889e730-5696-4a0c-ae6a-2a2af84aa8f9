"""
Diálogo para agregar/editar artículos en obras.
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLineEdit, QLabel,
    QFormLayout, QDialogButtonBox, QComboBox, QDoubleSpinBox, QSpinBox,
    QMessageBox, QGroupBox, QTextEdit, QTableWidget, QTableWidgetItem,
    QHeaderView
)
from PyQt6.QtCore import Qt, QDateTime
import json
from PyQt6.QtGui import QFont

from models.base import get_db
from models.articulo import Articulo, ObraArticulo
from models.obra import Obra
from ui.utils.window_utils import force_dialog_maximized
# Sistema profesional directo integrado


class ObraArticuloDialog(QDialog):
    """Diálogo para agregar/editar un artículo en una obra."""
    
    def __init__(self, parent=None, obra=None, obra_articulo=None):
        super().__init__(parent)
        self.obra = obra
        self.obra_articulo = obra_articulo  # Para edición
        self.diseno_data = None  # Para almacenar datos del diseñador
        
        self.setWindowTitle("Agregar Artículo a la Obra" if not obra_articulo else "Editar Artículo")
        self.setModal(True)

        # ✅ FORZAR MAXIMIZACIÓN COMPLETA
        force_dialog_maximized(self, "Agregar Artículo a la Obra" if not obra_articulo else "Editar Artículo")
        
        self._inicializar_ui()
        self._cargar_articulos()
        
        if self.obra_articulo:
            self._cargar_datos_articulo()

    def _abrir_disenador(self):
        """Abre el diálogo de diseño gráfico profesional."""
        from PyQt6.QtWidgets import QMessageBox

        # Usar el mismo sistema profesional que agregar_articulo_mejorado_dialog
        try:
            # Importar el método del otro diálogo
            from ui.modulos.obras.agregar_articulo_mejorado_dialog import AgregarArticuloMejoradoDialog

            # Crear una instancia temporal para usar su método
            temp_dialog = AgregarArticuloMejoradoDialog(None, None)
            temp_dialog.campo_altura = self.campo_altura
            temp_dialog.campo_anchura = self.campo_anchura
            temp_dialog.campo_notas = self.campo_notas
            temp_dialog.diseno_data = self.diseno_data
            temp_dialog._actualizar_previsualizacion = lambda: None  # Método dummy

            # Llamar al método profesional
            temp_dialog._abrir_disenador()

            # Copiar los datos actualizados
            self.diseno_data = temp_dialog.diseno_data

        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ Error",
                f"Error al abrir el diseñador profesional:\n{e}\n\n"
                "Se usará configuración básica."
            )

            # Configuración básica de emergencia
            altura_actual = self.campo_altura.value()
            anchura_actual = self.campo_anchura.value()

            self.diseno_data = {
                'altura': altura_actual,
                'anchura': anchura_actual,
                'sistema': 'basico_emergencia',
                'timestamp': str(QDateTime.currentDateTime().toString())
            }

            self.campo_notas.setPlainText(json.dumps(self.diseno_data, indent=2, ensure_ascii=False))
    
    def _inicializar_ui(self):
        """Configura la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)
        
        # Título
        titulo = QLabel("Configuración del Artículo en la Obra")
        titulo.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout_principal.addWidget(titulo)
        
        # Información de la obra
        if self.obra:
            info_obra = QLabel(f"Obra: {self.obra.codigo} - {self.obra.nombre}")
            info_obra.setStyleSheet("color: #666; font-style: italic;")
            layout_principal.addWidget(info_obra)
        
        # Formulario principal
        grupo_datos = QGroupBox("Datos del Artículo")
        layout_datos = QFormLayout(grupo_datos)
        
        # Artículo
        self.combo_articulo = QComboBox()
        self.combo_articulo.currentIndexChanged.connect(self._on_articulo_changed)
        layout_datos.addRow("Artículo*:", self.combo_articulo)
        
        # Medidas
        layout_medidas = QHBoxLayout()
        
        self.campo_altura = QSpinBox()
        self.campo_altura.setRange(100, 5000)
        self.campo_altura.setValue(1200)
        self.campo_altura.setSuffix(" mm")
        self.campo_altura.valueChanged.connect(self._calcular_precio)
        layout_medidas.addWidget(QLabel("Altura:"))
        layout_medidas.addWidget(self.campo_altura)
        
        self.campo_anchura = QSpinBox()
        self.campo_anchura.setRange(100, 5000)
        self.campo_anchura.setValue(800)
        self.campo_anchura.setSuffix(" mm")
        self.campo_anchura.valueChanged.connect(self._calcular_precio)
        layout_medidas.addWidget(QLabel("Anchura:"))
        layout_medidas.addWidget(self.campo_anchura)

        self.boton_diseno = QPushButton("Diseñar Artículo")
        self.boton_diseno.clicked.connect(self._abrir_disenador)
        layout_medidas.addWidget(self.boton_diseno)
        
        layout_datos.addRow("Medidas:", layout_medidas)
        
        # Cantidad
        self.campo_cantidad = QSpinBox()
        self.campo_cantidad.setRange(1, 999)
        self.campo_cantidad.setValue(1)
        self.campo_cantidad.valueChanged.connect(self._calcular_precio)
        layout_datos.addRow("Cantidad:", self.campo_cantidad)
        
        # Precios (solo lectura)
        self.campo_precio_unitario = QDoubleSpinBox()
        self.campo_precio_unitario.setRange(0, 999999.99)
        self.campo_precio_unitario.setDecimals(2)
        self.campo_precio_unitario.setPrefix("€ ")
        self.campo_precio_unitario.setReadOnly(True)
        layout_datos.addRow("Precio Unitario:", self.campo_precio_unitario)
        
        self.campo_precio_total = QDoubleSpinBox()
        self.campo_precio_total.setRange(0, 999999.99)
        self.campo_precio_total.setDecimals(2)
        self.campo_precio_total.setPrefix("€ ")
        self.campo_precio_total.setReadOnly(True)
        layout_datos.addRow("Precio Total:", self.campo_precio_total)
        
        # Notas
        self.campo_notas = QTextEdit()
        self.campo_notas.setMaximumHeight(80)
        self.campo_notas.setPlaceholderText("Notas adicionales sobre este artículo")
        layout_datos.addRow("Notas:", self.campo_notas)
        
        layout_principal.addWidget(grupo_datos)
        
        # Botón de calcular
        boton_calcular = QPushButton("Recalcular Precio")
        boton_calcular.clicked.connect(self._calcular_precio)
        layout_principal.addWidget(boton_calcular)
        
        # Área de detalles de materiales
        grupo_materiales = QGroupBox("Detalles de Materiales")
        layout_materiales = QVBoxLayout(grupo_materiales)
        
        self.texto_materiales = QTextEdit()
        self.texto_materiales.setReadOnly(True)
        self.texto_materiales.setMaximumHeight(150)
        self.texto_materiales.setFont(QFont("Courier", 9))
        layout_materiales.addWidget(self.texto_materiales)
        
        layout_principal.addWidget(grupo_materiales)
        
        # Botones
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        botones.accepted.connect(self._aceptar)
        botones.rejected.connect(self.reject)
        layout_principal.addWidget(botones)
    
    def _cargar_articulos(self):
        """Carga los artículos disponibles."""
        db = next(get_db())
        try:
            articulos = db.query(Articulo).filter(Articulo.activo == True).order_by(Articulo.codigo).all()
            
            self.combo_articulo.clear()
            self.combo_articulo.addItem("-- Seleccionar artículo --", None)
            
            for articulo in articulos:
                self.combo_articulo.addItem(f"{articulo.codigo} - {articulo.descripcion}", articulo.id)
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al cargar artículos: {str(e)}")
        finally:
            db.close()
    
    def _cargar_datos_articulo(self):
        """Carga los datos del artículo para edición."""
        if not self.obra_articulo:
            return
        
        # Buscar el artículo en el combo
        for i in range(self.combo_articulo.count()):
            if self.combo_articulo.itemData(i) == self.obra_articulo.articulo_id:
                self.combo_articulo.setCurrentIndex(i)
                break
        
        self.campo_altura.setValue(int(self.obra_articulo.altura))
        self.campo_anchura.setValue(int(self.obra_articulo.anchura))
        self.campo_cantidad.setValue(self.obra_articulo.cantidad)
        self.campo_precio_unitario.setValue(self.obra_articulo.precio_unitario or 0.0)
        self.campo_precio_total.setValue(self.obra_articulo.precio_total or 0.0)
        notas = self.obra_articulo.notas or ""
        self.campo_notas.setPlainText(notas)

        # Intentar cargar datos de diseño desde las notas
        try:
            datos_guardados = json.loads(notas)
            if isinstance(datos_guardados, dict) and 'altura' in datos_guardados:
                self.diseno_data = datos_guardados
                print("Datos de diseño cargados desde las notas.")
        except (json.JSONDecodeError, TypeError):
            self.diseno_data = None # No es un JSON de diseño válido
        
        # Mostrar materiales
        self._mostrar_materiales()
    
    def _on_articulo_changed(self):
        """Se ejecuta cuando cambia la selección del artículo."""
        self._calcular_precio()
    
    def _calcular_precio(self):
        """Calcula el precio del artículo con las medidas actuales."""
        articulo_id = self.combo_articulo.currentData()
        if not articulo_id:
            self.campo_precio_unitario.setValue(0.0)
            self.campo_precio_total.setValue(0.0)
            self.texto_materiales.clear()
            return
        
        db = next(get_db())
        try:
            articulo = db.query(Articulo).filter(Articulo.id == articulo_id).first()
            if not articulo:
                return
            
            altura = self.campo_altura.value()
            anchura = self.campo_anchura.value()
            cantidad = self.campo_cantidad.value()
            
            # Calcular materiales
            materiales = articulo.calcular_materiales(altura, anchura, diseno_data=self.diseno_data)
            
            # Calcular precio
            precio_unitario = 0.0
            
            # Sumar costes de perfiles
            for material in materiales['perfiles']:
                if material['perfil'] and material['perfil'].precio_metro:
                    precio_unitario += material['metros_totales'] * material['perfil'].precio_metro
            
            # Sumar costes de accesorios
            for material in materiales['accesorios']:
                if material['accesorio'] and material['accesorio'].precio:
                    precio_unitario += material['cantidad'] * material['accesorio'].precio
            
            # Sumar costes de cristales
            for material in materiales['cristales']:
                if material['cristal'] and material['cristal'].precio_metro_cuadrado:
                    precio_unitario += material['metros_cuadrados'] * material['cristal'].precio_metro_cuadrado
            
            precio_total = precio_unitario * cantidad
            
            self.campo_precio_unitario.setValue(precio_unitario)
            self.campo_precio_total.setValue(precio_total)
            
            # Mostrar detalles de materiales
            self._mostrar_materiales_detalle(articulo, altura, anchura, materiales)
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al calcular precio: {str(e)}")
        finally:
            db.close()
    
    def _mostrar_materiales(self):
        """Muestra los materiales del artículo actual."""
        articulo_id = self.combo_articulo.currentData()
        if not articulo_id:
            return
        
        db = next(get_db())
        try:
            articulo = db.query(Articulo).filter(Articulo.id == articulo_id).first()
            if articulo:
                altura = self.campo_altura.value()
                anchura = self.campo_anchura.value()
                materiales = articulo.calcular_materiales(altura, anchura)
                self._mostrar_materiales_detalle(articulo, altura, anchura, materiales)
        except Exception as e:
            self.texto_materiales.setText(f"Error al mostrar materiales: {str(e)}")
        finally:
            db.close()
    
    def _mostrar_materiales_detalle(self, articulo, altura, anchura, materiales):
        """Muestra el detalle de materiales calculados."""
        resultado = f"MATERIALES PARA {articulo.codigo}\n"
        resultado += f"Medidas: {altura} x {anchura} mm\n"
        resultado += "=" * 40 + "\n\n"
        
        # Perfiles
        if materiales['perfiles']:
            resultado += "PERFILES:\n"
            total_metros = 0
            total_precio = 0
            for material in materiales['perfiles']:
                perfil = material['perfil']
                cantidad = material['cantidad']
                medida = material['medida']
                metros = material['metros_totales']
                total_metros += metros
                
                precio = metros * perfil.precio_metro if perfil.precio_metro else 0
                total_precio += precio
                
                resultado += f"  {perfil.codigo}: {cantidad:.1f} uds, {metros:.3f}m, {precio:.2f}€\n"
            
            resultado += f"  Total: {total_metros:.3f}m, {total_precio:.2f}€\n\n"
        
        # Accesorios
        if materiales['accesorios']:
            resultado += "ACCESORIOS:\n"
            total_precio = 0
            for material in materiales['accesorios']:
                accesorio = material['accesorio']
                cantidad = material['cantidad']
                
                precio = cantidad * accesorio.precio if accesorio.precio else 0
                total_precio += precio
                
                resultado += f"  {accesorio.codigo}: {cantidad:.1f} uds, {precio:.2f}€\n"
            
            resultado += f"  Total: {total_precio:.2f}€\n\n"
        
        # Cristales
        if materiales['cristales']:
            resultado += "CRISTALES:\n"
            total_m2 = 0
            total_precio = 0
            for material in materiales['cristales']:
                cristal = material['cristal']
                cantidad = material['cantidad']
                m2 = material['metros_cuadrados']
                total_m2 += m2
                
                precio = m2 * cristal.precio_metro_cuadrado if cristal.precio_metro_cuadrado else 0
                total_precio += precio
                
                resultado += f"  {cristal.codigo}: {cantidad:.1f} uds, {m2:.3f}m², {precio:.2f}€\n"
            
            resultado += f"  Total: {total_m2:.3f}m², {total_precio:.2f}€\n\n"
        
        self.texto_materiales.setText(resultado)
    
    def _aceptar(self):
        """Valida y guarda el artículo."""
        if self.combo_articulo.currentData() is None:
            QMessageBox.warning(self, "Error", "Debe seleccionar un artículo.")
            return
        
        if self._guardar_articulo():
            self.accept()
    
    def _guardar_articulo(self):
        """Guarda el artículo en la obra."""
        db = next(get_db())
        
        try:
            if self.obra_articulo:
                # Editar artículo existente
                obra_articulo_db = db.query(ObraArticulo).filter(
                    ObraArticulo.id == self.obra_articulo.id
                ).first()
                if not obra_articulo_db:
                    QMessageBox.critical(self, "Error", "No se encontró el artículo a editar.")
                    return False
            else:
                # Crear nuevo artículo
                obra_articulo_db = ObraArticulo()
                obra_articulo_db.obra_id = self.obra.id
                db.add(obra_articulo_db)
            
            # Asignar valores
            obra_articulo_db.articulo_id = self.combo_articulo.currentData()
            obra_articulo_db.altura = float(self.campo_altura.value())
            obra_articulo_db.anchura = float(self.campo_anchura.value())
            obra_articulo_db.cantidad = self.campo_cantidad.value()
            obra_articulo_db.precio_unitario = self.campo_precio_unitario.value()
            obra_articulo_db.precio_total = self.campo_precio_total.value()

            # Guardar el diseño en las notas si existe
            if self.diseno_data:
                obra_articulo_db.notas = json.dumps(self.diseno_data)
            else:
                obra_articulo_db.notas = self.campo_notas.toPlainText().strip() or None

            # Aquí se usarían los datos de self.diseno_data para un cálculo detallado
            if self.diseno_data:
                print("Calculando precio con datos de diseño...")
                # TODO: Implementar lógica de cálculo basada en el diseño

            db.commit()
            
            QMessageBox.information(self, "Éxito", "Artículo guardado correctamente.")
            return True
            
        except Exception as e:
            db.rollback()
            QMessageBox.critical(self, "Error", f"No se pudo guardar el artículo: {str(e)}")
            return False
        finally:
            db.close()
