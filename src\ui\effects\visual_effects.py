"""
Efectos visuales avanzados para PRO-2000
Animaciones, transiciones y efectos modernos
"""

from PyQt6.QtCore import QPropertyAnimation, QEasingCurve, QRect, pyqtSignal, QTimer, QPoint
from PyQt6.QtWidgets import QGraphicsDropShadowEffect, QWidget, QGraphicsOpacityEffect
from PyQt6.QtGui import QColor, QPalette, QFont
from PyQt6.QtCore import QParallelAnimationGroup, QSequentialAnimationGroup


class VisualEffectsManager:
    """Manager para efectos visuales modernos"""
    
    @staticmethod
    def add_glow_effect(widget, color="#6366f1", blur_radius=20):
        """Añade efecto de resplandor a un widget"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(blur_radius)
        shadow.setColor(QColor(color))
        shadow.setOffset(0, 0)
        widget.setGraphicsEffect(shadow)
        return shadow
    
    @staticmethod
    def add_fade_in_animation(widget, duration=300):
        """Animación de aparición gradual"""
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        
        animation = QPropertyAnimation(effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(0.0)
        animation.setEndValue(1.0)
        animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        animation.start()
        return animation
    
    @staticmethod
    def add_slide_in_animation(widget, direction="left", duration=400):
        """Animación de deslizamiento"""
        start_pos = widget.pos()
        
        if direction == "left":
            widget.move(start_pos.x() - 300, start_pos.y())
        elif direction == "right":
            widget.move(start_pos.x() + 300, start_pos.y())
        elif direction == "top":
            widget.move(start_pos.x(), start_pos.y() - 300)
        elif direction == "bottom":
            widget.move(start_pos.x(), start_pos.y() + 300)
        
        animation = QPropertyAnimation(widget, b"pos")
        animation.setDuration(duration)
        animation.setEndValue(start_pos)
        animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        animation.start()
        return animation
    
    @staticmethod
    def add_scale_animation(widget, scale_from=0.8, scale_to=1.0, duration=300):
        """Animación de escalado"""
        # Nota: PyQt6 no soporta directamente animaciones de escala
        # Usaremos animación de geometría como alternativa
        original_geometry = widget.geometry()
        
        # Calcular geometría inicial escalada
        center = original_geometry.center()
        scaled_width = int(original_geometry.width() * scale_from)
        scaled_height = int(original_geometry.height() * scale_from)
        
        start_geometry = QRect(
            center.x() - scaled_width // 2,
            center.y() - scaled_height // 2,
            scaled_width,
            scaled_height
        )
        
        widget.setGeometry(start_geometry)
        
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(duration)
        animation.setEndValue(original_geometry)
        animation.setEasingCurve(QEasingCurve.Type.OutBack)
        
        animation.start()
        return animation
    
    @staticmethod
    def add_hover_effect(widget, hover_color="#8b5cf6", normal_color="#6366f1"):
        """Efecto hover con cambio de color"""
        def on_enter():
            widget.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {hover_color}, stop:1 #06b6d4);
                    border-radius: 14px;
                    padding: 14px 28px;
                    font-weight: 600;
                    color: white;
                }}
            """)
        
        def on_leave():
            widget.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {normal_color}, stop:1 #8b5cf6);
                    border-radius: 14px;
                    padding: 14px 28px;
                    font-weight: 600;
                    color: white;
                }}
            """)
        
        widget.enterEvent = lambda event: on_enter()
        widget.leaveEvent = lambda event: on_leave()
    
    @staticmethod
    def add_pulse_animation(widget, duration=1000):
        """Animación de pulso continuo"""
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        
        animation = QPropertyAnimation(effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(0.5)
        animation.setEndValue(1.0)
        animation.setEasingCurve(QEasingCurve.Type.InOutSine)
        animation.setLoopCount(-1)  # Infinito
        
        # Animación de ida y vuelta
        animation.finished.connect(lambda: animation.setDirection(
            QPropertyAnimation.Direction.Backward if animation.direction() == QPropertyAnimation.Direction.Forward 
            else QPropertyAnimation.Direction.Forward
        ))
        
        animation.start()
        return animation
    
    @staticmethod
    def add_button_press_animation(button):
        """Animación al presionar botón"""
        original_geometry = button.geometry()
        
        def on_press():
            # Reducir ligeramente el tamaño
            pressed_geometry = QRect(
                original_geometry.x() + 2,
                original_geometry.y() + 2,
                original_geometry.width() - 4,
                original_geometry.height() - 4
            )
            
            animation = QPropertyAnimation(button, b"geometry")
            animation.setDuration(100)
            animation.setEndValue(pressed_geometry)
            animation.setEasingCurve(QEasingCurve.Type.OutCubic)
            animation.start()
        
        def on_release():
            # Volver al tamaño original
            animation = QPropertyAnimation(button, b"geometry")
            animation.setDuration(100)
            animation.setEndValue(original_geometry)
            animation.setEasingCurve(QEasingCurve.Type.OutCubic)
            animation.start()
        
        button.pressed.connect(on_press)
        button.released.connect(on_release)


class ModernLoadingSpinner:
    """Spinner de carga moderno"""
    
    def __init__(self, parent):
        self.parent = parent
        self.timer = QTimer()
        self.angle = 0
        
    def start(self):
        """Inicia la animación de carga"""
        self.timer.timeout.connect(self.rotate)
        self.timer.start(50)  # 50ms = 20 FPS
        
    def stop(self):
        """Detiene la animación"""
        self.timer.stop()
        
    def rotate(self):
        """Rota el spinner"""
        self.angle = (self.angle + 10) % 360
        # Aquí se actualizaría la rotación visual del spinner


class AnimationSequence:
    """Secuencia de animaciones coordinadas"""
    
    def __init__(self):
        self.animations = []
        self.group = QSequentialAnimationGroup()
    
    def add_animation(self, animation):
        """Añade una animación a la secuencia"""
        self.animations.append(animation)
        self.group.addAnimation(animation)
    
    def add_parallel_group(self, animations):
        """Añade un grupo de animaciones paralelas"""
        parallel_group = QParallelAnimationGroup()
        for animation in animations:
            parallel_group.addAnimation(animation)
        self.group.addAnimation(parallel_group)
    
    def start(self):
        """Inicia la secuencia de animaciones"""
        self.group.start()
    
    def stop(self):
        """Detiene la secuencia"""
        self.group.stop()


class ThemeTransition:
    """Transiciones suaves entre temas"""
    
    @staticmethod
    def fade_transition(widget, new_stylesheet, duration=500):
        """Transición con desvanecimiento entre estilos"""
        # Crear efecto de opacidad
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        
        # Animación de desvanecimiento
        fade_out = QPropertyAnimation(effect, b"opacity")
        fade_out.setDuration(duration // 2)
        fade_out.setStartValue(1.0)
        fade_out.setEndValue(0.0)
        fade_out.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        fade_in = QPropertyAnimation(effect, b"opacity")
        fade_in.setDuration(duration // 2)
        fade_in.setStartValue(0.0)
        fade_in.setEndValue(1.0)
        fade_in.setEasingCurve(QEasingCurve.Type.InCubic)
        
        # Cambiar stylesheet en el medio
        def change_style():
            widget.setStyleSheet(new_stylesheet)
            fade_in.start()
        
        fade_out.finished.connect(change_style)
        fade_out.start()
        
        return fade_out, fade_in


class InteractiveEffects:
    """Efectos interactivos para mejorar la UX"""
    
    @staticmethod
    def add_ripple_effect(widget, color="#6366f1"):
        """Efecto ripple al hacer clic (simulado)"""
        def create_ripple(event):
            # Crear efecto visual de ondas
            # En una implementación completa, esto crearía un widget temporal
            # que simule el efecto ripple de Material Design
            pass
        
        widget.mousePressEvent = create_ripple
    
    @staticmethod
    def add_magnetic_effect(widget, strength=10):
        """Efecto magnético que atrae el cursor"""
        original_pos = widget.pos()
        
        def on_hover_move(event):
            # Calcular la atracción hacia el centro del widget
            center = widget.rect().center()
            mouse_pos = event.pos()
            
            # Aplicar un pequeño desplazamiento hacia el cursor
            offset_x = (mouse_pos.x() - center.x()) * 0.1
            offset_y = (mouse_pos.y() - center.y()) * 0.1
            
            new_pos = QPoint(
                original_pos.x() + int(offset_x),
                original_pos.y() + int(offset_y)
            )
            
            # Animar hacia la nueva posición
            animation = QPropertyAnimation(widget, b"pos")
            animation.setDuration(100)
            animation.setEndValue(new_pos)
            animation.setEasingCurve(QEasingCurve.Type.OutCubic)
            animation.start()
        
        def on_leave():
            # Volver a la posición original
            animation = QPropertyAnimation(widget, b"pos")
            animation.setDuration(200)
            animation.setEndValue(original_pos)
            animation.setEasingCurve(QEasingCurve.Type.OutCubic)
            animation.start()
        
        widget.mouseMoveEvent = on_hover_move
        widget.leaveEvent = lambda event: on_leave()
