
/* Estilos mejorados para mejor legibilidad - PRO-2000 */

/* Ventana principal con mejor contraste */
QMainWindow {
    background-color: #ffffff;
    color: #1a1a1a;
    font-family: "Segoe UI", Arial, sans-serif;
    font-size: 14px;
}

/* Pestañas con mejor visibilidad */
QTabWidget::pane {
    border: 2px solid #d1d5db;
    background-color: #ffffff;
    margin-top: 12px;
    border-radius: 6px;
}

QTabBar::tab {
    background-color: #f3f4f6;
    color: #374151;
    padding: 14px 24px;
    margin-right: 3px;
    border: 2px solid #d1d5db;
    border-bottom: none;
    min-width: 120px;
    font-weight: 600;
    font-size: 14px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

QTabBar::tab:selected {
    background-color: #ffffff;
    color: #1f2937;
    font-weight: 700;
    border-color: #3b82f6;
    border-bottom: 2px solid #ffffff;
}

QTabBar::tab:hover {
    background-color: #e5e7eb;
    color: #1f2937;
}

/* Botones con mejor contraste y legibilidad */
QPushButton {
    background-color: #3b82f6;
    color: #ffffff;
    border: none;
    padding: 12px 20px;
    font-weight: 600;
    min-width: 100px;
    font-size: 14px;
    border-radius: 6px;
}

QPushButton:hover {
    background-color: #2563eb;
    color: #ffffff;
}

QPushButton:pressed {
    background-color: #1d4ed8;
    color: #ffffff;
}

/* Barra de menú mejorada */
QMenuBar {
    background-color: #ffffff;
    color: #1f2937;
    border-bottom: 2px solid #e5e7eb;
    padding: 6px;
    font-size: 15px;
    font-weight: 600;
}

QMenuBar::item {
    background-color: transparent;
    padding: 10px 16px;
    border-radius: 4px;
    margin: 2px;
}

QMenuBar::item:selected {
    background-color: #f3f4f6;
    color: #1f2937;
}

QMenuBar::item:pressed {
    background-color: #e5e7eb;
}

/* Menús desplegables mejorados */
QMenu {
    background-color: #ffffff;
    border: 2px solid #d1d5db;
    padding: 6px;
    border-radius: 8px;
    font-size: 14px;
}

QMenu::item {
    padding: 10px 20px;
    border-radius: 4px;
    margin: 1px;
}

QMenu::item:selected {
    background-color: #3b82f6;
    color: #ffffff;
}

/* Campos de entrada con mejor legibilidad */
QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
    background-color: #ffffff;
    border: 2px solid #d1d5db;
    padding: 10px 14px;
    color: #1f2937;
    font-size: 14px;
    border-radius: 6px;
}

QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
    border-color: #3b82f6;
    background-color: #f8faff;
    outline: none;
}

/* Tablas con mejor legibilidad */
QTableWidget {
    background-color: #ffffff;
    alternate-background-color: #f9fafb;
    gridline-color: #e5e7eb;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
}

QTableWidget::item {
    padding: 12px 8px;
    color: #1f2937;
}

QTableWidget::item:selected {
    background-color: #dbeafe;
    color: #1e40af;
}

QHeaderView::section {
    background-color: #f3f4f6;
    color: #1f2937;
    padding: 14px 12px;
    border: none;
    border-right: 1px solid #d1d5db;
    border-bottom: 2px solid #d1d5db;
    font-weight: 700;
    font-size: 14px;
}

/* Grupos con mejor visibilidad */
QGroupBox {
    font-weight: 700;
    color: #1f2937;
    border: 2px solid #d1d5db;
    margin-top: 16px;
    padding-top: 16px;
    border-radius: 8px;
    font-size: 15px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 12px;
    padding: 4px 12px;
    background-color: #ffffff;
    border-radius: 4px;
}

/* Barra de estado mejorada */
QStatusBar {
    background-color: #f9fafb;
    border-top: 2px solid #e5e7eb;
    color: #4b5563;
    font-size: 13px;
    padding: 6px;
}

/* Botones secundarios */
QPushButton[class="secondary"] {
    background-color: #6b7280;
    color: #ffffff;
}

QPushButton[class="secondary"]:hover {
    background-color: #4b5563;
}

/* Botones de éxito */
QPushButton[class="success"] {
    background-color: #10b981;
    color: #ffffff;
}

QPushButton[class="success"]:hover {
    background-color: #059669;
}

/* Botones de peligro */
QPushButton[class="danger"] {
    background-color: #ef4444;
    color: #ffffff;
}

QPushButton[class="danger"]:hover {
    background-color: #dc2626;
}

/* Etiquetas con mejor contraste */
QLabel {
    color: #1f2937;
    font-size: 14px;
}

/* Scrollbars mejoradas */
QScrollBar:vertical {
    background-color: #f3f4f6;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #9ca3af;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #6b7280;
}

QScrollBar:horizontal {
    background-color: #f3f4f6;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #9ca3af;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #6b7280;
}
