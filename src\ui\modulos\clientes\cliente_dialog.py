"""
<PERSON><PERSON><PERSON><PERSON> que define el diálogo para gestionar clientes.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QLineEdit, QTableWidget, QTableWidgetItem, QMessageBox, QHeaderView,
    QDialogButtonBox, QFormLayout, QCheckBox, QComboBox, QTextEdit, QGroupBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QIntValidator

from models.base import get_db
from models.cliente import Cliente

class ClienteDialog(QDialog):
    """Diálogo para gestionar clientes."""
    
    def __init__(self, parent=None):
        """
        Inicializa el diálogo de gestión de clientes.
        
        Args:
            parent: Widget padre
        """
        super().__init__(parent)
        self.setWindowTitle("Gestión de Clientes")
        
        # Configuración inteligente del diálogo
        from ui.utils.window_utils import smart_dialog_setup
        smart_dialog_setup(self, "management", "Gestión de Clientes", maximize=True)
        
        # Variables
        self.cliente_actual = None
        
        # Configurar interfaz
        self._configurar_ui()
        
        # Cargar datos
        self._cargar_clientes()
    
    def _configurar_ui(self):
        """Configura la interfaz de usuario del diálogo."""
        # Layout principal
        layout_principal = QVBoxLayout(self)
        
        # Barra de herramientas
        layout_botones = QHBoxLayout()
        
        self.boton_nuevo = QPushButton("Nuevo")
        self.boton_editar = QPushButton("Editar")
        self.boton_eliminar = QPushButton("Eliminar")
        self.boton_actualizar = QPushButton("Actualizar")
        
        # Deshabilitar botones hasta que se seleccione un cliente
        self.boton_editar.setEnabled(False)
        self.boton_eliminar.setEnabled(False)
        
        layout_botones.addWidget(self.boton_nuevo)
        layout_botones.addWidget(self.boton_editar)
        layout_botones.addWidget(self.boton_eliminar)
        layout_botones.addStretch()
        layout_botones.addWidget(self.boton_actualizar)
        
        # Barra de búsqueda
        layout_busqueda = QHBoxLayout()
        self.buscar_input = QLineEdit()
        self.buscar_input.setPlaceholderText("Buscar por nombre, NIF o teléfono...")
        self.buscar_input.textChanged.connect(self._filtrar_clientes)
        layout_busqueda.addWidget(QLabel("Buscar:"))
        layout_busqueda.addWidget(self.buscar_input)
        
        # Tabla de clientes
        self.tabla_clientes = QTableWidget()
        self.tabla_clientes.setColumnCount(10)
        self.tabla_clientes.setHorizontalHeaderLabels([
            "Código", "Nombre/Razón Social", "Tipo", "NIF/CIF", "Teléfono", 
            "Email", "Dirección", "Ciudad", "Provincia", "Activo"
        ])
        
        # Ajustar el ancho de las columnas
        header = self.tabla_clientes.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # Código
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Nombre/Razón Social
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Tipo
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # NIF/CIF
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Teléfono
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # Email
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # Dirección
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)  # Ciudad
        header.setSectionResizeMode(8, QHeaderView.ResizeMode.ResizeToContents)  # Provincia
        header.setSectionResizeMode(9, QHeaderView.ResizeMode.ResizeToContents)  # Activo
        
        # Permitir ordenación por columnas
        self.tabla_clientes.setSortingEnabled(True)
        
        # Conectar señales
        self.boton_nuevo.clicked.connect(self._on_nuevo_cliente)
        self.boton_editar.clicked.connect(self._on_editar_cliente)
        self.boton_eliminar.clicked.connect(self._on_eliminar_cliente)
        self.boton_actualizar.clicked.connect(self._cargar_clientes)
        self.tabla_clientes.itemSelectionChanged.connect(self._on_seleccion_cambiada)
        self.tabla_clientes.doubleClicked.connect(self._on_editar_cliente)
        
        # Añadir widgets al layout principal
        layout_principal.addLayout(layout_botones)
        layout_principal.addLayout(layout_busqueda)
        layout_principal.addWidget(self.tabla_clientes)
        
        # Botones de diálogo
        botones_dialogo = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        botones_dialogo.rejected.connect(self.reject)
        layout_principal.addWidget(botones_dialogo)
    
    def _cargar_clientes(self, filtro=None):
        """
        Carga la lista de clientes desde la base de datos.
        
        Args:
            filtro: Texto para filtrar clientes (opcional)
        """
        db = next(get_db())
        
        try:
            # Construir consulta
            query = db.query(Cliente)
            
            # Aplicar filtro si se proporciona
            if filtro and filtro.strip():
                filtro = f"%{filtro.strip()}%"
                query = query.filter(
                    (Cliente.nombre.ilike(filtro)) |
                    (Cliente.nif.ilike(filtro)) |
                    (Cliente.telefono.ilike(filtro)) |
                    (Cliente.email.ilike(filtro))
                )
            
            # Ordenar por nombre
            clientes = query.order_by(Cliente.nombre).all()
            
            # Configurar la tabla
            self.tabla_clientes.setRowCount(len(clientes))
            
            for fila, cliente in enumerate(clientes):
                # Código
                self.tabla_clientes.setItem(fila, 0, QTableWidgetItem(cliente.codigo))
                
                # Nombre/Razón Social
                self.tabla_clientes.setItem(fila, 1, QTableWidgetItem(cliente.nombre or ""))
                
                # Tipo
                self.tabla_clientes.setItem(fila, 2, QTableWidgetItem(cliente.tipo or ""))
                
                # NIF/CIF
                self.tabla_clientes.setItem(fila, 3, QTableWidgetItem(cliente.nif or ""))
                
                # Teléfono
                self.tabla_clientes.setItem(fila, 4, QTableWidgetItem(cliente.telefono or ""))
                
                # Email
                self.tabla_clientes.setItem(fila, 5, QTableWidgetItem(cliente.email or ""))
                
                # Dirección
                self.tabla_clientes.setItem(fila, 6, QTableWidgetItem(cliente.direccion or ""))
                
                # Ciudad
                self.tabla_clientes.setItem(fila, 7, QTableWidgetItem(cliente.ciudad or ""))
                
                # Provincia
                self.tabla_clientes.setItem(fila, 8, QTableWidgetItem(cliente.provincia or ""))
                
                # Activo
                activo_item = QTableWidgetItem()
                activo_item.setFlags(Qt.ItemFlag.ItemIsUserCheckable | Qt.ItemFlag.ItemIsEnabled)
                activo_item.setCheckState(
                    Qt.CheckState.Checked if cliente.activo else Qt.CheckState.Unchecked
                )
                activo_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.tabla_clientes.setItem(fila, 9, activo_item)
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudieron cargar los clientes: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        finally:
            db.close()
    
    def _filtrar_clientes(self):
        """Filtra la lista de clientes según el texto de búsqueda."""
        texto_busqueda = self.buscar_input.text().strip()
        self._cargar_clientes(texto_busqueda if texto_busqueda else None)
    
    def _on_seleccion_cambiada(self):
        """Maneja el evento de cambio de selección en la tabla."""
        seleccion = self.tabla_clientes.selectedItems()
        habilitar = len(seleccion) > 0
        
        self.boton_editar.setEnabled(habilitar)
        self.boton_eliminar.setEnabled(habilitar)
    
    def _on_nuevo_cliente(self):
        """Maneja el evento de crear un nuevo cliente."""
        dialogo = ClienteEditarDialog(self)
        if dialogo.exec() == QDialog.DialogCode.Accepted:
            # Guardar el nuevo cliente
            datos = dialogo.get_datos()
            db = next(get_db())

            try:
                nuevo_cliente = Cliente(**datos)
                db.add(nuevo_cliente)
                db.commit()

                QMessageBox.information(
                    self,
                    "Éxito",
                    f"Cliente '{datos['codigo']}' creado correctamente.",
                    QMessageBox.StandardButton.Ok
                )

                self._cargar_clientes()

            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self,
                    "Error",
                    f"No se pudo crear el cliente: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )
            finally:
                db.close()
    
    def _on_editar_cliente(self):
        """Maneja el evento de editar un cliente existente."""
        fila = self.tabla_clientes.currentRow()
        if fila < 0:
            return

        codigo = self.tabla_clientes.item(fila, 0).text()
        db = next(get_db())

        try:
            cliente = db.query(Cliente).filter(Cliente.codigo == codigo).first()
            if not cliente:
                raise ValueError("Cliente no encontrado")

            dialogo = ClienteEditarDialog(self, cliente)
            if dialogo.exec() == QDialog.DialogCode.Accepted:
                # Actualizar el cliente existente
                datos = dialogo.get_datos()

                cliente.tipo = datos['tipo']
                cliente.nombre = datos['nombre']
                cliente.nif = datos['nif']
                cliente.telefono = datos['telefono']
                cliente.movil = datos['movil']
                cliente.email = datos['email']
                cliente.web = datos['web']
                cliente.direccion = datos['direccion']
                cliente.codigo_postal = datos['codigo_postal']
                cliente.ciudad = datos['ciudad']
                cliente.provincia = datos['provincia']
                cliente.pais = datos['pais']
                cliente.persona_contacto = datos['persona_contacto']
                cliente.telefono_contacto = datos['telefono_contacto']
                cliente.notas = datos['notas']
                cliente.activo = datos['activo']

                db.commit()

                QMessageBox.information(
                    self,
                    "Éxito",
                    f"Cliente '{cliente.codigo}' actualizado correctamente.",
                    QMessageBox.StandardButton.Ok
                )

                self._cargar_clientes()

        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudo actualizar el cliente: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        finally:
            db.close()
    
    def _on_eliminar_cliente(self):
        """Maneja el evento de eliminar un cliente."""
        fila = self.tabla_clientes.currentRow()
        if fila < 0:
            return
        
        codigo = self.tabla_clientes.item(fila, 0).text()
        nombre = self.tabla_clientes.item(fila, 1).text()
        
        respuesta = QMessageBox.question(
            self,
            "Confirmar eliminación",
            f"¿Está seguro de que desea eliminar al cliente '{codigo} - {nombre}'?\n"
            "Esta acción no se puede deshacer.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if respuesta == QMessageBox.StandardButton.Yes:
            db = next(get_db())
            try:
                cliente = db.query(Cliente).filter(Cliente.codigo == codigo).first()
                if cliente:
                    # Verificar si el cliente tiene obras asociadas
                    if hasattr(cliente, 'obras') and cliente.obras:
                        QMessageBox.warning(
                            self,
                            "No se puede eliminar",
                            "No se puede eliminar el cliente porque tiene obras asociadas. "
                            "Elimine primero las obras asociadas.",
                            QMessageBox.StandardButton.Ok
                        )
                        return
                    
                    db.delete(cliente)
                    db.commit()
                    self._cargar_clientes()
            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self,
                    "Error",
                    f"No se pudo eliminar el cliente: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )
            finally:
                db.close()


class ClienteEditarDialog(QDialog):
    """Diálogo para editar o crear un cliente."""
    
    TIPOS_CLIENTE = ["", "Particular", "Empresa", "Autónomo", "Administración Pública"]
    PROVINCIAS = [
        "", "Álava", "Albacete", "Alicante", "Almería", "Asturias", "Ávila", "Badajoz", 
        "Islas Baleares", "Barcelona", "Burgos", "Cáceres", "Cádiz", "Cantabria", 
        "Castellón", "Ciudad Real", "Córdoba", "La Coruña", "Cuenca", "Gerona", 
        "Granada", "Guadalajara", "Guipúzcoa", "Huelva", "Huesca", "Jaén", "León", 
        "Lérida", "Lugo", "Madrid", "Málaga", "Murcia", "Navarra", "Orense", "Palencia", 
        "Las Palmas", "Pontevedra", "La Rioja", "Salamanca", "Segovia", "Sevilla", 
        "Soria", "Tarragona", "Santa Cruz de Tenerife", "Teruel", "Toledo", "Valencia", 
        "Valladolid", "Vizcaya", "Zamora", "Zaragoza"
    ]
    
    def __init__(self, parent=None, cliente=None):
        """
        Inicializa el diálogo de edición de cliente.
        
        Args:
            parent: Widget padre
            cliente: Instancia de Cliente a editar (None para nuevo)
        """
        super().__init__(parent)
        
        self.cliente = cliente
        self.setWindowTitle("Editar Cliente" if cliente else "Nuevo Cliente")
        
        # Configuración inteligente del diálogo
        from ui.utils.window_utils import smart_dialog_setup
        smart_dialog_setup(self, "simple", self.windowTitle(), maximize=False)
        
        # Configurar interfaz
        self._configurar_ui()
        
        # Si se está editando un cliente, cargar sus datos
        if cliente:
            self._cargar_datos_cliente()
    
    def _configurar_ui(self):
        """Configura la interfaz de usuario del diálogo."""
        altura_fila = 32  # Altura uniforme para todos los campos
        layout_principal = QVBoxLayout(self)

        # Mensaje de error fijo debajo del título
        self.etiqueta_error = QLabel()
        self.etiqueta_error.setVisible(False)
        layout_principal.addWidget(self.etiqueta_error)

        # Grupo de datos principales
        grupo_datos = QGroupBox("Datos Principales")
        layout_datos = QFormLayout()
        layout_datos.setLabelAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        layout_datos.setFormAlignment(Qt.AlignmentFlag.AlignTop)
        
        # Código
        self.codigo = QLineEdit()
        self.codigo.setMaxLength(10)
        self.codigo.setMinimumHeight(altura_fila)
        layout_datos.addRow("Código*:", self.codigo)
        
        # Nombre/Razón Social
        self.nombre = QLineEdit()
        self.nombre.setMaxLength(200)
        self.nombre.setMinimumHeight(altura_fila)
        layout_datos.addRow("Nombre/Razón Social*:", self.nombre)
        
        # Tipo de cliente
        self.tipo = QComboBox()
        self.tipo.addItems(self.TIPOS_CLIENTE)
        self.tipo.setMinimumHeight(altura_fila)
        layout_datos.addRow("Tipo de cliente:", self.tipo)
        
        # NIF/CIF/NIE
        self.nif = QLineEdit()
        self.nif.setMaxLength(20)
        self.nif.setMinimumHeight(altura_fila)
        layout_datos.addRow("NIF/CIF/NIE*:", self.nif)
        
        grupo_datos.setLayout(layout_datos)

        # Grupo de contacto
        grupo_contacto = QGroupBox("Datos de Contacto")
        layout_contacto = QFormLayout()
        layout_contacto.setLabelAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        layout_contacto.setFormAlignment(Qt.AlignmentFlag.AlignTop)
        
        # Teléfono
        self.telefono = QLineEdit()
        self.telefono.setMaxLength(20)
        self.telefono.setMinimumHeight(altura_fila)
        layout_contacto.addRow("Teléfono:", self.telefono)
        
        # Móvil
        self.movil = QLineEdit()
        self.movil.setMaxLength(20)
        self.movil.setMinimumHeight(altura_fila)
        layout_contacto.addRow("Móvil:", self.movil)
        
        # Email
        self.email = QLineEdit()
        self.email.setMaxLength(200)
        self.email.setMinimumHeight(altura_fila)
        layout_contacto.addRow("Email:", self.email)
        
        # Web
        self.web = QLineEdit()
        self.web.setMaxLength(200)
        self.web.setMinimumHeight(altura_fila)
        layout_contacto.addRow("Web:", self.web)
        
        grupo_contacto.setLayout(layout_contacto)

        # Grupo de dirección
        grupo_direccion = QGroupBox("Dirección")
        layout_direccion = QFormLayout()
        layout_direccion.setLabelAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        layout_direccion.setFormAlignment(Qt.AlignmentFlag.AlignTop)
        
        # Dirección
        self.direccion = QLineEdit()
        self.direccion.setMaxLength(200)
        self.direccion.setMinimumHeight(altura_fila)
        layout_direccion.addRow("Dirección:", self.direccion)
        
        # Código Postal
        self.codigo_postal = QLineEdit()
        self.codigo_postal.setMaxLength(10)
        self.codigo_postal.setMinimumHeight(altura_fila)
        layout_direccion.addRow("Código Postal:", self.codigo_postal)
        
        # Población
        self.ciudad = QLineEdit()
        self.ciudad.setMaxLength(100)
        self.ciudad.setMinimumHeight(altura_fila)
        layout_direccion.addRow("Población:", self.ciudad)
        
        # Provincia
        self.provincia = QComboBox()
        self.provincia.addItems(self.PROVINCIAS)
        self.provincia.setMinimumHeight(altura_fila)
        layout_direccion.addRow("Provincia:", self.provincia)
        
        # País
        self.pais = QLineEdit()
        self.pais.setMaxLength(100)
        self.pais.setText("España")  # Valor por defecto
        self.pais.setMinimumHeight(altura_fila)
        layout_direccion.addRow("País:", self.pais)
        
        grupo_direccion.setLayout(layout_direccion)

        # Grupo de datos adicionales
        grupo_adicionales = QGroupBox("Datos Adicionales")
        layout_adicionales = QFormLayout()
        layout_adicionales.setLabelAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        layout_adicionales.setFormAlignment(Qt.AlignmentFlag.AlignTop)
        
        # Persona de contacto
        self.persona_contacto = QLineEdit()
        self.persona_contacto.setMaxLength(100)
        self.persona_contacto.setMinimumHeight(altura_fila)
        layout_adicionales.addRow("Persona de contacto:", self.persona_contacto)
        
        # Teléfono de contacto
        self.telefono_contacto = QLineEdit()
        self.telefono_contacto.setMaxLength(20)
        self.telefono_contacto.setMinimumHeight(altura_fila)
        layout_adicionales.addRow("Teléfono de contacto:", self.telefono_contacto)
        
        # Notas
        self.notas = QTextEdit()
        self.notas.setFixedHeight(altura_fila)
        layout_adicionales.addRow("Notas:", self.notas)
        
        # Activo
        self.activo = QCheckBox("Cliente activo")
        self.activo.setChecked(True)
        layout_adicionales.addRow("", self.activo)
        
        grupo_adicionales.setLayout(layout_adicionales)

        # Añadir grupos al layout principal
        layout_principal.addWidget(grupo_datos)
        layout_principal.addWidget(grupo_contacto)
        layout_principal.addWidget(grupo_direccion)
        layout_principal.addWidget(grupo_adicionales)
        layout_principal.addStretch()

        # Botones estándar sin iconos ni estilos extra
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        botones.accepted.connect(self._validar_y_aceptar)
        botones.rejected.connect(self.reject)
        layout_principal.addWidget(botones)

        # Conectar señales para validación en tiempo real
        self.codigo.textChanged.connect(self._validar_formulario)
        self.nombre.textChanged.connect(self._validar_formulario)
        self.nif.textChanged.connect(self._validar_formulario)
    
    def _cargar_datos_cliente(self):
        """Carga los datos del cliente en el formulario."""
        if not self.cliente:
            return
            
        self.codigo.setText(self.cliente.codigo)
        self.codigo.setReadOnly(True)  # No permitir editar el código
        
        # Establecer el tipo de cliente
        index_tipo = self.tipo.findText(self.cliente.tipo or "")
        if index_tipo >= 0:
            self.tipo.setCurrentIndex(index_tipo)
            
        self.nombre.setText(self.cliente.nombre or "")
        self.nif.setText(self.cliente.nif or "")
        self.telefono.setText(self.cliente.telefono or "")
        self.movil.setText(getattr(self.cliente, 'movil', '') or "")
        self.email.setText(self.cliente.email or "")
        self.web.setText(getattr(self.cliente, 'web', '') or "")
        self.direccion.setText(self.cliente.direccion or "")
        self.codigo_postal.setText(getattr(self.cliente, 'codigo_postal', '') or "")
        self.ciudad.setText(self.cliente.ciudad or "")
        
        # Establecer la provincia
        provincia = getattr(self.cliente, 'provincia', '') or ""
        index_provincia = self.provincia.findText(provincia)
        if index_provincia >= 0:
            self.provincia.setCurrentIndex(index_provincia)
            
        self.pais.setText(getattr(self.cliente, 'pais', '') or "España")
        self.persona_contacto.setText(getattr(self.cliente, 'persona_contacto', '') or "")
        self.telefono_contacto.setText(getattr(self.cliente, 'telefono_contacto', '') or "")
        self.notas.setPlainText(getattr(self.cliente, 'notas', '') or "")
        self.activo.setChecked(bool(getattr(self.cliente, 'activo', True)))
    
    def _validar_formulario(self):
        """Valida los campos del formulario."""
        codigo = self.codigo.text().strip()
        nombre = self.nombre.text().strip()
        nif = self.nif.text().strip()
        
        if not codigo:
            self.mostrar_error("El código es obligatorio")
            return False
            
        if not nombre:
            self.mostrar_error("El nombre/razón social es obligatorio")
            return False
            
        if not nif:
            self.mostrar_error("El NIF/CIF/NIE es obligatorio")
            return False
            
        # Validar formato de email si se ha introducido
        email = self.email.text().strip()
        if email and "@" not in email:
            self.mostrar_error("El formato del email no es válido")
            return False
            
        self.ocultar_error()
        return True
    
    def mostrar_error(self, mensaje):
        """Muestra un mensaje de error en el formulario."""
        if not hasattr(self, 'etiqueta_error'):
            self.etiqueta_error = QLabel()
            self.etiqueta_error.setStyleSheet("color: red;")
            self.layout().insertWidget(0, self.etiqueta_error)
            
        self.etiqueta_error.setText(mensaje)
        self.etiqueta_error.setVisible(True)
    
    def ocultar_error(self):
        """Oculta el mensaje de error."""
        if hasattr(self, 'etiqueta_error'):
            self.etiqueta_error.setVisible(False)
    
    def _validar_y_aceptar(self):
        """Valida el formulario y acepta el diálogo si es válido."""
        if not self._validar_formulario():
            return
            
        # Validar que el código no esté duplicado (solo para nuevos clientes)
        if not self.cliente:
            db = next(get_db())
            try:
                existe = db.query(Cliente).filter(Cliente.codigo == self.codigo.text().strip()).first()
                if existe:
                    self.mostrar_error("Ya existe un cliente con este código")
                    return
            finally:
                db.close()
        
        self.accept()
    
    def get_datos(self):
        """
        Devuelve un diccionario con los datos del formulario.
        
        Returns:
            dict: Datos del cliente
        """
        return {
            'codigo': self.codigo.text().strip(),
            'tipo': self.tipo.currentText() or None,
            'nombre': self.nombre.text().strip(),
            'nif': self.nif.text().strip(),
            'telefono': self.telefono.text().strip() or None,
            'movil': self.movil.text().strip() or None,
            'email': self.email.text().strip() or None,
            'web': self.web.text().strip() or None,
            'direccion': self.direccion.text().strip() or None,
            'codigo_postal': self.codigo_postal.text().strip() or None,
            'ciudad': self.ciudad.text().strip() or None,
            'provincia': self.provincia.currentText() or None,
            'pais': self.pais.text().strip() or None,
            'persona_contacto': self.persona_contacto.text().strip() or None,
            'telefono_contacto': self.telefono_contacto.text().strip() or None,
            'notas': self.notas.toPlainText().strip() or None,
            'activo': self.activo.isChecked()
        }
