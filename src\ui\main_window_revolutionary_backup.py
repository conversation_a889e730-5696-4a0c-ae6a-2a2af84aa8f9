"""
RESPALDO DEL ARCHIVO ORIGINAL - main_window_revolutionary.py
Creado automáticamente antes de aplicar las reparaciones
Fecha: $(Get-Date)
"""
from datetime import datetime
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QStatusBar, QStackedWidget, QMessageBox, QApplication,
    QFrame, QScrollArea, QGridLayout, QSizePolicy, QSpacerItem
)
from PyQt6.QtCore import Qt, QSize, QPropertyAnimation, QEasingCurve, pyqtSignal
from PyQt6.QtGui import QIcon, QAction, QPixmap, QFont, QPainter, QColor, QBrush

from models.base import get_db
from models.usuario import Usuario
from .themes.theme_config import ThemeConfig

# Importar componentes del nuevo sistema
try:
    from .components import (
        ProfessionalButton,
        ProfessionalCard,
        ProfessionalFormSection,
        ProfessionalActionBar,
        ProfessionalStatusBar,
        create_professional_message_box
    )
except ImportError:
    # Fallback si los componentes no están disponibles
    print("⚠️ Componentes profesionales no disponibles, usando componentes básicos")
    ProfessionalButton = QPushButton
    ProfessionalCard = QFrame
    ProfessionalFormSection = QFrame
    ProfessionalActionBar = QFrame
    ProfessionalStatusBar = QStatusBar
    
    def create_professional_message_box(parent, title, message, message_type="info", buttons=None):
        msg = QMessageBox(parent)
        msg.setWindowTitle(title)
        msg.setText(message)
        if message_type == "error":
            msg.setIcon(QMessageBox.Icon.Critical)
        elif message_type == "warning":
            msg.setIcon(QMessageBox.Icon.Warning)
        elif message_type == "question":
            msg.setIcon(QMessageBox.Icon.Question)
            if buttons:
                msg.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        else:
            msg.setIcon(QMessageBox.Icon.Information)
        
        result = msg.exec()
        if buttons and result == QMessageBox.StandardButton.Yes:
            return buttons[0]
        elif buttons and result == QMessageBox.StandardButton.No:
            return buttons[1] if len(buttons) > 1 else "No"
        return "Ok"


class ModernCard(QFrame):
    """Tarjeta moderna para el dashboard"""
    clicked = pyqtSignal()
    
    def __init__(self, title, subtitle, icon_text, color="#3b82f6"):
        super().__init__()
        self.color = color
        self.setup_ui(title, subtitle, icon_text)
        
    def setup_ui(self, title, subtitle, icon_text):
        self.setFixedSize(280, 160)
        self.setStyleSheet(f"""
            ModernCard {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}, stop:1 {self.color}dd);
                border-radius: 20px;
                border: none;
            }}
            ModernCard:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}ee, stop:1 {self.color}cc);
            }}
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Icono
        icon_label = QLabel(icon_text)
        icon_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 36px;
                font-weight: bold;
                background: transparent;
            }
        """)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        
        # Título
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                background: transparent;
                margin-top: 10px;
            }
        """)
        
        # Subtítulo
        subtitle_label = QLabel(subtitle)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                background: transparent;
                margin-top: 5px;
            }
        """)
        
        layout.addWidget(icon_label)
        layout.addWidget(title_label)
        layout.addWidget(subtitle_label)
        layout.addStretch()
        
        self.setLayout(layout)
        
    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)


class SidebarButton(QPushButton):
    """Botón personalizado para la barra lateral"""
    
    def __init__(self, text, icon_text="", is_active=False):
        super().__init__()
        self.icon_text = icon_text
        self.is_active = is_active
        self.setText(f"{icon_text}  {text}")
        self.setFixedHeight(60)
        self.update_style()
        
    def update_style(self):
        if self.is_active:
            self.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #3b82f6, stop:1 #1d4ed8);
                    color: white;
                    border: none;
                    border-radius: 15px;
                    padding: 15px 20px;
                    font-size: 16px;
                    font-weight: 600;
                    text-align: left;
                    margin: 5px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #2563eb, stop:1 #1e40af);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #1d4ed8, stop:1 #1e3a8a);
                }
            """)
        else:
            self.setStyleSheet("""
                QPushButton {
                    background: transparent;
                    color: #64748b;
                    border: none;
                    border-radius: 15px;
                    padding: 15px 20px;
                    font-size: 16px;
                    font-weight: 500;
                    text-align: left;
                    margin: 5px;
                }
                QPushButton:hover {
                    background: rgba(59, 130, 246, 0.15);
                    color: #3b82f6;
                    font-weight: 600;
                }
                QPushButton:pressed {
                    background: rgba(59, 130, 246, 0.25);
                    color: #2563eb;
                }
            """)
    
    def set_active(self, active):
        self.is_active = active
        self.update_style()


class RevolutionaryMainWindow(QMainWindow):
    """Ventana principal completamente rediseñada"""
    
    def __init__(self, usuario):
        super().__init__()
        self.usuario = usuario
        self.current_module = None
        self.sidebar_buttons = []
        
        self.setWindowTitle("PRO-2000 - Sistema de Gestión Revolucionario")
        self.setMinimumSize(900, 600)
        self.resize(1050, 700)
        # Quitar los controles de ventana (minimizar, maximizar, cerrar)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        
        # Configurar la interfaz
        self.setup_ui()
        self.setup_connections()
        
        # Mostrar dashboard por defecto
        self.show_dashboard()