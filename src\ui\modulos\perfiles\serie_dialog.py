"""
Diálogo para gestionar series de perfiles
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QLineEdit, QTableWidget, QTableWidgetItem, QMessageBox, QHeaderView,
    QDialogButtonBox, QFormLayout, QTextEdit, QCheckBox, QWidget
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from datetime import datetime

from models.base import get_db
from models.perfil import SeriePerfil
from ui.utils.window_utils import setup_maximized_dialog


class SerieDialog(QDialog):
    """Diálogo para gestionar series de perfiles."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Gestión de Series de Perfiles")

        # ✅ CONFIGURACIÓN SIMPLE Y EFECTIVA
        setup_maximized_dialog(self, "Gestión de Series de Perfiles")

        self.serie_actual = None
        self._configurar_ui()
        self._cargar_series()
        
    def _configurar_ui(self):
        """Configura la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)
        layout_principal.setContentsMargins(16, 16, 16, 16)
        layout_principal.setSpacing(16)
        
        # Título
        titulo = QLabel("📚 Gestión de Series de Perfiles")
        titulo.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        titulo.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout_principal.addWidget(titulo)
        
        # Layout horizontal para tabla y formulario
        layout_horizontal = QHBoxLayout()
        
        # Panel izquierdo - Lista de series
        panel_izquierdo = self._crear_panel_lista()
        layout_horizontal.addWidget(panel_izquierdo, 2)
        
        # Panel derecho - Formulario
        panel_derecho = self._crear_panel_formulario()
        layout_horizontal.addWidget(panel_derecho, 1)
        
        layout_principal.addLayout(layout_horizontal)
        
        # Botones de diálogo
        botones = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        botones.rejected.connect(self.accept)
        layout_principal.addWidget(botones)
        
    def _crear_panel_lista(self):
        """Crea el panel de lista de series."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(0, 0, 8, 0)
        
        # Título y botones
        header_layout = QHBoxLayout()
        
        titulo_lista = QLabel("Lista de Series")
        titulo_lista.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        header_layout.addWidget(titulo_lista)
        
        header_layout.addStretch()
        
        self.btn_actualizar = QPushButton("🔄 Actualizar")
        self.btn_actualizar.clicked.connect(self._cargar_series)
        header_layout.addWidget(self.btn_actualizar)
        
        layout.addLayout(header_layout)
        
        # Tabla
        self.tabla_series = QTableWidget()
        self.tabla_series.setColumnCount(4)
        self.tabla_series.setHorizontalHeaderLabels([
            "Código", "Nombre", "Descripción", "Estado"
        ])
        
        # Configurar tabla
        self.tabla_series.setAlternatingRowColors(False)  # Desactivar filas alternadas
        self.tabla_series.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        header = self.tabla_series.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        
        self.tabla_series.itemSelectionChanged.connect(self._on_seleccion_cambiada)
        
        layout.addWidget(self.tabla_series)
        
        return panel
        
    def _crear_panel_formulario(self):
        """Crea el panel del formulario."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(8, 0, 0, 0)
        
        # Título
        titulo_form = QLabel("Datos de la Serie")
        titulo_form.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(titulo_form)
        
        # Formulario
        form_layout = QFormLayout()
        form_layout.setSpacing(12)
        
        # Código
        self.codigo = QLineEdit()
        self.codigo.setMaxLength(20)
        self.codigo.setPlaceholderText("Ej: COR-70")
        form_layout.addRow("Código*:", self.codigo)
        
        # Nombre
        self.nombre = QLineEdit()
        self.nombre.setMaxLength(100)
        self.nombre.setPlaceholderText("Nombre de la serie")
        form_layout.addRow("Nombre*:", self.nombre)
        
        # Descripción
        self.descripcion = QTextEdit()
        self.descripcion.setMaximumHeight(100)
        self.descripcion.setPlaceholderText("Descripción detallada de la serie")
        form_layout.addRow("Descripción:", self.descripcion)
        
        # Activo
        self.activo = QCheckBox("Serie activa")
        self.activo.setChecked(True)
        form_layout.addRow("Estado:", self.activo)
        
        layout.addLayout(form_layout)
        
        # Mensaje de error
        self.label_error = QLabel()
        self.label_error.setStyleSheet("color: #e74c3c; font-weight: bold;")
        self.label_error.setVisible(False)
        layout.addWidget(self.label_error)
        
        # Botones de acción
        botones_layout = QVBoxLayout()
        botones_layout.setSpacing(8)
        
        self.btn_nuevo = QPushButton("➕ Nueva Serie")
        self.btn_nuevo.setStyleSheet(self._get_button_style("#27ae60"))
        self.btn_nuevo.clicked.connect(self._nueva_serie)
        botones_layout.addWidget(self.btn_nuevo)
        
        self.btn_guardar = QPushButton("💾 Guardar")
        self.btn_guardar.setStyleSheet(self._get_button_style("#3498db"))
        self.btn_guardar.setEnabled(False)
        self.btn_guardar.clicked.connect(self._guardar_serie)
        botones_layout.addWidget(self.btn_guardar)
        
        self.btn_eliminar = QPushButton("🗑️ Eliminar")
        self.btn_eliminar.setStyleSheet(self._get_button_style("#e74c3c"))
        self.btn_eliminar.setEnabled(False)
        self.btn_eliminar.clicked.connect(self._eliminar_serie)
        botones_layout.addWidget(self.btn_eliminar)
        
        layout.addLayout(botones_layout)
        layout.addStretch()
        
        return panel
    
    def _get_button_style(self, color):
        """Obtiene el estilo para botones."""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 10px 16px;
                border-radius: 6px;
                font-weight: bold;
                min-height: 35px;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
            }}
            QPushButton:pressed {{
                background-color: {color}aa;
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
        """
    
    def _cargar_series(self):
        """Carga la lista de series."""
        db = next(get_db())
        try:
            series = db.query(SeriePerfil).order_by(SeriePerfil.nombre).all()
            
            self.tabla_series.setRowCount(len(series))
            
            for fila, serie in enumerate(series):
                # Código
                self.tabla_series.setItem(fila, 0, QTableWidgetItem(serie.codigo))
                
                # Nombre
                self.tabla_series.setItem(fila, 1, QTableWidgetItem(serie.nombre))
                
                # Descripción
                descripcion = (serie.descripcion or "")[:50] + "..." if serie.descripcion and len(serie.descripcion) > 50 else (serie.descripcion or "")
                self.tabla_series.setItem(fila, 2, QTableWidgetItem(descripcion))
                
                # Estado
                estado = "✅ Activa" if serie.activo else "❌ Inactiva"
                estado_item = QTableWidgetItem(estado)
                estado_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.tabla_series.setItem(fila, 3, estado_item)
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error cargando series: {str(e)}")
        finally:
            db.close()
    
    def _on_seleccion_cambiada(self):
        """Maneja el cambio de selección."""
        fila = self.tabla_series.currentRow()
        if fila >= 0:
            codigo = self.tabla_series.item(fila, 0).text()
            self._cargar_serie(codigo)
            self.btn_guardar.setEnabled(True)
            self.btn_eliminar.setEnabled(True)
        else:
            self._limpiar_formulario()
            self.btn_guardar.setEnabled(False)
            self.btn_eliminar.setEnabled(False)
    
    def _cargar_serie(self, codigo):
        """Carga una serie en el formulario."""
        db = next(get_db())
        try:
            serie = db.query(SeriePerfil).filter(SeriePerfil.codigo == codigo).first()
            if serie:
                self.serie_actual = serie
                self.codigo.setText(serie.codigo)
                self.codigo.setReadOnly(True)
                self.nombre.setText(serie.nombre)
                self.descripcion.setPlainText(serie.descripcion or "")
                self.activo.setChecked(bool(serie.activo) if serie.activo is not None else True)
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error cargando serie: {str(e)}")
        finally:
            db.close()
    
    def _nueva_serie(self):
        """Prepara el formulario para una nueva serie."""
        self._limpiar_formulario()
        self.serie_actual = None
        self.codigo.setReadOnly(False)
        self.btn_guardar.setEnabled(True)
        self.btn_eliminar.setEnabled(False)
        self.codigo.setFocus()
    
    def _limpiar_formulario(self):
        """Limpia el formulario."""
        self.codigo.clear()
        self.nombre.clear()
        self.descripcion.clear()
        self.activo.setChecked(True)
        self.label_error.setVisible(False)
    
    def _validar_formulario(self):
        """Valida el formulario."""
        if not self.codigo.text().strip():
            self._mostrar_error("El código es obligatorio")
            return False
        
        if not self.nombre.text().strip():
            self._mostrar_error("El nombre es obligatorio")
            return False
        
        self.label_error.setVisible(False)
        return True
    
    def _mostrar_error(self, mensaje):
        """Muestra un mensaje de error."""
        self.label_error.setText(mensaje)
        self.label_error.setVisible(True)
    
    def _guardar_serie(self):
        """Guarda la serie."""
        if not self._validar_formulario():
            return
        
        db = next(get_db())
        try:
            if self.serie_actual:
                # Actualizar existente
                self.serie_actual.nombre = self.nombre.text().strip()
                self.serie_actual.descripcion = self.descripcion.toPlainText().strip() or None
                self.serie_actual.activo = self.activo.isChecked()
            else:
                # Crear nueva
                # Verificar que no exista el código
                existe = db.query(SeriePerfil).filter(SeriePerfil.codigo == self.codigo.text().strip()).first()
                if existe:
                    self._mostrar_error("Ya existe una serie con este código")
                    return
                
                nueva_serie = SeriePerfil(
                    codigo=self.codigo.text().strip(),
                    nombre=self.nombre.text().strip(),
                    descripcion=self.descripcion.toPlainText().strip() or None,
                    activo=self.activo.isChecked(),
                    fecha_creacion=datetime.now()
                )
                db.add(nueva_serie)
            
            db.commit()
            QMessageBox.information(self, "Éxito", "Serie guardada correctamente")
            self._cargar_series()
            
        except Exception as e:
            db.rollback()
            QMessageBox.critical(self, "Error", f"Error guardando serie: {str(e)}")
        finally:
            db.close()
    
    def _eliminar_serie(self):
        """Elimina la serie actual."""
        if not self.serie_actual:
            return
        
        respuesta = QMessageBox.question(
            self,
            "Confirmar eliminación",
            f"¿Está seguro de eliminar la serie '{self.serie_actual.nombre}'?\n"
            "Esta acción no se puede deshacer.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if respuesta == QMessageBox.StandardButton.Yes:
            db = next(get_db())
            try:
                db.delete(self.serie_actual)
                db.commit()
                QMessageBox.information(self, "Éxito", "Serie eliminada correctamente")
                self._cargar_series()
                self._limpiar_formulario()
                self.serie_actual = None
                self.btn_guardar.setEnabled(False)
                self.btn_eliminar.setEnabled(False)
                
            except Exception as e:
                db.rollback()
                QMessageBox.critical(self, "Error", f"Error eliminando serie: {str(e)}")
            finally:
                db.close()
