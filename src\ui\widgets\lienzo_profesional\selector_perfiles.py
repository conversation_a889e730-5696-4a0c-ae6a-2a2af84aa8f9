"""
Selector profesional de perfiles de la base de datos.
Permite filtrar y seleccionar perfiles específicos según el tipo de elemento.
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QComboBox,
    QPushButton, QTableWidget, QTableWidgetItem, QGroupBox, QFormLayout,
    QTextEdit, QDialogButtonBox, QHeaderView, QMessageBox, QSplitter
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from models.base import get_db
from models.perfil import Perfil, TipoPerfil, Distribuidor, SeriePerfil
from ui.utils.window_utils import setup_maximized_dialog


class SelectorPerfilesProfesional(QDialog):
    """
    Selector profesional de perfiles de la base de datos.
    Filtra perfiles según el tipo de elemento y permite selección detallada.
    """
    
    def __init__(self, tipo_elemento, parent=None):
        super().__init__(parent)
        self.tipo_elemento = tipo_elemento
        self.perfil_seleccionado = None

        self.setModal(True)

        # ✅ CONFIGURACIÓN SIMPLE Y EFECTIVA
        setup_maximized_dialog(self, "🔍 Selector Profesional de Perfiles")

        self._crear_interfaz()
        self._cargar_filtros()
        self._cargar_perfiles()
        self._conectar_señales()
    
    def _crear_interfaz(self):
        """Crea la interfaz del selector."""
        layout = QVBoxLayout(self)
        
        # Título
        titulo = QLabel(f"🔍 Seleccionar Perfil para: {self._obtener_nombre_tipo()}")
        titulo.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        titulo.setStyleSheet("color: #0078d4; margin: 10px;")
        layout.addWidget(titulo)
        
        # Splitter principal
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # Panel izquierdo: Filtros
        self._crear_panel_filtros(splitter)
        
        # Panel derecho: Lista de perfiles y detalles
        self._crear_panel_perfiles(splitter)
        
        # Configurar proporciones del splitter
        splitter.setSizes([300, 700])
        
        # Botones
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        botones.accepted.connect(self._aceptar_seleccion)
        botones.rejected.connect(self.reject)
        layout.addWidget(botones)
        
        # Inicialmente deshabilitar OK
        botones.button(QDialogButtonBox.StandardButton.Ok).setEnabled(False)
        self.btn_ok = botones.button(QDialogButtonBox.StandardButton.Ok)
    
    def _crear_panel_filtros(self, parent):
        """Crea el panel de filtros."""
        widget_filtros = QGroupBox("🔧 Filtros de Búsqueda")
        layout = QVBoxLayout(widget_filtros)
        
        # Filtros básicos
        form_layout = QFormLayout()
        
        # Búsqueda por texto
        self.line_busqueda = QLineEdit()
        self.line_busqueda.setPlaceholderText("Buscar por código o descripción...")
        form_layout.addRow("🔍 Búsqueda:", self.line_busqueda)
        
        # Filtro por tipo
        self.combo_tipo = QComboBox()
        form_layout.addRow("📋 Tipo:", self.combo_tipo)
        
        # Filtro por material
        self.combo_material = QComboBox()
        self.combo_material.addItems(["Todos", "PVC", "Aluminio", "Madera", "Acero"])
        form_layout.addRow("🔬 Material:", self.combo_material)
        
        # Filtro por distribuidor
        self.combo_distribuidor = QComboBox()
        form_layout.addRow("🏢 Distribuidor:", self.combo_distribuidor)
        
        # Filtro por serie
        self.combo_serie = QComboBox()
        form_layout.addRow("📦 Serie:", self.combo_serie)
        
        layout.addLayout(form_layout)
        
        # Botón de limpiar filtros
        btn_limpiar = QPushButton("🗑️ Limpiar Filtros")
        btn_limpiar.clicked.connect(self._limpiar_filtros)
        layout.addWidget(btn_limpiar)
        
        # Información de filtros aplicados
        self.label_resultados = QLabel("Resultados: 0 perfiles")
        self.label_resultados.setStyleSheet("font-weight: bold; color: #666;")
        layout.addWidget(self.label_resultados)
        
        parent.addWidget(widget_filtros)
    
    def _crear_panel_perfiles(self, parent):
        """Crea el panel de perfiles y detalles."""
        widget_perfiles = QGroupBox("📋 Perfiles Disponibles")
        layout = QVBoxLayout(widget_perfiles)
        
        # Tabla de perfiles
        self.tabla_perfiles = QTableWidget()
        self.tabla_perfiles.setColumnCount(6)
        self.tabla_perfiles.setHorizontalHeaderLabels([
            "Código", "Descripción", "Material", "Dimensiones", "Precio €/m", "Estado"
        ])
        
        # Configurar tabla
        header = self.tabla_perfiles.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        
        self.tabla_perfiles.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.tabla_perfiles.setAlternatingRowColors(True)
        
        layout.addWidget(self.tabla_perfiles)
        
        # Panel de detalles del perfil seleccionado
        grupo_detalles = QGroupBox("📄 Detalles del Perfil Seleccionado")
        layout_detalles = QVBoxLayout(grupo_detalles)
        
        self.text_detalles = QTextEdit()
        self.text_detalles.setMaximumHeight(150)
        self.text_detalles.setReadOnly(True)
        layout_detalles.addWidget(self.text_detalles)
        
        layout.addWidget(grupo_detalles)
        
        parent.addWidget(widget_perfiles)
    
    def _obtener_nombre_tipo(self):
        """Obtiene el nombre descriptivo del tipo de elemento."""
        nombres = {
            'marco_superior': 'Marco Superior',
            'marco_inferior': 'Marco Inferior', 
            'marco_izquierdo': 'Marco Lateral Izquierdo',
            'marco_derecho': 'Marco Lateral Derecho',
            'division_v': 'División Vertical / Travesaño Vertical',
            'division_h': 'División Horizontal / Travesaño Horizontal',
            'hoja': 'Hoja / Parte Móvil'
        }
        return nombres.get(self.tipo_elemento, 'Perfil')
    
    def _cargar_filtros(self):
        """Carga los datos para los filtros."""
        db = next(get_db())
        
        try:
            # Cargar tipos
            tipos = db.query(TipoPerfil).filter(TipoPerfil.activo == True).all()
            self.combo_tipo.addItem("Todos", None)
            for tipo in tipos:
                self.combo_tipo.addItem(f"{tipo.nombre} ({tipo.categoria})", tipo.id)
            
            # Filtrar tipos relevantes según el elemento
            self._filtrar_tipos_relevantes()
            
            # Cargar distribuidores
            distribuidores = db.query(Distribuidor).filter(Distribuidor.activo == True).all()
            self.combo_distribuidor.addItem("Todos", None)
            for dist in distribuidores:
                self.combo_distribuidor.addItem(dist.nombre, dist.id)
            
            # Cargar series
            series = db.query(SeriePerfil).filter(SeriePerfil.activo == True).all()
            self.combo_serie.addItem("Todas", None)
            for serie in series:
                self.combo_serie.addItem(serie.nombre, serie.id)
                
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Error al cargar filtros: {str(e)}")
        finally:
            db.close()
    
    def _filtrar_tipos_relevantes(self):
        """Filtra los tipos relevantes según el elemento."""
        # Mapeo de elementos a categorías de tipos relevantes
        categorias_relevantes = {
            'marco_superior': ['marco', 'jamba'],
            'marco_inferior': ['marco', 'jamba'],
            'marco_izquierdo': ['marco', 'jamba'],
            'marco_derecho': ['marco', 'jamba'],
            'division_v': ['travesaño', 'inversor', 'union'],
            'division_h': ['travesaño', 'inversor', 'union'],
            'hoja': ['hoja', 'inversor']
        }
        
        categorias = categorias_relevantes.get(self.tipo_elemento, [])
        if categorias:
            # Filtrar combo_tipo para mostrar solo tipos relevantes
            for i in range(self.combo_tipo.count() - 1, 0, -1):  # Empezar desde el final, saltar "Todos"
                item_text = self.combo_tipo.itemText(i)
                # Verificar si alguna categoría relevante está en el texto
                es_relevante = any(cat in item_text.lower() for cat in categorias)
                if not es_relevante:
                    self.combo_tipo.removeItem(i)
    
    def _cargar_perfiles(self):
        """Carga los perfiles en la tabla aplicando filtros."""
        db = next(get_db())
        
        try:
            # Query base
            query = db.query(Perfil).filter(Perfil.activo == True)
            
            # Aplicar filtros
            texto_busqueda = self.line_busqueda.text().strip()
            if texto_busqueda:
                query = query.filter(
                    (Perfil.codigo.ilike(f"%{texto_busqueda}%")) |
                    (Perfil.descripcion.ilike(f"%{texto_busqueda}%"))
                )
            
            tipo_id = self.combo_tipo.currentData()
            if tipo_id:
                query = query.filter(Perfil.tipo_id == tipo_id)
            
            material = self.combo_material.currentText()
            if material != "Todos":
                query = query.filter(Perfil.material == material)
            
            distribuidor_id = self.combo_distribuidor.currentData()
            if distribuidor_id:
                query = query.filter(Perfil.distribuidor_id == distribuidor_id)
            
            serie_id = self.combo_serie.currentData()
            if serie_id:
                query = query.filter(Perfil.serie_id == serie_id)
            
            # Obtener resultados
            perfiles = query.all()
            
            # Llenar tabla
            self.tabla_perfiles.setRowCount(len(perfiles))
            
            for row, perfil in enumerate(perfiles):
                # Código
                self.tabla_perfiles.setItem(row, 0, QTableWidgetItem(perfil.codigo))
                
                # Descripción
                self.tabla_perfiles.setItem(row, 1, QTableWidgetItem(perfil.descripcion))
                
                # Material
                material_text = perfil.material or "N/A"
                self.tabla_perfiles.setItem(row, 2, QTableWidgetItem(material_text))
                
                # Dimensiones
                dimensiones = "N/A"
                if perfil.ancho and perfil.alto:
                    dimensiones = f"{perfil.ancho}x{perfil.alto} mm"
                elif perfil.ancho:
                    dimensiones = f"{perfil.ancho} mm"
                self.tabla_perfiles.setItem(row, 3, QTableWidgetItem(dimensiones))
                
                # Precio
                precio_text = f"{perfil.precio_metro:.2f}" if perfil.precio_metro else "N/A"
                self.tabla_perfiles.setItem(row, 4, QTableWidgetItem(precio_text))
                
                # Estado
                estado = "Activo" if perfil.activo else "Inactivo"
                self.tabla_perfiles.setItem(row, 5, QTableWidgetItem(estado))
                
                # Guardar referencia al perfil
                self.tabla_perfiles.item(row, 0).setData(Qt.ItemDataRole.UserRole, perfil)
            
            # Actualizar contador
            self.label_resultados.setText(f"Resultados: {len(perfiles)} perfiles")
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error al cargar perfiles: {str(e)}")
        finally:
            db.close()
    
    def _conectar_señales(self):
        """Conecta las señales de los controles."""
        # Filtros
        self.line_busqueda.textChanged.connect(self._cargar_perfiles)
        self.combo_tipo.currentTextChanged.connect(self._cargar_perfiles)
        self.combo_material.currentTextChanged.connect(self._cargar_perfiles)
        self.combo_distribuidor.currentTextChanged.connect(self._cargar_perfiles)
        self.combo_serie.currentTextChanged.connect(self._cargar_perfiles)
        
        # Tabla
        self.tabla_perfiles.itemSelectionChanged.connect(self._seleccion_cambiada)
        self.tabla_perfiles.itemDoubleClicked.connect(self._aceptar_seleccion)
    
    def _limpiar_filtros(self):
        """Limpia todos los filtros."""
        self.line_busqueda.clear()
        self.combo_tipo.setCurrentIndex(0)
        self.combo_material.setCurrentIndex(0)
        self.combo_distribuidor.setCurrentIndex(0)
        self.combo_serie.setCurrentIndex(0)
    
    def _seleccion_cambiada(self):
        """Maneja el cambio de selección en la tabla."""
        fila_actual = self.tabla_perfiles.currentRow()
        
        if fila_actual >= 0:
            item = self.tabla_perfiles.item(fila_actual, 0)
            perfil = item.data(Qt.ItemDataRole.UserRole)
            
            if perfil:
                self._mostrar_detalles_perfil(perfil)
                self.btn_ok.setEnabled(True)
                self.perfil_seleccionado = perfil
        else:
            self.text_detalles.clear()
            self.btn_ok.setEnabled(False)
            self.perfil_seleccionado = None
    
    def _mostrar_detalles_perfil(self, perfil):
        """Muestra los detalles del perfil seleccionado."""
        # Obtener información relacionada de forma segura
        try:
            db = get_db()
            # Recargar el perfil con sus relaciones
            from sqlalchemy.orm import joinedload
            perfil_completo = db.query(Perfil).options(
                joinedload(Perfil.distribuidor),
                joinedload(Perfil.serie),
                joinedload(Perfil.tipo)
            ).filter(Perfil.id == perfil.id).first()

            if perfil_completo:
                # Obtener nombres de forma segura
                distribuidor_nombre = perfil_completo.distribuidor.nombre if perfil_completo.distribuidor else 'N/A'
                serie_nombre = perfil_completo.serie.nombre if perfil_completo.serie else 'N/A'
                tipo_nombre = perfil_completo.tipo.nombre if perfil_completo.tipo else 'N/A'
            else:
                distribuidor_nombre = 'N/A'
                serie_nombre = 'N/A'
                tipo_nombre = 'N/A'
                perfil_completo = perfil

        except Exception as e:
            # Si hay error, usar información básica sin relaciones
            distribuidor_nombre = 'N/A'
            serie_nombre = 'N/A'
            tipo_nombre = 'N/A'
            perfil_completo = perfil
        finally:
            if 'db' in locals():
                db.close()

        detalles = f"""
<h3>📋 {perfil_completo.codigo} - {perfil_completo.descripcion}</h3>

<b>🏢 Información General:</b><br>
• Distribuidor: {distribuidor_nombre}<br>
• Serie: {serie_nombre}<br>
• Tipo: {tipo_nombre}<br>

<b>🔬 Características Técnicas:</b><br>
• Material: {perfil_completo.material or 'N/A'}<br>
• Color: {perfil_completo.color or 'N/A'}<br>
• Acabado: {perfil_completo.acabado or 'N/A'}<br>
• Dimensiones: {perfil_completo.ancho or 'N/A'} x {perfil_completo.alto or 'N/A'} mm<br>
• Espesor: {perfil_completo.espesor or 'N/A'} mm<br>
• Peso: {perfil_completo.peso_metro or 'N/A'} kg/m<br>

<b>💰 Información Comercial:</b><br>
• Precio: €{perfil_completo.precio_metro or 0:.2f}/m<br>
• Referencia: {perfil_completo.referencia or 'N/A'}<br>
• Código de barras: {perfil_completo.codigo_barras or 'N/A'}<br>

<b>📝 Observaciones:</b><br>
{perfil_completo.observaciones or 'Sin observaciones'}
        """

        self.text_detalles.setHtml(detalles)
    
    def _aceptar_seleccion(self):
        """Acepta la selección actual."""
        if self.perfil_seleccionado:
            self.accept()
        else:
            QMessageBox.warning(self, "Advertencia", "Por favor, seleccione un perfil.")
    
    def obtener_perfil_seleccionado(self):
        """Obtiene el perfil seleccionado."""
        return self.perfil_seleccionado
