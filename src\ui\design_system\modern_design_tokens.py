"""
Sistema de Design Tokens moderno basado en Material Design 3 y mejores prácticas 2024.
Inspirado en Figma, AutoCAD, y sistemas de diseño modernos.
"""

from dataclasses import dataclass
from typing import Dict, Any
from enum import Enum

class ColorScheme(Enum):
    """Esquemas de color modernos."""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"

class ComponentSize(Enum):
    """Tamaños estándar de componentes."""
    XS = "xs"    # 24px
    SM = "sm"    # 32px
    MD = "md"    # 40px
    LG = "lg"    # 48px
    XL = "xl"    # 56px

@dataclass
class ColorTokens:
    """Tokens de color modernos con soporte para temas."""
    
    # Colores primarios (basados en Material Design 3)
    primary: str = "#1976d2"
    primary_variant: str = "#1565c0"
    primary_container: str = "#e3f2fd"
    on_primary: str = "#ffffff"
    on_primary_container: str = "#0d47a1"
    
    # Colores secundarios
    secondary: str = "#03dac6"
    secondary_variant: str = "#018786"
    secondary_container: str = "#e0f2f1"
    on_secondary: str = "#000000"
    on_secondary_container: str = "#004d40"
    
    # Colores de superficie
    surface: str = "#ffffff"
    surface_variant: str = "#f5f5f5"
    surface_container: str = "#fafafa"
    surface_container_high: str = "#f0f0f0"
    on_surface: str = "#1c1b1f"
    on_surface_variant: str = "#49454f"
    
    # Colores de estado
    error: str = "#ba1a1a"
    error_container: str = "#ffdad6"
    on_error: str = "#ffffff"
    on_error_container: str = "#410002"
    
    warning: str = "#ff9800"
    warning_container: str = "#fff3e0"
    on_warning: str = "#000000"
    on_warning_container: str = "#e65100"
    
    success: str = "#4caf50"
    success_container: str = "#e8f5e8"
    on_success: str = "#ffffff"
    on_success_container: str = "#1b5e20"
    
    # Colores de contorno
    outline: str = "#79747e"
    outline_variant: str = "#cac4d0"
    
    # Colores específicos para CAD/Técnico
    technical_blue: str = "#2196f3"
    technical_green: str = "#4caf50"
    technical_orange: str = "#ff9800"
    technical_red: str = "#f44336"
    technical_purple: str = "#9c27b0"
    
    # Colores de grid y guías
    grid_major: str = "#e0e0e0"
    grid_minor: str = "#f5f5f5"
    guide_line: str = "#2196f3"
    snap_point: str = "#ff5722"

@dataclass
class TypographyTokens:
    """Tokens de tipografía modernos."""
    
    # Familias de fuentes
    font_family_primary: str = "'Inter', 'Segoe UI', system-ui, sans-serif"
    font_family_monospace: str = "'JetBrains Mono', 'Consolas', monospace"
    font_family_technical: str = "'Roboto Mono', 'Courier New', monospace"
    
    # Tamaños de fuente (escala modular)
    font_size_xs: str = "0.75rem"    # 12px
    font_size_sm: str = "0.875rem"   # 14px
    font_size_md: str = "1rem"       # 16px
    font_size_lg: str = "1.125rem"   # 18px
    font_size_xl: str = "1.25rem"    # 20px
    font_size_2xl: str = "1.5rem"    # 24px
    font_size_3xl: str = "1.875rem"  # 30px
    
    # Pesos de fuente
    font_weight_light: int = 300
    font_weight_regular: int = 400
    font_weight_medium: int = 500
    font_weight_semibold: int = 600
    font_weight_bold: int = 700
    
    # Alturas de línea
    line_height_tight: float = 1.25
    line_height_normal: float = 1.5
    line_height_relaxed: float = 1.75

@dataclass
class SpacingTokens:
    """Tokens de espaciado basados en sistema de 8px."""
    
    # Espaciado base (8px system)
    space_0: str = "0"
    space_1: str = "0.25rem"   # 4px
    space_2: str = "0.5rem"    # 8px
    space_3: str = "0.75rem"   # 12px
    space_4: str = "1rem"      # 16px
    space_5: str = "1.25rem"   # 20px
    space_6: str = "1.5rem"    # 24px
    space_8: str = "2rem"      # 32px
    space_10: str = "2.5rem"   # 40px
    space_12: str = "3rem"     # 48px
    space_16: str = "4rem"     # 64px
    space_20: str = "5rem"     # 80px
    
    # Espaciado semántico
    component_padding: str = "1rem"
    section_margin: str = "1.5rem"
    page_margin: str = "2rem"

@dataclass
class BorderTokens:
    """Tokens de bordes y radios."""
    
    # Anchos de borde
    border_width_none: str = "0"
    border_width_thin: str = "1px"
    border_width_medium: str = "2px"
    border_width_thick: str = "4px"
    
    # Radios de borde (Material Design 3)
    border_radius_none: str = "0"
    border_radius_xs: str = "4px"
    border_radius_sm: str = "8px"
    border_radius_md: str = "12px"
    border_radius_lg: str = "16px"
    border_radius_xl: str = "24px"
    border_radius_full: str = "9999px"

@dataclass
class ShadowTokens:
    """Tokens de sombras (Material Design 3)."""
    
    shadow_none: str = "none"
    shadow_sm: str = "0 1px 2px 0 rgb(0 0 0 / 0.05)"
    shadow_md: str = "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)"
    shadow_lg: str = "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)"
    shadow_xl: str = "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)"
    
    # Sombras específicas para elevación
    elevation_1: str = "0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)"
    elevation_2: str = "0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)"
    elevation_3: str = "0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23)"

class ModernDesignSystem:
    """Sistema de diseño moderno unificado."""
    
    def __init__(self, scheme: ColorScheme = ColorScheme.LIGHT):
        self.scheme = scheme
        self.colors = self._get_color_tokens()
        self.typography = TypographyTokens()
        self.spacing = SpacingTokens()
        self.borders = BorderTokens()
        self.shadows = ShadowTokens()
    
    def _get_color_tokens(self) -> ColorTokens:
        """Obtiene tokens de color según el esquema."""
        if self.scheme == ColorScheme.DARK:
            return self._get_dark_colors()
        return ColorTokens()  # Light por defecto
    
    def _get_dark_colors(self) -> ColorTokens:
        """Tokens de color para tema oscuro."""
        return ColorTokens(
            # Colores primarios para tema oscuro
            primary="#90caf9",
            primary_variant="#64b5f6",
            primary_container="#1565c0",
            on_primary="#000000",
            on_primary_container="#e3f2fd",
            
            # Superficies oscuras
            surface="#121212",
            surface_variant="#1e1e1e",
            surface_container="#2d2d2d",
            surface_container_high="#383838",
            on_surface="#ffffff",
            on_surface_variant="#e0e0e0",
            
            # Contornos para tema oscuro
            outline="#938f99",
            outline_variant="#49454f",
            
            # Grid para tema oscuro
            grid_major="#424242",
            grid_minor="#303030",
        )
    
    def get_component_style(self, component_type: str, size: ComponentSize = ComponentSize.MD) -> Dict[str, Any]:
        """Genera estilos para componentes específicos."""
        base_styles = {
            "font-family": self.typography.font_family_primary,
            "border-radius": self.borders.border_radius_sm,
            "transition": "all 0.2s ease-in-out",
        }
        
        size_map = {
            ComponentSize.XS: {"height": "24px", "padding": self.spacing.space_2},
            ComponentSize.SM: {"height": "32px", "padding": self.spacing.space_3},
            ComponentSize.MD: {"height": "40px", "padding": self.spacing.space_4},
            ComponentSize.LG: {"height": "48px", "padding": self.spacing.space_5},
            ComponentSize.XL: {"height": "56px", "padding": self.spacing.space_6},
        }
        
        component_styles = {
            "button_primary": {
                "background-color": self.colors.primary,
                "color": self.colors.on_primary,
                "border": "none",
                "box-shadow": self.shadows.elevation_1,
                "font-weight": self.typography.font_weight_medium,
            },
            "button_secondary": {
                "background-color": self.colors.secondary_container,
                "color": self.colors.on_secondary_container,
                "border": f"{self.borders.border_width_thin} solid {self.colors.outline}",
            },
            "input": {
                "background-color": self.colors.surface_container,
                "color": self.colors.on_surface,
                "border": f"{self.borders.border_width_thin} solid {self.colors.outline_variant}",
            },
            "card": {
                "background-color": self.colors.surface_container,
                "color": self.colors.on_surface,
                "border-radius": self.borders.border_radius_lg,
                "box-shadow": self.shadows.elevation_1,
                "padding": self.spacing.space_6,
            },
            "dialog": {
                "background-color": self.colors.surface,
                "color": self.colors.on_surface,
                "border-radius": self.borders.border_radius_xl,
                "box-shadow": self.shadows.elevation_3,
            }
        }
        
        style = {**base_styles, **size_map[size]}
        if component_type in component_styles:
            style.update(component_styles[component_type])
        
        return style
    
    def to_qss(self) -> str:
        """Convierte el sistema de diseño a QSS (Qt Style Sheets)."""
        return f"""
        /* Sistema de Diseño Moderno - QSS */
        
        QDialog {{
            background-color: {self.colors.surface};
            color: {self.colors.on_surface};
            font-family: {self.typography.font_family_primary};
            font-size: {self.typography.font_size_md};
        }}
        
        QPushButton {{
            background-color: {self.colors.primary};
            color: {self.colors.on_primary};
            border: none;
            border-radius: {self.borders.border_radius_sm};
            padding: {self.spacing.space_3} {self.spacing.space_4};
            font-weight: {self.typography.font_weight_medium};
            min-height: 32px;
        }}
        
        QPushButton:hover {{
            background-color: {self.colors.primary_variant};
        }}
        
        QPushButton:pressed {{
            background-color: {self.colors.primary_container};
        }}
        
        QComboBox, QSpinBox, QDoubleSpinBox, QLineEdit {{
            background-color: {self.colors.surface_container};
            color: {self.colors.on_surface};
            border: {self.borders.border_width_thin} solid {self.colors.outline_variant};
            border-radius: {self.borders.border_radius_xs};
            padding: {self.spacing.space_2} {self.spacing.space_3};
            min-height: 32px;
        }}
        
        QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QLineEdit:focus {{
            border-color: {self.colors.primary};
            outline: {self.borders.border_width_medium} solid {self.colors.primary_container};
        }}
        
        QGroupBox {{
            font-weight: {self.typography.font_weight_semibold};
            border: {self.borders.border_width_medium} solid {self.colors.outline_variant};
            border-radius: {self.borders.border_radius_md};
            margin-top: {self.spacing.space_4};
            padding-top: {self.spacing.space_4};
            background-color: {self.colors.surface_container};
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: {self.spacing.space_4};
            padding: 0 {self.spacing.space_2} 0 {self.spacing.space_2};
            color: {self.colors.on_surface};
            background-color: {self.colors.surface_container};
        }}
        
        QTabWidget::pane {{
            border: {self.borders.border_width_thin} solid {self.colors.outline_variant};
            border-radius: {self.borders.border_radius_md};
            background-color: {self.colors.surface_container};
        }}
        
        QTabBar::tab {{
            background-color: {self.colors.surface_variant};
            color: {self.colors.on_surface_variant};
            border: {self.borders.border_width_thin} solid {self.colors.outline_variant};
            padding: {self.spacing.space_3} {self.spacing.space_5};
            margin-right: {self.spacing.space_1};
            border-top-left-radius: {self.borders.border_radius_sm};
            border-top-right-radius: {self.borders.border_radius_sm};
        }}
        
        QTabBar::tab:selected {{
            background-color: {self.colors.surface_container};
            color: {self.colors.on_surface};
            border-bottom-color: {self.colors.surface_container};
        }}
        
        QListWidget {{
            background-color: {self.colors.surface_container};
            color: {self.colors.on_surface};
            border: {self.borders.border_width_thin} solid {self.colors.outline_variant};
            border-radius: {self.borders.border_radius_sm};
            padding: {self.spacing.space_2};
        }}
        
        QScrollArea {{
            background-color: {self.colors.surface_container};
            border: {self.borders.border_width_thin} solid {self.colors.outline_variant};
            border-radius: {self.borders.border_radius_sm};
        }}
        """

# Instancia global del sistema de diseño
design_system = ModernDesignSystem()
