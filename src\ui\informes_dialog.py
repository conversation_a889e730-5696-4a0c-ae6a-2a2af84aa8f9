"""
<PERSON><PERSON><PERSON><PERSON> que define el diálogo para gestionar informes.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox, QToolBar,
    QWidget, QFileDialog, QTextEdit, QComboBox, QFormLayout, QDialogButtonBox,
    QTabWidget, QCheckBox, QGroupBox, QSpinBox, QDateEdit, QTimeEdit, QStyle
)

import os
import sys
from pathlib import Path

# Asegurarse de que el directorio raíz esté en el PYTHONPATH
root_dir = str(Path(__file__).parent.parent.parent.absolute())
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QTextCursor

from models.base import get_db
from models.informe import Informe, LineaInforme
from ui.utils.window_utils import setup_maximized_dialog

class EditorInforme(QWidget):
    """Widget para editar el contenido de un informe."""
    
    def __init__(self, informe=None, parent=None):
        """
        Inicializa el editor de informes.
        
        Args:
            informe: Instancia de Informe a editar, o None para crear uno nuevo
            parent: Widget padre
        """
        super().__init__(parent)
        self.informe = informe
        self._configurar_ui()
    
    def _configurar_ui(self):
        """Configura la interfaz de usuario del editor."""
        layout = QVBoxLayout(self)
        
        # Formulario de propiedades del informe
        formulario = QFormLayout()
        
        # Código del informe
        self.campo_codigo = QLineEdit()
        self.campo_codigo.setPlaceholderText("Código único del informe")
        
        # Descripción
        self.campo_descripcion = QLineEdit()
        self.campo_descripcion.setPlaceholderText("Descripción del informe")
        
        formulario.addRow("Código:", self.campo_codigo)
        formulario.addRow("Descripción:", self.campo_descripcion)
        
        # Editor de texto para el contenido del informe
        self.editor = QTextEdit()
        self.editor.setPlaceholderText("Escriba aquí el contenido del informe...")
        
        # Barra de herramientas para el editor
        barra_herramientas = QHBoxLayout()
        
        self.boton_guardar = QPushButton("Guardar")
        self.boton_guardar.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_DialogSaveButton))
        
        self.boton_limpiar = QPushButton("Limpiar")
        self.boton_limpiar.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_DialogResetButton))
        
        barra_herramientas.addWidget(self.boton_guardar)
        barra_herramientas.addWidget(self.boton_limpiar)
        barra_herramientas.addStretch()
        
        # Añadir todo al layout principal
        layout.addLayout(formulario)
        layout.addLayout(barra_herramientas)
        layout.addWidget(QLabel("Contenido del informe:"))
        layout.addWidget(self.editor)
        
        # Conectar señales
        self.boton_guardar.clicked.connect(self._on_guardar)
        self.boton_limpiar.clicked.connect(self._on_limpiar)
        
        # Si se está editando un informe, cargar sus datos
        if self.informe:
            self._cargar_datos_informe()
    
    def _cargar_datos_informe(self):
        """Carga los datos del informe en el formulario."""
        if not self.informe:
            return
        
        self.campo_codigo.setText(self.informe.codigo)
        self.campo_descripcion.setText(self.informe.descripcion or "")
        
        # Cargar las líneas del informe
        db = next(get_db())
        try:
            lineas = LineaInforme.obtener_por_informe(db, self.informe.id)
            contenido = "\n".join(linea.texto or "" for linea in lineas)
            self.editor.setPlainText(contenido)
        finally:
            db.close()
    
    def _on_guardar(self):
        """Guarda el informe."""
        # Validar campos obligatorios
        codigo = self.campo_codigo.text().strip()
        if not codigo:
            QMessageBox.warning(
                self,
                "Campo requerido",
                "El código del informe es obligatorio.",
                QMessageBox.StandardButton.Ok
            )
            self.campo_codigo.setFocus()
            return False
        
        # Obtener los datos del formulario
        datos = {
            'codigo': codigo,
            'descripcion': self.campo_descripcion.text().strip(),
            'contenido': self.editor.toPlainText()
        }
        
        # Guardar en la base de datos
        db = next(get_db())
        try:
            if self.informe:
                # Actualizar informe existente
                informe_actualizado = db.query(Informe).filter(Informe.id == self.informe.id).first()
                if not informe_actualizado:
                    raise ValueError("El informe ya no existe.")
                
                informe_actualizado.codigo = datos['codigo']
                informe_actualizado.descripcion = datos['descripcion'] or None
                
                # Eliminar líneas existentes
                db.query(LineaInforme).filter(LineaInforme.id_informe == informe_actualizado.id).delete()
                
                # Añadir nuevas líneas
                self._guardar_lineas(db, informe_actualizado.id, datos['contenido'])
                
                db.commit()
                
                QMessageBox.information(
                    self,
                    "Informe actualizado",
                    f"El informe '{datos['codigo']}' ha sido actualizado correctamente.",
                    QMessageBox.StandardButton.Ok
                )
                
                return True
            else:
                # Crear nuevo informe
                nuevo_informe = Informe(
                    codigo=datos['codigo'],
                    descripcion=datos['descripcion'] or None
                )
                
                db.add(nuevo_informe)
                db.flush()  # Para obtener el ID del nuevo informe
                
                # Añadir líneas
                self._guardar_lineas(db, nuevo_informe.id, datos['contenido'])
                
                db.commit()
                
                QMessageBox.information(
                    self,
                    "Informe creado",
                    f"El informe '{datos['codigo']}' ha sido creado correctamente.",
                    QMessageBox.StandardButton.Ok
                )
                
                return True
                
        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudo guardar el informe: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
            return False
        finally:
            db.close()
    
    def _guardar_lineas(self, db, id_informe, contenido):
        """
        Guarda las líneas del informe en la base de datos.
        
        Args:
            db: Sesión de base de datos
            id_informe: ID del informe al que pertenecen las líneas
            contenido: Texto del informe (se dividirá en líneas)
        """
        lineas = contenido.split('\n')
        
        for numero, texto in enumerate(lineas, 1):
            linea = LineaInforme(
                id_informe=id_informe,
                linea=numero,
                texto=texto.strip(),
                tipo="TEXTO"  # Tipo por defecto
            )
            db.add(linea)
    
    def _on_limpiar(self):
        """Limpia el editor de informes."""
        if not self.informe:
            self.campo_codigo.clear()
            self.campo_descripcion.clear()
        self.editor.clear()

class InformesDialog(QDialog):
    """Diálogo para gestionar informes del sistema."""
    
    def __init__(self, parent=None):
        """Inicializa el diálogo de gestión de informes."""
        super().__init__(parent)
        self.setWindowTitle("Gestión de Informes")

        # ✅ CONFIGURACIÓN SIMPLE Y EFECTIVA
        setup_maximized_dialog(self, "Gestión de Informes")
        
        # Configurar la interfaz de usuario
        self._configurar_ui()
        
        # Cargar la lista de informes
        self._cargar_informes()
    
    def _configurar_ui(self):
        """Configura los elementos de la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)
        
        # Botones superiores
        layout_botones = QHBoxLayout()
        
        self.boton_nuevo = QPushButton("Nuevo Informe")
        self.boton_editar = QPushButton("Editar Informe")
        self.boton_eliminar = QPushButton("Eliminar Informe")
        self.boton_exportar_pdf = QPushButton("Exportar a PDF")
        self.boton_exportar_csv = QPushButton("Exportar a CSV")
        self.boton_actualizar = QPushButton("Actualizar Lista")
        
        # Deshabilitar botones hasta que se seleccione un informe
        self.boton_editar.setEnabled(False)
        self.boton_eliminar.setEnabled(False)
        self.boton_exportar_pdf.setEnabled(False)
        self.boton_exportar_csv.setEnabled(False)
        
        layout_botones.addWidget(self.boton_nuevo)
        layout_botones.addWidget(self.boton_editar)
        layout_botones.addWidget(self.boton_eliminar)
        
        # Separador
        layout_botones.addStretch()
        
        # Botones de exportación
        layout_botones.addWidget(QLabel("Exportar a:"))
        layout_botones.addWidget(self.boton_exportar_pdf)
        layout_botones.addWidget(self.boton_exportar_csv)
        
        # Botón de actualización
        layout_botones.addStretch()
        layout_botones.addWidget(self.boton_actualizar)
        
        # Tabla de informes
        self.tabla_informes = QTableWidget()
        self.tabla_informes.setColumnCount(3)
        self.tabla_informes.setHorizontalHeaderLabels(["Código", "Descripción", "Líneas"])
        self.tabla_informes.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.tabla_informes.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.tabla_informes.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        
        # Conectar señales
        self.tabla_informes.itemSelectionChanged.connect(self._on_seleccion_cambiada)
        self.tabla_informes.itemDoubleClicked.connect(self._on_editar_informe)
        self.boton_nuevo.clicked.connect(self._on_nuevo_informe)
        self.boton_editar.clicked.connect(self._on_editar_informe)
        self.boton_eliminar.clicked.connect(self._on_eliminar_informe)
        self.boton_exportar_pdf.clicked.connect(lambda: self._on_exportar_informe('pdf'))
        self.boton_exportar_csv.clicked.connect(lambda: self._on_exportar_informe('csv'))
        self.boton_actualizar.clicked.connect(self._cargar_informes)
        
        # Añadir widgets al layout principal
        layout_principal.addLayout(layout_botones)
        layout_principal.addWidget(self.tabla_informes)
        
        # Botones de diálogo
        botones_dialogo = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        botones_dialogo.rejected.connect(self.reject)
        
        layout_principal.addWidget(botones_dialogo)

    def _cargar_informes(self):
        """Carga la lista de informes desde la base de datos."""
        db = next(get_db())
        
        try:
            from sqlalchemy import func
            
            # Primero obtenemos todos los informes
            informes = db.query(Informe).all()
            
            # Configurar la tabla
            self.tabla_informes.setRowCount(len(informes))
            
            for fila, informe in enumerate(informes):
                # Contar las líneas para este informe
                num_lineas = db.query(func.count(LineaInforme.id))\
                              .filter(LineaInforme.id_informe == informe.id)\
                              .scalar() or 0
                
                # Código
                self.tabla_informes.setItem(fila, 0, QTableWidgetItem(informe.codigo))
                
                # Descripción
                descripcion = informe.descripcion or ""
                self.tabla_informes.setItem(fila, 1, QTableWidgetItem(descripcion))
                
                # Número de líneas
                self.tabla_informes.setItem(fila, 2, QTableWidgetItem(str(num_lineas)))
            
            # Ajustar el ancho de las columnas
            self.tabla_informes.resizeColumnsToContents()
            
            # Almacenar los informes para referencia
            self.informes = informes
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"No se pudieron cargar los informes: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        finally:
            db.close()
    
    def _on_seleccion_cambiada(self):
        """Maneja el evento de cambio de selección en la tabla."""
        seleccion = self.tabla_informes.selectedItems()
        habilitar = len(seleccion) > 0
        
        self.boton_editar.setEnabled(habilitar)
        self.boton_eliminar.setEnabled(habilitar)
    
    def _obtener_informe_seleccionado(self):
        """
        Obtiene el informe seleccionado en la tabla.
        
        Returns:
            Informe: El informe seleccionado, o None si no hay selección.
        """
        filas_seleccionadas = self.tabla_informes.selectionModel().selectedRows()
        
        if not filas_seleccionadas:
            return None
        
        # Obtener el código del informe de la primera columna de la fila seleccionada
        fila = filas_seleccionadas[0].row()
        codigo = self.tabla_informes.item(fila, 0).text()
        
        # Obtener el informe de la base de datos
        db = next(get_db())
        try:
            return db.query(Informe).filter(Informe.codigo == codigo).first()
        finally:
            db.close()
    
    def _on_nuevo_informe(self):
        """Maneja el evento de crear un nuevo informe."""
        editor = EditorInforme()
        
        dialogo = QDialog(self)
        dialogo.setWindowTitle("Nuevo Informe")
        layout = QVBoxLayout(dialogo)
        layout.addWidget(editor)
        
        # Botones de diálogo
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel
        )
        botones.accepted.connect(editor._on_guardar)
        botones.rejected.connect(dialogo.reject)
        
        layout.addWidget(botones)
        
        if dialogo.exec() == QDialog.DialogCode.Accepted:
            # Si se guardó correctamente, actualizar la lista
            self._cargar_informes()
    
    def _on_editar_informe(self):
        """Maneja el evento de editar un informe existente."""
        informe = self._obtener_informe_seleccionado()
        
        if not informe:
            return
        
        editor = EditorInforme(informe)
        
        dialogo = QDialog(self)
        dialogo.setWindowTitle(f"Editar Informe: {informe.codigo}")
        layout = QVBoxLayout(dialogo)
        layout.addWidget(editor)
        
        # Botones de diálogo
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel
        )
        botones.accepted.connect(editor._on_guardar)
        botones.rejected.connect(dialogo.reject)
        
        layout.addWidget(botones)
        
        if dialogo.exec() == QDialog.DialogCode.Accepted:
            # Si se guardó correctamente, actualizar la lista
            self._cargar_informes()
    
    def _on_eliminar_informe(self):
        """Maneja el evento de eliminar un informe."""
        informe = self._obtener_informe_seleccionado()
        
        if not informe:
            return
        
        # Confirmar eliminación
        respuesta = QMessageBox.question(
            self,
            "Confirmar eliminación",
            f"¿Está seguro de que desea eliminar el informe '{informe.codigo}'?\n"
            "Esta acción no se puede deshacer.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if respuesta == QMessageBox.StandardButton.Yes:
            db = next(get_db())
            try:
                # Obtener el informe a eliminar
                informe_eliminar = db.query(Informe).filter(Informe.id == informe.id).first()
                
                if not informe_eliminar:
                    raise ValueError("El informe ya no existe.")
                
                # Guardar el código para mostrarlo en el mensaje
                codigo = informe_eliminar.codigo
                
                # Eliminar el informe (las líneas se eliminarán en cascada)
                db.delete(informe_eliminar)
                db.commit()
                
                QMessageBox.information(
                    self,
                    "Informe eliminado",
                    f"El informe '{codigo}' ha sido eliminado correctamente.",
                    QMessageBox.StandardButton.Ok
                )
                
                # Actualizar la lista de informes
                self._cargar_informes()
                
            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self,
                    "Error",
                    f"No se pudo eliminar el informe: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )
            finally:
                db.close()
    
    def _on_exportar_informe(self, formato: str):
        """
        Maneja el evento de exportar un informe.
        
        Args:
            formato: Formato de exportación ('pdf' o 'csv')
        """
        informe = self._obtener_informe_seleccionado()
        
        if not informe:
            return
        
        # Importar aquí para evitar dependencia circular
        from utils.report_generator import ReportGenerator
        
        generador = None
        try:
            generador = ReportGenerator(self)
            
            if formato == 'pdf':
                ruta = generador.generate_pdf(informe.id)
                mensaje = "PDF generado correctamente"
            elif formato == 'csv':
                ruta = generador.generate_csv(informe.id)
                mensaje = "CSV generado correctamente"
            else:
                raise ValueError(f"Formato no soportado: {formato}")
            
            if ruta:  # Si se generó correctamente
                QMessageBox.information(
                    self,
                    "Exportación exitosa",
                    f"{mensaje} en:\n{ruta}",
                    QMessageBox.StandardButton.Ok
                )
        
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error al exportar",
                f"No se pudo exportar el informe: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        finally:
            # Asegurarse de cerrar la conexión a la base de datos
            if generador is not None:
                del generador
