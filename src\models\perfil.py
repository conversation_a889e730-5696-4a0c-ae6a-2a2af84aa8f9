from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Text, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime
from .base import Base

class Distribuidor(Base):
    """
    Modelo que representa un distribuidor de perfiles.
    """
    __tablename__ = 'distribuidores'

    id = Column(Integer, primary_key=True)
    codigo = Column(String(10), unique=True, nullable=False)
    nombre = Column(String(100), nullable=False)
    contacto = Column(String(100))
    telefono = Column(String(20))
    email = Column(String(100))
    direccion = Column(Text)
    activo = Column(Boolean, default=True)
    fecha_creacion = Column(DateTime, default=datetime.now)

    # Relaciones
    perfiles = relationship("Perfil", back_populates="distribuidor")
    accesorios = relationship("Accesorio", back_populates="distribuidor")

    def __repr__(self):
        return f"<Distribuidor(codigo='{self.codigo}', nombre='{self.nombre}')>"

class SeriePerfil(Base):
    """
    Modelo que representa una serie de perfiles.
    """
    __tablename__ = 'series_perfiles'

    id = Column(Integer, primary_key=True)
    codigo = Column(String(20), unique=True, nullable=False)
    nombre = Column(String(100), nullable=False)
    descripcion = Column(Text)
    activo = Column(Boolean, default=True)
    fecha_creacion = Column(DateTime, default=datetime.now)

    # Relaciones
    perfiles = relationship("Perfil", back_populates="serie")

    def __repr__(self):
        return f"<SeriePerfil(codigo='{self.codigo}', nombre='{self.nombre}')>"

class TipoPerfil(Base):
    """
    Modelo que representa un tipo de perfil.
    """
    __tablename__ = 'tipos_perfiles'

    id = Column(Integer, primary_key=True)
    codigo = Column(String(20), unique=True, nullable=False)
    nombre = Column(String(100), nullable=False)
    descripcion = Column(Text)
    categoria = Column(String(50))  # marco, hoja, inversor, travesaño, junquillo, etc.
    activo = Column(Boolean, default=True)
    fecha_creacion = Column(DateTime, default=datetime.now)

    # Relaciones
    perfiles = relationship("Perfil", back_populates="tipo")

    def __repr__(self):
        return f"<TipoPerfil(codigo='{self.codigo}', nombre='{self.nombre}')>"

class Perfil(Base):
    """
    Modelo mejorado que representa un perfil de aluminio/PVC en el sistema.
    """
    __tablename__ = 'perfiles'

    id = Column(Integer, primary_key=True)
    codigo = Column(String(20), unique=True, nullable=False)
    descripcion = Column(String(200), nullable=False)
    referencia = Column(String(50))

    # Relaciones con otras entidades
    distribuidor_id = Column(Integer, ForeignKey('distribuidores.id'))
    serie_id = Column(Integer, ForeignKey('series_perfiles.id'))
    tipo_id = Column(Integer, ForeignKey('tipos_perfiles.id'))

    # Características técnicas
    material = Column(String(20), default='Aluminio')  # Aluminio, PVC, etc.
    color = Column(String(50))
    acabado = Column(String(50))  # Anodizado, Lacado, etc.

    # Dimensiones
    ancho = Column(Float)  # mm
    alto = Column(Float)   # mm
    espesor = Column(Float)  # mm
    peso_metro = Column(Float)  # kg/m

    # Precios
    precio_metro = Column(Float, nullable=False, default=0.0)
    precio_compra = Column(Float, default=0.0)
    margen_beneficio = Column(Float, default=0.0)  # %

    # Información adicional
    observaciones = Column(Text)
    codigo_barras = Column(String(50))
    stock_minimo = Column(Integer, default=0)
    unidad_medida = Column(String(10), default='m')  # m, mm, pza

    # Estado y fechas
    activo = Column(Boolean, default=True)
    fecha_creacion = Column(DateTime, default=datetime.now)
    fecha_modificacion = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # Relaciones
    distribuidor = relationship("Distribuidor", back_populates="perfiles")
    serie = relationship("SeriePerfil", back_populates="perfiles")
    tipo = relationship("TipoPerfil", back_populates="perfiles")
    obras = relationship("Obra", back_populates="perfil")

    def __repr__(self):
        return f"<Perfil(codigo='{self.codigo}', descripcion='{self.descripcion}')>"

    def to_dict(self):
        """Convierte el objeto a un diccionario."""
        return {
            'id': self.id,
            'codigo': self.codigo,
            'descripcion': self.descripcion,
            'referencia': self.referencia,
            'distribuidor_id': self.distribuidor_id,
            'distribuidor_nombre': self.distribuidor.nombre if self.distribuidor else None,
            'serie_id': self.serie_id,
            'serie_nombre': self.serie.nombre if self.serie else None,
            'tipo_id': self.tipo_id,
            'tipo_nombre': self.tipo.nombre if self.tipo else None,
            'material': self.material,
            'color': self.color,
            'acabado': self.acabado,
            'ancho': self.ancho,
            'alto': self.alto,
            'espesor': self.espesor,
            'peso_metro': self.peso_metro,
            'precio_metro': self.precio_metro,
            'precio_compra': self.precio_compra,
            'margen_beneficio': self.margen_beneficio,
            'observaciones': self.observaciones,
            'codigo_barras': self.codigo_barras,
            'stock_minimo': self.stock_minimo,
            'unidad_medida': self.unidad_medida,
            'activo': self.activo,
            'fecha_creacion': self.fecha_creacion,
            'fecha_modificacion': self.fecha_modificacion
        }

    @property
    def precio_venta_calculado(self):
        """Calcula el precio de venta basado en el precio de compra y margen."""
        if self.precio_compra and self.margen_beneficio:
            return self.precio_compra * (1 + self.margen_beneficio / 100)
        return self.precio_metro

    @property
    def nombre_completo(self):
        """Devuelve el nombre completo del perfil."""
        partes = [self.codigo, self.descripcion]
        if self.serie:
            partes.insert(1, f"Serie {self.serie.nombre}")
        return " - ".join(partes)
