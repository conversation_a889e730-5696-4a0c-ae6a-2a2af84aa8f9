"""
Diálogo de Configuración Avanzada para PRO-2000
Incluye todas las opciones de configuración del sistema
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTabWidget, QWidget, QFormLayout, QLineEdit, QSpinBox,
    QDoubleSpinBox, QCheckBox, QComboBox, QGroupBox, QTextEdit,
    QDialogButtonBox, QFileDialog, QMessageBox, QSlider,
    QProgressBar, QListWidget, QListWidgetItem
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ui.utils.window_utils import setup_maximized_dialog
from utils.config_manager import ConfigManager
import os
import shutil
from datetime import datetime


class ConfiguracionAvanzadaDialog(QDialog):
    """Diálogo completo de configuración avanzada."""
    
    configuracion_cambiada = pyqtSignal()  # Señal cuando cambia la configuración
    
    def __init__(self, parent=None):
        super().__init__(parent)

        self.setModal(True)

        # Inicializar ConfigManager
        self.config_manager = ConfigManager()

        # ✅ CONFIGURACIÓN SIMPLE Y EFECTIVA
        setup_maximized_dialog(self, "Configuración Avanzada - PRO-2000")

        self._inicializar_ui()
        self._cargar_configuracion()
    
    def _inicializar_ui(self):
        """Configura la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)
        
        # Título
        titulo = QLabel("⚙️ Configuración Avanzada del Sistema")
        titulo.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        titulo.setAlignment(Qt.AlignmentFlag.AlignCenter)
        titulo.setStyleSheet("padding: 15px; color: #2c3e50;")
        layout_principal.addWidget(titulo)
        
        # Tabs de configuración
        self.tabs = QTabWidget()
        layout_principal.addWidget(self.tabs)
        
        # Crear todas las pestañas
        self._crear_tab_general()
        self._crear_tab_temas()
        self._crear_tab_base_datos()
        self._crear_tab_optimizacion()
        self._crear_tab_rendimiento()
        self._crear_tab_seguridad()
        self._crear_tab_respaldos()
        self._crear_tab_avanzado()
        
        # Botones de acción
        self._crear_botones_accion(layout_principal)
    
    def _crear_tab_general(self):
        """Crea la pestaña de configuración general."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Configuración de la aplicación
        grupo_app = QGroupBox("🏢 Configuración de la Aplicación")
        layout_app = QFormLayout(grupo_app)
        
        self.campo_nombre_empresa = QLineEdit()
        self.campo_nombre_empresa.setPlaceholderText("Nombre de su empresa")
        layout_app.addRow("Nombre de la empresa:", self.campo_nombre_empresa)
        
        self.combo_idioma = QComboBox()
        self.combo_idioma.addItems(["Español", "English", "Français"])
        layout_app.addRow("Idioma:", self.combo_idioma)
        
        self.combo_moneda = QComboBox()
        self.combo_moneda.addItems(["EUR (€)", "USD ($)", "GBP (£)"])
        layout_app.addRow("Moneda:", self.combo_moneda)
        
        layout.addWidget(grupo_app)
        
        # Configuración de ventanas
        grupo_ventanas = QGroupBox("🪟 Configuración de Ventanas")
        layout_ventanas = QFormLayout(grupo_ventanas)
        
        self.check_maximizar_ventanas = QCheckBox("Maximizar ventanas automáticamente")
        self.check_maximizar_ventanas.setChecked(True)
        layout_ventanas.addRow(self.check_maximizar_ventanas)
        
        self.check_recordar_posicion = QCheckBox("Recordar posición de ventanas")
        layout_ventanas.addRow(self.check_recordar_posicion)
        
        self.check_animaciones = QCheckBox("Habilitar animaciones")
        self.check_animaciones.setChecked(True)
        layout_ventanas.addRow(self.check_animaciones)
        
        layout.addWidget(grupo_ventanas)
        
        # Configuración de guardado
        grupo_guardado = QGroupBox("💾 Configuración de Guardado")
        layout_guardado = QFormLayout(grupo_guardado)
        
        self.check_autoguardado = QCheckBox("Habilitar autoguardado")
        self.check_autoguardado.setChecked(True)
        layout_guardado.addRow(self.check_autoguardado)
        
        self.spin_intervalo_guardado = QSpinBox()
        self.spin_intervalo_guardado.setRange(1, 60)
        self.spin_intervalo_guardado.setValue(5)
        self.spin_intervalo_guardado.setSuffix(" minutos")
        layout_guardado.addRow("Intervalo de autoguardado:", self.spin_intervalo_guardado)
        
        layout.addWidget(grupo_guardado)
        
        layout.addStretch()
        self.tabs.addTab(tab, "🏠 General")
    
    def _crear_tab_temas(self):
        """Crea la pestaña de configuración de temas."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Selector de tema
        grupo_tema = QGroupBox("🎨 Selección de Tema")
        layout_tema = QFormLayout(grupo_tema)
        
        self.combo_tema = QComboBox()
        self.combo_tema.addItems([
            "🚀 Revolucionario (Recomendado)",
            "💼 Profesional",
            "🌙 Oscuro",
            "☀️ Claro",
            "🎨 Moderno Oscuro",
            "✨ Moderno Claro"
        ])
        layout_tema.addRow("Tema actual:", self.combo_tema)
        
        btn_vista_previa = QPushButton("👁️ Vista Previa")
        btn_vista_previa.clicked.connect(self._mostrar_vista_previa_tema)
        layout_tema.addRow(btn_vista_previa)
        
        layout.addWidget(grupo_tema)
        
        # Personalización
        grupo_personalizacion = QGroupBox("🎨 Personalización")
        layout_personalizacion = QFormLayout(grupo_personalizacion)
        
        self.combo_fuente = QComboBox()
        self.combo_fuente.addItems(["Segoe UI", "Arial", "Calibri", "Tahoma"])
        layout_personalizacion.addRow("Fuente:", self.combo_fuente)
        
        self.spin_tamano_fuente = QSpinBox()
        self.spin_tamano_fuente.setRange(8, 24)
        self.spin_tamano_fuente.setValue(14)
        layout_personalizacion.addRow("Tamaño de fuente:", self.spin_tamano_fuente)
        
        layout.addWidget(grupo_personalizacion)
        
        layout.addStretch()
        self.tabs.addTab(tab, "🎨 Temas")
    
    def _crear_tab_base_datos(self):
        """Crea la pestaña de configuración de base de datos."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Configuración de conexión
        grupo_conexion = QGroupBox("🗄️ Configuración de Base de Datos")
        layout_conexion = QFormLayout(grupo_conexion)
        
        self.combo_tipo_bd = QComboBox()
        self.combo_tipo_bd.addItems(["SQLite (Local)", "PostgreSQL", "MySQL"])
        layout_conexion.addRow("Tipo de base de datos:", self.combo_tipo_bd)
        
        self.campo_ruta_bd = QLineEdit()
        self.campo_ruta_bd.setText("data/pro2000.db")
        layout_conexion.addRow("Ruta de la base de datos:", self.campo_ruta_bd)
        
        btn_examinar_bd = QPushButton("📁 Examinar...")
        btn_examinar_bd.clicked.connect(self._examinar_base_datos)
        layout_conexion.addRow(btn_examinar_bd)
        
        layout.addWidget(grupo_conexion)
        
        # Configuración de rendimiento
        grupo_rendimiento_bd = QGroupBox("⚡ Rendimiento de Base de Datos")
        layout_rendimiento_bd = QFormLayout(grupo_rendimiento_bd)
        
        self.spin_pool_conexiones = QSpinBox()
        self.spin_pool_conexiones.setRange(1, 50)
        self.spin_pool_conexiones.setValue(10)
        layout_rendimiento_bd.addRow("Pool de conexiones:", self.spin_pool_conexiones)
        
        self.spin_timeout_consulta = QSpinBox()
        self.spin_timeout_consulta.setRange(5, 300)
        self.spin_timeout_consulta.setValue(30)
        self.spin_timeout_consulta.setSuffix(" segundos")
        layout_rendimiento_bd.addRow("Timeout de consulta:", self.spin_timeout_consulta)
        
        layout.addWidget(grupo_rendimiento_bd)
        
        # Mantenimiento
        grupo_mantenimiento = QGroupBox("🔧 Mantenimiento")
        layout_mantenimiento = QVBoxLayout(grupo_mantenimiento)
        
        btn_optimizar_bd = QPushButton("🚀 Optimizar Base de Datos")
        btn_optimizar_bd.clicked.connect(self._optimizar_base_datos)
        layout_mantenimiento.addWidget(btn_optimizar_bd)
        
        btn_verificar_bd = QPushButton("🔍 Verificar Integridad")
        btn_verificar_bd.clicked.connect(self._verificar_integridad)
        layout_mantenimiento.addWidget(btn_verificar_bd)
        
        layout.addWidget(grupo_mantenimiento)
        
        layout.addStretch()
        self.tabs.addTab(tab, "🗄️ Base de Datos")
    
    def _crear_tab_optimizacion(self):
        """Crea la pestaña de configuración de optimización."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Configuración de cortes
        grupo_cortes = QGroupBox("✂️ Optimización de Cortes")
        layout_cortes = QFormLayout(grupo_cortes)
        
        self.spin_longitud_barra_defecto = QDoubleSpinBox()
        self.spin_longitud_barra_defecto.setRange(1000, 10000)
        self.spin_longitud_barra_defecto.setValue(6000)
        self.spin_longitud_barra_defecto.setSuffix(" mm")
        layout_cortes.addRow("Longitud de barra por defecto:", self.spin_longitud_barra_defecto)
        
        self.spin_grosor_corte_defecto = QDoubleSpinBox()
        self.spin_grosor_corte_defecto.setRange(0.5, 10)
        self.spin_grosor_corte_defecto.setValue(3.0)
        self.spin_grosor_corte_defecto.setSuffix(" mm")
        layout_cortes.addRow("Grosor de corte por defecto:", self.spin_grosor_corte_defecto)
        
        self.combo_algoritmo_defecto = QComboBox()
        self.combo_algoritmo_defecto.addItems([
            "Best Fit Decreasing (BFD)",
            "First Fit Decreasing (FFD)",
            "Next Fit Decreasing (NFD)"
        ])
        layout_cortes.addRow("Algoritmo por defecto:", self.combo_algoritmo_defecto)
        
        layout.addWidget(grupo_cortes)
        
        # Configuración de cálculos
        grupo_calculos = QGroupBox("🧮 Configuración de Cálculos")
        layout_calculos = QFormLayout(grupo_calculos)
        
        self.spin_precision_decimal = QSpinBox()
        self.spin_precision_decimal.setRange(0, 6)
        self.spin_precision_decimal.setValue(2)
        layout_calculos.addRow("Precisión decimal:", self.spin_precision_decimal)
        
        self.check_redondeo_automatico = QCheckBox("Redondeo automático")
        self.check_redondeo_automatico.setChecked(True)
        layout_calculos.addRow(self.check_redondeo_automatico)
        
        layout.addWidget(grupo_calculos)

        # Acciones de optimización
        grupo_acciones_opt = QGroupBox("🛠️ Herramientas de Optimización")
        layout_acciones_opt = QVBoxLayout(grupo_acciones_opt)

        btn_abrir_optimizacion = QPushButton("⚡ Abrir Optimización de Cortes")
        btn_abrir_optimizacion.clicked.connect(self._abrir_optimizacion)
        layout_acciones_opt.addWidget(btn_abrir_optimizacion)

        btn_configurar_materiales = QPushButton("📦 Configurar Materiales por Defecto")
        btn_configurar_materiales.clicked.connect(self._configurar_materiales)
        layout_acciones_opt.addWidget(btn_configurar_materiales)

        layout.addWidget(grupo_acciones_opt)

        layout.addStretch()
        self.tabs.addTab(tab, "⚡ Optimización")

    def _crear_tab_rendimiento(self):
        """Crea la pestaña de configuración de rendimiento."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Configuración de memoria
        grupo_memoria = QGroupBox("🧠 Gestión de Memoria")
        layout_memoria = QFormLayout(grupo_memoria)

        self.spin_cache_size = QSpinBox()
        self.spin_cache_size.setRange(32, 1024)
        self.spin_cache_size.setValue(128)
        self.spin_cache_size.setSuffix(" MB")
        layout_memoria.addRow("Tamaño de caché:", self.spin_cache_size)

        self.check_lazy_loading = QCheckBox("Carga perezosa de datos")
        self.check_lazy_loading.setChecked(True)
        layout_memoria.addRow(self.check_lazy_loading)

        layout.addWidget(grupo_memoria)

        # Configuración de hilos
        grupo_hilos = QGroupBox("⚡ Procesamiento")
        layout_hilos = QFormLayout(grupo_hilos)

        self.spin_max_workers = QSpinBox()
        self.spin_max_workers.setRange(1, 16)
        self.spin_max_workers.setValue(4)
        layout_hilos.addRow("Máximo de hilos:", self.spin_max_workers)

        layout.addWidget(grupo_hilos)

        layout.addStretch()
        self.tabs.addTab(tab, "🚀 Rendimiento")

    def _crear_tab_seguridad(self):
        """Crea la pestaña de configuración de seguridad."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Configuración de sesión
        grupo_sesion = QGroupBox("🔐 Configuración de Sesión")
        layout_sesion = QFormLayout(grupo_sesion)

        self.spin_timeout_sesion = QSpinBox()
        self.spin_timeout_sesion.setRange(30, 1440)
        self.spin_timeout_sesion.setValue(480)
        self.spin_timeout_sesion.setSuffix(" minutos")
        layout_sesion.addRow("Timeout de sesión:", self.spin_timeout_sesion)

        self.spin_max_intentos = QSpinBox()
        self.spin_max_intentos.setRange(1, 10)
        self.spin_max_intentos.setValue(3)
        layout_sesion.addRow("Máximo intentos de login:", self.spin_max_intentos)

        layout.addWidget(grupo_sesion)

        # Configuración de contraseñas
        grupo_passwords = QGroupBox("🔑 Configuración de Contraseñas")
        layout_passwords = QFormLayout(grupo_passwords)

        self.spin_min_longitud = QSpinBox()
        self.spin_min_longitud.setRange(4, 20)
        self.spin_min_longitud.setValue(6)
        layout_passwords.addRow("Longitud mínima:", self.spin_min_longitud)

        self.check_encriptacion = QCheckBox("Habilitar encriptación")
        self.check_encriptacion.setChecked(True)
        layout_passwords.addRow(self.check_encriptacion)

        layout.addWidget(grupo_passwords)

        layout.addStretch()
        self.tabs.addTab(tab, "🔐 Seguridad")

    def _crear_tab_respaldos(self):
        """Crea la pestaña de configuración de respaldos."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Configuración de respaldos automáticos
        grupo_auto = QGroupBox("🔄 Respaldos Automáticos")
        layout_auto = QFormLayout(grupo_auto)

        self.check_respaldos_auto = QCheckBox("Habilitar respaldos automáticos")
        self.check_respaldos_auto.setChecked(True)
        layout_auto.addRow(self.check_respaldos_auto)

        self.spin_intervalo_respaldo = QSpinBox()
        self.spin_intervalo_respaldo.setRange(1, 168)
        self.spin_intervalo_respaldo.setValue(24)
        self.spin_intervalo_respaldo.setSuffix(" horas")
        layout_auto.addRow("Intervalo de respaldo:", self.spin_intervalo_respaldo)

        self.spin_max_respaldos = QSpinBox()
        self.spin_max_respaldos.setRange(1, 100)
        self.spin_max_respaldos.setValue(30)
        layout_auto.addRow("Máximo de respaldos:", self.spin_max_respaldos)

        layout.addWidget(grupo_auto)

        # Configuración de ubicación
        grupo_ubicacion = QGroupBox("📁 Ubicación de Respaldos")
        layout_ubicacion = QFormLayout(grupo_ubicacion)

        self.campo_ruta_respaldos = QLineEdit()
        self.campo_ruta_respaldos.setText("data/backup")
        layout_ubicacion.addRow("Carpeta de respaldos:", self.campo_ruta_respaldos)

        btn_examinar_respaldos = QPushButton("📁 Examinar...")
        btn_examinar_respaldos.clicked.connect(self._examinar_respaldos)
        layout_ubicacion.addRow(btn_examinar_respaldos)

        layout.addWidget(grupo_ubicacion)

        # Acciones de respaldo
        grupo_acciones = QGroupBox("🛠️ Acciones de Respaldo")
        layout_acciones = QVBoxLayout(grupo_acciones)

        btn_crear_respaldo = QPushButton("💾 Crear Respaldo Ahora")
        btn_crear_respaldo.clicked.connect(self._crear_respaldo_manual)
        layout_acciones.addWidget(btn_crear_respaldo)

        btn_restaurar = QPushButton("🔄 Restaurar desde Respaldo")
        btn_restaurar.clicked.connect(self._restaurar_respaldo)
        layout_acciones.addWidget(btn_restaurar)

        layout.addWidget(grupo_acciones)

        layout.addStretch()
        self.tabs.addTab(tab, "💾 Respaldos")

    def _crear_tab_avanzado(self):
        """Crea la pestaña de configuración avanzada."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Configuración de logging
        grupo_logging = QGroupBox("📝 Configuración de Logs")
        layout_logging = QFormLayout(grupo_logging)

        self.combo_nivel_log = QComboBox()
        self.combo_nivel_log.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.combo_nivel_log.setCurrentText("INFO")
        layout_logging.addRow("Nivel de logging:", self.combo_nivel_log)

        self.check_log_archivo = QCheckBox("Guardar logs en archivo")
        self.check_log_archivo.setChecked(True)
        layout_logging.addRow(self.check_log_archivo)

        self.check_log_consola = QCheckBox("Mostrar logs en consola")
        self.check_log_consola.setChecked(True)
        layout_logging.addRow(self.check_log_consola)

        layout.addWidget(grupo_logging)

        # Configuración de desarrollo
        grupo_desarrollo = QGroupBox("🔧 Configuración de Desarrollo")
        layout_desarrollo = QFormLayout(grupo_desarrollo)

        self.check_modo_debug = QCheckBox("Modo debug")
        layout_desarrollo.addRow(self.check_modo_debug)

        self.check_mostrar_sql = QCheckBox("Mostrar consultas SQL")
        layout_desarrollo.addRow(self.check_mostrar_sql)

        layout.addWidget(grupo_desarrollo)

        # Acciones del sistema
        grupo_sistema = QGroupBox("🔧 Acciones del Sistema")
        layout_sistema = QVBoxLayout(grupo_sistema)

        btn_limpiar_cache = QPushButton("🧹 Limpiar Caché")
        btn_limpiar_cache.clicked.connect(self._limpiar_cache)
        layout_sistema.addWidget(btn_limpiar_cache)

        btn_reiniciar_config = QPushButton("🔄 Restaurar Configuración por Defecto")
        btn_reiniciar_config.clicked.connect(self._restaurar_configuracion_defecto)
        layout_sistema.addWidget(btn_reiniciar_config)

        layout.addWidget(grupo_sistema)

        layout.addStretch()
        self.tabs.addTab(tab, "🔧 Avanzado")

    def _examinar_respaldos(self):
        """Abre diálogo para seleccionar carpeta de respaldos."""
        carpeta = QFileDialog.getExistingDirectory(self, "Seleccionar Carpeta de Respaldos")
        if carpeta:
            self.campo_ruta_respaldos.setText(carpeta)

    def _crear_respaldo_manual(self):
        """Crea un respaldo manual."""
        try:
            # Crear directorio de respaldos si no existe
            backup_dir = self.campo_ruta_respaldos.text()
            os.makedirs(backup_dir, exist_ok=True)

            # Nombre del archivo de respaldo con timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"pro2000_backup_{timestamp}.json"
            backup_path = os.path.join(backup_dir, backup_filename)

            # Crear respaldo de la configuración
            backup_data = {
                "timestamp": timestamp,
                "version": "2.1.0",
                "config": self.config_manager.all_config,
                "backup_type": "manual"
            }

            # Guardar respaldo
            with open(backup_path, 'w', encoding='utf-8') as f:
                import json
                json.dump(backup_data, f, indent=2, ensure_ascii=False)

            # Respaldo de base de datos si existe
            db_path = self.config_manager.get("database.path", "data/pro2000.db")
            if os.path.exists(db_path):
                db_backup_path = os.path.join(backup_dir, f"pro2000_db_{timestamp}.db")
                shutil.copy2(db_path, db_backup_path)
                backup_data["database_backup"] = db_backup_path

                # Actualizar archivo de respaldo con info de BD
                with open(backup_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, indent=2, ensure_ascii=False)

            QMessageBox.information(
                self, "Respaldo",
                f"✅ Respaldo creado correctamente:\n{backup_path}"
            )
            print(f"✅ Respaldo manual creado: {backup_path}")

        except Exception as e:
            print(f"❌ Error creando respaldo: {e}")
            QMessageBox.critical(self, "Error", f"Error creando respaldo: {e}")

    def _restaurar_respaldo(self):
        """Restaura desde un respaldo."""
        try:
            # Seleccionar archivo de respaldo
            backup_dir = self.campo_ruta_respaldos.text()
            archivo, _ = QFileDialog.getOpenFileName(
                self, "Seleccionar Respaldo", backup_dir,
                "Respaldos JSON (*.json);;Todos los archivos (*.*)"
            )

            if not archivo:
                return

            # Confirmar restauración
            respuesta = QMessageBox.question(
                self, "Restaurar Respaldo",
                "⚠️ ¿Está seguro de que desea restaurar este respaldo?\n"
                "Se sobrescribirá la configuración actual.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if respuesta != QMessageBox.StandardButton.Yes:
                return

            # Cargar datos del respaldo
            with open(archivo, 'r', encoding='utf-8') as f:
                import json
                backup_data = json.load(f)

            # Validar formato del respaldo
            if "config" not in backup_data:
                QMessageBox.warning(self, "Error", "Archivo de respaldo inválido.")
                return

            # Restaurar configuración
            for key, value in backup_data["config"].items():
                if isinstance(value, dict):
                    for subkey, subvalue in value.items():
                        self.config_manager.set(f"{key}.{subkey}", subvalue)
                else:
                    self.config_manager.set(key, value)

            # Guardar configuración restaurada
            self.config_manager.save()

            # Recargar interfaz con nueva configuración
            self._cargar_configuracion()

            # Restaurar base de datos si existe en el respaldo
            if "database_backup" in backup_data:
                db_backup_path = backup_data["database_backup"]
                if os.path.exists(db_backup_path):
                    db_path = self.config_manager.get("database.path", "data/pro2000.db")

                    # Crear backup de la BD actual antes de restaurar
                    if os.path.exists(db_path):
                        backup_current = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        shutil.copy2(db_path, backup_current)

                    # Restaurar BD
                    shutil.copy2(db_backup_path, db_path)

            QMessageBox.information(
                self, "Restauración",
                f"✅ Respaldo restaurado correctamente.\n"
                f"Timestamp: {backup_data.get('timestamp', 'Desconocido')}"
            )
            print(f"✅ Respaldo restaurado desde: {archivo}")

        except Exception as e:
            print(f"❌ Error restaurando respaldo: {e}")
            QMessageBox.critical(self, "Error", f"Error restaurando respaldo: {e}")

    def _limpiar_cache(self):
        """Limpia el caché del sistema."""
        try:
            cache_dirs = [
                "data/temp",
                "data/cache",
                "__pycache__"
            ]

            archivos_eliminados = 0

            for cache_dir in cache_dirs:
                if os.path.exists(cache_dir):
                    for root, dirs, files in os.walk(cache_dir):
                        for file in files:
                            try:
                                os.remove(os.path.join(root, file))
                                archivos_eliminados += 1
                            except:
                                pass
                        for dir in dirs:
                            try:
                                shutil.rmtree(os.path.join(root, dir))
                            except:
                                pass

            QMessageBox.information(
                self, "Caché",
                f"✅ Caché limpiado correctamente.\n{archivos_eliminados} archivos eliminados."
            )
            print(f"✅ Caché limpiado: {archivos_eliminados} archivos")

        except Exception as e:
            print(f"❌ Error limpiando caché: {e}")
            QMessageBox.critical(self, "Error", f"Error limpiando caché: {e}")

    def _restaurar_configuracion_defecto(self):
        """Restaura la configuración por defecto."""
        respuesta = QMessageBox.question(
            self, "Restaurar Configuración",
            "¿Está seguro de que desea restaurar la configuración por defecto?\n"
            "Se perderán todos los cambios personalizados.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        if respuesta == QMessageBox.StandardButton.Yes:
            QMessageBox.information(self, "Configuración", "Configuración restaurada por defecto.")

    def _abrir_optimizacion(self):
        """Abre el módulo de optimización de cortes."""
        try:
            from ui.modulos.optimizacion.optimizacion_cortes_dialog import OptimizacionCortesDialog
            dialog = OptimizacionCortesDialog(self)
            dialog.exec()
        except Exception as e:
            QMessageBox.warning(self, "Error", f"No se pudo abrir el módulo de optimización: {e}")

    def _configurar_materiales(self):
        """Configura materiales por defecto."""
        QMessageBox.information(
            self, "Materiales",
            "Configuración de materiales por defecto.\n"
            "Esta función se implementará en una versión futura."
        )

    def _crear_botones_accion(self, layout_principal):
        """Crea los botones de acción."""
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel |
            QDialogButtonBox.StandardButton.Apply
        )
        
        botones.button(QDialogButtonBox.StandardButton.Ok).setText("Guardar y Cerrar")
        botones.button(QDialogButtonBox.StandardButton.Cancel).setText("Cancelar")
        botones.button(QDialogButtonBox.StandardButton.Apply).setText("Aplicar")
        
        botones.accepted.connect(self._guardar_y_cerrar)
        botones.rejected.connect(self.reject)
        botones.button(QDialogButtonBox.StandardButton.Apply).clicked.connect(self._aplicar_configuracion)
        
        layout_principal.addWidget(botones)
    
    def _cargar_configuracion(self):
        """Carga la configuración actual desde ConfigManager."""
        try:
            # Cargar configuración general
            self.campo_nombre_empresa.setText(self.config_manager.get("empresa.nombre", ""))

            # Cargar configuración de UI
            idioma = self.config_manager.get("ui.language", "es")
            if idioma == "es":
                self.combo_idioma.setCurrentText("Español")
            elif idioma == "en":
                self.combo_idioma.setCurrentText("English")
            elif idioma == "fr":
                self.combo_idioma.setCurrentText("Français")

            moneda = self.config_manager.get("empresa.moneda", "EUR")
            if moneda == "EUR":
                self.combo_moneda.setCurrentText("EUR (€)")
            elif moneda == "USD":
                self.combo_moneda.setCurrentText("USD ($)")
            elif moneda == "GBP":
                self.combo_moneda.setCurrentText("GBP (£)")

            # Cargar configuración de ventanas
            self.check_maximizar_ventanas.setChecked(
                self.config_manager.get("ui.window_maximized", True)
            )
            self.check_recordar_posicion.setChecked(
                self.config_manager.get("ui.remember_position", False)
            )
            self.check_animaciones.setChecked(
                self.config_manager.get("ui.animations", True)
            )

            # Cargar configuración de guardado
            self.check_autoguardado.setChecked(
                self.config_manager.get("features.auto_save", True)
            )
            self.spin_intervalo_guardado.setValue(
                self.config_manager.get("ui.auto_save_interval", 5)
            )

            # Cargar configuración de temas
            tema = self.config_manager.get("ui.theme", "modern")
            tema_map = {
                "revolutionary": "🚀 Revolucionario (Recomendado)",
                "professional": "💼 Profesional",
                "dark": "🌙 Oscuro",
                "light": "☀️ Claro",
                "modern_dark": "🎨 Moderno Oscuro",
                "modern_light": "✨ Moderno Claro"
            }
            if tema in tema_map:
                self.combo_tema.setCurrentText(tema_map[tema])

            # Cargar configuración de base de datos
            tipo_bd = self.config_manager.get("database.type", "sqlite")
            if tipo_bd == "sqlite":
                self.combo_tipo_bd.setCurrentText("SQLite (Local)")
            elif tipo_bd == "postgresql":
                self.combo_tipo_bd.setCurrentText("PostgreSQL")
            elif tipo_bd == "mysql":
                self.combo_tipo_bd.setCurrentText("MySQL")

            self.campo_ruta_bd.setText(
                self.config_manager.get("database.path", "data/pro2000.db")
            )

            # Cargar configuración de optimización
            self.spin_longitud_barra_defecto.setValue(
                self.config_manager.get("optimizacion.longitud_barra_defecto", 6000)
            )
            self.spin_grosor_corte_defecto.setValue(
                self.config_manager.get("optimizacion.grosor_corte_defecto", 3.0)
            )

            # Cargar configuración de respaldos
            self.check_respaldos_auto.setChecked(
                self.config_manager.get("backup.enabled", True)
            )
            self.spin_intervalo_respaldo.setValue(
                self.config_manager.get("backup.interval_hours", 24)
            )
            self.spin_max_respaldos.setValue(
                self.config_manager.get("backup.max_backups", 30)
            )
            self.campo_ruta_respaldos.setText(
                self.config_manager.get("backup.path", "data/backup")
            )

            print("✅ Configuración cargada correctamente")

        except Exception as e:
            print(f"❌ Error cargando configuración: {e}")
            QMessageBox.warning(self, "Error", f"Error cargando configuración: {e}")
    
    def _mostrar_vista_previa_tema(self):
        """Muestra vista previa del tema seleccionado."""
        from ui.themes.theme_selector_dialog import ThemeSelectorDialog
        dialog = ThemeSelectorDialog(self)
        dialog.exec()
    
    def _examinar_base_datos(self):
        """Abre diálogo para seleccionar archivo de base de datos."""
        archivo, _ = QFileDialog.getOpenFileName(
            self, "Seleccionar Base de Datos", "", "Base de Datos (*.db *.sqlite)"
        )
        if archivo:
            self.campo_ruta_bd.setText(archivo)
    
    def _optimizar_base_datos(self):
        """Optimiza la base de datos."""
        try:
            # Intentar múltiples rutas posibles
            possible_paths = [
                self.config_manager.get("database.path", "data/pro2000.db"),
                "data/pro2000.db",
                "pro2000.db",
                os.path.join(os.getcwd(), "data", "pro2000.db"),
                os.path.join(os.path.dirname(__file__), "..", "..", "data", "pro2000.db")
            ]

            db_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    db_path = path
                    break

            if not db_path:
                QMessageBox.warning(
                    self, "Error",
                    "Base de datos no encontrada en ninguna ubicación conocida.\n"
                    f"Rutas verificadas:\n" + "\n".join(possible_paths)
                )
                return

            print(f"🔧 Optimizando base de datos: {db_path}")

            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Ejecutar VACUUM para optimizar
            print("🔧 Ejecutando VACUUM...")
            cursor.execute("VACUUM")

            # Analizar estadísticas
            print("🔧 Ejecutando ANALYZE...")
            cursor.execute("ANALYZE")

            conn.close()

            QMessageBox.information(self, "Optimización", "✅ Base de datos optimizada correctamente.")
            print("✅ Base de datos optimizada")

        except Exception as e:
            print(f"❌ Error optimizando base de datos: {e}")
            QMessageBox.critical(self, "Error", f"Error optimizando base de datos: {e}")

    def _verificar_integridad(self):
        """Verifica la integridad de la base de datos."""
        try:
            # Intentar múltiples rutas posibles
            possible_paths = [
                self.config_manager.get("database.path", "data/pro2000.db"),
                "data/pro2000.db",
                "pro2000.db",
                os.path.join(os.getcwd(), "data", "pro2000.db"),
                os.path.join(os.path.dirname(__file__), "..", "..", "data", "pro2000.db")
            ]

            db_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    db_path = path
                    break

            if not db_path:
                QMessageBox.warning(
                    self, "Error",
                    "Base de datos no encontrada en ninguna ubicación conocida.\n"
                    f"Rutas verificadas:\n" + "\n".join(possible_paths)
                )
                return

            print(f"🔍 Verificando integridad de: {db_path}")

            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Verificar integridad
            print("🔍 Ejecutando PRAGMA integrity_check...")
            cursor.execute("PRAGMA integrity_check")
            result = cursor.fetchone()

            conn.close()

            if result and result[0] == "ok":
                QMessageBox.information(self, "Verificación", "✅ Integridad de la base de datos verificada correctamente.")
                print("✅ Integridad de BD verificada")
            else:
                QMessageBox.warning(self, "Verificación", f"⚠️ Problemas de integridad detectados: {result}")
                print(f"⚠️ Problemas de integridad: {result}")

        except Exception as e:
            print(f"❌ Error verificando integridad: {e}")
            QMessageBox.critical(self, "Error", f"Error verificando integridad: {e}")
    
    def _aplicar_configuracion(self):
        """Aplica la configuración sin cerrar el diálogo."""
        try:
            # Guardar configuración general
            self.config_manager.set("empresa.nombre", self.campo_nombre_empresa.text())

            # Guardar configuración de UI
            idioma_map = {
                "Español": "es",
                "English": "en",
                "Français": "fr"
            }
            self.config_manager.set("ui.language", idioma_map.get(self.combo_idioma.currentText(), "es"))

            moneda_map = {
                "EUR (€)": "EUR",
                "USD ($)": "USD",
                "GBP (£)": "GBP"
            }
            self.config_manager.set("empresa.moneda", moneda_map.get(self.combo_moneda.currentText(), "EUR"))

            # Guardar configuración de ventanas
            self.config_manager.set("ui.window_maximized", self.check_maximizar_ventanas.isChecked())
            self.config_manager.set("ui.remember_position", self.check_recordar_posicion.isChecked())
            self.config_manager.set("ui.animations", self.check_animaciones.isChecked())

            # Guardar configuración de guardado
            self.config_manager.set("features.auto_save", self.check_autoguardado.isChecked())
            self.config_manager.set("ui.auto_save_interval", self.spin_intervalo_guardado.value())

            # Guardar configuración de temas
            tema_map = {
                "🚀 Revolucionario (Recomendado)": "revolutionary",
                "💼 Profesional": "professional",
                "🌙 Oscuro": "dark",
                "☀️ Claro": "light",
                "🎨 Moderno Oscuro": "modern_dark",
                "✨ Moderno Claro": "modern_light"
            }
            self.config_manager.set("ui.theme", tema_map.get(self.combo_tema.currentText(), "modern"))

            # Guardar configuración de base de datos
            tipo_bd_map = {
                "SQLite (Local)": "sqlite",
                "PostgreSQL": "postgresql",
                "MySQL": "mysql"
            }
            self.config_manager.set("database.type", tipo_bd_map.get(self.combo_tipo_bd.currentText(), "sqlite"))
            self.config_manager.set("database.path", self.campo_ruta_bd.text())

            # Guardar configuración de optimización
            self.config_manager.set("optimizacion.longitud_barra_defecto", self.spin_longitud_barra_defecto.value())
            self.config_manager.set("optimizacion.grosor_corte_defecto", self.spin_grosor_corte_defecto.value())

            # Guardar configuración de respaldos
            self.config_manager.set("backup.enabled", self.check_respaldos_auto.isChecked())
            self.config_manager.set("backup.interval_hours", self.spin_intervalo_respaldo.value())
            self.config_manager.set("backup.max_backups", self.spin_max_respaldos.value())
            self.config_manager.set("backup.path", self.campo_ruta_respaldos.text())

            # Guardar al archivo
            self.config_manager.save()

            self.configuracion_cambiada.emit()
            QMessageBox.information(self, "Configuración", "✅ Configuración guardada correctamente.")
            print("✅ Configuración aplicada y guardada")

        except Exception as e:
            print(f"❌ Error guardando configuración: {e}")
            QMessageBox.critical(self, "Error", f"Error guardando configuración: {e}")

    def _guardar_y_cerrar(self):
        """Guarda la configuración y cierra el diálogo."""
        self._aplicar_configuracion()
        self.accept()
