"""
Diálogo de Configuración Avanzada para PRO-2000
Incluye todas las opciones de configuración del sistema
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTabWidget, QWidget, QFormLayout, QLineEdit, QSpinBox,
    QDoubleSpinBox, QCheckBox, QComboBox, QGroupBox, QTextEdit,
    QDialogButtonBox, QFileDialog, QMessageBox, QSlider,
    QProgressBar, QListWidget, QListWidgetItem
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ui.utils.window_utils import setup_maximized_dialog


class ConfiguracionAvanzadaDialog(QDialog):
    """Diálogo completo de configuración avanzada."""
    
    configuracion_cambiada = pyqtSignal()  # Señal cuando cambia la configuración
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setModal(True)
        
        # ✅ CONFIGURACIÓN SIMPLE Y EFECTIVA
        setup_maximized_dialog(self, "Configuración Avanzada - PRO-2000")
        
        self._inicializar_ui()
        self._cargar_configuracion()
    
    def _inicializar_ui(self):
        """Configura la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)
        
        # Título
        titulo = QLabel("⚙️ Configuración Avanzada del Sistema")
        titulo.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        titulo.setAlignment(Qt.AlignmentFlag.AlignCenter)
        titulo.setStyleSheet("padding: 15px; color: #2c3e50;")
        layout_principal.addWidget(titulo)
        
        # Tabs de configuración
        self.tabs = QTabWidget()
        layout_principal.addWidget(self.tabs)
        
        # Crear todas las pestañas
        self._crear_tab_general()
        self._crear_tab_temas()
        self._crear_tab_base_datos()
        self._crear_tab_optimizacion()
        self._crear_tab_rendimiento()
        self._crear_tab_seguridad()
        self._crear_tab_respaldos()
        self._crear_tab_avanzado()
        
        # Botones de acción
        self._crear_botones_accion(layout_principal)
    
    def _crear_tab_general(self):
        """Crea la pestaña de configuración general."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Configuración de la aplicación
        grupo_app = QGroupBox("🏢 Configuración de la Aplicación")
        layout_app = QFormLayout(grupo_app)
        
        self.campo_nombre_empresa = QLineEdit()
        self.campo_nombre_empresa.setPlaceholderText("Nombre de su empresa")
        layout_app.addRow("Nombre de la empresa:", self.campo_nombre_empresa)
        
        self.combo_idioma = QComboBox()
        self.combo_idioma.addItems(["Español", "English", "Français"])
        layout_app.addRow("Idioma:", self.combo_idioma)
        
        self.combo_moneda = QComboBox()
        self.combo_moneda.addItems(["EUR (€)", "USD ($)", "GBP (£)"])
        layout_app.addRow("Moneda:", self.combo_moneda)
        
        layout.addWidget(grupo_app)
        
        # Configuración de ventanas
        grupo_ventanas = QGroupBox("🪟 Configuración de Ventanas")
        layout_ventanas = QFormLayout(grupo_ventanas)
        
        self.check_maximizar_ventanas = QCheckBox("Maximizar ventanas automáticamente")
        self.check_maximizar_ventanas.setChecked(True)
        layout_ventanas.addRow(self.check_maximizar_ventanas)
        
        self.check_recordar_posicion = QCheckBox("Recordar posición de ventanas")
        layout_ventanas.addRow(self.check_recordar_posicion)
        
        self.check_animaciones = QCheckBox("Habilitar animaciones")
        self.check_animaciones.setChecked(True)
        layout_ventanas.addRow(self.check_animaciones)
        
        layout.addWidget(grupo_ventanas)
        
        # Configuración de guardado
        grupo_guardado = QGroupBox("💾 Configuración de Guardado")
        layout_guardado = QFormLayout(grupo_guardado)
        
        self.check_autoguardado = QCheckBox("Habilitar autoguardado")
        self.check_autoguardado.setChecked(True)
        layout_guardado.addRow(self.check_autoguardado)
        
        self.spin_intervalo_guardado = QSpinBox()
        self.spin_intervalo_guardado.setRange(1, 60)
        self.spin_intervalo_guardado.setValue(5)
        self.spin_intervalo_guardado.setSuffix(" minutos")
        layout_guardado.addRow("Intervalo de autoguardado:", self.spin_intervalo_guardado)
        
        layout.addWidget(grupo_guardado)
        
        layout.addStretch()
        self.tabs.addTab(tab, "🏠 General")
    
    def _crear_tab_temas(self):
        """Crea la pestaña de configuración de temas."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Selector de tema
        grupo_tema = QGroupBox("🎨 Selección de Tema")
        layout_tema = QFormLayout(grupo_tema)
        
        self.combo_tema = QComboBox()
        self.combo_tema.addItems([
            "🚀 Revolucionario (Recomendado)",
            "💼 Profesional",
            "🌙 Oscuro",
            "☀️ Claro",
            "🎨 Moderno Oscuro",
            "✨ Moderno Claro"
        ])
        layout_tema.addRow("Tema actual:", self.combo_tema)
        
        btn_vista_previa = QPushButton("👁️ Vista Previa")
        btn_vista_previa.clicked.connect(self._mostrar_vista_previa_tema)
        layout_tema.addRow(btn_vista_previa)
        
        layout.addWidget(grupo_tema)
        
        # Personalización
        grupo_personalizacion = QGroupBox("🎨 Personalización")
        layout_personalizacion = QFormLayout(grupo_personalizacion)
        
        self.combo_fuente = QComboBox()
        self.combo_fuente.addItems(["Segoe UI", "Arial", "Calibri", "Tahoma"])
        layout_personalizacion.addRow("Fuente:", self.combo_fuente)
        
        self.spin_tamano_fuente = QSpinBox()
        self.spin_tamano_fuente.setRange(8, 24)
        self.spin_tamano_fuente.setValue(14)
        layout_personalizacion.addRow("Tamaño de fuente:", self.spin_tamano_fuente)
        
        layout.addWidget(grupo_personalizacion)
        
        layout.addStretch()
        self.tabs.addTab(tab, "🎨 Temas")
    
    def _crear_tab_base_datos(self):
        """Crea la pestaña de configuración de base de datos."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Configuración de conexión
        grupo_conexion = QGroupBox("🗄️ Configuración de Base de Datos")
        layout_conexion = QFormLayout(grupo_conexion)
        
        self.combo_tipo_bd = QComboBox()
        self.combo_tipo_bd.addItems(["SQLite (Local)", "PostgreSQL", "MySQL"])
        layout_conexion.addRow("Tipo de base de datos:", self.combo_tipo_bd)
        
        self.campo_ruta_bd = QLineEdit()
        self.campo_ruta_bd.setText("data/pro2000.db")
        layout_conexion.addRow("Ruta de la base de datos:", self.campo_ruta_bd)
        
        btn_examinar_bd = QPushButton("📁 Examinar...")
        btn_examinar_bd.clicked.connect(self._examinar_base_datos)
        layout_conexion.addRow(btn_examinar_bd)
        
        layout.addWidget(grupo_conexion)
        
        # Configuración de rendimiento
        grupo_rendimiento_bd = QGroupBox("⚡ Rendimiento de Base de Datos")
        layout_rendimiento_bd = QFormLayout(grupo_rendimiento_bd)
        
        self.spin_pool_conexiones = QSpinBox()
        self.spin_pool_conexiones.setRange(1, 50)
        self.spin_pool_conexiones.setValue(10)
        layout_rendimiento_bd.addRow("Pool de conexiones:", self.spin_pool_conexiones)
        
        self.spin_timeout_consulta = QSpinBox()
        self.spin_timeout_consulta.setRange(5, 300)
        self.spin_timeout_consulta.setValue(30)
        self.spin_timeout_consulta.setSuffix(" segundos")
        layout_rendimiento_bd.addRow("Timeout de consulta:", self.spin_timeout_consulta)
        
        layout.addWidget(grupo_rendimiento_bd)
        
        # Mantenimiento
        grupo_mantenimiento = QGroupBox("🔧 Mantenimiento")
        layout_mantenimiento = QVBoxLayout(grupo_mantenimiento)
        
        btn_optimizar_bd = QPushButton("🚀 Optimizar Base de Datos")
        btn_optimizar_bd.clicked.connect(self._optimizar_base_datos)
        layout_mantenimiento.addWidget(btn_optimizar_bd)
        
        btn_verificar_bd = QPushButton("🔍 Verificar Integridad")
        btn_verificar_bd.clicked.connect(self._verificar_integridad)
        layout_mantenimiento.addWidget(btn_verificar_bd)
        
        layout.addWidget(grupo_mantenimiento)
        
        layout.addStretch()
        self.tabs.addTab(tab, "🗄️ Base de Datos")
    
    def _crear_tab_optimizacion(self):
        """Crea la pestaña de configuración de optimización."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Configuración de cortes
        grupo_cortes = QGroupBox("✂️ Optimización de Cortes")
        layout_cortes = QFormLayout(grupo_cortes)
        
        self.spin_longitud_barra_defecto = QDoubleSpinBox()
        self.spin_longitud_barra_defecto.setRange(1000, 10000)
        self.spin_longitud_barra_defecto.setValue(6000)
        self.spin_longitud_barra_defecto.setSuffix(" mm")
        layout_cortes.addRow("Longitud de barra por defecto:", self.spin_longitud_barra_defecto)
        
        self.spin_grosor_corte_defecto = QDoubleSpinBox()
        self.spin_grosor_corte_defecto.setRange(0.5, 10)
        self.spin_grosor_corte_defecto.setValue(3.0)
        self.spin_grosor_corte_defecto.setSuffix(" mm")
        layout_cortes.addRow("Grosor de corte por defecto:", self.spin_grosor_corte_defecto)
        
        self.combo_algoritmo_defecto = QComboBox()
        self.combo_algoritmo_defecto.addItems([
            "Best Fit Decreasing (BFD)",
            "First Fit Decreasing (FFD)",
            "Next Fit Decreasing (NFD)"
        ])
        layout_cortes.addRow("Algoritmo por defecto:", self.combo_algoritmo_defecto)
        
        layout.addWidget(grupo_cortes)
        
        # Configuración de cálculos
        grupo_calculos = QGroupBox("🧮 Configuración de Cálculos")
        layout_calculos = QFormLayout(grupo_calculos)
        
        self.spin_precision_decimal = QSpinBox()
        self.spin_precision_decimal.setRange(0, 6)
        self.spin_precision_decimal.setValue(2)
        layout_calculos.addRow("Precisión decimal:", self.spin_precision_decimal)
        
        self.check_redondeo_automatico = QCheckBox("Redondeo automático")
        self.check_redondeo_automatico.setChecked(True)
        layout_calculos.addRow(self.check_redondeo_automatico)
        
        layout.addWidget(grupo_calculos)

        # Acciones de optimización
        grupo_acciones_opt = QGroupBox("🛠️ Herramientas de Optimización")
        layout_acciones_opt = QVBoxLayout(grupo_acciones_opt)

        btn_abrir_optimizacion = QPushButton("⚡ Abrir Optimización de Cortes")
        btn_abrir_optimizacion.clicked.connect(self._abrir_optimizacion)
        layout_acciones_opt.addWidget(btn_abrir_optimizacion)

        btn_configurar_materiales = QPushButton("📦 Configurar Materiales por Defecto")
        btn_configurar_materiales.clicked.connect(self._configurar_materiales)
        layout_acciones_opt.addWidget(btn_configurar_materiales)

        layout.addWidget(grupo_acciones_opt)

        layout.addStretch()
        self.tabs.addTab(tab, "⚡ Optimización")

    def _crear_tab_rendimiento(self):
        """Crea la pestaña de configuración de rendimiento."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Configuración de memoria
        grupo_memoria = QGroupBox("🧠 Gestión de Memoria")
        layout_memoria = QFormLayout(grupo_memoria)

        self.spin_cache_size = QSpinBox()
        self.spin_cache_size.setRange(32, 1024)
        self.spin_cache_size.setValue(128)
        self.spin_cache_size.setSuffix(" MB")
        layout_memoria.addRow("Tamaño de caché:", self.spin_cache_size)

        self.check_lazy_loading = QCheckBox("Carga perezosa de datos")
        self.check_lazy_loading.setChecked(True)
        layout_memoria.addRow(self.check_lazy_loading)

        layout.addWidget(grupo_memoria)

        # Configuración de hilos
        grupo_hilos = QGroupBox("⚡ Procesamiento")
        layout_hilos = QFormLayout(grupo_hilos)

        self.spin_max_workers = QSpinBox()
        self.spin_max_workers.setRange(1, 16)
        self.spin_max_workers.setValue(4)
        layout_hilos.addRow("Máximo de hilos:", self.spin_max_workers)

        layout.addWidget(grupo_hilos)

        layout.addStretch()
        self.tabs.addTab(tab, "🚀 Rendimiento")

    def _crear_tab_seguridad(self):
        """Crea la pestaña de configuración de seguridad."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Configuración de sesión
        grupo_sesion = QGroupBox("🔐 Configuración de Sesión")
        layout_sesion = QFormLayout(grupo_sesion)

        self.spin_timeout_sesion = QSpinBox()
        self.spin_timeout_sesion.setRange(30, 1440)
        self.spin_timeout_sesion.setValue(480)
        self.spin_timeout_sesion.setSuffix(" minutos")
        layout_sesion.addRow("Timeout de sesión:", self.spin_timeout_sesion)

        self.spin_max_intentos = QSpinBox()
        self.spin_max_intentos.setRange(1, 10)
        self.spin_max_intentos.setValue(3)
        layout_sesion.addRow("Máximo intentos de login:", self.spin_max_intentos)

        layout.addWidget(grupo_sesion)

        # Configuración de contraseñas
        grupo_passwords = QGroupBox("🔑 Configuración de Contraseñas")
        layout_passwords = QFormLayout(grupo_passwords)

        self.spin_min_longitud = QSpinBox()
        self.spin_min_longitud.setRange(4, 20)
        self.spin_min_longitud.setValue(6)
        layout_passwords.addRow("Longitud mínima:", self.spin_min_longitud)

        self.check_encriptacion = QCheckBox("Habilitar encriptación")
        self.check_encriptacion.setChecked(True)
        layout_passwords.addRow(self.check_encriptacion)

        layout.addWidget(grupo_passwords)

        layout.addStretch()
        self.tabs.addTab(tab, "🔐 Seguridad")

    def _crear_tab_respaldos(self):
        """Crea la pestaña de configuración de respaldos."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Configuración de respaldos automáticos
        grupo_auto = QGroupBox("🔄 Respaldos Automáticos")
        layout_auto = QFormLayout(grupo_auto)

        self.check_respaldos_auto = QCheckBox("Habilitar respaldos automáticos")
        self.check_respaldos_auto.setChecked(True)
        layout_auto.addRow(self.check_respaldos_auto)

        self.spin_intervalo_respaldo = QSpinBox()
        self.spin_intervalo_respaldo.setRange(1, 168)
        self.spin_intervalo_respaldo.setValue(24)
        self.spin_intervalo_respaldo.setSuffix(" horas")
        layout_auto.addRow("Intervalo de respaldo:", self.spin_intervalo_respaldo)

        self.spin_max_respaldos = QSpinBox()
        self.spin_max_respaldos.setRange(1, 100)
        self.spin_max_respaldos.setValue(30)
        layout_auto.addRow("Máximo de respaldos:", self.spin_max_respaldos)

        layout.addWidget(grupo_auto)

        # Configuración de ubicación
        grupo_ubicacion = QGroupBox("📁 Ubicación de Respaldos")
        layout_ubicacion = QFormLayout(grupo_ubicacion)

        self.campo_ruta_respaldos = QLineEdit()
        self.campo_ruta_respaldos.setText("data/backup")
        layout_ubicacion.addRow("Carpeta de respaldos:", self.campo_ruta_respaldos)

        btn_examinar_respaldos = QPushButton("📁 Examinar...")
        btn_examinar_respaldos.clicked.connect(self._examinar_respaldos)
        layout_ubicacion.addRow(btn_examinar_respaldos)

        layout.addWidget(grupo_ubicacion)

        # Acciones de respaldo
        grupo_acciones = QGroupBox("🛠️ Acciones de Respaldo")
        layout_acciones = QVBoxLayout(grupo_acciones)

        btn_crear_respaldo = QPushButton("💾 Crear Respaldo Ahora")
        btn_crear_respaldo.clicked.connect(self._crear_respaldo_manual)
        layout_acciones.addWidget(btn_crear_respaldo)

        btn_restaurar = QPushButton("🔄 Restaurar desde Respaldo")
        btn_restaurar.clicked.connect(self._restaurar_respaldo)
        layout_acciones.addWidget(btn_restaurar)

        layout.addWidget(grupo_acciones)

        layout.addStretch()
        self.tabs.addTab(tab, "💾 Respaldos")

    def _crear_tab_avanzado(self):
        """Crea la pestaña de configuración avanzada."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Configuración de logging
        grupo_logging = QGroupBox("📝 Configuración de Logs")
        layout_logging = QFormLayout(grupo_logging)

        self.combo_nivel_log = QComboBox()
        self.combo_nivel_log.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.combo_nivel_log.setCurrentText("INFO")
        layout_logging.addRow("Nivel de logging:", self.combo_nivel_log)

        self.check_log_archivo = QCheckBox("Guardar logs en archivo")
        self.check_log_archivo.setChecked(True)
        layout_logging.addRow(self.check_log_archivo)

        self.check_log_consola = QCheckBox("Mostrar logs en consola")
        self.check_log_consola.setChecked(True)
        layout_logging.addRow(self.check_log_consola)

        layout.addWidget(grupo_logging)

        # Configuración de desarrollo
        grupo_desarrollo = QGroupBox("🔧 Configuración de Desarrollo")
        layout_desarrollo = QFormLayout(grupo_desarrollo)

        self.check_modo_debug = QCheckBox("Modo debug")
        layout_desarrollo.addRow(self.check_modo_debug)

        self.check_mostrar_sql = QCheckBox("Mostrar consultas SQL")
        layout_desarrollo.addRow(self.check_mostrar_sql)

        layout.addWidget(grupo_desarrollo)

        # Acciones del sistema
        grupo_sistema = QGroupBox("🔧 Acciones del Sistema")
        layout_sistema = QVBoxLayout(grupo_sistema)

        btn_limpiar_cache = QPushButton("🧹 Limpiar Caché")
        btn_limpiar_cache.clicked.connect(self._limpiar_cache)
        layout_sistema.addWidget(btn_limpiar_cache)

        btn_reiniciar_config = QPushButton("🔄 Restaurar Configuración por Defecto")
        btn_reiniciar_config.clicked.connect(self._restaurar_configuracion_defecto)
        layout_sistema.addWidget(btn_reiniciar_config)

        layout.addWidget(grupo_sistema)

        layout.addStretch()
        self.tabs.addTab(tab, "🔧 Avanzado")

    def _examinar_respaldos(self):
        """Abre diálogo para seleccionar carpeta de respaldos."""
        carpeta = QFileDialog.getExistingDirectory(self, "Seleccionar Carpeta de Respaldos")
        if carpeta:
            self.campo_ruta_respaldos.setText(carpeta)

    def _crear_respaldo_manual(self):
        """Crea un respaldo manual."""
        QMessageBox.information(self, "Respaldo", "Respaldo creado correctamente.")

    def _restaurar_respaldo(self):
        """Restaura desde un respaldo."""
        archivo, _ = QFileDialog.getOpenFileName(
            self, "Seleccionar Respaldo", "", "Respaldos (*.backup *.bak)"
        )
        if archivo:
            QMessageBox.information(self, "Restauración", "Respaldo restaurado correctamente.")

    def _limpiar_cache(self):
        """Limpia el caché del sistema."""
        QMessageBox.information(self, "Caché", "Caché limpiado correctamente.")

    def _restaurar_configuracion_defecto(self):
        """Restaura la configuración por defecto."""
        respuesta = QMessageBox.question(
            self, "Restaurar Configuración",
            "¿Está seguro de que desea restaurar la configuración por defecto?\n"
            "Se perderán todos los cambios personalizados.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        if respuesta == QMessageBox.StandardButton.Yes:
            QMessageBox.information(self, "Configuración", "Configuración restaurada por defecto.")

    def _abrir_optimizacion(self):
        """Abre el módulo de optimización de cortes."""
        try:
            from ui.modulos.optimizacion.optimizacion_cortes_dialog import OptimizacionCortesDialog
            dialog = OptimizacionCortesDialog(self)
            dialog.exec()
        except Exception as e:
            QMessageBox.warning(self, "Error", f"No se pudo abrir el módulo de optimización: {e}")

    def _configurar_materiales(self):
        """Configura materiales por defecto."""
        QMessageBox.information(
            self, "Materiales",
            "Configuración de materiales por defecto.\n"
            "Esta función se implementará en una versión futura."
        )

    def _crear_botones_accion(self, layout_principal):
        """Crea los botones de acción."""
        botones = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel |
            QDialogButtonBox.StandardButton.Apply
        )
        
        botones.button(QDialogButtonBox.StandardButton.Ok).setText("Guardar y Cerrar")
        botones.button(QDialogButtonBox.StandardButton.Cancel).setText("Cancelar")
        botones.button(QDialogButtonBox.StandardButton.Apply).setText("Aplicar")
        
        botones.accepted.connect(self._guardar_y_cerrar)
        botones.rejected.connect(self.reject)
        botones.button(QDialogButtonBox.StandardButton.Apply).clicked.connect(self._aplicar_configuracion)
        
        layout_principal.addWidget(botones)
    
    def _cargar_configuracion(self):
        """Carga la configuración actual."""
        # Aquí cargarías la configuración desde el archivo o base de datos
        pass
    
    def _mostrar_vista_previa_tema(self):
        """Muestra vista previa del tema seleccionado."""
        from ui.themes.theme_selector_dialog import ThemeSelectorDialog
        dialog = ThemeSelectorDialog(self)
        dialog.exec()
    
    def _examinar_base_datos(self):
        """Abre diálogo para seleccionar archivo de base de datos."""
        archivo, _ = QFileDialog.getOpenFileName(
            self, "Seleccionar Base de Datos", "", "Base de Datos (*.db *.sqlite)"
        )
        if archivo:
            self.campo_ruta_bd.setText(archivo)
    
    def _optimizar_base_datos(self):
        """Optimiza la base de datos."""
        QMessageBox.information(self, "Optimización", "Base de datos optimizada correctamente.")
    
    def _verificar_integridad(self):
        """Verifica la integridad de la base de datos."""
        QMessageBox.information(self, "Verificación", "Integridad de la base de datos verificada.")
    
    def _aplicar_configuracion(self):
        """Aplica la configuración sin cerrar el diálogo."""
        self.configuracion_cambiada.emit()
        QMessageBox.information(self, "Configuración", "Configuración aplicada correctamente.")
    
    def _guardar_y_cerrar(self):
        """Guarda la configuración y cierra el diálogo."""
        self._aplicar_configuracion()
        self.accept()
