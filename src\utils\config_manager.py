"""
Gestor de configuración para PRO-2000
Maneja la configuración de la aplicación de forma centralizada
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, Optional


class ConfigManager:
    """Gestor centralizado de configuración"""
    
    def __init__(self):
        self.config_dir = Path.home() / ".pro2000"
        self.config_file = self.config_dir / "config.json"
        self.default_config = {
            "database": {
                "type": "sqlite",
                "path": "pro2000.db"
            },
            "ui": {
                "theme": "modern",
                "language": "es",
                "window_size": [1200, 800],
                "window_maximized": False
            },
            "backup": {
                "enabled": True,
                "interval_hours": 24,
                "max_backups": 30,
                "path": "backups"
            },
            "logging": {
                "level": "INFO",
                "file_enabled": True,
                "console_enabled": True,
                "max_file_size_mb": 10,
                "backup_count": 5
            },
            "features": {
                "auto_save": True,
                "optimization_enabled": True,
                "reports_enabled": True,
                "advanced_features": True
            }
        }
        self._config = None
        self._ensure_config_dir()
        self._load_config()
    
    def _ensure_config_dir(self):
        """Asegura que el directorio de configuración existe"""
        self.config_dir.mkdir(exist_ok=True)
    
    def _load_config(self):
        """Carga la configuración desde el archivo"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                # Fusionar configuración por defecto con la cargada
                self._config = self._merge_configs(self.default_config, loaded_config)
            else:
                self._config = self.default_config.copy()
                self._save_config()
        except Exception as e:
            print(f"Error al cargar configuración: {e}")
            self._config = self.default_config.copy()
    
    def _merge_configs(self, default: Dict, loaded: Dict) -> Dict:
        """Fusiona configuraciones manteniendo la estructura por defecto"""
        result = default.copy()
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        return result
    
    def _save_config(self):
        """Guarda la configuración actual al archivo"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error al guardar configuración: {e}")

    def save(self):
        """Método público para guardar la configuración"""
        self._save_config()
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Obtiene un valor de configuración usando notación de punto
        Ejemplo: get('ui.theme') -> 'modern'
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any, save: bool = True):
        """
        Establece un valor de configuración usando notación de punto
        Ejemplo: set('ui.theme', 'dark')
        """
        keys = key.split('.')
        config = self._config
        
        # Navegar hasta el penúltimo nivel
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Establecer el valor
        config[keys[-1]] = value
        
        if save:
            self._save_config()
    
    def get_section(self, section: str) -> Dict:
        """Obtiene una sección completa de configuración"""
        return self._config.get(section, {})
    
    def update_section(self, section: str, values: Dict, save: bool = True):
        """Actualiza una sección completa de configuración"""
        if section not in self._config:
            self._config[section] = {}
        
        self._config[section].update(values)
        
        if save:
            self._save_config()
    
    def reset_to_defaults(self):
        """Restaura la configuración a los valores por defecto"""
        self._config = self.default_config.copy()
        self._save_config()
    
    def backup_config(self) -> str:
        """Crea una copia de seguridad de la configuración actual"""
        backup_file = self.config_dir / f"config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
            return str(backup_file)
        except Exception as e:
            print(f"Error al crear backup de configuración: {e}")
            return ""
    
    def restore_config(self, backup_path: str) -> bool:
        """Restaura la configuración desde un backup"""
        try:
            with open(backup_path, 'r', encoding='utf-8') as f:
                backup_config = json.load(f)
            
            self._config = self._merge_configs(self.default_config, backup_config)
            self._save_config()
            return True
        except Exception as e:
            print(f"Error al restaurar configuración: {e}")
            return False
    
    @property
    def config_path(self) -> str:
        """Ruta del archivo de configuración"""
        return str(self.config_file)
    
    @property
    def all_config(self) -> Dict:
        """Toda la configuración actual"""
        return self._config.copy()


# Instancia global del gestor de configuración
config_manager = ConfigManager()


def get_config(key: str, default: Any = None) -> Any:
    """Función de conveniencia para obtener configuración"""
    return config_manager.get(key, default)


def set_config(key: str, value: Any, save: bool = True):
    """Función de conveniencia para establecer configuración"""
    config_manager.set(key, value, save)


if __name__ == "__main__":
    # Ejemplo de uso
    from datetime import datetime
    
    print("🔧 Probando ConfigManager...")
    
    # Obtener configuración
    theme = get_config('ui.theme')
    print(f"Tema actual: {theme}")
    
    # Establecer configuración
    set_config('ui.theme', 'dark')
    print(f"Nuevo tema: {get_config('ui.theme')}")
    
    # Restaurar tema original
    set_config('ui.theme', theme)
    
    print("✅ ConfigManager funcionando correctamente")
