"""
Script de migración para añadir la tabla tipos_accesorios y la columna tipo_accesorio_id a la tabla accesorios
"""
import sqlite3
import os

def migrate_database():
    """Añade la tabla tipos_accesorios y la columna tipo_accesorio_id a la tabla accesorios."""

    # Buscar la base de datos en varias ubicaciones posibles
    possible_paths = [
        "data/pro2000.db",
        "../data/pro2000.db",
        "../../data/pro2000.db",
        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'pro2000.db')
    ]

    db_path = None
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            break

    if not db_path:
        print("❌ No se pudo encontrar la base de datos pro2000.db")
        return

    print(f"📍 Usando base de datos: {db_path}")

    conn = None
    try:
        # Conectar a la base de datos
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 1. Crear tabla tipos_accesorios si no existe
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tipos_accesorios (
                id INTEGER PRIMARY KEY,
                codigo VARCHAR(10) UNIQUE NOT NULL,
                nombre VARCHAR(50) NOT NULL,
                descripcion VARCHAR(200),
                activo BOOLEAN DEFAULT 1,
                fecha_creacion DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ Tabla tipos_accesorios creada o ya existe.")

        # 2. Verificar si la columna tipo_accesorio_id ya existe en accesorios
        cursor.execute("PRAGMA table_info(accesorios)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'tipo_accesorio_id' not in columns:
            print("Añadiendo columna tipo_accesorio_id a la tabla accesorios...")

            # Añadir la columna
            cursor.execute("""
                ALTER TABLE accesorios
                ADD COLUMN tipo_accesorio_id INTEGER
                REFERENCES tipos_accesorios(id)
            """)

            print("✅ Columna tipo_accesorio_id añadida exitosamente!")
        else:
            print("✅ La columna tipo_accesorio_id ya existe en la tabla accesorios.")

        # 3. Insertar tipos predefinidos si la tabla está vacía
        cursor.execute("SELECT COUNT(*) FROM tipos_accesorios")
        count = cursor.fetchone()[0]
        
        if count == 0:
            print("Insertando tipos de accesorios predefinidos...")
            tipos_predefinidos = [
                ("HER", "Herraje", "Herrajes diversos"),
                ("RUE", "Rueda", "Ruedas y rodamientos"),
                ("CIE", "Cierre", "Cierres y cerraduras"),
                ("POM", "Pomo", "Pomos y tiradores"),
                ("MAN", "Manivela", "Manivelas y manillas"),
                ("GUI", "Guía", "Guías y carriles"),
                ("JAM", "Jamba", "Jambas y marcos"),
                ("JUN", "Junquillo", "Junquillos y perfiles"),
                ("OTR", "Otro", "Otros accesorios")
            ]
            
            cursor.executemany("""
                INSERT INTO tipos_accesorios (codigo, nombre, descripcion)
                VALUES (?, ?, ?)
            """, tipos_predefinidos)
            
            print(f"✅ {len(tipos_predefinidos)} tipos de accesorios predefinidos insertados.")

        conn.commit()
        print("✅ Migración completada exitosamente!")

    except Exception as e:
        if conn:
            conn.rollback()
        print(f"❌ Error durante la migración: {e}")
        raise

    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    migrate_database()
