"""
Utilidades para PRO-2000
Módulo con sistemas de soporte y herramientas auxiliares
"""

from .error_handler import (
    error_handler,
    handle_exception,
    handle_database_errors,
    handle_file_errors,
    handle_ui_errors,
    safe_import,
    ValidationError,
    ErrorLevel,
    ErrorCategory,
    log_info,
    log_warning,
    log_error
)

from .backup_manager import (
    BackupManager,
    get_backup_manager
)

__all__ = [
    'error_handler',
    'handle_exception',
    'handle_database_errors',
    'handle_file_errors',
    'handle_ui_errors',
    'safe_import',
    'ValidationError',
    'ErrorLevel',
    'ErrorCategory',
    'log_info',
    'log_warning',
    'log_error',
    'BackupManager',
    'get_backup_manager'
]
