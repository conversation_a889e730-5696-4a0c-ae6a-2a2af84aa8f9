"""
Sistema de Ribbon moderno para el diálogo de selección de componentes.
Inspirado en AutoCAD, LogiKal y Office Ribbon.
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QGroupBox, 
    QPushButton, QLabel, QComboBox, QSpinBox, QDoubleSpinBox,
    QCheckBox, QFrame, QSizePolicy, QToolButton, QButtonGroup,
    QGridLayout, QScrollArea
)
from PyQt6.QtCore import Qt, pyqtSignal, QSize
from PyQt6.QtGui import QFont, QIcon, QPalette, QColor

class RibbonButton(QPushButton):
    """Botón especializado para el ribbon."""
    
    def __init__(self, text="", icon=None, large=True):
        super().__init__()
        self.large = large
        self.setup_button(text, icon)
    
    def setup_button(self, text, icon):
        """Configura el botón según el estilo ribbon."""
        if self.large:
            # Botón grande (icono arriba, texto abajo)
            self.setMinimumSize(80, 60)
            self.setMaximumSize(100, 80)
            self.setText(text)
            if icon:
                self.setIcon(icon)
                self.setIconSize(QSize(32, 32))
            self.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextUnderIcon)
        else:
            # Botón pequeño (icono a la izquierda, texto a la derecha)
            self.setMinimumSize(60, 25)
            self.setMaximumSize(120, 30)
            self.setText(text)
            if icon:
                self.setIcon(icon)
                self.setIconSize(QSize(16, 16))
            self.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextBesideIcon)
        
        # Estilo común
        self.setStyleSheet("""
            RibbonButton {
                border: 1px solid transparent;
                border-radius: 3px;
                padding: 2px;
                text-align: center;
                background-color: transparent;
            }
            RibbonButton:hover {
                border: 1px solid #c0c0c0;
                background-color: #e5f3ff;
            }
            RibbonButton:pressed {
                background-color: #cce8ff;
                border: 1px solid #0078d4;
            }
        """)

class RibbonGroup(QGroupBox):
    """Grupo de controles en el ribbon."""
    
    def __init__(self, title=""):
        super().__init__(title)
        self.setFixedHeight(100)
        self.setMinimumWidth(120)
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(5, 15, 5, 5)
        self.layout.setSpacing(3)
        
        # Estilo del grupo
        self.setStyleSheet("""
            RibbonGroup {
                font-size: 11px;
                font-weight: normal;
                border: 1px solid #d0d0d0;
                border-radius: 3px;
                margin-top: 10px;
                padding-top: 5px;
                background-color: transparent;
            }
            RibbonGroup::title {
                subcontrol-origin: margin;
                left: 5px;
                padding: 0 3px 0 3px;
                color: #666;
                background-color: #f0f0f0;
            }
        """)
    
    def add_large_button(self, text, icon=None, callback=None):
        """Añade un botón grande al grupo."""
        button = RibbonButton(text, icon, large=True)
        if callback:
            button.clicked.connect(callback)
        self.layout.addWidget(button)
        return button
    
    def add_small_button(self, text, icon=None, callback=None):
        """Añade un botón pequeño al grupo."""
        button = RibbonButton(text, icon, large=False)
        if callback:
            button.clicked.connect(callback)
        self.layout.addWidget(button)
        return button
    
    def add_combo(self, items, callback=None):
        """Añade un combo box al grupo."""
        combo = QComboBox()
        combo.addItems(items)
        combo.setMinimumWidth(100)
        combo.setMaximumWidth(150)
        if callback:
            combo.currentTextChanged.connect(callback)
        self.layout.addWidget(combo)
        return combo
    
    def add_spinbox(self, min_val=0, max_val=100, suffix="", callback=None):
        """Añade un spinbox al grupo."""
        spin = QSpinBox()
        spin.setRange(min_val, max_val)
        spin.setSuffix(suffix)
        spin.setMinimumWidth(60)
        spin.setMaximumWidth(80)
        if callback:
            spin.valueChanged.connect(callback)
        self.layout.addWidget(spin)
        return spin
    
    def add_widget(self, widget):
        """Añade un widget personalizado al grupo."""
        self.layout.addWidget(widget)

class RibbonTab(QWidget):
    """Pestaña del ribbon con grupos de controles."""
    
    def __init__(self, name=""):
        super().__init__()
        self.name = name
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(5, 5, 5, 5)
        self.layout.setSpacing(5)
        self.layout.addStretch()  # Para empujar grupos a la izquierda
        
        # Lista de grupos
        self.groups = []
    
    def add_group(self, title):
        """Añade un nuevo grupo al tab."""
        group = RibbonGroup(title)
        self.groups.append(group)
        
        # Insertar antes del stretch
        self.layout.insertWidget(len(self.groups) - 1, group)
        return group

class ModernRibbonWidget(QWidget):
    """Widget principal del ribbon moderno."""
    
    # Señales para comunicación
    marco_changed = pyqtSignal(dict)
    hoja_changed = pyqtSignal(dict)
    cristal_changed = pyqtSignal(dict)
    herrajes_changed = pyqtSignal(dict)
    validar_requested = pyqtSignal()
    produccion_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(130)
        self.setup_ui()
        self.setup_styles()
    
    def setup_ui(self):
        """Configura la interfaz del ribbon."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Crear el tab widget para el ribbon
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        layout.addWidget(self.tab_widget)
        
        # Crear las pestañas del ribbon
        self.create_marco_tab()
        self.create_hoja_tab()
        self.create_cristal_tab()
        self.create_herrajes_tab()
        self.create_validacion_tab()
        self.create_produccion_tab()
    
    def create_marco_tab(self):
        """Crea la pestaña de configuración del marco."""
        tab = RibbonTab("Marco")
        
        # Grupo: Perfil Principal
        grupo_perfil = tab.add_group("Perfil Principal")
        self.combo_perfil_marco = grupo_perfil.add_combo([
            "Cortizo 4500", "Cortizo 4600", "Technal Soleal", "Schüco AWS 70"
        ], self._on_marco_perfil_changed)
        
        # Grupo: Material y Color
        grupo_material = tab.add_group("Material")
        self.combo_material = grupo_material.add_combo([
            "PVC", "Aluminio", "Madera", "Acero"
        ], self._on_marco_material_changed)
        
        self.combo_color = grupo_material.add_combo([
            "Blanco", "Gris Antracita", "Marrón", "Negro", "RAL Personalizado"
        ], self._on_marco_color_changed)
        
        # Grupo: Dimensiones
        grupo_dimensiones = tab.add_group("Dimensiones")
        self.spin_grosor = grupo_dimensiones.add_spinbox(50, 120, "mm", self._on_marco_grosor_changed)
        self.spin_grosor.setValue(70)
        
        # Grupo: Opciones Avanzadas
        grupo_opciones = tab.add_group("Opciones")
        grupo_opciones.add_large_button("🔧\nAvanzado", callback=self._on_marco_avanzado)
        grupo_opciones.add_large_button("📐\nMedidas", callback=self._on_marco_medidas)
        
        self.tab_widget.addTab(tab, "🏗️ MARCO")
    
    def create_hoja_tab(self):
        """Crea la pestaña de configuración de la hoja."""
        tab = RibbonTab("Hoja")
        
        # Grupo: Tipo de Apertura
        grupo_apertura = tab.add_group("Tipo de Apertura")
        grupo_apertura.add_large_button("🔷\nFija", callback=lambda: self._on_tipo_apertura_changed("fija"))
        grupo_apertura.add_large_button("🚪\nBatiente", callback=lambda: self._on_tipo_apertura_changed("batiente"))
        grupo_apertura.add_large_button("↔️\nCorredera", callback=lambda: self._on_tipo_apertura_changed("corredera"))
        
        # Grupo: Configuración
        grupo_config = tab.add_group("Configuración")
        self.spin_num_hojas = grupo_config.add_spinbox(1, 4, " hojas", self._on_hoja_num_changed)
        self.combo_sentido = grupo_config.add_combo([
            "Derecha Interior", "Izquierda Interior", "Derecha Exterior", "Izquierda Exterior"
        ], self._on_hoja_sentido_changed)
        
        # Grupo: Características
        grupo_caracteristicas = tab.add_group("Características")
        grupo_caracteristicas.add_large_button("🌬️\nMicro\nVentilación", callback=self._on_microventilacion)
        grupo_caracteristicas.add_large_button("👶\nAnti\nPalmas", callback=self._on_antipalmas)
        
        self.tab_widget.addTab(tab, "🚪 HOJA")
    
    def create_cristal_tab(self):
        """Crea la pestaña de configuración del cristal."""
        tab = RibbonTab("Cristal")
        
        # Grupo: Tipo de Cristal
        grupo_tipo = tab.add_group("Tipo de Cristal")
        self.combo_tipo_cristal = grupo_tipo.add_combo([
            "Simple 4mm", "Simple 6mm", "Doble 4+12+4", "Doble 6+12+6", "Triple 4+12+4+12+4"
        ], self._on_cristal_tipo_changed)
        
        # Grupo: Tratamientos
        grupo_tratamientos = tab.add_group("Tratamientos")
        grupo_tratamientos.add_large_button("🌡️\nBajo\nEmisivo", callback=self._on_bajo_emisivo)
        grupo_tratamientos.add_large_button("☀️\nControl\nSolar", callback=self._on_control_solar)
        grupo_tratamientos.add_large_button("🔇\nAcústico", callback=self._on_acustico)
        
        # Grupo: Seguridad
        grupo_seguridad = tab.add_group("Seguridad")
        grupo_seguridad.add_large_button("🛡️\nLaminado", callback=self._on_laminado)
        grupo_seguridad.add_large_button("💎\nTemplado", callback=self._on_templado)
        
        self.tab_widget.addTab(tab, "🔷 CRISTAL")
    
    def create_herrajes_tab(self):
        """Crea la pestaña de herrajes y accesorios."""
        tab = RibbonTab("Herrajes")
        
        # Grupo: Apertura
        grupo_apertura = tab.add_group("Apertura")
        self.combo_manivela = grupo_apertura.add_combo([
            "Sin Manivela", "Manivela Estándar", "Manivela con Llave", "Pomo", "Tirador"
        ], self._on_herraje_manivela_changed)
        
        # Grupo: Seguridad
        grupo_seguridad = tab.add_group("Seguridad")
        self.combo_cerradura = grupo_seguridad.add_combo([
            "Sin Cerradura", "Simple", "Multipunto", "Seguridad", "Electrónica"
        ], self._on_herraje_cerradura_changed)
        
        # Grupo: Bisagras
        grupo_bisagras = tab.add_group("Bisagras")
        self.combo_bisagras = grupo_bisagras.add_combo([
            "Estándar", "Reforzadas", "Ocultas", "Regulables"
        ], self._on_herraje_bisagras_changed)
        self.spin_num_bisagras = grupo_bisagras.add_spinbox(2, 6, " uds", self._on_herraje_num_bisagras_changed)
        self.spin_num_bisagras.setValue(3)
        
        self.tab_widget.addTab(tab, "🔧 HERRAJES")
    
    def create_validacion_tab(self):
        """Crea la pestaña de validación."""
        tab = RibbonTab("Validación")
        
        # Grupo: Verificaciones
        grupo_verificaciones = tab.add_group("Verificaciones")
        grupo_verificaciones.add_large_button("✅\nValidar\nTodo", callback=self._on_validar_todo)
        grupo_verificaciones.add_large_button("🔍\nRevisar\nErrores", callback=self._on_revisar_errores)
        
        # Grupo: Normativas
        grupo_normativas = tab.add_group("Normativas")
        grupo_normativas.add_large_button("📋\nCTE\nDB-HE", callback=self._on_validar_cte)
        grupo_normativas.add_large_button("🌡️\nTérmica", callback=self._on_validar_termica)
        grupo_normativas.add_large_button("🔇\nAcústica", callback=self._on_validar_acustica)
        
        # Grupo: Informes
        grupo_informes = tab.add_group("Informes")
        grupo_informes.add_large_button("📊\nInforme\nTécnico", callback=self._on_generar_informe)
        grupo_informes.add_large_button("📋\nFicha\nProducto", callback=self._on_generar_ficha)
        
        self.tab_widget.addTab(tab, "✅ VALIDACIÓN")
    
    def create_produccion_tab(self):
        """Crea la pestaña de producción."""
        tab = RibbonTab("Producción")
        
        # Grupo: Optimización
        grupo_optimizacion = tab.add_group("Optimización")
        grupo_optimizacion.add_large_button("📏\nOptimizar\nPerfiles", callback=self._on_optimizar_perfiles)
        grupo_optimizacion.add_large_button("🔷\nOptimizar\nCristal", callback=self._on_optimizar_cristal)
        
        # Grupo: Documentación
        grupo_documentacion = tab.add_group("Documentación")
        grupo_documentacion.add_large_button("📋\nLista\nMateriales", callback=self._on_lista_materiales)
        grupo_documentacion.add_large_button("🏷️\nEtiquetas", callback=self._on_generar_etiquetas)
        grupo_documentacion.add_large_button("📐\nPlanos", callback=self._on_generar_planos)
        
        # Grupo: Exportar
        grupo_exportar = tab.add_group("Exportar")
        grupo_exportar.add_large_button("💾\nGuardar\nConfig", callback=self._on_guardar_config)
        grupo_exportar.add_large_button("📧\nEnviar\nPresup.", callback=self._on_enviar_presupuesto)
        
        self.tab_widget.addTab(tab, "🏭 PRODUCCIÓN")
    
    def setup_styles(self):
        """Configura los estilos del ribbon."""
        self.setStyleSheet("""
            ModernRibbonWidget {
                background-color: #f0f0f0;
                border-bottom: 1px solid #d0d0d0;
            }
            
            QTabWidget::pane {
                border: 1px solid #d0d0d0;
                border-top: none;
                background-color: #f8f8f8;
            }
            
            QTabBar::tab {
                background-color: #e8e8e8;
                border: 1px solid #d0d0d0;
                border-bottom: none;
                padding: 6px 12px;
                margin-right: 1px;
                font-weight: bold;
                font-size: 11px;
            }
            
            QTabBar::tab:selected {
                background-color: #f8f8f8;
                border-bottom: 1px solid #f8f8f8;
            }
            
            QTabBar::tab:hover {
                background-color: #e0e0e0;
            }
            
            QComboBox {
                border: 1px solid #c0c0c0;
                border-radius: 2px;
                padding: 2px 5px;
                background-color: white;
                min-height: 20px;
            }
            
            QComboBox:focus {
                border: 2px solid #0078d4;
            }
            
            QSpinBox {
                border: 1px solid #c0c0c0;
                border-radius: 2px;
                padding: 2px;
                background-color: white;
                min-height: 20px;
            }
            
            QSpinBox:focus {
                border: 2px solid #0078d4;
            }
        """)
    
    # Métodos de callback (placeholder - se conectarán con la lógica principal)
    def _on_marco_perfil_changed(self, value):
        self.marco_changed.emit({"perfil": value})
    
    def _on_marco_material_changed(self, value):
        self.marco_changed.emit({"material": value})
    
    def _on_marco_color_changed(self, value):
        self.marco_changed.emit({"color": value})
    
    def _on_marco_grosor_changed(self, value):
        self.marco_changed.emit({"grosor": value})
    
    def _on_marco_avanzado(self):
        print("🔧 Configuración avanzada del marco")
    
    def _on_marco_medidas(self):
        print("📐 Configuración de medidas del marco")
    
    def _on_tipo_apertura_changed(self, tipo):
        self.hoja_changed.emit({"tipo_apertura": tipo})
    
    def _on_hoja_num_changed(self, value):
        self.hoja_changed.emit({"num_hojas": value})
    
    def _on_hoja_sentido_changed(self, value):
        self.hoja_changed.emit({"sentido": value})
    
    def _on_microventilacion(self):
        print("🌬️ Microventilación activada")
    
    def _on_antipalmas(self):
        print("👶 Protección antipalmas activada")
    
    def _on_cristal_tipo_changed(self, value):
        self.cristal_changed.emit({"tipo": value})
    
    def _on_bajo_emisivo(self):
        print("🌡️ Bajo emisivo activado")
    
    def _on_control_solar(self):
        print("☀️ Control solar activado")
    
    def _on_acustico(self):
        print("🔇 Cristal acústico activado")
    
    def _on_laminado(self):
        print("🛡️ Cristal laminado activado")
    
    def _on_templado(self):
        print("💎 Cristal templado activado")
    
    def _on_herraje_manivela_changed(self, value):
        self.herrajes_changed.emit({"manivela": value})
    
    def _on_herraje_cerradura_changed(self, value):
        self.herrajes_changed.emit({"cerradura": value})
    
    def _on_herraje_bisagras_changed(self, value):
        self.herrajes_changed.emit({"bisagras": value})
    
    def _on_herraje_num_bisagras_changed(self, value):
        self.herrajes_changed.emit({"num_bisagras": value})
    
    def _on_validar_todo(self):
        self.validar_requested.emit()
        print("✅ Validando toda la configuración")
    
    def _on_revisar_errores(self):
        print("🔍 Revisando errores")
    
    def _on_validar_cte(self):
        print("📋 Validando CTE DB-HE")
    
    def _on_validar_termica(self):
        print("🌡️ Validando térmica")
    
    def _on_validar_acustica(self):
        print("🔇 Validando acústica")
    
    def _on_generar_informe(self):
        print("📊 Generando informe técnico")
    
    def _on_generar_ficha(self):
        print("📋 Generando ficha de producto")
    
    def _on_optimizar_perfiles(self):
        print("📏 Optimizando perfiles")
    
    def _on_optimizar_cristal(self):
        print("🔷 Optimizando cristal")
    
    def _on_lista_materiales(self):
        print("📋 Generando lista de materiales")
    
    def _on_generar_etiquetas(self):
        print("🏷️ Generando etiquetas")
    
    def _on_generar_planos(self):
        print("📐 Generando planos")
    
    def _on_guardar_config(self):
        print("💾 Guardando configuración")
    
    def _on_enviar_presupuesto(self):
        self.produccion_requested.emit()
        print("📧 Enviando presupuesto")
