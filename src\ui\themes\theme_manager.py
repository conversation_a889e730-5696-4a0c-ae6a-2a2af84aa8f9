"""
Gestor de temas simplificado para PRO-2000.
Solo estilos CSS básicos compatibles con Qt.
"""

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QSettings
import os


class ThemeManager:
    """Gestor de temas simplificado."""

    def __init__(self):
        self.settings = QSettings("PRO-2000", "ThemeSettings")
        self.current_theme = self.settings.value("current_theme", "claro")

        # Definir temas básicos
        self.themes = {
            "claro": self._get_light_theme(),
            "oscuro": self._get_dark_theme(),
            "azul": self._get_blue_theme()
        }
    
    def _get_light_theme(self):
        """Tema claro (por defecto)."""
        return {
            "name": "Tema Claro",
            "description": "Tema claro y limpio para uso diario",
            "stylesheet": """
                QMainWindow {
                    background-color: #f8f9fa;
                    color: #212529;
                }
                
                QMenuBar {
                    background-color: #ffffff;
                    color: #212529;
                    border-bottom: 1px solid #dee2e6;
                    padding: 4px;
                }
                
                QMenuBar::item {
                    background-color: transparent;
                    padding: 8px 12px;
                    border-radius: 4px;
                }
                
                QMenuBar::item:selected {
                    background-color: #e9ecef;
                }
                
                QMenu {
                    background-color: #ffffff;
                    border: 1px solid #dee2e6;
                    border-radius: 6px;
                    padding: 4px;
                }
                
                QMenu::item {
                    padding: 8px 16px;
                    border-radius: 4px;
                }
                
                QMenu::item:selected {
                    background-color: #0d6efd;
                    color: white;
                }
                
                QTabWidget::pane {
                    border: 1px solid #dee2e6;
                    background-color: #ffffff;
                }

                QTabBar::tab {
                    background-color: #f8f9fa;
                    color: #495057;
                    padding: 10px 16px;
                    margin-right: 2px;
                    border: 1px solid #dee2e6;
                    border-bottom: none;
                }

                QTabBar::tab:selected {
                    background-color: #ffffff;
                    color: #212529;
                    border-bottom: 2px solid #0d6efd;
                }

                QPushButton {
                    background-color: #0d6efd;
                    color: white;
                    border: none;
                    padding: 10px 16px;
                    font-weight: 500;
                    min-width: 80px;
                }

                QPushButton:hover {
                    background-color: #0b5ed7;
                }

                QPushButton:pressed {
                    background-color: #0a58ca;
                }

                QPushButton:disabled {
                    background-color: #6c757d;
                    color: #adb5bd;
                }

                QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                    background-color: #ffffff;
                    border: 1px solid #ced4da;
                    padding: 8px 12px;
                    color: #495057;
                }

                QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
                    border-color: #86b7fe;
                    background-color: #f8f9ff;
                }

                QTableWidget {
                    background-color: #ffffff;
                    alternate-background-color: #f8f9fa;
                    gridline-color: #dee2e6;
                    border: 1px solid #dee2e6;
                }

                QHeaderView::section {
                    background-color: #e9ecef;
                    color: #495057;
                    padding: 10px;
                    border: none;
                    border-right: 1px solid #dee2e6;
                    font-weight: 600;
                }

                QGroupBox {
                    font-weight: 600;
                    color: #495057;
                    border: 2px solid #dee2e6;
                    margin-top: 10px;
                    padding-top: 10px;
                }

                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 8px 0 8px;
                    background-color: #f8f9fa;
                }

                QProgressBar {
                    border: 1px solid #dee2e6;
                    text-align: center;
                    background-color: #f8f9fa;
                }

                QProgressBar::chunk {
                    background-color: #198754;
                }
            """
        }
    
    def _get_dark_theme(self):
        """Tema oscuro."""
        return {
            "name": "Tema Oscuro",
            "description": "Tema oscuro para reducir fatiga visual",
            "stylesheet": """
                QMainWindow {
                    background-color: #1a1a1a;
                    color: #ffffff;
                }
                
                QMenuBar {
                    background-color: #2d2d2d;
                    color: #ffffff;
                    border-bottom: 1px solid #404040;
                    padding: 4px;
                }
                
                QMenuBar::item {
                    background-color: transparent;
                    padding: 8px 12px;
                    border-radius: 4px;
                }
                
                QMenuBar::item:selected {
                    background-color: #404040;
                }
                
                QMenu {
                    background-color: #2d2d2d;
                    border: 1px solid #404040;
                    border-radius: 6px;
                    padding: 4px;
                    color: #ffffff;
                }
                
                QMenu::item {
                    padding: 8px 16px;
                    border-radius: 4px;
                }
                
                QMenu::item:selected {
                    background-color: #0d6efd;
                    color: white;
                }
                
                QTabWidget::pane {
                    border: 1px solid #404040;
                    background-color: #2d2d2d;
                }

                QTabBar::tab {
                    background-color: #1a1a1a;
                    color: #cccccc;
                    padding: 10px 16px;
                    margin-right: 2px;
                    border: 1px solid #404040;
                    border-bottom: none;
                }

                QTabBar::tab:selected {
                    background-color: #2d2d2d;
                    color: #ffffff;
                    border-bottom: 2px solid #0d6efd;
                }

                QPushButton {
                    background-color: #0d6efd;
                    color: white;
                    border: none;
                    padding: 10px 16px;
                    font-weight: 500;
                    min-width: 80px;
                }

                QPushButton:hover {
                    background-color: #0b5ed7;
                }

                QPushButton:pressed {
                    background-color: #0a58ca;
                }

                QPushButton:disabled {
                    background-color: #404040;
                    color: #666666;
                }

                QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                    background-color: #404040;
                    border: 1px solid #666666;
                    padding: 8px 12px;
                    color: #ffffff;
                }

                QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
                    border-color: #86b7fe;
                    background-color: #4a4a4a;
                }

                QTableWidget {
                    background-color: #2d2d2d;
                    alternate-background-color: #1a1a1a;
                    gridline-color: #404040;
                    border: 1px solid #404040;
                    color: #ffffff;
                }

                QHeaderView::section {
                    background-color: #404040;
                    color: #ffffff;
                    padding: 10px;
                    border: none;
                    border-right: 1px solid #666666;
                    font-weight: 600;
                }

                QGroupBox {
                    font-weight: 600;
                    color: #ffffff;
                    border: 2px solid #404040;
                    margin-top: 10px;
                    padding-top: 10px;
                }

                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 8px 0 8px;
                    background-color: #1a1a1a;
                }

                QProgressBar {
                    border: 1px solid #404040;
                    text-align: center;
                    background-color: #1a1a1a;
                    color: #ffffff;
                }

                QProgressBar::chunk {
                    background-color: #198754;
                }
                
                QLabel {
                    color: #ffffff;
                }
                
                QCheckBox {
                    color: #ffffff;
                }
                
                QRadioButton {
                    color: #ffffff;
                }
            """
        }
    
    def _get_blue_theme(self):
        """Tema azul profesional."""
        return {
            "name": "Tema Azul",
            "description": "Tema azul profesional para empresas",
            "stylesheet": """
                QMainWindow {
                    background-color: #f0f4f8;
                    color: #1a365d;
                }
                
                QMenuBar {
                    background-color: #2c5282;
                    color: #ffffff;
                    border-bottom: 1px solid #2a4365;
                    padding: 4px;
                }
                
                QMenuBar::item {
                    background-color: transparent;
                    padding: 8px 12px;
                    border-radius: 4px;
                }
                
                QMenuBar::item:selected {
                    background-color: #2a4365;
                }
                
                QPushButton {
                    background-color: #3182ce;
                    color: white;
                    border: none;
                    padding: 10px 16px;
                    border-radius: 6px;
                    font-weight: 500;
                    min-width: 80px;
                }
                
                QPushButton:hover {
                    background-color: #2c5282;
                }
                
                QTabBar::tab:selected {
                    background-color: #ffffff;
                    color: #1a365d;
                    border-bottom: 2px solid #3182ce;
                }
                
                QGroupBox {
                    font-weight: 600;
                    color: #1a365d;
                    border: 2px solid #bee3f8;
                    border-radius: 8px;
                    margin-top: 10px;
                    padding-top: 10px;
                }
            """ + self._get_light_theme()["stylesheet"]
        }
    
    def _get_green_theme(self):
        """Tema verde natural."""
        return {
            "name": "Tema Verde",
            "description": "Tema verde natural y relajante",
            "stylesheet": """
                QMainWindow {
                    background-color: #f0fff4;
                    color: #1a202c;
                }
                
                QMenuBar {
                    background-color: #38a169;
                    color: #ffffff;
                    border-bottom: 1px solid #2f855a;
                    padding: 4px;
                }
                
                QPushButton {
                    background-color: #48bb78;
                    color: white;
                    border: none;
                    padding: 10px 16px;
                    font-weight: 500;
                    min-width: 80px;
                }

                QPushButton:hover {
                    background-color: #38a169;
                }

                QTabBar::tab:selected {
                    border-bottom: 2px solid #48bb78;
                }
            """ + self._get_light_theme()["stylesheet"]
        }
    
    def _get_professional_theme(self):
        """Tema profesional corporativo."""
        return {
            "name": "Tema Profesional",
            "description": "Tema corporativo para uso empresarial",
            "stylesheet": """
                QMainWindow {
                    background-color: #fafafa;
                    color: #263238;
                }
                
                QMenuBar {
                    background-color: #37474f;
                    color: #ffffff;
                    border-bottom: 1px solid #263238;
                    padding: 4px;
                }
                
                QPushButton {
                    background-color: #546e7a;
                    color: white;
                    border: none;
                    padding: 10px 16px;
                    font-weight: 500;
                    min-width: 80px;
                }

                QPushButton:hover {
                    background-color: #455a64;
                }

                QTabBar::tab:selected {
                    border-bottom: 2px solid #546e7a;
                }

                QGroupBox {
                    font-weight: 600;
                    color: #263238;
                    border: 2px solid #cfd8dc;
                    margin-top: 10px;
                    padding-top: 10px;
                }
            """ + self._get_light_theme()["stylesheet"]
        }
    
    def get_available_themes(self):
        """Obtiene la lista de temas disponibles."""
        return list(self.themes.keys())
    
    def get_theme_info(self, theme_name):
        """Obtiene información de un tema."""
        return self.themes.get(theme_name, {})
    
    def apply_theme(self, theme_name):
        """Aplica un tema a la aplicación."""
        if theme_name not in self.themes:
            theme_name = "claro"
        
        app = QApplication.instance()
        if app:
            theme = self.themes[theme_name]
            app.setStyleSheet(theme["stylesheet"])
            
            # Guardar tema actual
            self.current_theme = theme_name
            self.settings.setValue("current_theme", theme_name)
            
            return True
        return False
    
    def get_current_theme(self):
        """Obtiene el tema actual."""
        return self.current_theme
    
    def load_saved_theme(self):
        """Carga el tema guardado."""
        saved_theme = self.settings.value("current_theme", "claro")
        self.apply_theme(saved_theme)
