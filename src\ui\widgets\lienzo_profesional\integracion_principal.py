"""
Integración del sistema profesional con la aplicación principal.
Conecta el nuevo lienzo profesional con el sistema existente.
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QMessageBox, QFrame, QSplitter, QGroupBox, QFormLayout,
    QSpinBox, QComboBox
)
from PyQt6.QtCore import Qt, pyqtSignal

from ui.widgets.lienzo_profesional.lienzo_articulo import LienzoArticuloProfesional


class IntegracionSistemaProfesional(QWidget):
    """
    Widget principal que integra el sistema profesional de diseño de artículos
    con la aplicación existente.
    """

    # Señales
    articulo_creado = pyqtSignal(object)  # Emite cuando se crea un artículo
    cambios_guardados = pyqtSignal()     # Emite cuando se guardan cambios

    def __init__(self, parent=None):
        super().__init__(parent)
        print("🔍 DEBUG: Iniciando IntegracionSistemaProfesional.__init__")

        self.lienzo_profesional = None
        self.articulo_actual = None

        try:
            self._crear_interfaz()
            print("✅ DEBUG: Interfaz profesional creada exitosamente")
        except Exception as e:
            print(f"❌ DEBUG: Error en __init__ de IntegracionSistemaProfesional: {e}")
            import traceback
            print(f"❌ DEBUG: Traceback: {traceback.format_exc()}")
            # Crear interfaz mínima en caso de error
            self._crear_interfaz_minima()

    def _crear_interfaz_minima(self):
        """Crea una interfaz mínima en caso de error."""
        layout = QVBoxLayout(self)
        label = QLabel("⚠️ Sistema Profesional - Modo Seguro")
        label.setStyleSheet("color: orange; font-weight: bold; padding: 20px;")
        layout.addWidget(label)

    def _crear_interfaz(self):
        """Crea la interfaz principal de integración."""
        print("🔍 DEBUG: Creando interfaz profesional...")
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # Barra de herramientas superior
        self._crear_barra_herramientas(layout)

        # Splitter principal (horizontal)
        splitter_principal = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter_principal)

        # Panel izquierdo - Herramientas
        self._crear_panel_izquierdo(splitter_principal)

        # Panel central - Lienzo de diseño
        self._crear_panel_central(splitter_principal)

        # Panel derecho - Propiedades
        self._crear_panel_derecho(splitter_principal)

        # Configurar proporciones del splitter
        splitter_principal.setSizes([200, 500, 200])

        # Barra de estado
        self._crear_barra_estado(layout)

    def _crear_barra_herramientas(self, layout):
        """Crea la barra de herramientas superior."""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: #2d3748;
                color: white;
                padding: 10px;
                border-radius: 5px;
            }
        """)
        toolbar_layout = QHBoxLayout(toolbar_frame)

        # Título del sistema
        titulo = QLabel("🏗️ Sistema Profesional de Diseño de Artículos")
        titulo.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        toolbar_layout.addWidget(titulo)

        toolbar_layout.addStretch()

        # Botones principales
        self.btn_nuevo = QPushButton("📄 Nuevo")
        self.btn_nuevo.setStyleSheet(self._estilo_boton())
        self.btn_nuevo.clicked.connect(self._nuevo_articulo)
        toolbar_layout.addWidget(self.btn_nuevo)

        self.btn_generar = QPushButton("🏗️ Generar")
        self.btn_generar.setStyleSheet(self._estilo_boton())
        self.btn_generar.clicked.connect(self._generar_articulo)
        toolbar_layout.addWidget(self.btn_generar)

        layout.addWidget(toolbar_frame)

    def _estilo_boton(self):
        """Retorna el estilo para botones."""
        return """
            QPushButton {
                background-color: #4a5568;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #2b6cb0;
            }
            QPushButton:pressed {
                background-color: #2c5282;
            }
        """

    def _crear_panel_izquierdo(self, splitter):
        """Crea el panel izquierdo con herramientas."""
        panel_izq = QFrame()
        panel_izq.setStyleSheet("background-color: #f8f9fa; border: 1px solid #dee2e6;")
        layout_izq = QVBoxLayout(panel_izq)

        # Herramientas
        grupo_herramientas = QGroupBox("🔧 Herramientas")
        layout_herramientas = QVBoxLayout(grupo_herramientas)

        self.btn_seleccionar = QPushButton("👆 Seleccionar")
        self.btn_seleccionar.setCheckable(True)
        self.btn_seleccionar.setChecked(True)
        layout_herramientas.addWidget(self.btn_seleccionar)

        self.btn_marco = QPushButton("🔲 Marco")
        self.btn_marco.setCheckable(True)
        layout_herramientas.addWidget(self.btn_marco)

        self.btn_hoja = QPushButton("🚪 Hoja")
        self.btn_hoja.setCheckable(True)
        layout_herramientas.addWidget(self.btn_hoja)

        layout_izq.addWidget(grupo_herramientas)

        # Dimensiones
        grupo_dim = QGroupBox("📐 Dimensiones")
        layout_dim = QFormLayout(grupo_dim)

        self.spin_ancho = QSpinBox()
        self.spin_ancho.setRange(100, 5000)
        self.spin_ancho.setValue(1200)
        self.spin_ancho.setSuffix(" mm")
        layout_dim.addRow("Ancho:", self.spin_ancho)

        self.spin_alto = QSpinBox()
        self.spin_alto.setRange(100, 3000)
        self.spin_alto.setValue(1500)
        self.spin_alto.setSuffix(" mm")
        layout_dim.addRow("Alto:", self.spin_alto)

        layout_izq.addWidget(grupo_dim)
        layout_izq.addStretch()

        splitter.addWidget(panel_izq)

    def _crear_panel_central(self, splitter):
        """Crea el panel central con el lienzo."""
        panel_central = QFrame()
        panel_central.setStyleSheet("background-color: white; border: 1px solid #dee2e6;")
        layout_central = QVBoxLayout(panel_central)

        # Título del lienzo
        titulo_lienzo = QLabel("📐 Área de Diseño Profesional")
        titulo_lienzo.setStyleSheet("""
            font-weight: bold;
            font-size: 14px;
            color: #2d3748;
            padding: 8px;
            background-color: #edf2f7;
            border-radius: 3px;
            margin-bottom: 5px;
        """)
        layout_central.addWidget(titulo_lienzo)

        # Lienzo profesional
        try:
            print("🔍 DEBUG: Creando LienzoArticuloProfesional...")
            self.lienzo_profesional = LienzoArticuloProfesional()
            self.lienzo_profesional.setMinimumSize(400, 300)
            layout_central.addWidget(self.lienzo_profesional)
            print("✅ DEBUG: LienzoArticuloProfesional creado exitosamente")
        except Exception as e:
            print(f"❌ DEBUG: Error creando lienzo: {e}")
            error_label = QLabel(f"⚠️ Error: {str(e)}")
            error_label.setStyleSheet("color: red; padding: 20px;")
            layout_central.addWidget(error_label)

        splitter.addWidget(panel_central)

    def _crear_panel_derecho(self, splitter):
        """Crea el panel derecho con propiedades."""
        panel_der = QFrame()
        panel_der.setStyleSheet("background-color: #f8f9fa; border: 1px solid #dee2e6;")
        layout_der = QVBoxLayout(panel_der)

        # Propiedades del elemento
        grupo_propiedades = QGroupBox("⚙️ Propiedades")
        layout_prop = QFormLayout(grupo_propiedades)

        self.combo_tipo_perfil = QComboBox()
        self.combo_tipo_perfil.addItems(["Marco", "Travesaño", "Montante", "Hoja"])
        layout_prop.addRow("Tipo:", self.combo_tipo_perfil)

        layout_der.addWidget(grupo_propiedades)
        layout_der.addStretch()

        splitter.addWidget(panel_der)

    def _crear_barra_estado(self, layout):
        """Crea la barra de estado inferior."""
        estado_frame = QFrame()
        estado_frame.setStyleSheet("""
            QFrame {
                background-color: #2d3748;
                color: white;
                padding: 5px 10px;
                border-radius: 3px;
            }
        """)
        estado_layout = QHBoxLayout(estado_frame)

        self.label_estado = QLabel("✅ Sistema Profesional listo para diseñar")
        self.label_estado.setStyleSheet("color: #68d391; font-weight: bold;")
        estado_layout.addWidget(self.label_estado)

        estado_layout.addStretch()

        self.label_info = QLabel("Elementos: 0")
        self.label_info.setStyleSheet("color: #a0aec0;")
        estado_layout.addWidget(self.label_info)

        layout.addWidget(estado_frame)

    def _nuevo_articulo(self):
        """Crea un nuevo artículo."""
        if self.lienzo_profesional and hasattr(self.lienzo_profesional, 'limpiar_lienzo'):
            self.lienzo_profesional.limpiar_lienzo()

        self.articulo_actual = None
        self.label_estado.setText("✅ Nuevo artículo creado")

        QMessageBox.information(self, "Nuevo Artículo", "Nuevo artículo creado correctamente.")

    def _generar_articulo(self):
        """Genera el artículo completo."""
        # Crear artículo básico
        articulo = {
            'codigo': 'ART-001',
            'descripcion': 'Artículo profesional',
            'dimensiones': {
                'ancho': self.spin_ancho.value(),
                'alto': self.spin_alto.value()
            }
        }

        self.articulo_actual = articulo
        self.articulo_creado.emit(articulo)
        self.label_estado.setText("🏗️ Artículo generado correctamente")

        QMessageBox.information(
            self,
            "Artículo Generado",
            f"Artículo '{articulo['codigo']}' generado correctamente.\n"
            f"Dimensiones: {articulo['dimensiones']['ancho']}x{articulo['dimensiones']['alto']} mm"
        )

    def obtener_lienzo_profesional(self):
        """Obtiene el lienzo profesional."""
        return self.lienzo_profesional

    def obtener_articulo_actual(self):
        """Obtiene el artículo actual."""
        return self.articulo_actual
