"""
Módulo de optimización de cortes para PRO-2000.
Implementa algoritmos de optimización para minimizar desperdicio.
"""

from typing import List, Dict, Any
from models.base import get_db
from models.articulo import ObraArticulo


class OptimizadorCortes:
    """Optimizador de cortes de perfiles."""
    
    def __init__(self, obra, configuracion: Dict[str, Any]):
        """
        Inicializa el optimizador.
        
        Args:
            obra: Obra a optimizar
            configuracion: Configuración del optimizador
        """
        self.obra = obra
        self.config = configuracion
        self.cortes_necesarios = []
        
    def optimizar(self) -> Dict[str, Any]:
        """
        Ejecuta la optimización de cortes.
        
        Returns:
            Diccionario con los resultados de la optimización
        """
        # Obtener cortes necesarios
        self._obtener_cortes_necesarios()
        
        # Ejecutar algoritmo de optimización
        if self.config['algoritmo'] == "Best Fit Decreasing (BFD)":
            barras = self._algoritmo_best_fit_decreasing()
        elif self.config['algoritmo'] == "Next Fit Decreasing (NFD)":
            barras = self._algoritmo_next_fit_decreasing()
        else:  # First Fit Decreasing por defecto
            barras = self._algoritmo_first_fit_decreasing()
        
        # Calcular estadísticas
        estadisticas = self._calcular_estadisticas(barras)
        
        return {
            'barras': barras,
            'eficiencia': estadisticas['eficiencia'],
            'desperdicio_total': estadisticas['desperdicio_total'],
            'coste_estimado': estadisticas['coste_estimado'],
            'algoritmo_usado': self.config['algoritmo']
        }
    
    def _obtener_cortes_necesarios(self):
        """Obtiene todos los cortes necesarios de la obra."""
        db = next(get_db())
        
        try:
            obra_articulos = db.query(ObraArticulo).filter(
                ObraArticulo.obra_id == self.obra.id
            ).all()
            
            self.cortes_necesarios = []
            
            for obra_articulo in obra_articulos:
                if not obra_articulo.articulo or not obra_articulo.articulo.perfiles:
                    continue
                
                # Calcular materiales necesarios
                materiales = obra_articulo.articulo.calcular_materiales(
                    obra_articulo.altura,
                    obra_articulo.anchura,
                    getattr(obra_articulo, 'altura_1', None),
                    getattr(obra_articulo, 'altura_2', None),
                    getattr(obra_articulo, 'altura_3', None),
                    getattr(obra_articulo, 'altura_4', None),
                    getattr(obra_articulo, 'anchura_1', None),
                    getattr(obra_articulo, 'anchura_2', None),
                    getattr(obra_articulo, 'anchura_3', None),
                    getattr(obra_articulo, 'anchura_4', None)
                )
                
                # Agregar perfiles a la lista de cortes
                for material_perfil in materiales['perfiles']:
                    perfil = material_perfil['perfil']
                    cantidad = material_perfil['cantidad'] * obra_articulo.cantidad
                    metros_por_unidad = material_perfil['metros_totales'] / material_perfil['cantidad']
                    longitud_mm = metros_por_unidad * 1000  # Convertir a mm
                    
                    # Agregar cada unidad como un corte individual
                    for _ in range(int(cantidad)):
                        corte = {
                            'longitud': longitud_mm,
                            'perfil_codigo': perfil.codigo,
                            'perfil_id': perfil.id,
                            'tipo_perfil': getattr(material_perfil, 'tipo_perfil', 'marco'),
                            'angulo_izquierdo': getattr(material_perfil, 'angulo_izquierdo', 90.0),
                            'angulo_derecho': getattr(material_perfil, 'angulo_derecho', 90.0)
                        }
                        self.cortes_necesarios.append(corte)
        
        finally:
            db.close()
    
    def _algoritmo_first_fit_decreasing(self) -> List[Dict[str, Any]]:
        """
        Algoritmo First Fit Decreasing.
        Ordena los cortes de mayor a menor y los asigna a la primera barra que los contenga.
        """
        # Ordenar cortes por longitud (descendente)
        cortes_ordenados = sorted(self.cortes_necesarios, key=lambda x: x['longitud'], reverse=True)
        
        barras = []
        longitud_barra = self.config['longitud_barra']
        grosor_corte = self.config['grosor_corte']
        
        for corte in cortes_ordenados:
            # Buscar primera barra que pueda contener el corte
            barra_encontrada = False
            
            for barra in barras:
                if self._puede_agregar_corte(barra, corte, longitud_barra, grosor_corte):
                    self._agregar_corte_a_barra(barra, corte, grosor_corte)
                    barra_encontrada = True
                    break
            
            # Si no se encontró barra, crear nueva
            if not barra_encontrada:
                nueva_barra = self._crear_nueva_barra(corte)
                barras.append(nueva_barra)
        
        return barras
    
    def _algoritmo_best_fit_decreasing(self) -> List[Dict[str, Any]]:
        """
        Algoritmo Best Fit Decreasing.
        Ordena los cortes y los asigna a la barra con menor espacio libre que los contenga.
        """
        # Ordenar cortes por longitud (descendente)
        cortes_ordenados = sorted(self.cortes_necesarios, key=lambda x: x['longitud'], reverse=True)
        
        barras = []
        longitud_barra = self.config['longitud_barra']
        grosor_corte = self.config['grosor_corte']
        
        for corte in cortes_ordenados:
            mejor_barra = None
            menor_desperdicio = float('inf')
            
            # Buscar la barra con menor desperdicio que pueda contener el corte
            for barra in barras:
                if self._puede_agregar_corte(barra, corte, longitud_barra, grosor_corte):
                    espacio_libre = self._calcular_espacio_libre(barra, longitud_barra)
                    desperdicio_resultante = espacio_libre - corte['longitud'] - grosor_corte
                    
                    if desperdicio_resultante < menor_desperdicio:
                        menor_desperdicio = desperdicio_resultante
                        mejor_barra = barra
            
            # Agregar a la mejor barra o crear nueva
            if mejor_barra:
                self._agregar_corte_a_barra(mejor_barra, corte, grosor_corte)
            else:
                nueva_barra = self._crear_nueva_barra(corte)
                barras.append(nueva_barra)
        
        return barras
    
    def _algoritmo_next_fit_decreasing(self) -> List[Dict[str, Any]]:
        """
        Algoritmo Next Fit Decreasing.
        Ordena los cortes y los asigna solo a la última barra abierta.
        """
        # Ordenar cortes por longitud (descendente)
        cortes_ordenados = sorted(self.cortes_necesarios, key=lambda x: x['longitud'], reverse=True)
        
        barras = []
        longitud_barra = self.config['longitud_barra']
        grosor_corte = self.config['grosor_corte']
        
        for corte in cortes_ordenados:
            # Solo intentar con la última barra
            if barras and self._puede_agregar_corte(barras[-1], corte, longitud_barra, grosor_corte):
                self._agregar_corte_a_barra(barras[-1], corte, grosor_corte)
            else:
                # Crear nueva barra
                nueva_barra = self._crear_nueva_barra(corte)
                barras.append(nueva_barra)
        
        return barras
    
    def _puede_agregar_corte(self, barra: Dict[str, Any], corte: Dict[str, Any], 
                           longitud_barra: float, grosor_corte: float) -> bool:
        """Verifica si se puede agregar un corte a una barra."""
        espacio_libre = self._calcular_espacio_libre(barra, longitud_barra)
        espacio_necesario = corte['longitud'] + grosor_corte
        
        # Verificar espacio
        if espacio_libre < espacio_necesario:
            return False
        
        # Verificar agrupación por perfil si está habilitada
        if self.config.get('agrupar_perfiles', False):
            if barra['cortes'] and barra['cortes'][0]['perfil_id'] != corte['perfil_id']:
                return False
        
        return True
    
    def _calcular_espacio_libre(self, barra: Dict[str, Any], longitud_barra: float) -> float:
        """Calcula el espacio libre en una barra."""
        return longitud_barra - barra['longitud_usada']
    
    def _agregar_corte_a_barra(self, barra: Dict[str, Any], corte: Dict[str, Any], grosor_corte: float):
        """Agrega un corte a una barra."""
        barra['cortes'].append(corte)
        barra['longitud_usada'] += corte['longitud'] + grosor_corte
    
    def _crear_nueva_barra(self, primer_corte: Dict[str, Any]) -> Dict[str, Any]:
        """Crea una nueva barra con el primer corte."""
        return {
            'cortes': [primer_corte],
            'longitud_usada': primer_corte['longitud']
        }
    
    def _calcular_estadisticas(self, barras: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calcula las estadísticas de la optimización."""
        longitud_barra = self.config['longitud_barra']
        
        longitud_total_usada = sum(barra['longitud_usada'] for barra in barras)
        longitud_total_disponible = len(barras) * longitud_barra
        
        eficiencia = (longitud_total_usada / longitud_total_disponible) * 100 if longitud_total_disponible > 0 else 0
        desperdicio_total = longitud_total_disponible - longitud_total_usada
        
        # Estimar coste (precio por barra)
        precio_por_barra = 25.0  # Precio estimado por barra
        coste_estimado = len(barras) * precio_por_barra
        
        return {
            'eficiencia': eficiencia,
            'desperdicio_total': desperdicio_total,
            'coste_estimado': coste_estimado
        }
