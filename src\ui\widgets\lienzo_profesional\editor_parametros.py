"""
Editor de parámetros técnicos para perfiles.
Permite configurar medidas exactas, ángulos de corte, materiales y propiedades técnicas.
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QLabel, QSpinBox, QDoubleSpinBox, QComboBox, QLineEdit,
    QTextEdit, QCheckBox, QPushButton, QSlider, QTabWidget
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont


class EditorParametrosTecnicos(QWidget):
    """
    Editor avanzado de parámetros técnicos para perfiles.
    Permite configuración detallada de todas las propiedades técnicas.
    """
    
    # Señales
    parametros_cambiados = pyqtSignal(dict)
    
    def __init__(self, elemento, parent=None):
        super().__init__(parent)
        self.elemento = elemento
        self.parametros = elemento.propiedades.copy()
        
        self._crear_interfaz()
        self._cargar_parametros()
        self._conectar_señales()
    
    def _crear_interfaz(self):
        """Crea la interfaz del editor."""
        layout = QVBoxLayout(self)
        
        # Título
        titulo = QLabel(f"⚙️ Parámetros Técnicos - {self.elemento.propiedades.get('categoria', 'Perfil')}")
        titulo.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        titulo.setStyleSheet("color: #0078d4; margin-bottom: 10px;")
        layout.addWidget(titulo)
        
        # Tabs para diferentes categorías de parámetros
        self.tabs = QTabWidget()
        layout.addWidget(self.tabs)
        
        # Tab 1: Dimensiones
        self._crear_tab_dimensiones()
        
        # Tab 2: Cortes y Ángulos
        self._crear_tab_cortes()
        
        # Tab 3: Material y Acabado
        self._crear_tab_material()
        
        # Tab 4: Propiedades Avanzadas
        self._crear_tab_avanzadas()
    
    def _crear_tab_dimensiones(self):
        """Crea el tab de dimensiones."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Dimensiones principales
        grupo_principales = QGroupBox("📏 Dimensiones Principales")
        form_principales = QFormLayout(grupo_principales)
        
        # Longitud/Medida principal
        self.spin_longitud = QSpinBox()
        self.spin_longitud.setRange(50, 10000)
        self.spin_longitud.setSuffix(" mm")
        form_principales.addRow("Longitud:", self.spin_longitud)
        
        # Ancho del perfil
        self.spin_ancho = QSpinBox()
        self.spin_ancho.setRange(10, 500)
        self.spin_ancho.setSuffix(" mm")
        form_principales.addRow("Ancho del perfil:", self.spin_ancho)
        
        # Alto del perfil
        self.spin_alto = QSpinBox()
        self.spin_alto.setRange(10, 500)
        self.spin_alto.setSuffix(" mm")
        form_principales.addRow("Alto del perfil:", self.spin_alto)
        
        # Espesor de pared
        self.spin_espesor = QDoubleSpinBox()
        self.spin_espesor.setRange(0.5, 20.0)
        self.spin_espesor.setDecimals(1)
        self.spin_espesor.setSuffix(" mm")
        form_principales.addRow("Espesor de pared:", self.spin_espesor)
        
        layout.addWidget(grupo_principales)
        
        # Tolerancias
        grupo_tolerancias = QGroupBox("📐 Tolerancias")
        form_tolerancias = QFormLayout(grupo_tolerancias)
        
        self.spin_tolerancia_longitud = QDoubleSpinBox()
        self.spin_tolerancia_longitud.setRange(-10.0, 10.0)
        self.spin_tolerancia_longitud.setDecimals(1)
        self.spin_tolerancia_longitud.setSuffix(" mm")
        form_tolerancias.addRow("Tolerancia longitud:", self.spin_tolerancia_longitud)
        
        self.spin_tolerancia_ancho = QDoubleSpinBox()
        self.spin_tolerancia_ancho.setRange(-5.0, 5.0)
        self.spin_tolerancia_ancho.setDecimals(1)
        self.spin_tolerancia_ancho.setSuffix(" mm")
        form_tolerancias.addRow("Tolerancia ancho:", self.spin_tolerancia_ancho)
        
        layout.addWidget(grupo_tolerancias)
        
        self.tabs.addTab(tab, "📏 Dimensiones")
    
    def _crear_tab_cortes(self):
        """Crea el tab de cortes y ángulos."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Ángulos de corte
        grupo_angulos = QGroupBox("✂️ Ángulos de Corte")
        form_angulos = QFormLayout(grupo_angulos)
        
        # Ángulo izquierdo
        self.spin_angulo_izq = QDoubleSpinBox()
        self.spin_angulo_izq.setRange(0, 180)
        self.spin_angulo_izq.setDecimals(1)
        self.spin_angulo_izq.setSuffix("°")
        form_angulos.addRow("Ángulo izquierdo:", self.spin_angulo_izq)
        
        # Ángulo derecho
        self.spin_angulo_der = QDoubleSpinBox()
        self.spin_angulo_der.setRange(0, 180)
        self.spin_angulo_der.setDecimals(1)
        self.spin_angulo_der.setSuffix("°")
        form_angulos.addRow("Ángulo derecho:", self.spin_angulo_der)
        
        # Tipo de corte
        self.combo_tipo_corte = QComboBox()
        self.combo_tipo_corte.addItems([
            "Corte recto", "Corte a inglete", "Corte biselado", 
            "Corte compuesto", "Corte personalizado"
        ])
        form_angulos.addRow("Tipo de corte:", self.combo_tipo_corte)
        
        layout.addWidget(grupo_angulos)
        
        # Mecanizados
        grupo_mecanizados = QGroupBox("🔧 Mecanizados")
        form_mecanizados = QFormLayout(grupo_mecanizados)
        
        # Taladros
        self.check_taladros = QCheckBox("Requiere taladros")
        form_mecanizados.addRow("", self.check_taladros)
        
        self.spin_num_taladros = QSpinBox()
        self.spin_num_taladros.setRange(0, 50)
        self.spin_num_taladros.setEnabled(False)
        form_mecanizados.addRow("Número de taladros:", self.spin_num_taladros)
        
        # Fresados
        self.check_fresados = QCheckBox("Requiere fresados")
        form_mecanizados.addRow("", self.check_fresados)
        
        # Ensambles
        self.combo_tipo_ensamble = QComboBox()
        self.combo_tipo_ensamble.addItems([
            "Sin ensamble", "Soldadura", "Atornillado", 
            "Encolado", "Machihembrado", "Unión mecánica"
        ])
        form_mecanizados.addRow("Tipo de ensamble:", self.combo_tipo_ensamble)
        
        layout.addWidget(grupo_mecanizados)
        
        self.tabs.addTab(tab, "✂️ Cortes")
    
    def _crear_tab_material(self):
        """Crea el tab de material y acabado."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Propiedades del material
        grupo_material = QGroupBox("🔬 Propiedades del Material")
        form_material = QFormLayout(grupo_material)
        
        # Material base
        self.combo_material = QComboBox()
        self.combo_material.addItems([
            "PVC", "Aluminio", "Madera", "Acero", "Acero inoxidable",
            "Fibra de vidrio", "Composite", "Otro"
        ])
        form_material.addRow("Material base:", self.combo_material)
        
        # Color
        self.combo_color = QComboBox()
        self.combo_color.addItems([
            "Blanco", "Marrón", "Negro", "Gris antracita", "Gris plata",
            "Roble", "Nogal", "Cerezo", "Pino", "Personalizado"
        ])
        form_material.addRow("Color:", self.combo_color)
        
        # Acabado superficial
        self.combo_acabado = QComboBox()
        self.combo_acabado.addItems([
            "Liso", "Texturizado", "Foliado madera", "Anodizado",
            "Lacado", "Pulido", "Satinado", "Rugoso"
        ])
        form_material.addRow("Acabado superficial:", self.combo_acabado)
        
        # Tratamiento
        self.combo_tratamiento = QComboBox()
        self.combo_tratamiento.addItems([
            "Sin tratamiento", "Anticorrosivo", "Ignífugo",
            "Antibacteriano", "UV resistente", "Antideslizante"
        ])
        form_material.addRow("Tratamiento especial:", self.combo_tratamiento)
        
        layout.addWidget(grupo_material)
        
        # Propiedades físicas
        grupo_fisicas = QGroupBox("⚖️ Propiedades Físicas")
        form_fisicas = QFormLayout(grupo_fisicas)
        
        # Peso específico
        self.spin_peso_especifico = QDoubleSpinBox()
        self.spin_peso_especifico.setRange(0.1, 20.0)
        self.spin_peso_especifico.setDecimals(2)
        self.spin_peso_especifico.setSuffix(" kg/m")
        form_fisicas.addRow("Peso específico:", self.spin_peso_especifico)
        
        # Resistencia
        self.spin_resistencia = QSpinBox()
        self.spin_resistencia.setRange(0, 1000)
        self.spin_resistencia.setSuffix(" N/mm²")
        form_fisicas.addRow("Resistencia:", self.spin_resistencia)
        
        # Elasticidad
        self.spin_elasticidad = QSpinBox()
        self.spin_elasticidad.setRange(0, 500000)
        self.spin_elasticidad.setSuffix(" N/mm²")
        form_fisicas.addRow("Módulo elástico:", self.spin_elasticidad)
        
        layout.addWidget(grupo_fisicas)
        
        self.tabs.addTab(tab, "🔬 Material")
    
    def _crear_tab_avanzadas(self):
        """Crea el tab de propiedades avanzadas."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Información de fabricación
        grupo_fabricacion = QGroupBox("🏭 Información de Fabricación")
        form_fabricacion = QFormLayout(grupo_fabricacion)
        
        # Código de fabricación
        self.line_codigo_fab = QLineEdit()
        form_fabricacion.addRow("Código de fabricación:", self.line_codigo_fab)
        
        # Lote
        self.line_lote = QLineEdit()
        form_fabricacion.addRow("Lote:", self.line_lote)
        
        # Fecha de fabricación
        self.line_fecha_fab = QLineEdit()
        self.line_fecha_fab.setPlaceholderText("DD/MM/AAAA")
        form_fabricacion.addRow("Fecha de fabricación:", self.line_fecha_fab)
        
        # Proveedor
        self.line_proveedor = QLineEdit()
        form_fabricacion.addRow("Proveedor:", self.line_proveedor)
        
        layout.addWidget(grupo_fabricacion)
        
        # Control de calidad
        grupo_calidad = QGroupBox("✅ Control de Calidad")
        form_calidad = QFormLayout(grupo_calidad)
        
        # Certificaciones
        self.check_ce = QCheckBox("Marcado CE")
        form_calidad.addRow("", self.check_ce)
        
        self.check_iso = QCheckBox("Certificación ISO")
        form_calidad.addRow("", self.check_iso)
        
        self.check_aenor = QCheckBox("Certificación AENOR")
        form_calidad.addRow("", self.check_aenor)
        
        # Nivel de calidad
        self.combo_calidad = QComboBox()
        self.combo_calidad.addItems(["Estándar", "Premium", "Profesional", "Industrial"])
        form_calidad.addRow("Nivel de calidad:", self.combo_calidad)
        
        layout.addWidget(grupo_calidad)
        
        # Observaciones técnicas
        grupo_observaciones = QGroupBox("📝 Observaciones Técnicas")
        layout_obs = QVBoxLayout(grupo_observaciones)
        
        self.text_observaciones = QTextEdit()
        self.text_observaciones.setMaximumHeight(100)
        self.text_observaciones.setPlaceholderText("Observaciones técnicas, instrucciones especiales, etc.")
        layout_obs.addWidget(self.text_observaciones)
        
        layout.addWidget(grupo_observaciones)
        
        self.tabs.addTab(tab, "⚙️ Avanzadas")
    
    def _cargar_parametros(self):
        """Carga los parámetros actuales del elemento."""
        props = self.elemento.propiedades
        
        # Dimensiones
        self.spin_longitud.setValue(props.get('medida', 1200))
        self.spin_ancho.setValue(props.get('ancho', 70))
        self.spin_alto.setValue(props.get('alto', 70))
        self.spin_espesor.setValue(props.get('espesor', 3.0))
        
        # Ángulos
        self.spin_angulo_izq.setValue(props.get('angulo_izquierdo', 45.0))
        self.spin_angulo_der.setValue(props.get('angulo_derecho', 45.0))
        
        # Material
        self.combo_material.setCurrentText(props.get('material', 'PVC'))
        self.combo_color.setCurrentText(props.get('color', 'Blanco'))
        self.combo_acabado.setCurrentText(props.get('acabado', 'Liso'))
        
        # Peso
        self.spin_peso_especifico.setValue(props.get('peso_metro', 2.5))
        
        # Observaciones
        self.text_observaciones.setText(props.get('observaciones_tecnicas', ''))
    
    def _conectar_señales(self):
        """Conecta las señales de los controles."""
        # Conectar todos los controles para emitir cambios
        controles = [
            self.spin_longitud, self.spin_ancho, self.spin_alto, self.spin_espesor,
            self.spin_angulo_izq, self.spin_angulo_der, self.combo_material,
            self.combo_color, self.combo_acabado, self.spin_peso_especifico
        ]
        
        for control in controles:
            if hasattr(control, 'valueChanged'):
                control.valueChanged.connect(self._emitir_cambios)
            elif hasattr(control, 'currentTextChanged'):
                control.currentTextChanged.connect(self._emitir_cambios)
        
        # Conectar checkboxes especiales
        self.check_taladros.toggled.connect(self._toggle_taladros)
        self.check_taladros.toggled.connect(self._emitir_cambios)
        
        # Conectar texto
        self.text_observaciones.textChanged.connect(self._emitir_cambios)
    
    def _toggle_taladros(self, checked):
        """Habilita/deshabilita el número de taladros."""
        self.spin_num_taladros.setEnabled(checked)
    
    def _emitir_cambios(self):
        """Emite los cambios de parámetros."""
        parametros_actualizados = self._obtener_parametros()
        self.parametros_cambiados.emit(parametros_actualizados)
    
    def _obtener_parametros(self):
        """Obtiene todos los parámetros actuales."""
        return {
            # Dimensiones
            'medida': self.spin_longitud.value(),
            'ancho': self.spin_ancho.value(),
            'alto': self.spin_alto.value(),
            'espesor': self.spin_espesor.value(),
            'tolerancia_longitud': self.spin_tolerancia_longitud.value(),
            'tolerancia_ancho': self.spin_tolerancia_ancho.value(),
            
            # Ángulos y cortes
            'angulo_izquierdo': self.spin_angulo_izq.value(),
            'angulo_derecho': self.spin_angulo_der.value(),
            'tipo_corte': self.combo_tipo_corte.currentText(),
            'requiere_taladros': self.check_taladros.isChecked(),
            'num_taladros': self.spin_num_taladros.value(),
            'requiere_fresados': self.check_fresados.isChecked(),
            'tipo_ensamble': self.combo_tipo_ensamble.currentText(),
            
            # Material
            'material': self.combo_material.currentText(),
            'color': self.combo_color.currentText(),
            'acabado': self.combo_acabado.currentText(),
            'tratamiento': self.combo_tratamiento.currentText(),
            'peso_metro': self.spin_peso_especifico.value(),
            'resistencia': self.spin_resistencia.value(),
            'modulo_elastico': self.spin_elasticidad.value(),
            
            # Avanzadas
            'codigo_fabricacion': self.line_codigo_fab.text(),
            'lote': self.line_lote.text(),
            'fecha_fabricacion': self.line_fecha_fab.text(),
            'proveedor': self.line_proveedor.text(),
            'marcado_ce': self.check_ce.isChecked(),
            'certificacion_iso': self.check_iso.isChecked(),
            'certificacion_aenor': self.check_aenor.isChecked(),
            'nivel_calidad': self.combo_calidad.currentText(),
            'observaciones_tecnicas': self.text_observaciones.toPlainText()
        }
    
    def obtener_parametros(self):
        """Obtiene los parámetros finales."""
        return self._obtener_parametros()
