"""
Editor Profesional de Artículos con Imagen y Medidas In-Situ
Permite colocar medidas directamente sobre el dibujo técnico del artículo
"""

import sys
import os
from pathlib import Path

# Agregar el directorio raíz al path
root_dir = str(Path(__file__).parent.parent.parent.parent.absolute())
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QFrame,
    QGridLayout, QLineEdit, QSpinBox, QDoubleSpinBox, QTextEdit,
    QGroupBox, QScrollArea, QWidget, QSplitter, QMessageBox,
    QFileDialog, QComboBox, QCheckBox, QTabWidget
)
from PyQt6.QtCore import Qt, QPoint, QRect, pyqtSignal
from PyQt6.QtGui import (
    Q<PERSON><PERSON>ter, QPen, QBrush, QColor, QFont, QPixmap, 
    QMouseEvent, QPaintEvent, QFontMetrics
)

try:
    from models.base import get_db
    from models.obra import Obra
    from models.articulo import Articulo, ObraArticulo
    from ui.utils.window_utils import setup_maximized_dialog
    from .editor_articulo_completo import EditorArticuloCompleto
except ImportError as e:
    print(f"⚠️ Error importando módulos: {e}")


class MedidaWidget(QFrame):
    """Widget para mostrar una medida con línea y texto"""
    
    def __init__(self, texto="", x=0, y=0, tipo="horizontal"):
        super().__init__()
        self.texto = texto
        self.x = x
        self.y = y
        self.tipo = tipo  # horizontal, vertical
        self.setFixedSize(100, 30)
        self.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 200);
                border: 2px solid #2196F3;
                border-radius: 5px;
                color: #1976D2;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        self.label = QLabel(texto)
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.label)
    
    def actualizar_texto(self, texto):
        """Actualiza el texto de la medida"""
        self.texto = texto
        self.label.setText(texto)


class CanvasImagenMedidas(QLabel):
    """Canvas para mostrar imagen y permitir colocar medidas"""
    
    medida_agregada = pyqtSignal(str, int, int, str)  # texto, x, y, tipo
    
    def __init__(self):
        super().__init__()
        self.setMinimumSize(600, 400)
        self.setStyleSheet("""
            QLabel {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                background-color: #f5f5f5;
            }
        """)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setText("📷 Seleccione una imagen del artículo")
        
        # Variables para medidas
        self.medidas = []
        self.imagen_original = None
        self.imagen_escalada = None
        self.modo_medida = False
        self.tipo_medida_actual = "horizontal"
        
        # Variables para interacción
        self.punto_inicio = None
        self.punto_fin = None
        self.arrastrando = False
    
    def cargar_imagen(self, ruta_imagen):
        """Carga una imagen en el canvas"""
        try:
            if os.path.exists(ruta_imagen):
                self.imagen_original = QPixmap(ruta_imagen)
                self._escalar_imagen()
                self.setText("")
                return True
            else:
                self.setText("❌ Imagen no encontrada")
                return False
        except Exception as e:
            self.setText(f"❌ Error cargando imagen: {e}")
            return False
    
    def _escalar_imagen(self):
        """Escala la imagen para ajustarla al widget"""
        if self.imagen_original:
            self.imagen_escalada = self.imagen_original.scaled(
                self.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
            self.setPixmap(self.imagen_escalada)
    
    def resizeEvent(self, event):
        """Reescala la imagen cuando cambia el tamaño del widget"""
        super().resizeEvent(event)
        if self.imagen_original:
            self._escalar_imagen()
    
    def activar_modo_medida(self, tipo="horizontal"):
        """Activa el modo de colocación de medidas"""
        self.modo_medida = True
        self.tipo_medida_actual = tipo
        self.setCursor(Qt.CursorShape.CrossCursor)
    
    def desactivar_modo_medida(self):
        """Desactiva el modo de colocación de medidas"""
        self.modo_medida = False
        self.setCursor(Qt.CursorShape.ArrowCursor)
    
    def mousePressEvent(self, event: QMouseEvent):
        """Maneja el clic del mouse para colocar medidas"""
        if self.modo_medida and event.button() == Qt.MouseButton.LeftButton:
            self.punto_inicio = event.pos()
            self.arrastrando = True
        super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """Maneja el movimiento del mouse"""
        if self.arrastrando and self.punto_inicio:
            self.punto_fin = event.pos()
            self.update()  # Redibuja para mostrar la línea temporal
        super().mouseMoveEvent(event)
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        """Maneja la liberación del mouse para finalizar la medida"""
        if self.arrastrando and self.punto_inicio and self.punto_fin:
            # Calcular posición central para el texto
            x_centro = (self.punto_inicio.x() + self.punto_fin.x()) // 2
            y_centro = (self.punto_inicio.y() + self.punto_fin.y()) // 2
            
            # Solicitar texto de la medida
            from PyQt6.QtWidgets import QInputDialog
            texto, ok = QInputDialog.getText(
                self, 
                "Nueva Medida", 
                f"Ingrese la medida ({self.tipo_medida_actual}):"
            )
            
            if ok and texto:
                # Agregar la medida
                medida = {
                    'texto': texto,
                    'x1': self.punto_inicio.x(),
                    'y1': self.punto_inicio.y(),
                    'x2': self.punto_fin.x(),
                    'y2': self.punto_fin.y(),
                    'tipo': self.tipo_medida_actual
                }
                self.medidas.append(medida)
                
                # Emitir señal
                self.medida_agregada.emit(texto, x_centro, y_centro, self.tipo_medida_actual)
                
                # Redibujar
                self.update()
        
        # Resetear variables
        self.punto_inicio = None
        self.punto_fin = None
        self.arrastrando = False
        self.desactivar_modo_medida()
        super().mouseReleaseEvent(event)
    
    def paintEvent(self, event: QPaintEvent):
        """Dibuja la imagen y las medidas"""
        super().paintEvent(event)
        
        if not self.imagen_escalada:
            return
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Dibujar medidas existentes
        for medida in self.medidas:
            self._dibujar_medida(painter, medida)
        
        # Dibujar línea temporal mientras se arrastra
        if self.arrastrando and self.punto_inicio and self.punto_fin:
            medida_temporal = {
                'texto': '',
                'x1': self.punto_inicio.x(),
                'y1': self.punto_inicio.y(),
                'x2': self.punto_fin.x(),
                'y2': self.punto_fin.y(),
                'tipo': self.tipo_medida_actual
            }
            self._dibujar_medida(painter, medida_temporal, temporal=True)
    
    def _dibujar_medida(self, painter, medida, temporal=False):
        """Dibuja una medida en el canvas"""
        # Configurar colores
        if temporal:
            color_linea = QColor(255, 0, 0, 128)  # Rojo semitransparente
            color_texto = QColor(255, 0, 0)
        else:
            color_linea = QColor(33, 150, 243)  # Azul
            color_texto = QColor(25, 118, 210)
        
        # Configurar pincel y pluma
        pen = QPen(color_linea, 2)
        painter.setPen(pen)
        
        x1, y1 = medida['x1'], medida['y1']
        x2, y2 = medida['x2'], medida['y2']
        
        # Dibujar línea principal
        painter.drawLine(x1, y1, x2, y2)
        
        # Dibujar marcadores en los extremos
        if medida['tipo'] == 'horizontal':
            # Líneas verticales en los extremos
            painter.drawLine(x1, y1-5, x1, y1+5)
            painter.drawLine(x2, y2-5, x2, y2+5)
        else:  # vertical
            # Líneas horizontales en los extremos
            painter.drawLine(x1-5, y1, x1+5, y1)
            painter.drawLine(x2-5, y2, x2+5, y2)
        
        # Dibujar texto si no es temporal
        if not temporal and medida['texto']:
            x_centro = (x1 + x2) // 2
            y_centro = (y1 + y2) // 2
            
            # Configurar fuente
            font = QFont("Arial", 10, QFont.Weight.Bold)
            painter.setFont(font)
            painter.setPen(QPen(color_texto))
            
            # Fondo para el texto
            fm = QFontMetrics(font)
            rect_texto = fm.boundingRect(medida['texto'])
            rect_texto.moveCenter(QPoint(x_centro, y_centro))
            rect_texto.adjust(-3, -2, 3, 2)
            
            painter.fillRect(rect_texto, QBrush(QColor(255, 255, 255, 200)))
            painter.drawRect(rect_texto)
            
            # Dibujar texto
            painter.drawText(x_centro - rect_texto.width()//2, y_centro + 4, medida['texto'])
    
    def limpiar_medidas(self):
        """Limpia todas las medidas"""
        self.medidas.clear()
        self.update()
    
    def obtener_medidas(self):
        """Obtiene todas las medidas colocadas"""
        return self.medidas.copy()


class EditorArticuloProfesional(QDialog):
    """Editor profesional de artículos con imagen y medidas"""
    
    def __init__(self, parent=None, obra=None, obra_articulo=None):
        super().__init__(parent)
        self.obra = obra
        self.obra_articulo = obra_articulo
        self.articulo_actual = None
        
        self.setWindowTitle("Editor Profesional de Artículos")
        setup_maximized_dialog(self, "Editor Profesional de Artículos")
        
        self.init_ui()
        self.cargar_articulos()
        
        if obra_articulo:
            self.cargar_datos_obra_articulo()
    
    def init_ui(self):
        """Inicializa la interfaz de usuario"""
        layout_principal = QVBoxLayout(self)
        layout_principal.setContentsMargins(8, 8, 8, 8)
        layout_principal.setSpacing(8)
        
        # Header
        header = self._crear_header()
        layout_principal.addWidget(header)
        
        # Contenido principal con splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Panel izquierdo - Controles
        panel_controles = self._crear_panel_controles()
        splitter.addWidget(panel_controles)
        
        # Panel derecho - Canvas de imagen
        panel_imagen = self._crear_panel_imagen()
        splitter.addWidget(panel_imagen)
        
        # Configurar proporciones del splitter
        splitter.setSizes([400, 800])
        layout_principal.addWidget(splitter)
        
        # Botones de acción
        botones = self._crear_botones_accion()
        layout_principal.addWidget(botones)
    
    def _crear_header(self):
        """Crea el header con información de la obra"""
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2196F3, stop:1 #1976D2);
                border-radius: 8px;
                color: white;
                padding: 12px;
            }
        """)
        header.setFixedHeight(60)
        
        layout = QHBoxLayout(header)
        
        # Información de la obra
        info_obra = QLabel(f"🏗️ Obra: {self.obra.codigo} - {self.obra.nombre}")
        info_obra.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        layout.addWidget(info_obra)
        
        layout.addStretch()
        
        # Estado
        estado = QLabel("✏️ Modo Edición")
        estado.setStyleSheet("font-size: 14px; color: white;")
        layout.addWidget(estado)
        
        return header

    def _crear_panel_controles(self):
        """Crea el panel de controles izquierdo"""
        panel = QFrame()
        panel.setFixedWidth(380)
        panel.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
            }
        """)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(12)

        # Selección de artículo
        grupo_articulo = self._crear_grupo_seleccion_articulo()
        layout.addWidget(grupo_articulo)

        # Medidas principales
        grupo_medidas = self._crear_grupo_medidas()
        layout.addWidget(grupo_medidas)

        # Herramientas de medición
        grupo_herramientas = self._crear_grupo_herramientas()
        layout.addWidget(grupo_herramientas)

        # Lista de medidas colocadas
        grupo_lista_medidas = self._crear_grupo_lista_medidas()
        layout.addWidget(grupo_lista_medidas)

        layout.addStretch()

        return panel

    def _crear_grupo_seleccion_articulo(self):
        """Crea el grupo de selección de artículo"""
        grupo = QGroupBox("📦 Selección de Artículo")
        grupo.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                color: #1976D2;
                border: 2px solid #e3f2fd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        layout = QVBoxLayout(grupo)
        layout.setSpacing(8)

        # Combo de artículos
        self.combo_articulo = QComboBox()
        self.combo_articulo.setFixedHeight(40)  # Altura fija más alta
        self.combo_articulo.setStyleSheet("""
            QComboBox {
                padding: 10px 12px;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                font-size: 13px;
                font-weight: 500;
                color: #333;
                background-color: white;
                min-height: 20px;
            }
            QComboBox:focus {
                border-color: #2196F3;
                background-color: #f8f9fa;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid #666;
                width: 6px;
                height: 6px;
                border-top: none;
                border-left: none;
                margin-right: 8px;
            }
        """)
        self.combo_articulo.currentTextChanged.connect(self.on_articulo_seleccionado)
        layout.addWidget(self.combo_articulo)

        # Información del artículo
        self.label_info_articulo = QLabel("Seleccione un artículo")
        self.label_info_articulo.setFixedHeight(80)
        self.label_info_articulo.setStyleSheet("""
            QLabel {
                color: #555;
                font-size: 12px;
                font-weight: 500;
                padding: 12px;
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                line-height: 1.4;
            }
        """)
        self.label_info_articulo.setWordWrap(True)
        layout.addWidget(self.label_info_articulo)

        return grupo

    def _crear_grupo_medidas(self):
        """Crea el grupo de medidas principales"""
        grupo = QGroupBox("📏 Medidas Principales")
        grupo.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                color: #1976D2;
                border: 2px solid #e3f2fd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        layout = QGridLayout(grupo)
        layout.setSpacing(8)

        # Etiquetas con mejor estilo
        label_style = """
            QLabel {
                font-size: 12px;
                font-weight: 600;
                color: #444;
                padding: 5px 0;
            }
        """

        # Altura
        label_altura = QLabel("Altura (mm):")
        label_altura.setStyleSheet(label_style)
        layout.addWidget(label_altura, 0, 0)

        self.campo_altura = QDoubleSpinBox()
        self.campo_altura.setRange(1, 10000)
        self.campo_altura.setValue(1000)
        self.campo_altura.setFixedHeight(40)
        self.campo_altura.setStyleSheet(self._get_input_style())
        layout.addWidget(self.campo_altura, 0, 1)

        # Anchura
        label_anchura = QLabel("Anchura (mm):")
        label_anchura.setStyleSheet(label_style)
        layout.addWidget(label_anchura, 1, 0)

        self.campo_anchura = QDoubleSpinBox()
        self.campo_anchura.setRange(1, 10000)
        self.campo_anchura.setValue(800)
        self.campo_anchura.setFixedHeight(40)
        self.campo_anchura.setStyleSheet(self._get_input_style())
        layout.addWidget(self.campo_anchura, 1, 1)

        # Cantidad
        label_cantidad = QLabel("Cantidad:")
        label_cantidad.setStyleSheet(label_style)
        layout.addWidget(label_cantidad, 2, 0)

        self.campo_cantidad = QSpinBox()
        self.campo_cantidad.setRange(1, 1000)
        self.campo_cantidad.setValue(1)
        self.campo_cantidad.setFixedHeight(40)
        self.campo_cantidad.setStyleSheet(self._get_input_style())
        layout.addWidget(self.campo_cantidad, 2, 1)

        return grupo

    def _crear_grupo_herramientas(self):
        """Crea el grupo de herramientas de medición"""
        grupo = QGroupBox("🛠️ Herramientas de Medición")
        grupo.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                color: #1976D2;
                border: 2px solid #e3f2fd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        layout = QVBoxLayout(grupo)
        layout.setSpacing(8)

        # Botones de herramientas
        btn_style = """
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """

        # Medida horizontal
        self.btn_medida_horizontal = QPushButton("📐 Medida Horizontal")
        self.btn_medida_horizontal.setStyleSheet(btn_style)
        self.btn_medida_horizontal.clicked.connect(lambda: self.activar_modo_medida("horizontal"))
        layout.addWidget(self.btn_medida_horizontal)

        # Medida vertical
        self.btn_medida_vertical = QPushButton("📏 Medida Vertical")
        self.btn_medida_vertical.setStyleSheet(btn_style)
        self.btn_medida_vertical.clicked.connect(lambda: self.activar_modo_medida("vertical"))
        layout.addWidget(self.btn_medida_vertical)

        # Limpiar medidas
        self.btn_limpiar_medidas = QPushButton("🗑️ Limpiar Medidas")
        self.btn_limpiar_medidas.setStyleSheet(btn_style.replace("#2196F3", "#f44336").replace("#1976D2", "#d32f2f").replace("#1565C0", "#c62828"))
        self.btn_limpiar_medidas.clicked.connect(self.limpiar_medidas)
        layout.addWidget(self.btn_limpiar_medidas)

        return grupo

    def _crear_grupo_lista_medidas(self):
        """Crea el grupo con la lista de medidas colocadas"""
        grupo = QGroupBox("📋 Medidas Colocadas")
        grupo.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                color: #1976D2;
                border: 2px solid #e3f2fd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        layout = QVBoxLayout(grupo)
        layout.setSpacing(8)

        # Lista de medidas
        self.lista_medidas = QTextEdit()
        self.lista_medidas.setMaximumHeight(140)
        self.lista_medidas.setStyleSheet("""
            QTextEdit {
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 12px;
                font-size: 12px;
                font-weight: 500;
                color: #444;
                background-color: #fafafa;
                line-height: 1.5;
                selection-background-color: #2196F3;
                selection-color: white;
            }
            QTextEdit:focus {
                border-color: #2196F3;
                background-color: white;
            }
        """)
        self.lista_medidas.setPlaceholderText("Las medidas colocadas aparecerán aquí...")
        layout.addWidget(self.lista_medidas)

        return grupo

    def _crear_panel_imagen(self):
        """Crea el panel de imagen derecho"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
            }
        """)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(12)

        # Header del panel de imagen
        header_imagen = QFrame()
        header_imagen.setFixedHeight(50)
        header_imagen.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:1 #388E3C);
                border-radius: 6px;
                color: white;
            }
        """)

        layout_header = QHBoxLayout(header_imagen)

        titulo_imagen = QLabel("🖼️ Imagen del Artículo con Medidas")
        titulo_imagen.setStyleSheet("font-size: 14px; font-weight: bold; color: white;")
        layout_header.addWidget(titulo_imagen)

        layout_header.addStretch()

        # Botón para cargar imagen
        self.btn_cargar_imagen = QPushButton("📁 Cargar Imagen")
        self.btn_cargar_imagen.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 200);
                color: #2E7D32;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 255);
            }
        """)
        self.btn_cargar_imagen.clicked.connect(self.cargar_imagen_articulo)
        layout_header.addWidget(self.btn_cargar_imagen)

        layout.addWidget(header_imagen)

        # Canvas de imagen
        self.canvas_imagen = CanvasImagenMedidas()
        self.canvas_imagen.medida_agregada.connect(self.on_medida_agregada)
        layout.addWidget(self.canvas_imagen)

        return panel

    def _crear_botones_accion(self):
        """Crea los botones de acción"""
        frame_botones = QFrame()
        frame_botones.setFixedHeight(60)
        frame_botones.setStyleSheet("""
            QFrame {
                background-color: #f5f5f5;
                border-radius: 8px;
            }
        """)

        layout = QHBoxLayout(frame_botones)
        layout.setContentsMargins(12, 12, 12, 12)

        # Botón cancelar
        self.btn_cancelar = QPushButton("❌ Cancelar")
        self.btn_cancelar.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        self.btn_cancelar.clicked.connect(self.reject)
        layout.addWidget(self.btn_cancelar)

        # Botón editor completo
        self.btn_editor_completo = QPushButton("🚀 Editor Completo")
        self.btn_editor_completo.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        self.btn_editor_completo.clicked.connect(self.abrir_editor_completo)
        layout.addWidget(self.btn_editor_completo)

        layout.addStretch()

        # Botón guardar
        self.btn_guardar = QPushButton("💾 Guardar Artículo")
        self.btn_guardar.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #388E3C;
            }
        """)
        self.btn_guardar.clicked.connect(self.guardar_articulo)
        layout.addWidget(self.btn_guardar)

        return frame_botones

    def _get_input_style(self):
        """Obtiene el estilo para campos de entrada"""
        return """
            QDoubleSpinBox, QSpinBox {
                padding: 10px 12px;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                color: #333;
                background-color: white;
                min-height: 20px;
                selection-background-color: #2196F3;
                selection-color: white;
            }
            QDoubleSpinBox:focus, QSpinBox:focus {
                border-color: #2196F3;
                background-color: #f8f9fa;
                outline: none;
            }
            QDoubleSpinBox:hover, QSpinBox:hover {
                border-color: #bbb;
            }
            QDoubleSpinBox::up-button, QSpinBox::up-button {
                subcontrol-origin: border;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #e0e0e0;
                border-bottom: 1px solid #e0e0e0;
                border-top-right-radius: 4px;
                background-color: #f5f5f5;
            }
            QDoubleSpinBox::down-button, QSpinBox::down-button {
                subcontrol-origin: border;
                subcontrol-position: bottom right;
                width: 20px;
                border-left: 1px solid #e0e0e0;
                border-top: 1px solid #e0e0e0;
                border-bottom-right-radius: 4px;
                background-color: #f5f5f5;
            }
            QDoubleSpinBox::up-button:hover, QSpinBox::up-button:hover,
            QDoubleSpinBox::down-button:hover, QSpinBox::down-button:hover {
                background-color: #e0e0e0;
            }
        """

    def cargar_articulos(self):
        """Carga la lista de artículos disponibles"""
        try:
            db = next(get_db())
            articulos = db.query(Articulo).filter(Articulo.activo == True).order_by(Articulo.codigo).all()

            self.combo_articulo.clear()
            self.combo_articulo.addItem("Seleccione un artículo...", None)

            for articulo in articulos:
                texto = f"{articulo.codigo} - {articulo.descripcion}"
                self.combo_articulo.addItem(texto, articulo.id)

            db.close()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error cargando artículos: {e}")

    def on_articulo_seleccionado(self):
        """Maneja la selección de un artículo"""
        articulo_id = self.combo_articulo.currentData()
        if not articulo_id:
            self.articulo_actual = None
            self.label_info_articulo.setText("Seleccione un artículo")
            self.canvas_imagen.setText("📷 Seleccione una imagen del artículo")
            return

        try:
            db = next(get_db())
            self.articulo_actual = db.query(Articulo).filter(Articulo.id == articulo_id).first()

            if self.articulo_actual:
                # Actualizar información
                info = f"Serie: {self.articulo_actual.serie or 'N/A'}\n"
                info += f"Descripción: {self.articulo_actual.descripcion}\n"
                info += f"Tiempo taller: {self.articulo_actual.tiempo_taller}h"
                self.label_info_articulo.setText(info)

                # Cargar imagen si existe
                if self.articulo_actual.imagen_path and os.path.exists(self.articulo_actual.imagen_path):
                    self.canvas_imagen.cargar_imagen(self.articulo_actual.imagen_path)
                else:
                    self.canvas_imagen.setText("📷 No hay imagen disponible\nHaga clic en 'Cargar Imagen'")

            db.close()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error cargando artículo: {e}")

    def cargar_imagen_articulo(self):
        """Permite cargar una nueva imagen para el artículo"""
        archivo, _ = QFileDialog.getOpenFileName(
            self,
            "Seleccionar imagen del artículo",
            "",
            "Imágenes (*.png *.jpg *.jpeg *.bmp *.gif);;Todos los archivos (*)"
        )

        if archivo:
            if self.canvas_imagen.cargar_imagen(archivo):
                # Actualizar la ruta en el artículo actual si existe
                if self.articulo_actual:
                    try:
                        db = next(get_db())
                        articulo_db = db.query(Articulo).filter(Articulo.id == self.articulo_actual.id).first()
                        if articulo_db:
                            articulo_db.imagen_path = archivo
                            db.commit()
                            self.articulo_actual.imagen_path = archivo
                        db.close()
                    except Exception as e:
                        print(f"Error actualizando ruta de imagen: {e}")

    def activar_modo_medida(self, tipo):
        """Activa el modo de medición en el canvas"""
        if not self.canvas_imagen.imagen_escalada:
            QMessageBox.warning(self, "Advertencia", "Primero debe cargar una imagen del artículo.")
            return

        self.canvas_imagen.activar_modo_medida(tipo)

        # Actualizar estado de botones
        if tipo == "horizontal":
            self.btn_medida_horizontal.setText("🎯 Colocando Medida Horizontal...")
            self.btn_medida_vertical.setEnabled(False)
        else:
            self.btn_medida_vertical.setText("🎯 Colocando Medida Vertical...")
            self.btn_medida_horizontal.setEnabled(False)

    def on_medida_agregada(self, texto, x, y, tipo):
        """Maneja cuando se agrega una nueva medida"""
        # Restaurar botones
        self.btn_medida_horizontal.setText("📐 Medida Horizontal")
        self.btn_medida_horizontal.setEnabled(True)
        self.btn_medida_vertical.setText("📏 Medida Vertical")
        self.btn_medida_vertical.setEnabled(True)

        # Actualizar lista de medidas
        self.actualizar_lista_medidas()

        # Mostrar información de la medida agregada (opcional)
        print(f"✅ Medida agregada: {texto} ({tipo}) en posición ({x}, {y})")

    def actualizar_lista_medidas(self):
        """Actualiza la lista de medidas colocadas"""
        medidas = self.canvas_imagen.obtener_medidas()

        if not medidas:
            self.lista_medidas.setText("No hay medidas colocadas")
            return

        texto_lista = "Medidas colocadas:\n\n"
        for i, medida in enumerate(medidas, 1):
            icono = "📐" if medida['tipo'] == "horizontal" else "📏"
            texto_lista += f"{i}. {icono} {medida['texto']} ({medida['tipo']})\n"

        self.lista_medidas.setText(texto_lista)

    def limpiar_medidas(self):
        """Limpia todas las medidas del canvas"""
        self.canvas_imagen.limpiar_medidas()
        self.actualizar_lista_medidas()

    def cargar_datos_obra_articulo(self):
        """Carga los datos si se está editando un artículo existente"""
        if not self.obra_articulo:
            return

        try:
            # Seleccionar el artículo en el combo
            for i in range(self.combo_articulo.count()):
                if self.combo_articulo.itemData(i) == self.obra_articulo.articulo_id:
                    self.combo_articulo.setCurrentIndex(i)
                    break

            # Cargar medidas
            self.campo_altura.setValue(self.obra_articulo.altura)
            self.campo_anchura.setValue(self.obra_articulo.anchura)
            self.campo_cantidad.setValue(self.obra_articulo.cantidad)

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error cargando datos: {e}")

    def guardar_articulo(self):
        """Guarda el artículo en la obra"""
        if not self.articulo_actual:
            QMessageBox.warning(self, "Error", "Debe seleccionar un artículo.")
            return

        try:
            db = next(get_db())

            if self.obra_articulo:
                # Editar existente
                obra_articulo_db = db.query(ObraArticulo).filter(ObraArticulo.id == self.obra_articulo.id).first()
                if not obra_articulo_db:
                    QMessageBox.critical(self, "Error", "No se encontró el artículo a editar.")
                    return
            else:
                # Crear nuevo
                obra_articulo_db = ObraArticulo()
                obra_articulo_db.obra_id = self.obra.id
                obra_articulo_db.articulo_id = self.articulo_actual.id
                db.add(obra_articulo_db)

            # Actualizar datos
            obra_articulo_db.altura = self.campo_altura.value()
            obra_articulo_db.anchura = self.campo_anchura.value()
            obra_articulo_db.cantidad = self.campo_cantidad.value()

            # Guardar medidas como JSON en notas
            medidas = self.canvas_imagen.obtener_medidas()
            if medidas:
                import json
                obra_articulo_db.notas = json.dumps(medidas, ensure_ascii=False)

            db.commit()

            QMessageBox.information(
                self,
                "✅ Éxito",
                f"Artículo {'actualizado' if self.obra_articulo else 'agregado'} correctamente.\n\n"
                f"Medidas: {obra_articulo_db.altura:.0f} x {obra_articulo_db.anchura:.0f} mm\n"
                f"Cantidad: {obra_articulo_db.cantidad} unidades\n"
                f"Medidas in-situ: {len(medidas)} colocadas"
            )

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error guardando artículo: {e}")
        finally:
            db.close()

    def abrir_editor_completo(self):
        """Abre el editor completo de artículos"""
        try:
            # Crear el editor completo
            editor_completo = EditorArticuloCompleto(
                parent=self,
                obra=self.obra,
                obra_articulo=self.obra_articulo
            )

            # Cerrar este diálogo y abrir el completo
            if editor_completo.exec() == QDialog.DialogCode.Accepted:
                # Si se guardó en el editor completo, cerrar este también
                self.accept()

        except Exception as e:
            QMessageBox.warning(self, "Error", f"Error abriendo editor completo: {e}")
