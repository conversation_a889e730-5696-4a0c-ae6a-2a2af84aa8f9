from sqlalchemy import Column, Integer, String, Text, DateTime
from sqlalchemy.sql import func
from .base import Base

class Lanzadera(Base):
    """
    Modelo que representa la tabla 'lanzadera' en la base de datos.
    Esta tabla parece ser utilizada para almacenar rutas o configuraciones temporales.
    """
    __tablename__ = 'lanzadera'
    
    # Columnas
    id = Column(Integer, primary_key=True, autoincrement=True)
    camino = Column(String(255), nullable=True, comment='Ruta o camino almacenado')
    clave = Column(String(100), nullable=True, comment='Clave para acceder al dato')
    fecha_creacion = Column(DateTime(timezone=True), server_default=func.now())
    fecha_actualizacion = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<Lanzadera(id={self.id}, clave='{self.clave}')>"
    
    @classmethod
    def obtener_por_clave(cls, db, clave):
        """Obtiene un registro por su clave"""
        return db.query(cls).filter(cls.clave == clave).first()
    
    @classmethod
    def obtener_o_crear_por_clave(cls, db, clave, valor_por_defecto=None):
        """
        Obtiene un registro por su clave o lo crea si no existe.
        
        Args:
            db: Sesión de base de datos
            clave: Clave del registro a buscar/crear
            valor_por_defecto: Valor por defecto para el campo 'camino' si se crea un nuevo registro
            
        Returns:
            Tupla (objeto, creado) donde 'creado' es True si se creó un nuevo registro
        """
        registro = cls.obtener_por_clave(db, clave)
        if registro:
            return registro, False
        
        # Crear nuevo registro
        nuevo_registro = cls(clave=clave, camino=valor_por_defecto)
        db.add(nuevo_registro)
        db.commit()
        db.refresh(nuevo_registro)
        
        return nuevo_registro, True
