"""
<PERSON><PERSON><PERSON><PERSON> que define el diálogo de inicio de sesión.
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QMessageBox, QFormLayout, QCheckBox, QWidget
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QPixmap

from models.base import get_db
from models.usuario import Usuario
from ui.utils.window_utils import setup_window_maximized, ensure_window_controls_visible

class LoginDialog(QDialog):
    """Diálogo de inicio de sesión para la aplicación."""
    
    login_exitoso = pyqtSignal(object)  # Señal que emite el usuario autenticado
    
    def __init__(self, parent=None):
        """Inicializa el diálogo de inicio de sesión."""
        super().__init__(parent)
        self.setWindowTitle("Iniciar sesión - PRO-2000")

        # Asegurar que los controles de ventana sean visibles
        ensure_window_controls_visible(self)

        # Configurar la interfaz de usuario
        self._configurar_ui()

        # Conectar señales
        self._conectar_señales()

        # Configurar tamaño reducido a la mitad para el diálogo de login
        self.resize(400, 300)  # Mitad del tamaño típico de un diálogo (800x600 -> 400x300)
    
    def _configurar_ui(self):
        """Configura los elementos de la interfaz de usuario."""
        layout_principal = QVBoxLayout(self)
        
        # Logo de la aplicación
        contenedor_logo = QWidget()
        layout_logo = QVBoxLayout(contenedor_logo)
        
        # Etiqueta para el logo (puedes reemplazarlo con un QLabel con un QPixmap)
        etiqueta_logo = QLabel("PRO-2000")
        etiqueta_logo.setAlignment(Qt.AlignmentFlag.AlignCenter)
        etiqueta_logo.setStyleSheet("font-size: 24px; font-weight: bold; margin: 20px 0;")
        
        layout_logo.addWidget(etiqueta_logo)
        
        # Formulario de inicio de sesión
        contenedor_formulario = QWidget()
        layout_formulario = QFormLayout(contenedor_formulario)
        layout_formulario.setContentsMargins(40, 20, 40, 20)
        layout_formulario.setSpacing(15)
        
        # Campo de usuario
        self.campo_usuario = QLineEdit()
        self.campo_usuario.setPlaceholderText("Ingrese su nombre de usuario")
        self.campo_usuario.setMinimumHeight(35)
        
        # Campo de contraseña
        self.campo_contrasena = QLineEdit()
        self.campo_contrasena.setPlaceholderText("Ingrese su contraseña")
        self.campo_contrasena.setEchoMode(QLineEdit.EchoMode.Password)
        self.campo_contrasena.setMinimumHeight(35)
        
        # Checkbox para recordar usuario
        self.check_recordar = QCheckBox("Recordar usuario")
        
        # Botones
        contenedor_botones = QWidget()
        layout_botones = QHBoxLayout(contenedor_botones)
        
        self.boton_ingresar = QPushButton("Ingresar")
        self.boton_ingresar.setMinimumHeight(40)
        self.boton_ingresar.setMinimumWidth(100)
        self.boton_ingresar.setStyleSheet("""
            QPushButton {
                background-color: #0d6efd;
                color: white;
                border: none;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
            }
            QPushButton:pressed {
                background-color: #0a58ca;
            }
        """)

        self.boton_cancelar = QPushButton("Cancelar")
        self.boton_cancelar.setMinimumHeight(40)
        self.boton_cancelar.setMinimumWidth(100)
        self.boton_cancelar.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #5c636a;
            }
            QPushButton:pressed {
                background-color: #565e64;
            }
        """)
        
        layout_botones.addWidget(self.boton_ingresar)
        layout_botones.addWidget(self.boton_cancelar)
        
        # Versión de la aplicación
        etiqueta_version = QLabel("Versión 1.0.0")
        etiqueta_version.setAlignment(Qt.AlignmentFlag.AlignCenter)
        etiqueta_version.setStyleSheet("color: #666; font-size: 10px;")
        
        # Añadir widgets al formulario
        layout_formulario.addRow("Usuario:", self.campo_usuario)
        layout_formulario.addRow("Contraseña:", self.campo_contrasena)
        layout_formulario.addRow("", self.check_recordar)
        layout_formulario.addRow("", contenedor_botones)
        
        # Añadir todo al layout principal
        layout_principal.addWidget(contenedor_logo)
        layout_principal.addWidget(contenedor_formulario)
        layout_principal.addStretch()
        layout_principal.addWidget(etiqueta_version)
        
        # Establecer el layout principal
        self.setLayout(layout_principal)
    
    def _conectar_señales(self):
        """Conecta las señales de los widgets."""
        self.boton_ingresar.clicked.connect(self._iniciar_sesion)
        self.boton_cancelar.clicked.connect(self.reject)
        self.campo_contrasena.returnPressed.connect(self._iniciar_sesion)
    
    def _iniciar_sesion(self):
        """Intenta autenticar al usuario con las credenciales proporcionadas."""
        usuario = self.campo_usuario.text().strip()
        contrasena = self.campo_contrasena.text()
        
        if not usuario or not contrasena:
            QMessageBox.warning(
                self,
                "Campos requeridos",
                "Por favor ingrese su nombre de usuario y contraseña.",
                QMessageBox.StandardButton.Ok
            )
            return
        
        # Obtener la sesión de la base de datos
        db = next(get_db())
        
        try:
            # Buscar al usuario en la base de datos
            usuario_autenticado = Usuario.autenticar(db, usuario, contrasena)
            
            if usuario_autenticado:
                # Guardar preferencia de recordar usuario
                # Aquí podrías guardar las credenciales en un archivo de configuración
                # si el checkbox está marcado
                
                # Emitir señal de login exitoso
                self.login_exitoso.emit(usuario_autenticado)
                self.accept()
            else:
                QMessageBox.warning(
                    self,
                    "Error de autenticación",
                    "Nombre de usuario o contraseña incorrectos.",
                    QMessageBox.StandardButton.Ok
                )
                self.campo_contrasena.clear()
                self.campo_contrasena.setFocus()
                
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Se produjo un error al intentar iniciar sesión: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
        finally:
            db.close()
    
    def closeEvent(self, event):
        """Evento que se dispara al intentar cerrar el diálogo."""
        respuesta = QMessageBox.question(
            self,
            "Confirmar salida",
            "¿Está seguro de que desea salir de la aplicación?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if respuesta == QMessageBox.StandardButton.Yes:
            event.accept()
        else:
            event.ignore()
