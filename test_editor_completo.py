#!/usr/bin/env python3
"""
Script de prueba para el Editor Completo de Artículos
Verifica que todas las funcionalidades estén funcionando correctamente
"""

import sys
import os
from pathlib import Path

# Agregar el directorio raíz al path
root_dir = str(Path(__file__).parent.absolute())
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt

def test_editor_completo():
    """Prueba el editor completo de artículos"""
    
    print("🧪 INICIANDO PRUEBAS DEL EDITOR COMPLETO")
    print("=" * 50)
    
    try:
        # Importar el editor completo
        from src.ui.modulos.obras.editor_articulo_completo import EditorArticuloCompleto
        print("✅ Importación del EditorArticuloCompleto: OK")
        
        # Importar canvas interactivo
        from src.ui.modulos.obras.editor_articulo_completo import CanvasInteractivoCompleto
        print("✅ Importación del CanvasInteractivoCompleto: OK")
        
        # Importar elementos interactivos
        from src.ui.modulos.obras.editor_articulo_completo import ElementoInteractivo
        print("✅ Importación del ElementoInteractivo: OK")
        
        # Crear una obra de prueba
        class ObraPrueba:
            def __init__(self):
                self.id = 1
                self.codigo = "TEST-001"
                self.nombre = "Obra de Prueba - Editor Completo"
        
        obra = ObraPrueba()
        print("✅ Creación de obra de prueba: OK")
        
        # Crear aplicación Qt
        app = QApplication(sys.argv)
        print("✅ Inicialización de aplicación Qt: OK")
        
        # Crear el editor
        editor = EditorArticuloCompleto(obra=obra)
        print("✅ Creación del editor completo: OK")
        
        # Verificar que tiene las pestañas correctas
        tabs_esperadas = ["🎯 Principal", "📐 Medidas Adicionales", "🔧 Perfiles Interactivo", "👁️ Previsualización"]
        tabs_actuales = []
        
        for i in range(editor.tabs.count()):
            tab_text = editor.tabs.tabText(i)
            tabs_actuales.append(tab_text)
        
        print(f"📋 Pestañas encontradas: {tabs_actuales}")
        
        for tab_esperada in tabs_esperadas:
            if tab_esperada in tabs_actuales:
                print(f"✅ Pestaña '{tab_esperada}': OK")
            else:
                print(f"❌ Pestaña '{tab_esperada}': FALTA")
        
        # Verificar canvas interactivo
        if hasattr(editor, 'canvas'):
            print("✅ Canvas interactivo: OK")
            
            # Verificar elementos interactivos
            elementos = editor.canvas.elementos_interactivos
            print(f"📦 Elementos interactivos encontrados: {len(elementos)}")
            
            tipos_elementos = set()
            for elemento in elementos:
                tipos_elementos.add(elemento.tipo)
            
            print(f"🔧 Tipos de elementos: {list(tipos_elementos)}")
            
            tipos_esperados = {"marco", "hoja", "travesano_v", "cristal"}
            for tipo in tipos_esperados:
                if tipo in tipos_elementos:
                    print(f"✅ Tipo de elemento '{tipo}': OK")
                else:
                    print(f"❌ Tipo de elemento '{tipo}': FALTA")
        else:
            print("❌ Canvas interactivo: FALTA")
        
        # Verificar campos de medidas adicionales
        if hasattr(editor, 'campos_alturas_adicionales'):
            alturas_esperadas = ['H1', 'H2', 'H3', 'H4']
            alturas_encontradas = list(editor.campos_alturas_adicionales.keys())
            print(f"📏 Campos de alturas adicionales: {alturas_encontradas}")
            
            for altura in alturas_esperadas:
                if altura in alturas_encontradas:
                    print(f"✅ Campo altura '{altura}': OK")
                else:
                    print(f"❌ Campo altura '{altura}': FALTA")
        else:
            print("❌ Campos de alturas adicionales: FALTA")
        
        if hasattr(editor, 'campos_anchuras_adicionales'):
            anchuras_esperadas = ['A1', 'A2', 'A3', 'A4']
            anchuras_encontradas = list(editor.campos_anchuras_adicionales.keys())
            print(f"📐 Campos de anchuras adicionales: {anchuras_encontradas}")
            
            for anchura in anchuras_esperadas:
                if anchura in anchuras_encontradas:
                    print(f"✅ Campo anchura '{anchura}': OK")
                else:
                    print(f"❌ Campo anchura '{anchura}': FALTA")
        else:
            print("❌ Campos de anchuras adicionales: FALTA")
        
        # Verificar tabla de perfiles
        if hasattr(editor, 'tabla_perfiles_asignados'):
            print("✅ Tabla de perfiles asignados: OK")
        else:
            print("❌ Tabla de perfiles asignados: FALTA")
        
        # Verificar área de previsualización
        if hasattr(editor, 'text_previsualizacion'):
            print("✅ Área de previsualización: OK")
        else:
            print("❌ Área de previsualización: FALTA")
        
        print("\n" + "=" * 50)
        print("🎉 PRUEBAS COMPLETADAS")
        print("=" * 50)
        
        # Mostrar el editor para inspección visual
        editor.show()
        
        # Mensaje informativo
        QMessageBox.information(
            editor,
            "🧪 Pruebas del Editor Completo",
            """
            <h3>🎉 Editor Completo de Artículos</h3>
            <p><b>Las pruebas se han completado exitosamente!</b></p>
            
            <p><b>🎯 Funcionalidades verificadas:</b></p>
            <ul>
                <li>✅ Pestañas principales</li>
                <li>✅ Canvas interactivo con elementos</li>
                <li>✅ Medidas adicionales H1-H4, A1-A4</li>
                <li>✅ Tabla de perfiles asignados</li>
                <li>✅ Área de previsualización</li>
            </ul>
            
            <p><b>🔧 Prueba las siguientes funciones:</b></p>
            <ul>
                <li>🖱️ Haz clic en elementos del canvas</li>
                <li>📐 Usa las herramientas de medición</li>
                <li>🔧 Asigna perfiles a elementos</li>
                <li>👁️ Revisa la previsualización</li>
            </ul>
            """
        )
        
        return app.exec()
        
    except ImportError as e:
        print(f"❌ Error de importación: {e}")
        return 1
    except Exception as e:
        print(f"❌ Error general: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(test_editor_completo())
