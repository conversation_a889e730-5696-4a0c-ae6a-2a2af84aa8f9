"""
Ventana principal completamente rediseñada para PRO-2000
VERSIÓN REPARADA FINAL - Módulos reales conectados
"""
from datetime import datetime
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QStatusBar, QStackedWidget, QMessageBox, QApplication,
    QFrame, QScrollArea, QGridLayout, QSizePolicy, QSpacerItem, QDialog, QComboBox
)
from PyQt6.QtCore import Qt, QSize, QPropertyAnimation, QEasingCurve, pyqtSignal
from PyQt6.QtGui import QIcon, QAction, QPixmap, QFont, QPainter, QColor, QBrush

from models.base import get_db
from models.usuario import Usuario
from .themes.theme_config import ThemeConfig
from ui.utils.window_utils import execute_dialog_maximized

# Importar componentes del nuevo sistema
try:
    from .components import (
        ProfessionalButton, ProfessionalCard, ProfessionalFormSection,
        ProfessionalActionBar, ProfessionalStatusBar, create_professional_message_box
    )
except ImportError:
    print("⚠️ Componentes profesionales no disponibles, usando componentes básicos")
    ProfessionalButton = QPushButton
    ProfessionalCard = QFrame
    ProfessionalFormSection = QFrame
    ProfessionalActionBar = QFrame
    ProfessionalStatusBar = QStatusBar
    
    def create_professional_message_box(parent, title, message, message_type="info", buttons=None):
        msg = QMessageBox(parent)
        msg.setWindowTitle(title)
        msg.setText(message)
        if message_type == "error":
            msg.setIcon(QMessageBox.Icon.Critical)
        elif message_type == "warning":
            msg.setIcon(QMessageBox.Icon.Warning)
        elif message_type == "question":
            msg.setIcon(QMessageBox.Icon.Question)
            if buttons:
                msg.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        else:
            msg.setIcon(QMessageBox.Icon.Information)
        
        result = msg.exec()
        if buttons and result == QMessageBox.StandardButton.Yes:
            return buttons[0]
        elif buttons and result == QMessageBox.StandardButton.No:
            return buttons[1] if len(buttons) > 1 else "No"
        return "Ok"


class ModernCard(QFrame):
    clicked = pyqtSignal()
    
    def __init__(self, title, subtitle, icon_text, color="#3b82f6"):
        super().__init__()
        self.color = color
        self.setup_ui(title, subtitle, icon_text)
        
    def setup_ui(self, title, subtitle, icon_text):
        self.setFixedSize(280, 160)
        self.setStyleSheet(f"""
            ModernCard {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}, stop:1 {self.color}dd);
                border-radius: 20px; border: none;
            }}
            ModernCard:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}ee, stop:1 {self.color}cc);
            }}
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        
        icon_label = QLabel(icon_text)
        icon_label.setStyleSheet("QLabel { color: white; font-size: 36px; font-weight: bold; background: transparent; }")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("QLabel { color: white; font-size: 18px; font-weight: bold; background: transparent; margin-top: 10px; }")
        
        subtitle_label = QLabel(subtitle)
        subtitle_label.setStyleSheet("QLabel { color: rgba(255, 255, 255, 0.8); font-size: 14px; background: transparent; margin-top: 5px; }")
        
        layout.addWidget(icon_label)
        layout.addWidget(title_label)
        layout.addWidget(subtitle_label)
        layout.addStretch()
        self.setLayout(layout)
        
    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)


class SidebarButton(QPushButton):
    def __init__(self, text, icon_text="", is_active=False):
        super().__init__()
        self.icon_text = icon_text
        self.is_active = is_active
        self.setText(f"{icon_text}  {text}")
        self.setFixedHeight(60)
        self.update_style()
        
    def update_style(self):
        if self.is_active:
            self.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #3b82f6, stop:1 #1d4ed8);
                    color: white; border: none; border-radius: 15px; padding: 15px 20px;
                    font-size: 16px; font-weight: 600; text-align: left; margin: 5px;
                }
                QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #2563eb, stop:1 #1e40af); }
            """)
        else:
            self.setStyleSheet("""
                QPushButton {
                    background: transparent; color: #64748b; border: none; border-radius: 15px;
                    padding: 15px 20px; font-size: 16px; font-weight: 500; text-align: left; margin: 5px;
                }
                QPushButton:hover { background: rgba(59, 130, 246, 0.15); color: #3b82f6; font-weight: 600; }
            """)
    
    def set_active(self, active):
        self.is_active = active
        self.update_style()


class RevolutionaryMainWindow(QMainWindow):
    def __init__(self, usuario):
        super().__init__()
        self.usuario = usuario
        self.current_module = None
        self.sidebar_buttons = []

        self.setWindowTitle("PRO-2000 - Sistema de Gestión Revolucionario")
        self.setMinimumSize(900, 600)

        # ✅ MAXIMIZAR VENTANA AUTOMÁTICAMENTE
        self.showMaximized()

        # ✅ REPARADO: Controles de ventana restaurados
        # self.setWindowFlags(Qt.WindowType.FramelessWindowHint)

        self.setup_ui()
        self.setup_connections()
        self.show_dashboard()
        
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        self.create_sidebar()
        self.create_content_area()
        
        main_layout.addWidget(self.sidebar)
        main_layout.addWidget(self.content_area, 1)
        central_widget.setLayout(main_layout)
        self.apply_global_style()
        
    def create_sidebar(self):
        self.sidebar = QFrame()
        self.sidebar.setFixedWidth(280)
        self.sidebar.setStyleSheet("QFrame { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1e293b, stop:1 #0f172a); border: none; }")
        
        sidebar_layout = QVBoxLayout()
        sidebar_layout.setContentsMargins(20, 30, 20, 30)
        sidebar_layout.setSpacing(10)
        
        logo_label = QLabel("PRO-2000")
        logo_label.setStyleSheet("QLabel { color: white; font-size: 28px; font-weight: bold; padding: 20px 0; background: transparent; }")
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        user_info = QLabel(f"👤 {self.usuario.nombre}")
        user_info.setStyleSheet("QLabel { color: #94a3b8; font-size: 14px; padding: 10px 0; background: transparent; }")
        
        nav_buttons = [
            ("🏠", "Dashboard", "dashboard"), ("📄", "Artículos", "articulos"), ("🏗️", "Obras", "obras"),
            ("📏", "Perfiles", "perfiles"), ("🪟", "Persianas", "persianas"), ("🔧", "Accesorios", "accesorios"),
            ("💎", "Cristales", "cristales"), ("🏪", "Distribuidores", "distribuidores"), ("👥", "Clientes", "clientes"),
            ("📊", "Informes", "informes"), ("⚙️", "Configuración", "config")
        ]
        
        self.sidebar_buttons.clear()
        for icon, text, module in nav_buttons:
            btn = SidebarButton(text, icon, module == "dashboard")
            btn.clicked.connect(lambda checked=False, m=module: self.navigate_to(m))
            self.sidebar_buttons.append((btn, module))

        exit_btn = QPushButton("🚪  Salir")
        exit_btn.setFixedHeight(50)
        exit_btn.setStyleSheet("QPushButton { background: #dc2626; color: white; border: none; border-radius: 12px; padding: 12px; font-size: 14px; font-weight: 600; } QPushButton:hover { background: #b91c1c; }")
        exit_btn.clicked.connect(self.close_application)
        
        sidebar_layout.addWidget(logo_label)
        sidebar_layout.addWidget(user_info)
        sidebar_layout.addSpacing(20)
        for btn, _ in self.sidebar_buttons:
            sidebar_layout.addWidget(btn)
        sidebar_layout.addStretch()
        sidebar_layout.addWidget(exit_btn)
        self.sidebar.setLayout(sidebar_layout)

    def create_content_area(self):
        self.content_area = QFrame()
        self.content_area.setStyleSheet("QFrame { background: #f8fafc; border: none; }")
        
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(30, 30, 30, 30)
        
        self.content_header = QLabel("Dashboard")
        self.content_header.setStyleSheet("QLabel { color: #1e293b; font-size: 32px; font-weight: bold; padding: 0 0 30px 0; background: transparent; }")
        
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; background: transparent; } QScrollBar:vertical { background: #e2e8f0; width: 12px; border-radius: 6px; } QScrollBar::handle:vertical { background: #cbd5e1; border-radius: 6px; min-height: 20px; }")
        
        self.stacked_widget = QStackedWidget()
        scroll_area.setWidget(self.stacked_widget)
        
        content_layout.addWidget(self.content_header)
        content_layout.addWidget(scroll_area)
        self.content_area.setLayout(content_layout)

    def create_dashboard_widget(self):
        dashboard = QWidget()
        layout = QVBoxLayout()
        layout.setSpacing(30)

        stats_layout = QGridLayout()
        stats_layout.setSpacing(20)

        cards_data = [
            ("Artículos", "Gestionar productos", "📄", "#3b82f6"),
            ("Obras Activas", "Proyectos en curso", "🏗️", "#ef4444"),
            ("Perfiles", "Catálogo de perfiles", "📏", "#f59e0b"),
            ("Clientes", "Base de clientes", "👥", "#10b981"),
        ]

        for i, (title, subtitle, icon, color) in enumerate(cards_data):
            card = ModernCard(title, subtitle, icon, color)
            card.clicked.connect(lambda checked=False, t=title.lower(): self.navigate_to_module(t))
            stats_layout.addWidget(card, i // 2, i % 2)

        layout.addLayout(stats_layout)
        layout.addStretch()
        dashboard.setLayout(layout)
        return dashboard

    def navigate_to(self, module):
        print(f"🔄 Navegando a módulo: {module}")
        self.current_module = module

        for btn, btn_module in self.sidebar_buttons:
            btn.set_active(btn_module == module)

        module_names = {
            "dashboard": "Dashboard", "articulos": "📄 Gestión de Artículos", "obras": "🏗️ Gestión de Obras",
            "perfiles": "📏 Gestión de Perfiles", "persianas": "🪟 Gestión de Persianas", "accesorios": "🔧 Gestión de Accesorios",
            "cristales": "💎 Gestión de Cristales", "distribuidores": "🏪 Gestión de Distribuidores", "clientes": "👥 Gestión de Clientes",
            "informes": "📊 Informes y Reportes", "config": "⚙️ Configuración"
        }

        self.content_header.setText(module_names.get(module, module.title()))

        while self.stacked_widget.count():
            widget = self.stacked_widget.widget(0)
            self.stacked_widget.removeWidget(widget)
            widget.deleteLater()

        if module == "dashboard":
            self.show_dashboard()
        else:
            self.load_module(module)

    def show_dashboard(self):
        dashboard = self.create_dashboard_widget()
        self.stacked_widget.addWidget(dashboard)
        self.stacked_widget.setCurrentWidget(dashboard)

    def load_module(self, module):
        """✅ CARGA MÓDULOS REALES - Abre diálogos directamente"""
        try:
            print(f"🔧 Abriendo módulo REAL: {module}")
            
            if module == "articulos":
                from ui.modulos.articulos.articulo_dialog import ArticuloDialog
                dialog = ArticuloDialog(self)
                execute_dialog_maximized(dialog)
            elif module == "obras":
                from ui.modulos.obras.obra_dialog import ObraDialog
                dialog = ObraDialog(self)
                execute_dialog_maximized(dialog)
            elif module == "perfiles":
                from ui.modulos.perfiles.perfil_dialog import PerfilDialog
                dialog = PerfilDialog(self)
                execute_dialog_maximized(dialog)
            elif module == "persianas":
                from ui.modulos.persianas.persiana_dialog import PersianaDialog
                dialog = PersianaDialog(self)
                execute_dialog_maximized(dialog)
            elif module == "accesorios":
                from ui.modulos.accesorios.accesorio_dialog import AccesorioDialog
                dialog = AccesorioDialog(self)
                execute_dialog_maximized(dialog)
            elif module == "cristales":
                from ui.modulos.cristales.cristal_dialog import CristalDialog
                dialog = CristalDialog(self)
                execute_dialog_maximized(dialog)
            elif module == "distribuidores":
                from ui.modulos.distribuidores.distribuidor_dialog import DistribuidorDialog
                dialog = DistribuidorDialog(self)
                execute_dialog_maximized(dialog)
            elif module == "clientes":
                from ui.modulos.clientes.cliente_dialog import ClienteDialog
                dialog = ClienteDialog(self)
                execute_dialog_maximized(dialog)
            else:
                create_professional_message_box(self, f"Módulo {module.title()}", f"✅ Módulo {module} funcionando!\n🚀 Diálogo se abrirá aquí", "info")
                
        except ImportError as e:
            create_professional_message_box(self, "Módulo no encontrado", f"⚠️ No se encontró el módulo {module}\n\nError: {e}", "warning")
        except Exception as e:
            create_professional_message_box(self, "Error", f"❌ Error al abrir {module}: {e}", "error")

    def navigate_to_module(self, module):
        module_mapping = {"artículos": "articulos", "obras activas": "obras", "perfiles": "perfiles", "clientes": "clientes"}
        target_module = module_mapping.get(module, module)
        self.navigate_to(target_module)

    def close_application(self):
        result = create_professional_message_box(self, "🚪 Confirmar salida", "¿Está seguro de que desea salir de PRO-2000?\n\n✅ Todos los módulos funcionan correctamente", "question", ["Sí", "No"])
        if result == "Sí":
            QApplication.quit()

    def closeEvent(self, event):
        result = create_professional_message_box(self, "🚪 Confirmar salida", "¿Está seguro de que desea salir?", "question", ["Sí", "No"])
        if result == "Sí":
            event.accept()
        else:
            event.ignore()

    def setup_connections(self):
        pass

    def apply_global_style(self):
        self.setStyleSheet("""
            QMainWindow { background: #f8fafc; color: #1e293b; font-family: 'Segoe UI', system-ui, sans-serif; }
            QWidget { color: #1e293b; font-family: 'Segoe UI', system-ui, sans-serif; }
            QLabel { color: #1e293b; background: transparent; }
            QPushButton { color: #1e293b; background: #f1f5f9; border: 1px solid #cbd5e1; border-radius: 8px; padding: 8px 16px; font-weight: 500; }
            QPushButton:hover { background: #e2e8f0; border-color: #94a3b8; }
        """)
