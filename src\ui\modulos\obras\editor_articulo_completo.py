"""
Editor Co<PERSON><PERSON> de Artículos - Versión Profesional Interactiva
Combina todas las funcionalidades del editor anterior con nuevas capacidades interactivas
"""

import sys
import os
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Agregar el directorio raíz al path
root_dir = str(Path(__file__).parent.parent.parent.parent.absolute())
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QFrame,
    QGridLayout, QLineEdit, QSpinBox, QDoubleSpinBox, QTextEdit,
    QGroupBox, QScrollArea, QWidget, QSplitter, QMessageBox,
    QFileDialog, QComboBox, QCheckBox, QTabWidget, QTableWidget,
    QTableWidgetItem, QHeaderView, QFormLayout, QDialogButtonBox
)
from PyQt6.QtCore import Qt, QPoint, QRect, pyqtSignal, QTimer
from PyQt6.QtGui import (
    QPainter, QPen, QBrush, QColor, QFont, QPixmap,
    QMouseEvent, QPaintEvent, QFontMetrics, QCursor, QLinearGradient
)

try:
    from models.base import get_db
    from models.obra import Obra
    from models.articulo import Articulo, ObraArticulo, ArticuloPerfil, ArticuloAccesorio, ArticuloCristal
    from models.perfil import Perfil, TipoPerfil
    from models.cristal import Cristal
    from models.accesorio import Accesorio
    from ui.utils.window_utils import setup_maximized_dialog
    from ui.widgets.lienzo_profesional.selector_perfiles import SelectorPerfilesProfesional
except ImportError as e:
    print(f"⚠️ Error importando módulos: {e}")


class ElementoMedida:
    """Representa una medida visual en el canvas"""

    def __init__(self, tipo, texto, rect, color, orientacion="horizontal"):
        self.tipo = tipo
        self.texto = texto
        self.rect = rect
        self.color = color
        self.orientacion = orientacion
        self.seleccionado = False
        self.hover = False

    def contiene_punto(self, punto):
        """Verifica si un punto está dentro del elemento"""
        return self.rect.contains(punto)

    def dibujar(self, painter: QPainter):
        """Dibuja la medida en el canvas"""
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Configurar fuente
        font = QFont("Arial", 9, QFont.Weight.Bold)
        painter.setFont(font)

        if self.orientacion == "horizontal":
            self._dibujar_medida_horizontal(painter)
        elif self.orientacion == "vertical":
            self._dibujar_medida_vertical(painter)
        else:
            self._dibujar_medida_adicional(painter)

    def _dibujar_medida_horizontal(self, painter):
        """Dibuja una medida horizontal"""
        # Línea principal
        painter.setPen(QPen(self.color, 2))
        y_center = self.rect.center().y()
        painter.drawLine(self.rect.left(), y_center, self.rect.right(), y_center)

        # Flechas en los extremos
        self._dibujar_flecha(painter, QPoint(self.rect.left(), y_center), "izquierda")
        self._dibujar_flecha(painter, QPoint(self.rect.right(), y_center), "derecha")

        # Texto centrado con fondo
        self._dibujar_texto_medida(painter)

    def _dibujar_medida_vertical(self, painter):
        """Dibuja una medida vertical"""
        # Línea principal
        painter.setPen(QPen(self.color, 2))
        x_center = self.rect.center().x()
        painter.drawLine(x_center, self.rect.top(), x_center, self.rect.bottom())

        # Flechas en los extremos
        self._dibujar_flecha(painter, QPoint(x_center, self.rect.top()), "arriba")
        self._dibujar_flecha(painter, QPoint(x_center, self.rect.bottom()), "abajo")

        # Texto centrado con fondo
        self._dibujar_texto_medida(painter)

    def _dibujar_medida_adicional(self, painter):
        """Dibuja una medida adicional (sin flechas)"""
        self._dibujar_texto_medida(painter)

    def _dibujar_flecha(self, painter, punto, direccion):
        """Dibuja una flecha en la dirección especificada"""
        size = 8
        painter.setBrush(QBrush(self.color))

        if direccion == "izquierda":
            flecha = [
                QPoint(punto.x(), punto.y()),
                QPoint(punto.x() + size, punto.y() - size//2),
                QPoint(punto.x() + size, punto.y() + size//2)
            ]
        elif direccion == "derecha":
            flecha = [
                QPoint(punto.x(), punto.y()),
                QPoint(punto.x() - size, punto.y() - size//2),
                QPoint(punto.x() - size, punto.y() + size//2)
            ]
        elif direccion == "arriba":
            flecha = [
                QPoint(punto.x(), punto.y()),
                QPoint(punto.x() - size//2, punto.y() + size),
                QPoint(punto.x() + size//2, punto.y() + size)
            ]
        elif direccion == "abajo":
            flecha = [
                QPoint(punto.x(), punto.y()),
                QPoint(punto.x() - size//2, punto.y() - size),
                QPoint(punto.x() + size//2, punto.y() - size)
            ]

        painter.drawPolygon(flecha)

    def _dibujar_texto_medida(self, painter):
        """Dibuja el texto de la medida con fondo"""
        # Fondo blanco semi-transparente
        fm = QFontMetrics(painter.font())
        text_rect = fm.boundingRect(self.texto)
        text_rect.moveCenter(self.rect.center())
        text_rect.adjust(-4, -2, 4, 2)

        painter.fillRect(text_rect, QBrush(QColor(255, 255, 255, 200)))
        painter.setPen(QPen(QColor(100, 100, 100), 1))
        painter.drawRect(text_rect)

        # Texto
        painter.setPen(QPen(QColor(0, 0, 0), 1))
        painter.drawText(self.rect, Qt.AlignmentFlag.AlignCenter, self.texto)


class ElementoInteractivo:
    """Representa un elemento interactivo en el canvas (marco, hoja, travesaño, etc.)"""
    
    def __init__(self, tipo: str, nombre: str, rect: QRect, color: QColor = None):
        self.tipo = tipo  # "marco", "hoja", "travesano_h", "travesano_v", "cristal"
        self.nombre = nombre  # "Marco Superior", "Hoja Izquierda", etc.
        self.rect = rect  # Rectángulo del elemento
        self.color = color or QColor(100, 150, 255, 100)
        self.perfil_asignado = None  # Perfil de la BD asignado
        self.seleccionado = False
        self.hover = False
    
    def contiene_punto(self, punto: QPoint) -> bool:
        """Verifica si un punto está dentro del elemento"""
        return self.rect.contains(punto)
    
    def dibujar(self, painter: QPainter):
        """Dibuja el elemento en el canvas con máxima visibilidad"""
        # Configurar antialiasing
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Colores más contrastados y visibles
        if self.seleccionado:
            color = QColor(255, 100, 100, 220)
            borde_color = QColor(255, 0, 0)
            borde_grosor = 4
        elif self.hover:
            color = QColor(120, 255, 120, 180)
            borde_color = QColor(0, 200, 0)
            borde_grosor = 3
        else:
            # Colores SÚPER CONTRASTADOS y visibles
            if self.tipo == "marco":
                color = QColor(101, 67, 33, 255)  # Marrón SÓLIDO
                borde_color = QColor(0, 0, 0)  # Negro
            elif self.tipo == "hoja":
                color = QColor(70, 130, 180, 200)  # Azul acero SÓLIDO
                borde_color = QColor(0, 0, 0)  # Negro
            elif self.tipo.startswith("travesano"):
                color = QColor(255, 140, 0, 255)  # Naranja SÓLIDO
                borde_color = QColor(0, 0, 0)  # Negro
            elif self.tipo == "cristal":
                color = QColor(173, 216, 230, 180)  # Azul claro VISIBLE
                borde_color = QColor(0, 0, 0)  # Negro
            else:
                color = self.color
                borde_color = QColor(0, 0, 0)  # Negro
            borde_grosor = 4  # Bordes MÁS GRUESOS

        # Dibujar sombra más pronunciada
        if not self.tipo == "cristal":
            sombra_rect = QRect(self.rect.x() + 3, self.rect.y() + 3,
                               self.rect.width(), self.rect.height())
            painter.fillRect(sombra_rect, QBrush(QColor(0, 0, 0, 80)))

        # Dibujar rectángulo principal
        painter.fillRect(self.rect, QBrush(color))

        # Dibujar borde más grueso y visible
        pen = QPen(borde_color, borde_grosor)
        if self.seleccionado:
            pen.setStyle(Qt.PenStyle.DashLine)
        painter.setPen(pen)
        painter.drawRect(self.rect)

        # Dibujar borde interior para mayor definición
        if not self.seleccionado and not self.hover:
            pen_interior = QPen(QColor(255, 255, 255, 100), 1)
            painter.setPen(pen_interior)
            interior_rect = QRect(self.rect.x() + 1, self.rect.y() + 1,
                                 self.rect.width() - 2, self.rect.height() - 2)
            painter.drawRect(interior_rect)

        # Dibujar patrón según tipo
        self._dibujar_patron_tipo(painter)

        # Dibujar texto
        self._dibujar_texto(painter)

    def _dibujar_patron_tipo(self, painter):
        """Dibuja patrones específicos según el tipo de elemento"""
        if self.tipo == "cristal":
            # Patrón de cristal (líneas diagonales sutiles)
            painter.setPen(QPen(QColor(255, 255, 255, 100), 1))
            for i in range(0, self.rect.width(), 20):
                painter.drawLine(
                    self.rect.x() + i, self.rect.y(),
                    self.rect.x() + i + 10, self.rect.y() + 10
                )

        elif self.tipo == "marco":
            # Patrón de madera (líneas horizontales)
            painter.setPen(QPen(QColor(139, 69, 19, 80), 1))
            for i in range(0, self.rect.height(), 8):
                painter.drawLine(
                    self.rect.x() + 2, self.rect.y() + i,
                    self.rect.right() - 2, self.rect.y() + i
                )

        elif self.tipo.startswith("travesano"):
            # Patrón de metal (líneas centrales)
            painter.setPen(QPen(QColor(255, 215, 0, 120), 2))
            if self.tipo == "travesano_v":
                # Línea vertical central
                center_x = self.rect.x() + self.rect.width() // 2
                painter.drawLine(center_x, self.rect.y() + 2, center_x, self.rect.bottom() - 2)
            else:
                # Línea horizontal central
                center_y = self.rect.y() + self.rect.height() // 2
                painter.drawLine(self.rect.x() + 2, center_y, self.rect.right() - 2, center_y)

    def _dibujar_texto(self, painter):
        """Dibuja el texto del elemento con máxima legibilidad"""
        # Configurar fuente según tamaño del elemento (más grande)
        area = self.rect.width() * self.rect.height()
        if area > 15000:
            font_size = 12
        elif area > 8000:
            font_size = 10
        elif area > 4000:
            font_size = 9
        else:
            font_size = 8

        font = QFont("Arial", font_size, QFont.Weight.Bold)
        painter.setFont(font)

        # Texto del elemento
        texto = self.nombre
        if self.perfil_asignado:
            texto += f"\n{self.perfil_asignado.codigo}"

        # Mostrar texto SIEMPRE con máxima visibilidad
        if self.rect.width() > 30 and self.rect.height() > 20:
            # Fondo SÚPER VISIBLE para el texto
            fm = QFontMetrics(font)
            text_rect = fm.boundingRect(self.rect, Qt.AlignmentFlag.AlignCenter, texto)
            text_rect.adjust(-8, -4, 8, 4)

            # Centrar el fondo del texto
            center_x = self.rect.center().x()
            center_y = self.rect.center().y()
            text_rect.moveCenter(QPoint(center_x, center_y))

            # Fondo BLANCO SÓLIDO con borde NEGRO GRUESO
            painter.fillRect(text_rect, QBrush(QColor(255, 255, 255, 255)))  # SÓLIDO
            painter.setPen(QPen(QColor(0, 0, 0), 2))  # Borde negro grueso
            painter.drawRect(text_rect)

            # Texto NEGRO GRANDE Y BOLD
            painter.setPen(QPen(QColor(0, 0, 0), 2))  # Texto negro grueso
            painter.drawText(self.rect, Qt.AlignmentFlag.AlignCenter, texto)


class CanvasInteractivoCompleto(QWidget):
    """Canvas interactivo completo con elementos seleccionables"""

    elemento_seleccionado = pyqtSignal(object)  # Emite el elemento seleccionado
    medida_agregada = pyqtSignal(str, int, int, str)  # texto, x, y, tipo

    def __init__(self):
        super().__init__()
        self.setMinimumSize(700, 500)
        self.setStyleSheet("""
            QWidget {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
        """)

        print("🎨 CANVAS: Inicializando CanvasInteractivoCompleto como QWidget...")
        
        # Variables del canvas
        self.imagen_original = None
        self.imagen_escalada = None
        self.elementos_interactivos = []
        self.elemento_hover = None
        self.elemento_seleccionado_actual = None
        
        # Variables para medidas
        self.medidas = []
        self.modo_medida = False
        self.tipo_medida_actual = "horizontal"
        self.punto_inicio = None
        self.punto_fin = None
        self.arrastrando = False
        
        # Configurar cursor
        self.setCursor(QCursor(Qt.CursorShape.ArrowCursor))

        print("🎨 Inicializando CanvasInteractivoCompleto...")

        # Crear elementos por defecto INMEDIATAMENTE
        print("🔧 Creando elementos por defecto...")
        self._crear_elementos_por_defecto()

        # FORZAR creación si no hay elementos
        if len(self.elementos_interactivos) == 0:
            print("⚠️ No hay elementos después de crear por defecto! Forzando creación...")
            self._crear_elementos_basicos_forzados()

        print(f"✅ Canvas inicializado con {len(self.elementos_interactivos)} elementos")

        # FORZAR REPINTADO INMEDIATO
        self.update()
        self.repaint()

        print("🎨 CANVAS: Repintado forzado ejecutado")
    
    def _crear_elementos_por_defecto(self):
        """Crea los elementos interactivos por defecto para una ventana configurable"""
        print("🔧 Creando elementos por defecto...")

        # Configuración inicial
        self.divisiones_horizontales = 0  # Número de divisiones horizontales
        self.divisiones_verticales = 1    # Número de divisiones verticales (por defecto 1)

        print(f"📐 Configuración inicial: {self.divisiones_verticales}V x {self.divisiones_horizontales}H")

        self._generar_elementos_ventana()

        print(f"✅ Elementos creados: {len(self.elementos_interactivos)} elementos")

    def _generar_elementos_ventana(self):
        """Genera los elementos de la ventana según las divisiones configuradas"""
        print("🏗️ Generando elementos de ventana...")

        # Limpiar elementos existentes
        self.elementos_interactivos = []

        # Dimensiones del canvas con mejor proporción
        canvas_width = self.width() if self.width() > 0 else 700
        canvas_height = self.height() if self.height() > 0 else 500

        print(f"📏 Dimensiones canvas: {canvas_width}x{canvas_height}")

        # Calcular dimensiones óptimas para la ventana (MÁS PEQUEÑA y centrada)
        max_ventana_width = min(canvas_width * 0.6, 400)  # Máximo 60% del canvas o 400px
        max_ventana_height = min(canvas_height * 0.6, 280)  # Máximo 60% del canvas o 280px

        # Mantener proporción 4:3 aproximadamente (convertir a enteros)
        if max_ventana_width / max_ventana_height > 1.4:
            ventana_width = int(max_ventana_height * 1.4)
            ventana_height = int(max_ventana_height)
        else:
            ventana_width = int(max_ventana_width)
            ventana_height = int(max_ventana_width / 1.4)

        # Centrar la ventana (convertir a enteros)
        x_offset = int((canvas_width - ventana_width) / 2)
        y_offset = int((canvas_height - ventana_height) / 2)

        # Grosores proporcionales al tamaño (MÁS VISIBLES)
        grosor_marco = max(20, int(ventana_width * 0.06))  # 6% del ancho, mínimo 20px
        grosor_travesano = max(15, int(ventana_width * 0.04))  # 4% del ancho, mínimo 15px

        # 1. CREAR MARCO EXTERIOR
        self._crear_marco_exterior(x_offset, y_offset, ventana_width, ventana_height, grosor_marco)

        # 2. CREAR DIVISIONES Y HOJAS
        self._crear_divisiones_y_hojas(x_offset, y_offset, ventana_width, ventana_height, grosor_marco, grosor_travesano)

        # 3. CREAR CRISTALES
        self._crear_cristales(x_offset, y_offset, ventana_width, ventana_height, grosor_marco, grosor_travesano)

        # 4. COLOCAR MEDIDAS AUTOMÁTICAS SI HAY ARTÍCULO SELECCIONADO
        self._colocar_medidas_automaticas(x_offset, y_offset, ventana_width, ventana_height)

        print(f"🎯 Elementos finales creados: {len(self.elementos_interactivos)}")
        for i, elem in enumerate(self.elementos_interactivos):
            print(f"  {i+1}. {elem.tipo}: {elem.nombre} - {elem.rect}")

        # FORZAR CREACIÓN SI NO HAY ELEMENTOS
        if len(self.elementos_interactivos) == 0:
            print("⚠️ No hay elementos! Creando elementos básicos...")
            self._crear_elementos_basicos_forzados()

        self.update()

    def _crear_elementos_basicos_forzados(self):
        """Crea elementos básicos cuando falla la generación normal"""
        print("🚨 Creando elementos básicos forzados...")

        # Dimensiones fijas para prueba
        canvas_width = 700
        canvas_height = 500
        x = 150
        y = 100
        width = 400
        height = 300

        # Marco superior
        marco_sup = ElementoInteractivo(
            "marco", "Marco Superior",
            QRect(x, y, width, 30),
            QColor(101, 67, 33, 255)
        )
        self.elementos_interactivos.append(marco_sup)

        # Marco inferior
        marco_inf = ElementoInteractivo(
            "marco", "Marco Inferior",
            QRect(x, y + height - 30, width, 30),
            QColor(101, 67, 33, 255)
        )
        self.elementos_interactivos.append(marco_inf)

        # Marco izquierdo
        marco_izq = ElementoInteractivo(
            "marco", "Marco Izquierdo",
            QRect(x, y, 30, height),
            QColor(101, 67, 33, 255)
        )
        self.elementos_interactivos.append(marco_izq)

        # Marco derecho
        marco_der = ElementoInteractivo(
            "marco", "Marco Derecho",
            QRect(x + width - 30, y, 30, height),
            QColor(101, 67, 33, 255)
        )
        self.elementos_interactivos.append(marco_der)

        # Hoja izquierda
        hoja_izq = ElementoInteractivo(
            "hoja", "Hoja Izquierda",
            QRect(x + 30, y + 30, (width - 90) // 2, height - 60),
            QColor(70, 130, 180, 200)
        )
        self.elementos_interactivos.append(hoja_izq)

        # Travesaño central
        travesano = ElementoInteractivo(
            "travesano_v", "Travesaño Central",
            QRect(x + 30 + (width - 90) // 2, y + 30, 30, height - 60),
            QColor(255, 140, 0, 255)
        )
        self.elementos_interactivos.append(travesano)

        # Hoja derecha
        hoja_der = ElementoInteractivo(
            "hoja", "Hoja Derecha",
            QRect(x + 60 + (width - 90) // 2, y + 30, (width - 90) // 2, height - 60),
            QColor(70, 130, 180, 200)
        )
        self.elementos_interactivos.append(hoja_der)

        # Cristal izquierdo
        cristal_izq = ElementoInteractivo(
            "cristal", "Cristal Izquierdo",
            QRect(x + 40, y + 40, (width - 110) // 2, height - 80),
            QColor(173, 216, 230, 180)
        )
        self.elementos_interactivos.append(cristal_izq)

        # Cristal derecho
        cristal_der = ElementoInteractivo(
            "cristal", "Cristal Derecho",
            QRect(x + 70 + (width - 110) // 2, y + 40, (width - 110) // 2, height - 80),
            QColor(173, 216, 230, 180)
        )
        self.elementos_interactivos.append(cristal_der)

        print(f"✅ Elementos básicos creados: {len(self.elementos_interactivos)}")

    def _crear_elementos_emergencia(self):
        """Crea elementos de emergencia directamente en paintEvent"""
        print("🚨 Creando elementos de emergencia...")

        # Limpiar lista
        self.elementos_interactivos = []

        # Dimensiones fijas
        x, y, w, h = 100, 80, 300, 200

        # Marco superior - ROJO SÓLIDO
        marco_sup = ElementoInteractivo(
            "marco", "Marco Superior",
            QRect(x, y, w, 25),
            QColor(255, 0, 0, 255)  # ROJO SÓLIDO
        )
        self.elementos_interactivos.append(marco_sup)

        # Hoja izquierda - AZUL SÓLIDO
        hoja_izq = ElementoInteractivo(
            "hoja", "Hoja Izquierda",
            QRect(x + 25, y + 25, (w - 75) // 2, h - 50),
            QColor(0, 0, 255, 255)  # AZUL SÓLIDO
        )
        self.elementos_interactivos.append(hoja_izq)

        # Travesaño - VERDE SÓLIDO
        travesano = ElementoInteractivo(
            "travesano_v", "Travesaño",
            QRect(x + 25 + (w - 75) // 2, y + 25, 25, h - 50),
            QColor(0, 255, 0, 255)  # VERDE SÓLIDO
        )
        self.elementos_interactivos.append(travesano)

        # Hoja derecha - AMARILLO SÓLIDO
        hoja_der = ElementoInteractivo(
            "hoja", "Hoja Derecha",
            QRect(x + 50 + (w - 75) // 2, y + 25, (w - 75) // 2, h - 50),
            QColor(255, 255, 0, 255)  # AMARILLO SÓLIDO
        )
        self.elementos_interactivos.append(hoja_der)

        print(f"🚨 Elementos de emergencia creados: {len(self.elementos_interactivos)}")

    def _dibujar_elementos_prueba(self, painter):
        """Dibuja elementos de prueba DIRECTAMENTE sin clases"""
        # Rectángulo de prueba MAGENTA
        painter.fillRect(QRect(450, 100, 100, 80), QBrush(QColor(255, 0, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 3))
        painter.drawRect(QRect(450, 100, 100, 80))

        # Texto de prueba
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        font = QFont("Arial", 12, QFont.Weight.Bold)
        painter.setFont(font)
        painter.drawText(QRect(450, 100, 100, 80), Qt.AlignmentFlag.AlignCenter, "PRUEBA")

        # Círculo de prueba CYAN
        painter.setBrush(QBrush(QColor(0, 255, 255, 255)))
        painter.drawEllipse(QRect(580, 100, 60, 60))

    def _dibujar_ventana_basica_directa(self, painter):
        """Dibuja una ventana básica DIRECTAMENTE sin clases - GARANTIZADO"""
        # Configurar antialiasing
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Posición y tamaño de la ventana
        x, y = 50, 50
        w, h = 350, 250
        grosor_marco = 20
        grosor_travesano = 15

        # 1. MARCO EXTERIOR - MARRÓN SÓLIDO
        painter.fillRect(QRect(x, y, w, grosor_marco), QBrush(QColor(139, 69, 19, 255)))  # Superior
        painter.fillRect(QRect(x, y + h - grosor_marco, w, grosor_marco), QBrush(QColor(139, 69, 19, 255)))  # Inferior
        painter.fillRect(QRect(x, y, grosor_marco, h), QBrush(QColor(139, 69, 19, 255)))  # Izquierdo
        painter.fillRect(QRect(x + w - grosor_marco, y, grosor_marco, h), QBrush(QColor(139, 69, 19, 255)))  # Derecho

        # 2. TRAVESAÑO CENTRAL VERTICAL - NARANJA SÓLIDO
        travesano_x = x + w // 2 - grosor_travesano // 2
        painter.fillRect(QRect(travesano_x, y + grosor_marco, grosor_travesano, h - 2 * grosor_marco),
                        QBrush(QColor(255, 140, 0, 255)))

        # 3. HOJAS - AZUL SÓLIDO
        hoja_w = (w - 2 * grosor_marco - grosor_travesano) // 2
        hoja_h = h - 2 * grosor_marco

        # Hoja izquierda
        painter.fillRect(QRect(x + grosor_marco, y + grosor_marco, hoja_w, hoja_h),
                        QBrush(QColor(70, 130, 180, 200)))

        # Hoja derecha
        painter.fillRect(QRect(x + grosor_marco + hoja_w + grosor_travesano, y + grosor_marco, hoja_w, hoja_h),
                        QBrush(QColor(70, 130, 180, 200)))

        # 4. CRISTALES - AZUL CLARO
        cristal_margin = 8
        cristal_w = hoja_w - 2 * cristal_margin
        cristal_h = hoja_h - 2 * cristal_margin

        # Cristal izquierdo
        painter.fillRect(QRect(x + grosor_marco + cristal_margin, y + grosor_marco + cristal_margin,
                              cristal_w, cristal_h), QBrush(QColor(173, 216, 230, 150)))

        # Cristal derecho
        painter.fillRect(QRect(x + grosor_marco + hoja_w + grosor_travesano + cristal_margin,
                              y + grosor_marco + cristal_margin, cristal_w, cristal_h),
                        QBrush(QColor(173, 216, 230, 150)))

        # 5. BORDES NEGROS GRUESOS PARA MÁXIMA VISIBILIDAD
        painter.setPen(QPen(QColor(0, 0, 0), 3))

        # Bordes del marco
        painter.drawRect(QRect(x, y, w, grosor_marco))  # Superior
        painter.drawRect(QRect(x, y + h - grosor_marco, w, grosor_marco))  # Inferior
        painter.drawRect(QRect(x, y, grosor_marco, h))  # Izquierdo
        painter.drawRect(QRect(x + w - grosor_marco, y, grosor_marco, h))  # Derecho

        # Borde del travesaño
        painter.drawRect(QRect(travesano_x, y + grosor_marco, grosor_travesano, h - 2 * grosor_marco))

        # Bordes de las hojas
        painter.drawRect(QRect(x + grosor_marco, y + grosor_marco, hoja_w, hoja_h))
        painter.drawRect(QRect(x + grosor_marco + hoja_w + grosor_travesano, y + grosor_marco, hoja_w, hoja_h))

        # 6. ETIQUETAS CON FONDO BLANCO
        painter.setFont(QFont("Arial", 10, QFont.Weight.Bold))

        # Etiqueta "Marco Superior"
        self._dibujar_etiqueta(painter, "Marco Superior", x + w//2, y + grosor_marco//2)

        # Etiqueta "Hoja Izquierda"
        self._dibujar_etiqueta(painter, "Hoja Izq.", x + grosor_marco + hoja_w//2, y + grosor_marco + hoja_h//2)

        # Etiqueta "Travesaño"
        self._dibujar_etiqueta(painter, "Travesaño", travesano_x + grosor_travesano//2, y + grosor_marco + hoja_h//2)

        # Etiqueta "Hoja Derecha"
        self._dibujar_etiqueta(painter, "Hoja Der.", x + grosor_marco + hoja_w + grosor_travesano + hoja_w//2,
                              y + grosor_marco + hoja_h//2)

    def _dibujar_etiqueta(self, painter, texto, x, y):
        """Dibuja una etiqueta con fondo blanco"""
        fm = QFontMetrics(painter.font())
        text_rect = fm.boundingRect(texto)
        text_rect.moveCenter(QPoint(int(x), int(y)))
        text_rect.adjust(-4, -2, 4, 2)

        # Fondo blanco
        painter.fillRect(text_rect, QBrush(QColor(255, 255, 255, 255)))
        painter.setPen(QPen(QColor(0, 0, 0), 1))
        painter.drawRect(text_rect)

        # Texto negro
        painter.setPen(QPen(QColor(0, 0, 0), 1))
        painter.drawText(text_rect, Qt.AlignmentFlag.AlignCenter, texto)

    def _crear_marco_exterior(self, x, y, width, height, grosor):
        """Crea el marco exterior de la ventana"""
        # Marco superior
        marco_superior = ElementoInteractivo(
            "marco", "Marco Superior",
            QRect(x, y, width, grosor),
            QColor(101, 67, 33, 180)  # Marrón más oscuro
        )

        # Marco inferior
        marco_inferior = ElementoInteractivo(
            "marco", "Marco Inferior",
            QRect(x, y + height - grosor, width, grosor),
            QColor(101, 67, 33, 180)
        )

        # Marco izquierdo
        marco_izquierdo = ElementoInteractivo(
            "marco", "Marco Izquierdo",
            QRect(x, y, grosor, height),
            QColor(101, 67, 33, 180)
        )

        # Marco derecho
        marco_derecho = ElementoInteractivo(
            "marco", "Marco Derecho",
            QRect(x + width - grosor, y, grosor, height),
            QColor(101, 67, 33, 180)
        )

        self.elementos_interactivos.extend([
            marco_superior, marco_inferior, marco_izquierdo, marco_derecho
        ])

    def _crear_divisiones_y_hojas(self, x, y, width, height, grosor_marco, grosor_travesano):
        """Crea las divisiones y hojas según la configuración"""
        # Área interior (sin marco)
        interior_x = x + grosor_marco
        interior_y = y + grosor_marco
        interior_width = width - 2 * grosor_marco
        interior_height = height - 2 * grosor_marco

        # Calcular dimensiones de cada celda
        num_cols = self.divisiones_verticales + 1
        num_rows = self.divisiones_horizontales + 1

        # Ancho y alto de cada celda (incluyendo travesaños) - asegurar enteros
        celda_width = int(interior_width // num_cols)
        celda_height = int(interior_height // num_rows)

        # CREAR TRAVESAÑOS VERTICALES
        for i in range(self.divisiones_verticales):
            travesano_x = int(interior_x + (i + 1) * celda_width - grosor_travesano // 2)
            travesano = ElementoInteractivo(
                "travesano_v", f"Travesaño Vertical {i+1}",
                QRect(travesano_x, int(interior_y), grosor_travesano, int(interior_height)),
                QColor(255, 140, 0, 150)  # Naranja
            )
            self.elementos_interactivos.append(travesano)

        # CREAR TRAVESAÑOS HORIZONTALES
        for i in range(self.divisiones_horizontales):
            travesano_y = int(interior_y + (i + 1) * celda_height - grosor_travesano // 2)
            travesano = ElementoInteractivo(
                "travesano_h", f"Travesaño Horizontal {i+1}",
                QRect(int(interior_x), travesano_y, int(interior_width), grosor_travesano),
                QColor(255, 140, 0, 150)  # Naranja
            )
            self.elementos_interactivos.append(travesano)

        # CREAR HOJAS
        for row in range(num_rows):
            for col in range(num_cols):
                # Calcular posición de la hoja
                hoja_x = int(interior_x + col * celda_width)
                hoja_y = int(interior_y + row * celda_height)
                hoja_width = celda_width
                hoja_height = celda_height

                # Ajustar por travesaños
                if col > 0:  # No es la primera columna
                    hoja_x += grosor_travesano // 2
                    hoja_width -= grosor_travesano // 2
                if col < num_cols - 1:  # No es la última columna
                    hoja_width -= grosor_travesano // 2

                if row > 0:  # No es la primera fila
                    hoja_y += grosor_travesano // 2
                    hoja_height -= grosor_travesano // 2
                if row < num_rows - 1:  # No es la última fila
                    hoja_height -= grosor_travesano // 2

                # Crear la hoja
                hoja = ElementoInteractivo(
                    "hoja", f"Hoja {row+1}-{col+1}",
                    QRect(hoja_x, hoja_y, int(hoja_width), int(hoja_height)),
                    QColor(70, 130, 180, 100)  # Azul acero
                )
                self.elementos_interactivos.append(hoja)

    def _crear_cristales(self, x, y, width, height, grosor_marco, grosor_travesano):
        """Crea los cristales en cada hoja"""
        # Área interior
        interior_x = x + grosor_marco
        interior_y = y + grosor_marco
        interior_width = width - 2 * grosor_marco
        interior_height = height - 2 * grosor_marco

        # Calcular dimensiones
        num_cols = self.divisiones_verticales + 1
        num_rows = self.divisiones_horizontales + 1
        celda_width = interior_width // num_cols
        celda_height = interior_height // num_rows

        # Margen para el cristal dentro de cada hoja
        margen_cristal = 8

        for row in range(num_rows):
            for col in range(num_cols):
                # Calcular posición del cristal
                cristal_x = int(interior_x + col * celda_width + margen_cristal)
                cristal_y = int(interior_y + row * celda_height + margen_cristal)
                cristal_width = celda_width - 2 * margen_cristal
                cristal_height = celda_height - 2 * margen_cristal

                # Ajustar por travesaños
                if col > 0:
                    cristal_x += grosor_travesano // 2
                    cristal_width -= grosor_travesano // 2
                if col < num_cols - 1:
                    cristal_width -= grosor_travesano // 2

                if row > 0:
                    cristal_y += grosor_travesano // 2
                    cristal_height -= grosor_travesano // 2
                if row < num_rows - 1:
                    cristal_height -= grosor_travesano // 2

                # Crear el cristal
                cristal = ElementoInteractivo(
                    "cristal", f"Cristal {row+1}-{col+1}",
                    QRect(cristal_x, cristal_y, int(cristal_width), int(cristal_height)),
                    QColor(135, 206, 235, 80)  # Azul cielo claro
                )
                self.elementos_interactivos.append(cristal)

    def agregar_division_vertical(self):
        """Agrega una división vertical"""
        if self.divisiones_verticales < 4:  # Máximo 5 columnas
            self.divisiones_verticales += 1
            self._generar_elementos_ventana()

    def quitar_division_vertical(self):
        """Quita una división vertical"""
        if self.divisiones_verticales > 0:
            self.divisiones_verticales -= 1
            self._generar_elementos_ventana()

    def agregar_division_horizontal(self):
        """Agrega una división horizontal"""
        if self.divisiones_horizontales < 4:  # Máximo 5 filas
            self.divisiones_horizontales += 1
            self._generar_elementos_ventana()

    def quitar_division_horizontal(self):
        """Quita una división horizontal"""
        if self.divisiones_horizontales > 0:
            self.divisiones_horizontales -= 1
            self._generar_elementos_ventana()

    def _colocar_medidas_automaticas(self, x, y, width, height):
        """Coloca medidas automáticas en posiciones inteligentes"""
        if not hasattr(self, 'parent_editor') or not self.parent_editor:
            return

        try:
            # Obtener medidas del artículo actual
            medidas = self.parent_editor._obtener_medidas_actuales()
            if not medidas:
                return

            # Crear elementos de medida visual
            self._crear_elementos_medida(x, y, width, height, medidas)

        except Exception as e:
            print(f"⚠️ Error colocando medidas automáticas: {e}")

    def _crear_elementos_medida(self, x, y, width, height, medidas):
        """Crea elementos visuales para las medidas"""
        # Medida principal horizontal (anchura)
        if medidas.get('anchura', 0) > 0:
            medida_h = ElementoMedida(
                "medida_h", f"A = {medidas['anchura']:.0f}mm",
                QRect(int(x), int(y - 25), int(width), 20),
                QColor(255, 0, 0, 150), "horizontal"
            )
            self.elementos_interactivos.append(medida_h)

        # Medida principal vertical (altura)
        if medidas.get('altura', 0) > 0:
            medida_v = ElementoMedida(
                "medida_v", f"H = {medidas['altura']:.0f}mm",
                QRect(int(x - 80), int(y), 75, int(height)),
                QColor(255, 0, 0, 150), "vertical"
            )
            self.elementos_interactivos.append(medida_v)

        # Medidas adicionales si existen
        offset_y = 30
        for i, (codigo, valor) in enumerate(medidas.get('adicionales', {}).items()):
            if valor and valor > 0:
                if codigo.startswith('H'):  # Altura adicional
                    medida_adic = ElementoMedida(
                        f"medida_{codigo}", f"{codigo} = {valor:.0f}mm",
                        QRect(int(x - 160), int(y + i * offset_y), 75, 25),
                        QColor(0, 150, 255, 150), "adicional"
                    )
                else:  # Anchura adicional
                    medida_adic = ElementoMedida(
                        f"medida_{codigo}", f"{codigo} = {valor:.0f}mm",
                        QRect(int(x + i * 100), int(y - 50), 90, 20),
                        QColor(0, 150, 255, 150), "adicional"
                    )
                self.elementos_interactivos.append(medida_adic)

    def resizeEvent(self, event):
        """Reescala la imagen y regenera elementos cuando cambia el tamaño"""
        super().resizeEvent(event)
        if self.imagen_original:
            self._escalar_imagen()
        if hasattr(self, 'divisiones_verticales'):
            self._generar_elementos_ventana()
    
    def cargar_imagen(self, ruta_imagen):
        """Carga una imagen de fondo"""
        try:
            if os.path.exists(ruta_imagen):
                self.imagen_original = QPixmap(ruta_imagen)
                self._escalar_imagen()
                return True
            else:
                return False
        except Exception as e:
            print(f"Error cargando imagen: {e}")
            return False
    
    def _escalar_imagen(self):
        """Escala la imagen para ajustarla al widget"""
        if self.imagen_original:
            self.imagen_escalada = self.imagen_original.scaled(
                self.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
            self.setPixmap(self.imagen_escalada)
    
    def resizeEvent(self, event):
        """Reescala la imagen cuando cambia el tamaño del widget"""
        super().resizeEvent(event)
        if self.imagen_original:
            self._escalar_imagen()
    
    def mousePressEvent(self, event: QMouseEvent):
        """Maneja el clic del mouse"""
        if event.button() == Qt.MouseButton.LeftButton:
            if self.modo_medida:
                # Modo medición
                self.punto_inicio = event.pos()
                self.arrastrando = True
            else:
                # Modo selección de elementos
                elemento_clickeado = self._obtener_elemento_en_punto(event.pos())
                if elemento_clickeado:
                    # Deseleccionar elemento anterior
                    if self.elemento_seleccionado_actual:
                        self.elemento_seleccionado_actual.seleccionado = False
                    
                    # Seleccionar nuevo elemento
                    elemento_clickeado.seleccionado = True
                    self.elemento_seleccionado_actual = elemento_clickeado
                    
                    # Emitir señal
                    self.elemento_seleccionado.emit(elemento_clickeado)
                    
                    self.update()
        
        super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """Maneja el movimiento del mouse"""
        if self.arrastrando and self.punto_inicio:
            # Modo medición
            self.punto_fin = event.pos()
            self.update()
        else:
            # Modo hover
            elemento_hover = self._obtener_elemento_en_punto(event.pos())
            
            # Actualizar hover
            if self.elemento_hover != elemento_hover:
                if self.elemento_hover:
                    self.elemento_hover.hover = False
                
                self.elemento_hover = elemento_hover
                
                if self.elemento_hover:
                    self.elemento_hover.hover = True
                    self.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
                else:
                    self.setCursor(QCursor(Qt.CursorShape.ArrowCursor))
                
                self.update()
        
        super().mouseMoveEvent(event)
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        """Maneja la liberación del mouse"""
        if self.arrastrando and self.punto_inicio and self.punto_fin:
            # Finalizar medición
            x_centro = (self.punto_inicio.x() + self.punto_fin.x()) // 2
            y_centro = (self.punto_inicio.y() + self.punto_fin.y()) // 2
            
            from PyQt6.QtWidgets import QInputDialog
            texto, ok = QInputDialog.getText(
                self, 
                "Nueva Medida", 
                f"Ingrese la medida ({self.tipo_medida_actual}):"
            )
            
            if ok and texto:
                medida = {
                    'texto': texto,
                    'x1': self.punto_inicio.x(),
                    'y1': self.punto_inicio.y(),
                    'x2': self.punto_fin.x(),
                    'y2': self.punto_fin.y(),
                    'tipo': self.tipo_medida_actual
                }
                self.medidas.append(medida)
                self.medida_agregada.emit(texto, x_centro, y_centro, self.tipo_medida_actual)
                self.update()
        
        # Resetear variables
        self.punto_inicio = None
        self.punto_fin = None
        self.arrastrando = False
        self.desactivar_modo_medida()
        
        super().mouseReleaseEvent(event)
    
    def _obtener_elemento_en_punto(self, punto: QPoint) -> Optional[ElementoInteractivo]:
        """Obtiene el elemento que contiene el punto dado"""
        # Buscar en orden inverso para priorizar elementos superiores
        for elemento in reversed(self.elementos_interactivos):
            if elemento.contiene_punto(punto):
                return elemento
        return None
    
    def paintEvent(self, event: QPaintEvent):
        """Dibuja el canvas con elementos SIEMPRE VISIBLES"""
        print("🎨 PAINTEVENT: Ejecutando paintEvent...")

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        print(f"🎨 PAINTEVENT: Painter creado, canvas size: {self.width()}x{self.height()}")

        # Fondo con gradiente sutil
        gradient = QLinearGradient(0, 0, 0, self.height())
        gradient.setColorAt(0, QColor(250, 250, 250))
        gradient.setColorAt(1, QColor(240, 240, 240))
        painter.fillRect(self.rect(), QBrush(gradient))

        # Dibujar imagen de fondo si existe
        if hasattr(self, 'imagen_escalada') and self.imagen_escalada:
            painter.drawPixmap(0, 0, self.imagen_escalada)

        # FORZAR CREACIÓN DE ELEMENTOS SI NO EXISTEN
        if len(self.elementos_interactivos) == 0:
            print("🚨 PAINTEVENT: No hay elementos! Creando elementos de emergencia...")
            self._crear_elementos_emergencia()

        # Dibujar elementos interactivos
        for elemento in self.elementos_interactivos:
            elemento.dibujar(painter)

        # Dibujar medidas existentes
        for medida in self.medidas:
            self._dibujar_medida(painter, medida)

        # Dibujar información adicional
        self._dibujar_info_canvas(painter)

        # DIBUJAR ELEMENTOS DE PRUEBA DIRECTAMENTE - SIEMPRE VISIBLES
        self._dibujar_elementos_prueba(painter)

        # DIBUJAR VENTANA BÁSICA DIRECTAMENTE - GARANTIZADO
        self._dibujar_ventana_basica_directa(painter)
        
    def _dibujar_info_canvas(self, painter):
        """Dibuja información adicional en el canvas"""
        # Información de divisiones en la esquina superior izquierda
        info_text = f"Divisiones: {self.divisiones_verticales}V × {self.divisiones_horizontales}H"
        painter.setPen(QPen(QColor(100, 100, 100), 1))
        font = QFont("Arial", 10, QFont.Weight.Bold)
        painter.setFont(font)

        # Fondo para el texto
        fm = QFontMetrics(font)
        text_rect = fm.boundingRect(info_text)
        text_rect.adjust(-4, -2, 4, 2)
        text_rect.moveTo(10, 10)

        painter.fillRect(text_rect, QBrush(QColor(255, 255, 255, 180)))
        painter.setPen(QPen(QColor(150, 150, 150), 1))
        painter.drawRect(text_rect)

        painter.setPen(QPen(QColor(0, 0, 0), 1))
        painter.drawText(text_rect, Qt.AlignmentFlag.AlignCenter, info_text)

        # Información del artículo en la esquina superior derecha
        if hasattr(self, 'parent_editor') and self.parent_editor and self.parent_editor.articulo_actual:
            articulo_text = f"Artículo: {self.parent_editor.articulo_actual.codigo}"
            text_rect2 = fm.boundingRect(articulo_text)
            text_rect2.adjust(-4, -2, 4, 2)
            text_rect2.moveTo(self.width() - text_rect2.width() - 10, 10)

            painter.fillRect(text_rect2, QBrush(QColor(255, 255, 255, 180)))
            painter.setPen(QPen(QColor(150, 150, 150), 1))
            painter.drawRect(text_rect2)

            painter.setPen(QPen(QColor(0, 0, 0), 1))
            painter.drawText(text_rect2, Qt.AlignmentFlag.AlignCenter, articulo_text)

        # Dibujar línea temporal mientras se arrastra
        if self.arrastrando and self.punto_inicio and self.punto_fin:
            medida_temporal = {
                'texto': '',
                'x1': self.punto_inicio.x(),
                'y1': self.punto_inicio.y(),
                'x2': self.punto_fin.x(),
                'y2': self.punto_fin.y(),
                'tipo': self.tipo_medida_actual
            }
            self._dibujar_medida(painter, medida_temporal, temporal=True)
    
    def _dibujar_medida(self, painter, medida, temporal=False):
        """Dibuja una medida en el canvas"""
        # Configurar colores
        if temporal:
            color_linea = QColor(255, 0, 0, 128)
            color_texto = QColor(255, 0, 0)
        else:
            color_linea = QColor(33, 150, 243)
            color_texto = QColor(25, 118, 210)
        
        pen = QPen(color_linea, 2)
        painter.setPen(pen)
        
        x1, y1 = medida['x1'], medida['y1']
        x2, y2 = medida['x2'], medida['y2']
        
        # Dibujar línea principal
        painter.drawLine(x1, y1, x2, y2)
        
        # Dibujar marcadores en los extremos
        if medida['tipo'] == 'horizontal':
            painter.drawLine(x1, y1-5, x1, y1+5)
            painter.drawLine(x2, y2-5, x2, y2+5)
        else:
            painter.drawLine(x1-5, y1, x1+5, y1)
            painter.drawLine(x2-5, y2, x2+5, y2)
        
        # Dibujar texto si no es temporal
        if not temporal and medida['texto']:
            x_centro = (x1 + x2) // 2
            y_centro = (y1 + y2) // 2
            
            font = QFont("Arial", 10, QFont.Weight.Bold)
            painter.setFont(font)
            painter.setPen(QPen(color_texto))
            
            fm = QFontMetrics(font)
            rect_texto = fm.boundingRect(medida['texto'])
            rect_texto.moveCenter(QPoint(x_centro, y_centro))
            rect_texto.adjust(-3, -2, 3, 2)
            
            painter.fillRect(rect_texto, QBrush(QColor(255, 255, 255, 200)))
            painter.drawRect(rect_texto)
            painter.drawText(x_centro - rect_texto.width()//2, y_centro + 4, medida['texto'])
    
    def activar_modo_medida(self, tipo="horizontal"):
        """Activa el modo de colocación de medidas"""
        self.modo_medida = True
        self.tipo_medida_actual = tipo
        self.setCursor(Qt.CursorShape.CrossCursor)
    
    def desactivar_modo_medida(self):
        """Desactiva el modo de colocación de medidas"""
        self.modo_medida = False
        self.setCursor(Qt.CursorShape.ArrowCursor)
    
    def asignar_perfil_a_elemento_seleccionado(self, perfil):
        """Asigna un perfil al elemento seleccionado"""
        if self.elemento_seleccionado_actual:
            self.elemento_seleccionado_actual.perfil_asignado = perfil
            self.update()
    
    def obtener_elementos_con_perfiles(self):
        """Obtiene todos los elementos que tienen perfiles asignados"""
        return [elem for elem in self.elementos_interactivos if elem.perfil_asignado]
    
    def limpiar_medidas(self):
        """Limpia todas las medidas"""
        self.medidas.clear()
        self.update()
    
    def obtener_medidas(self):
        """Obtiene todas las medidas colocadas"""
        return self.medidas.copy()


class EditorArticuloCompleto(QDialog):
    """Editor completo de artículos con todas las funcionalidades profesionales"""

    def __init__(self, parent=None, obra=None, obra_articulo=None):
        print("🚀 EDITOR: Iniciando constructor EditorArticuloCompleto...")
        super().__init__(parent)
        print("🚀 EDITOR: Super().__init__ completado")

        self.obra = obra
        self.obra_articulo = obra_articulo
        self.articulo_actual = None
        self.diseno_data = {}
        print("🚀 EDITOR: Variables básicas inicializadas")

        self.setWindowTitle("Editor Profesional de Artículos")
        setup_maximized_dialog(self, "Editor Profesional de Artículos")
        print("🚀 EDITOR: Título y maximización configurados")

        print("🚀 EDITOR: Iniciando init_ui()...")
        self.init_ui()
        print("🚀 EDITOR: init_ui() completado")

        print("🚀 EDITOR: Cargando artículos...")
        self.cargar_articulos()
        print("🚀 EDITOR: Artículos cargados")

        if obra_articulo:
            print("🚀 EDITOR: Cargando datos de obra_articulo...")
            self.cargar_datos_obra_articulo()
            print("🚀 EDITOR: Datos de obra_articulo cargados")

        print("🚀 EDITOR: Constructor completado exitosamente")

    def init_ui(self):
        """Inicializa la interfaz de usuario"""
        print("🎨 INIT_UI: Iniciando init_ui()...")
        layout_principal = QVBoxLayout(self)
        layout_principal.setContentsMargins(8, 8, 8, 8)
        layout_principal.setSpacing(8)
        print("🎨 INIT_UI: Layout principal creado")

        # Header
        header = self._crear_header()
        layout_principal.addWidget(header)

        # Pestañas principales
        print("🎨 INIT_UI: Creando tabs...")
        self.tabs = QTabWidget()
        print("🎨 INIT_UI: Tabs creado")
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f5f5f5;
                color: #333;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QTabBar::tab:selected {
                background-color: #2196F3;
                color: white;
            }
            QTabBar::tab:hover:!selected {
                background-color: #e3f2fd;
            }
        """)

        # Crear pestañas
        print("🎨 INIT_UI: Creando tab principal...")
        self._crear_tab_principal()
        print("🎨 INIT_UI: Tab principal creado")

        print("🎨 INIT_UI: Creando tab medidas adicionales...")
        self._crear_tab_medidas_adicionales()
        print("🎨 INIT_UI: Tab medidas adicionales creado")

        print("🎨 INIT_UI: Creando tab perfiles interactivo...")
        self._crear_tab_perfiles_interactivo()
        print("🎨 INIT_UI: Tab perfiles interactivo creado")

        print("🎨 INIT_UI: Creando tab previsualización...")
        self._crear_tab_previsualizacion()
        print("🎨 INIT_UI: Tab previsualización creado")

        layout_principal.addWidget(self.tabs)

        # Botones de acción
        botones = self._crear_botones_accion()
        layout_principal.addWidget(botones)

    def _crear_header(self):
        """Crea el header con información de la obra"""
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2196F3, stop:1 #1976D2);
                border-radius: 8px;
                color: white;
                padding: 12px;
            }
        """)
        header.setFixedHeight(80)

        layout = QVBoxLayout(header)

        # Título
        titulo = QLabel("🏗️ EDITOR PROFESIONAL DE ARTÍCULOS")
        titulo.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")
        layout.addWidget(titulo)

        # Información de la obra
        info_obra = QLabel(f"Obra: {self.obra.codigo} - {self.obra.nombre}")
        info_obra.setStyleSheet("font-size: 14px; color: white;")
        layout.addWidget(info_obra)

        return header

    def _crear_tab_principal(self):
        """Crea la pestaña principal con selección y medidas básicas"""
        tab = QWidget()
        layout = QHBoxLayout(tab)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)

        # Panel izquierdo - Controles
        panel_controles = self._crear_panel_controles_principal()
        layout.addWidget(panel_controles)

        # Panel derecho - Canvas interactivo
        panel_canvas = self._crear_panel_canvas_interactivo()
        layout.addWidget(panel_canvas)

        # Configurar proporciones
        layout.setStretch(0, 1)  # Panel controles
        layout.setStretch(1, 2)  # Panel canvas

        self.tabs.addTab(tab, "🎯 Principal")

    def _crear_panel_controles_principal(self):
        """Crea el panel de controles principal"""
        panel = QFrame()
        panel.setFixedWidth(400)
        panel.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
            }
        """)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)

        # Selección de artículo
        grupo_articulo = self._crear_grupo_seleccion_articulo()
        layout.addWidget(grupo_articulo)

        # Medidas principales
        grupo_medidas = self._crear_grupo_medidas_principales()
        layout.addWidget(grupo_medidas)

        # Herramientas de medición
        grupo_herramientas = self._crear_grupo_herramientas_medicion()
        layout.addWidget(grupo_herramientas)

        # Controles de divisiones
        grupo_divisiones = self._crear_grupo_divisiones()
        layout.addWidget(grupo_divisiones)

        # Información del elemento seleccionado
        grupo_elemento = self._crear_grupo_elemento_seleccionado()
        layout.addWidget(grupo_elemento)

        layout.addStretch()

        return panel

    def _crear_grupo_seleccion_articulo(self):
        """Crea el grupo de selección de artículo"""
        grupo = QGroupBox("📦 Selección de Artículo")
        grupo.setStyleSheet(self._get_groupbox_style())

        layout = QVBoxLayout(grupo)
        layout.setSpacing(12)

        # Combo de artículos
        self.combo_articulo = QComboBox()
        self.combo_articulo.setFixedHeight(45)
        self.combo_articulo.setStyleSheet(self._get_combo_style())
        self.combo_articulo.currentTextChanged.connect(self.on_articulo_seleccionado)
        layout.addWidget(self.combo_articulo)

        # Información del artículo
        self.label_info_articulo = QLabel("Seleccione un artículo")
        self.label_info_articulo.setFixedHeight(80)
        self.label_info_articulo.setStyleSheet(self._get_info_style())
        self.label_info_articulo.setWordWrap(True)
        layout.addWidget(self.label_info_articulo)

        return grupo

    def _crear_grupo_medidas_principales(self):
        """Crea el grupo de medidas principales"""
        grupo = QGroupBox("📏 Medidas Principales")
        grupo.setStyleSheet(self._get_groupbox_style())

        layout = QGridLayout(grupo)
        layout.setSpacing(12)

        # Altura principal
        layout.addWidget(QLabel("H - Altura (mm):"), 0, 0)
        self.campo_altura = QDoubleSpinBox()
        self.campo_altura.setRange(1, 10000)
        self.campo_altura.setValue(1500)
        self.campo_altura.setFixedHeight(40)
        self.campo_altura.setStyleSheet(self._get_input_style())
        layout.addWidget(self.campo_altura, 0, 1)

        # Anchura principal
        layout.addWidget(QLabel("A - Anchura (mm):"), 1, 0)
        self.campo_anchura = QDoubleSpinBox()
        self.campo_anchura.setRange(1, 10000)
        self.campo_anchura.setValue(1200)
        self.campo_anchura.setFixedHeight(40)
        self.campo_anchura.setStyleSheet(self._get_input_style())
        layout.addWidget(self.campo_anchura, 1, 1)

        # Cantidad
        layout.addWidget(QLabel("Cantidad:"), 2, 0)
        self.campo_cantidad = QSpinBox()
        self.campo_cantidad.setRange(1, 1000)
        self.campo_cantidad.setValue(1)
        self.campo_cantidad.setFixedHeight(40)
        self.campo_cantidad.setStyleSheet(self._get_input_style())
        layout.addWidget(self.campo_cantidad, 2, 1)

        return grupo

    def _crear_grupo_herramientas_medicion(self):
        """Crea el grupo de herramientas de medición"""
        grupo = QGroupBox("🛠️ Herramientas de Medición")
        grupo.setStyleSheet(self._get_groupbox_style())

        layout = QVBoxLayout(grupo)
        layout.setSpacing(8)

        # Botones de herramientas
        btn_style = self._get_button_style("#2196F3")

        self.btn_medida_horizontal = QPushButton("📐 Medida Horizontal")
        self.btn_medida_horizontal.setStyleSheet(btn_style)
        self.btn_medida_horizontal.clicked.connect(lambda: self.activar_modo_medida("horizontal"))
        layout.addWidget(self.btn_medida_horizontal)

        self.btn_medida_vertical = QPushButton("📏 Medida Vertical")
        self.btn_medida_vertical.setStyleSheet(btn_style)
        self.btn_medida_vertical.clicked.connect(lambda: self.activar_modo_medida("vertical"))
        layout.addWidget(self.btn_medida_vertical)

        self.btn_limpiar_medidas = QPushButton("🗑️ Limpiar Medidas")
        self.btn_limpiar_medidas.setStyleSheet(self._get_button_style("#f44336"))
        self.btn_limpiar_medidas.clicked.connect(self.limpiar_medidas)
        layout.addWidget(self.btn_limpiar_medidas)

        return grupo

    def _crear_grupo_divisiones(self):
        """Crea el grupo de controles para divisiones"""
        grupo = QGroupBox("🔧 Configuración de Divisiones")
        grupo.setStyleSheet(self._get_groupbox_style())

        layout = QVBoxLayout(grupo)
        layout.setSpacing(12)

        # Divisiones verticales
        layout_v = QHBoxLayout()
        layout_v.addWidget(QLabel("Divisiones Verticales:"))

        self.btn_menos_v = QPushButton("➖")
        self.btn_menos_v.setFixedSize(30, 30)
        self.btn_menos_v.setStyleSheet(self._get_button_style("#f44336"))
        self.btn_menos_v.clicked.connect(self.quitar_division_vertical)
        layout_v.addWidget(self.btn_menos_v)

        self.label_divisiones_v = QLabel("1")
        self.label_divisiones_v.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_divisiones_v.setFixedWidth(30)
        self.label_divisiones_v.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 4px;
                font-weight: bold;
            }
        """)
        layout_v.addWidget(self.label_divisiones_v)

        self.btn_mas_v = QPushButton("➕")
        self.btn_mas_v.setFixedSize(30, 30)
        self.btn_mas_v.setStyleSheet(self._get_button_style("#4CAF50"))
        self.btn_mas_v.clicked.connect(self.agregar_division_vertical)
        layout_v.addWidget(self.btn_mas_v)

        layout_v.addStretch()
        layout.addLayout(layout_v)

        # Divisiones horizontales
        layout_h = QHBoxLayout()
        layout_h.addWidget(QLabel("Divisiones Horizontales:"))

        self.btn_menos_h = QPushButton("➖")
        self.btn_menos_h.setFixedSize(30, 30)
        self.btn_menos_h.setStyleSheet(self._get_button_style("#f44336"))
        self.btn_menos_h.clicked.connect(self.quitar_division_horizontal)
        layout_h.addWidget(self.btn_menos_h)

        self.label_divisiones_h = QLabel("0")
        self.label_divisiones_h.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_divisiones_h.setFixedWidth(30)
        self.label_divisiones_h.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 4px;
                font-weight: bold;
            }
        """)
        layout_h.addWidget(self.label_divisiones_h)

        self.btn_mas_h = QPushButton("➕")
        self.btn_mas_h.setFixedSize(30, 30)
        self.btn_mas_h.setStyleSheet(self._get_button_style("#4CAF50"))
        self.btn_mas_h.clicked.connect(self.agregar_division_horizontal)
        layout_h.addWidget(self.btn_mas_h)

        layout_h.addStretch()
        layout.addLayout(layout_h)

        # Botón de reset
        btn_reset = QPushButton("🔄 Resetear Divisiones")
        btn_reset.setStyleSheet(self._get_button_style("#FF9800"))
        btn_reset.clicked.connect(self.resetear_divisiones)
        layout.addWidget(btn_reset)

        return grupo

    def _crear_grupo_elemento_seleccionado(self):
        """Crea el grupo para mostrar información del elemento seleccionado"""
        grupo = QGroupBox("🎯 Elemento Seleccionado")
        grupo.setStyleSheet(self._get_groupbox_style())

        layout = QVBoxLayout(grupo)
        layout.setSpacing(12)

        # Información del elemento
        self.label_elemento_info = QLabel("Haga clic en un elemento del dibujo")
        self.label_elemento_info.setFixedHeight(60)
        self.label_elemento_info.setStyleSheet(self._get_info_style())
        self.label_elemento_info.setWordWrap(True)
        layout.addWidget(self.label_elemento_info)

        # Botón para asignar perfil
        self.btn_asignar_perfil = QPushButton("🔧 Asignar Perfil")
        self.btn_asignar_perfil.setStyleSheet(self._get_button_style("#4CAF50"))
        self.btn_asignar_perfil.setEnabled(False)
        self.btn_asignar_perfil.clicked.connect(self.asignar_perfil_elemento)
        layout.addWidget(self.btn_asignar_perfil)

        return grupo

    def _crear_panel_canvas_interactivo(self):
        """Crea el panel del canvas interactivo"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
            }
        """)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        # Header del canvas
        header_canvas = QFrame()
        header_canvas.setFixedHeight(50)
        header_canvas.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:1 #388E3C);
                border-radius: 6px;
                color: white;
            }
        """)

        layout_header = QHBoxLayout(header_canvas)

        titulo_canvas = QLabel("🖼️ Canvas Interactivo - Haga clic en los elementos")
        titulo_canvas.setStyleSheet("font-size: 14px; font-weight: bold; color: white;")
        layout_header.addWidget(titulo_canvas)

        layout_header.addStretch()

        # Botón para cargar imagen
        self.btn_cargar_imagen = QPushButton("📁 Cargar Imagen")
        self.btn_cargar_imagen.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 200);
                color: #2E7D32;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 255);
            }
        """)
        self.btn_cargar_imagen.clicked.connect(self.cargar_imagen_articulo)
        layout_header.addWidget(self.btn_cargar_imagen)

        layout.addWidget(header_canvas)

        # Canvas interactivo
        print("🎨 EDITOR: Creando canvas interactivo...")
        self.canvas = CanvasInteractivoCompleto()
        print(f"🎨 EDITOR: Canvas instanciado: {type(self.canvas)}")
        self.canvas.elemento_seleccionado.connect(self.on_elemento_seleccionado)
        self.canvas.medida_agregada.connect(self.on_medida_agregada)
        layout.addWidget(self.canvas)
        print(f"✅ EDITOR: Canvas agregado al layout con {len(self.canvas.elementos_interactivos)} elementos")

        # FORZAR VISIBILIDAD Y REPINTADO
        self.canvas.show()
        self.canvas.update()
        self.canvas.repaint()
        print("🎨 EDITOR: Canvas forzado a mostrar y repintar")

        return panel

    def _crear_tab_medidas_adicionales(self):
        """Crea la pestaña de medidas adicionales H1-H4, A1-A4"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)

        # Información
        info_label = QLabel("""
        <b>📐 Medidas Adicionales</b><br/>
        Las medidas adicionales son útiles para artículos complejos con múltiples dimensiones.<br/>
        Por ejemplo: ventanas con divisiones, marcos con diferentes alturas, etc.<br/>
        <i>Deje en 0 las medidas que no necesite.</i>
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                color: #1976D2;
                padding: 16px;
                background: #e3f2fd;
                border: 2px solid #2196F3;
                border-radius: 8px;
                font-size: 13px;
            }
        """)
        layout.addWidget(info_label)

        # Layout horizontal para los dos grupos
        grupos_layout = QHBoxLayout()

        # Grupo: Alturas Adicionales
        grupo_alturas = QGroupBox("📏 Alturas Adicionales")
        grupo_alturas.setStyleSheet(self._get_groupbox_style())
        layout_alturas = QGridLayout(grupo_alturas)

        self.campos_alturas_adicionales = {}

        for i, (codigo, descripcion) in enumerate([
            ('H1', 'Segunda altura'),
            ('H2', 'Tercera altura'),
            ('H3', 'Cuarta altura'),
            ('H4', 'Quinta altura')
        ]):
            row = i

            label = QLabel(f"{codigo} - {descripcion}:")
            label.setStyleSheet("font-weight: 600; color: #444;")
            layout_alturas.addWidget(label, row, 0)

            campo = QDoubleSpinBox()
            campo.setRange(0, 9999)
            campo.setDecimals(1)
            campo.setSuffix(" mm")
            campo.setValue(0.0)
            campo.setFixedHeight(40)
            campo.setStyleSheet(self._get_input_style())

            self.campos_alturas_adicionales[codigo] = campo
            layout_alturas.addWidget(campo, row, 1)

        grupos_layout.addWidget(grupo_alturas)

        # Grupo: Anchuras Adicionales
        grupo_anchuras = QGroupBox("📐 Anchuras Adicionales")
        grupo_anchuras.setStyleSheet(self._get_groupbox_style())
        layout_anchuras = QGridLayout(grupo_anchuras)

        self.campos_anchuras_adicionales = {}

        for i, (codigo, descripcion) in enumerate([
            ('A1', 'Segunda anchura'),
            ('A2', 'Tercera anchura'),
            ('A3', 'Cuarta anchura'),
            ('A4', 'Quinta anchura')
        ]):
            row = i

            label = QLabel(f"{codigo} - {descripcion}:")
            label.setStyleSheet("font-weight: 600; color: #444;")
            layout_anchuras.addWidget(label, row, 0)

            campo = QDoubleSpinBox()
            campo.setRange(0, 9999)
            campo.setDecimals(1)
            campo.setSuffix(" mm")
            campo.setValue(0.0)
            campo.setFixedHeight(40)
            campo.setStyleSheet(self._get_input_style())

            self.campos_anchuras_adicionales[codigo] = campo
            layout_anchuras.addWidget(campo, row, 1)

        grupos_layout.addWidget(grupo_anchuras)

        layout.addLayout(grupos_layout)

        # Botones de ayuda
        layout_botones = QHBoxLayout()

        btn_copiar_h = QPushButton("📋 Copiar H a todas las alturas")
        btn_copiar_h.setStyleSheet(self._get_button_style("#FF9800"))
        btn_copiar_h.clicked.connect(self._copiar_altura_principal)
        layout_botones.addWidget(btn_copiar_h)

        btn_copiar_a = QPushButton("📋 Copiar A a todas las anchuras")
        btn_copiar_a.setStyleSheet(self._get_button_style("#FF9800"))
        btn_copiar_a.clicked.connect(self._copiar_anchura_principal)
        layout_botones.addWidget(btn_copiar_a)

        btn_limpiar = QPushButton("🗑️ Limpiar adicionales")
        btn_limpiar.setStyleSheet(self._get_button_style("#f44336"))
        btn_limpiar.clicked.connect(self._limpiar_medidas_adicionales)
        layout_botones.addWidget(btn_limpiar)

        layout_botones.addStretch()
        layout.addLayout(layout_botones)

        layout.addStretch()

        self.tabs.addTab(tab, "📐 Medidas Adicionales")

    def _crear_tab_perfiles_interactivo(self):
        """Crea la pestaña de asignación interactiva de perfiles"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)

        # Información
        info_label = QLabel("""
        <b>🔧 Asignación Interactiva de Perfiles</b><br/>
        Haga clic en los elementos del dibujo (marco, hojas, travesaños) para asignarles perfiles específicos.<br/>
        Los elementos seleccionados se resaltarán y podrá asignarles perfiles de la base de datos.
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                color: #F57C00;
                padding: 16px;
                background: #fff3e0;
                border: 2px solid #FF9800;
                border-radius: 8px;
                font-size: 13px;
            }
        """)
        layout.addWidget(info_label)

        # Tabla de elementos con perfiles asignados
        grupo_tabla = QGroupBox("📋 Perfiles Asignados")
        grupo_tabla.setStyleSheet(self._get_groupbox_style())

        layout_tabla = QVBoxLayout(grupo_tabla)

        self.tabla_perfiles_asignados = QTableWidget()
        self.tabla_perfiles_asignados.setColumnCount(4)
        self.tabla_perfiles_asignados.setHorizontalHeaderLabels([
            "Elemento", "Tipo", "Perfil Asignado", "Código"
        ])

        # Configurar tabla
        header = self.tabla_perfiles_asignados.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)

        self.tabla_perfiles_asignados.setAlternatingRowColors(True)
        self.tabla_perfiles_asignados.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.tabla_perfiles_asignados.verticalHeader().setVisible(False)
        self.tabla_perfiles_asignados.setFixedHeight(200)

        self.tabla_perfiles_asignados.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                font-size: 12px;
                background-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f1f5f9;
            }
            QHeaderView::section {
                background-color: #f8fafc;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-size: 11px;
            }
        """)

        layout_tabla.addWidget(self.tabla_perfiles_asignados)

        # Botones de gestión
        botones_layout = QHBoxLayout()

        btn_actualizar_tabla = QPushButton("🔄 Actualizar Tabla")
        btn_actualizar_tabla.setStyleSheet(self._get_button_style("#2196F3"))
        btn_actualizar_tabla.clicked.connect(self.actualizar_tabla_perfiles)
        botones_layout.addWidget(btn_actualizar_tabla)

        btn_limpiar_perfiles = QPushButton("🗑️ Limpiar Perfiles")
        btn_limpiar_perfiles.setStyleSheet(self._get_button_style("#f44336"))
        btn_limpiar_perfiles.clicked.connect(self.limpiar_perfiles_asignados)
        botones_layout.addWidget(btn_limpiar_perfiles)

        botones_layout.addStretch()
        layout_tabla.addLayout(botones_layout)

        layout.addWidget(grupo_tabla)

        layout.addStretch()

        self.tabs.addTab(tab, "🔧 Perfiles Interactivo")

    def _crear_tab_previsualizacion(self):
        """Crea la pestaña de previsualización"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)

        # Información
        info_label = QLabel("""
        <b>👁️ Previsualización del Artículo</b><br/>
        Aquí puede ver un resumen completo del artículo configurado con todas sus medidas y perfiles.
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                color: #7B1FA2;
                padding: 16px;
                background: #f3e5f5;
                border: 2px solid #9C27B0;
                border-radius: 8px;
                font-size: 13px;
            }
        """)
        layout.addWidget(info_label)

        # Área de previsualización
        self.text_previsualizacion = QTextEdit()
        self.text_previsualizacion.setStyleSheet("""
            QTextEdit {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 16px;
                font-size: 12px;
                font-family: 'Courier New', monospace;
                background-color: #fafafa;
            }
        """)
        self.text_previsualizacion.setPlaceholderText("La previsualización aparecerá aquí...")
        layout.addWidget(self.text_previsualizacion)

        # Botón de actualización
        btn_actualizar_preview = QPushButton("🔄 Actualizar Previsualización")
        btn_actualizar_preview.setStyleSheet(self._get_button_style("#9C27B0"))
        btn_actualizar_preview.clicked.connect(self.actualizar_previsualizacion)
        layout.addWidget(btn_actualizar_preview)

        self.tabs.addTab(tab, "👁️ Previsualización")

    def _crear_botones_accion(self):
        """Crea los botones de acción del diálogo"""
        frame_botones = QFrame()
        frame_botones.setFixedHeight(70)
        frame_botones.setStyleSheet("""
            QFrame {
                background-color: #f5f5f5;
                border-top: 2px solid #e0e0e0;
            }
        """)

        layout = QHBoxLayout(frame_botones)
        layout.setContentsMargins(16, 16, 16, 16)

        # Botón cancelar
        btn_cancelar = QPushButton("❌ Cancelar")
        btn_cancelar.setFixedSize(120, 40)
        btn_cancelar.setStyleSheet(self._get_button_style("#f44336"))
        btn_cancelar.clicked.connect(self.reject)
        layout.addWidget(btn_cancelar)

        layout.addStretch()

        # Botón guardar
        btn_guardar = QPushButton("💾 Guardar Artículo")
        btn_guardar.setFixedSize(150, 40)
        btn_guardar.setStyleSheet(self._get_button_style("#4CAF50"))
        btn_guardar.clicked.connect(self.guardar_articulo)
        layout.addWidget(btn_guardar)

        return frame_botones

    # ==================== MÉTODOS DE ESTILO ====================

    def _get_groupbox_style(self):
        """Estilo para QGroupBox"""
        return """
            QGroupBox {
                font-weight: bold;
                font-size: 13px;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 8px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                background-color: white;
                color: #1976D2;
            }
        """

    def _get_combo_style(self):
        """Estilo para QComboBox"""
        return """
            QComboBox {
                padding: 10px 12px;
                font-size: 13px;
                font-weight: 500;
                color: #333;
                background-color: white;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                min-height: 20px;
            }
            QComboBox:focus {
                border-color: #2196F3;
                background-color: #f8f9fa;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid #666;
                width: 6px;
                height: 6px;
                border-top: none;
                border-right: none;
                transform: rotate(-45deg);
            }
        """

    def _get_input_style(self):
        """Estilo para campos de entrada"""
        return """
            QDoubleSpinBox, QSpinBox {
                padding: 10px 12px;
                font-size: 14px;
                font-weight: 500;
                color: #333;
                background-color: white;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                min-height: 20px;
            }
            QDoubleSpinBox:focus, QSpinBox:focus {
                border-color: #2196F3;
                background-color: #f8f9fa;
            }
            QDoubleSpinBox::up-button, QSpinBox::up-button {
                subcontrol-origin: border;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #e0e0e0;
                border-bottom: 1px solid #e0e0e0;
                border-top-right-radius: 4px;
                background-color: #f5f5f5;
            }
            QDoubleSpinBox::down-button, QSpinBox::down-button {
                subcontrol-origin: border;
                subcontrol-position: bottom right;
                width: 20px;
                border-left: 1px solid #e0e0e0;
                border-top: 1px solid #e0e0e0;
                border-bottom-right-radius: 4px;
                background-color: #f5f5f5;
            }
        """

    def _get_info_style(self):
        """Estilo para etiquetas de información"""
        return """
            QLabel {
                color: #555;
                font-size: 12px;
                font-weight: 500;
                padding: 12px;
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                line-height: 1.4;
            }
        """

    def _get_button_style(self, color):
        """Estilo para botones"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 12px;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: {self._darken_color(color)};
                transform: translateY(-1px);
            }}
            QPushButton:pressed {{
                background-color: {self._darken_color(color, 0.8)};
                transform: translateY(0px);
            }}
            QPushButton:disabled {{
                background-color: #cccccc;
                color: #666666;
            }}
        """

    def _darken_color(self, color, factor=0.9):
        """Oscurece un color"""
        if color.startswith('#'):
            color = color[1:]

        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)

        r = int(r * factor)
        g = int(g * factor)
        b = int(b * factor)

        return f"#{r:02x}{g:02x}{b:02x}"

    # ==================== MÉTODOS DE FUNCIONALIDAD ====================

    def cargar_articulos(self):
        """Carga los artículos disponibles"""
        try:
            print("📦 Cargando artículos...")
            db = next(get_db())
            articulos = db.query(Articulo).filter(Articulo.activo == True).all()
            print(f"📦 Encontrados {len(articulos)} artículos activos")

            self.combo_articulo.clear()
            self.combo_articulo.addItem("Seleccione un artículo...", None)

            for articulo in articulos:
                texto = f"{articulo.codigo} - {articulo.descripcion}"
                self.combo_articulo.addItem(texto, articulo)
                print(f"📦 Agregado: {texto}")

            db.close()
            print(f"✅ Combo cargado con {self.combo_articulo.count()} elementos")

        except Exception as e:
            print(f"❌ Error cargando artículos: {e}")
            QMessageBox.warning(self, "Error", f"Error cargando artículos: {e}")

    def on_articulo_seleccionado(self):
        """Maneja la selección de un artículo"""
        articulo = self.combo_articulo.currentData()
        if articulo:
            self.articulo_actual = articulo

            # Actualizar información
            info_text = f"""
            <b>Código:</b> {articulo.codigo}<br/>
            <b>Descripción:</b> {articulo.descripcion}<br/>
            <b>Serie:</b> {articulo.serie or 'No especificada'}
            """
            self.label_info_articulo.setText(info_text)

            # Actualizar previsualización
            self.actualizar_previsualizacion()
        else:
            self.articulo_actual = None
            self.label_info_articulo.setText("Seleccione un artículo")

    def activar_modo_medida(self, tipo):
        """Activa el modo de medición en el canvas"""
        self.canvas.activar_modo_medida(tipo)

        # Actualizar botones
        if tipo == "horizontal":
            self.btn_medida_horizontal.setText("🎯 Colocando medida horizontal...")
            self.btn_medida_horizontal.setEnabled(False)
        else:
            self.btn_medida_vertical.setText("🎯 Colocando medida vertical...")
            self.btn_medida_vertical.setEnabled(False)

    def on_medida_agregada(self, texto, x, y, tipo):
        """Maneja cuando se agrega una nueva medida"""
        # Restaurar botones
        self.btn_medida_horizontal.setText("📐 Medida Horizontal")
        self.btn_medida_horizontal.setEnabled(True)
        self.btn_medida_vertical.setText("📏 Medida Vertical")
        self.btn_medida_vertical.setEnabled(True)

        # Actualizar previsualización
        self.actualizar_previsualizacion()

        print(f"✅ Medida agregada: {texto} ({tipo}) en posición ({x}, {y})")

    def limpiar_medidas(self):
        """Limpia todas las medidas del canvas"""
        self.canvas.limpiar_medidas()
        self.actualizar_previsualizacion()

    def on_elemento_seleccionado(self, elemento):
        """Maneja la selección de un elemento en el canvas"""
        if elemento:
            # Actualizar información del elemento
            info_text = f"""
            <b>Elemento:</b> {elemento.nombre}<br/>
            <b>Tipo:</b> {elemento.tipo.replace('_', ' ').title()}<br/>
            <b>Perfil:</b> {elemento.perfil_asignado.codigo if elemento.perfil_asignado else 'Sin asignar'}
            """
            self.label_elemento_info.setText(info_text)
            self.btn_asignar_perfil.setEnabled(True)
        else:
            self.label_elemento_info.setText("Haga clic en un elemento del dibujo")
            self.btn_asignar_perfil.setEnabled(False)

    def asignar_perfil_elemento(self):
        """Abre el selector de perfiles para el elemento seleccionado"""
        if not self.canvas.elemento_seleccionado_actual:
            return

        elemento = self.canvas.elemento_seleccionado_actual

        try:
            # Determinar el tipo de perfil según el elemento
            tipo_filtro = None
            if elemento.tipo == "marco":
                tipo_filtro = "Marco"
            elif elemento.tipo == "hoja":
                tipo_filtro = "Hoja"
            elif elemento.tipo.startswith("travesano"):
                tipo_filtro = "Travesaño"

            # Abrir selector de perfiles
            selector = SelectorPerfilesProfesional(tipo_filtro, self)
            if selector.exec() == QDialog.DialogCode.Accepted:
                perfil_seleccionado = selector.obtener_perfil_seleccionado()
                if perfil_seleccionado:
                    # Asignar perfil al elemento
                    self.canvas.asignar_perfil_a_elemento_seleccionado(perfil_seleccionado)

                    # Actualizar información
                    self.on_elemento_seleccionado(elemento)

                    # Actualizar tabla de perfiles
                    self.actualizar_tabla_perfiles()

                    QMessageBox.information(
                        self,
                        "Perfil Asignado",
                        f"Perfil {perfil_seleccionado.codigo} asignado a {elemento.nombre}"
                    )

        except Exception as e:
            QMessageBox.warning(self, "Error", f"Error asignando perfil: {e}")

    def cargar_imagen_articulo(self):
        """Carga una imagen para el artículo"""
        archivo, _ = QFileDialog.getOpenFileName(
            self,
            "Seleccionar imagen del artículo",
            "",
            "Imágenes (*.png *.jpg *.jpeg *.bmp *.gif)"
        )

        if archivo:
            if self.canvas.cargar_imagen(archivo):
                QMessageBox.information(self, "Éxito", "Imagen cargada correctamente")
            else:
                QMessageBox.warning(self, "Error", "No se pudo cargar la imagen")

    def agregar_division_vertical(self):
        """Agrega una división vertical al canvas"""
        self.canvas.agregar_division_vertical()
        self._actualizar_labels_divisiones()
        self.actualizar_tabla_perfiles()

    def quitar_division_vertical(self):
        """Quita una división vertical del canvas"""
        self.canvas.quitar_division_vertical()
        self._actualizar_labels_divisiones()
        self.actualizar_tabla_perfiles()

    def agregar_division_horizontal(self):
        """Agrega una división horizontal al canvas"""
        self.canvas.agregar_division_horizontal()
        self._actualizar_labels_divisiones()
        self.actualizar_tabla_perfiles()

    def quitar_division_horizontal(self):
        """Quita una división horizontal del canvas"""
        self.canvas.quitar_division_horizontal()
        self._actualizar_labels_divisiones()
        self.actualizar_tabla_perfiles()

    def resetear_divisiones(self):
        """Resetea las divisiones a la configuración por defecto"""
        self.canvas.divisiones_verticales = 1
        self.canvas.divisiones_horizontales = 0
        self.canvas._generar_elementos_ventana()
        self._actualizar_labels_divisiones()
        self.actualizar_tabla_perfiles()

    def _actualizar_labels_divisiones(self):
        """Actualiza las etiquetas que muestran el número de divisiones"""
        if hasattr(self, 'label_divisiones_v'):
            self.label_divisiones_v.setText(str(self.canvas.divisiones_verticales))
        if hasattr(self, 'label_divisiones_h'):
            self.label_divisiones_h.setText(str(self.canvas.divisiones_horizontales))

    def actualizar_tabla_perfiles(self):
        """Actualiza la tabla de perfiles asignados"""
        elementos_con_perfiles = self.canvas.obtener_elementos_con_perfiles()

        self.tabla_perfiles_asignados.setRowCount(len(elementos_con_perfiles))

        for i, elemento in enumerate(elementos_con_perfiles):
            # Nombre del elemento
            item_nombre = QTableWidgetItem(elemento.nombre)
            self.tabla_perfiles_asignados.setItem(i, 0, item_nombre)

            # Tipo del elemento
            tipo_texto = elemento.tipo.replace('_', ' ').title()
            item_tipo = QTableWidgetItem(tipo_texto)
            self.tabla_perfiles_asignados.setItem(i, 1, item_tipo)

            # Perfil asignado
            if elemento.perfil_asignado:
                item_perfil = QTableWidgetItem(elemento.perfil_asignado.descripcion)
                item_codigo = QTableWidgetItem(elemento.perfil_asignado.codigo)
            else:
                item_perfil = QTableWidgetItem("Sin asignar")
                item_codigo = QTableWidgetItem("-")

            self.tabla_perfiles_asignados.setItem(i, 2, item_perfil)
            self.tabla_perfiles_asignados.setItem(i, 3, item_codigo)

    def limpiar_perfiles_asignados(self):
        """Limpia todos los perfiles asignados"""
        for elemento in self.canvas.elementos_interactivos:
            elemento.perfil_asignado = None

        self.canvas.update()
        self.actualizar_tabla_perfiles()

        # Actualizar información del elemento seleccionado si hay uno
        if self.canvas.elemento_seleccionado_actual:
            self.on_elemento_seleccionado(self.canvas.elemento_seleccionado_actual)

    def _copiar_altura_principal(self):
        """Copia la altura principal a todas las alturas adicionales"""
        altura_principal = self.campo_altura.value()

        for campo in self.campos_alturas_adicionales.values():
            campo.setValue(altura_principal)

        QMessageBox.information(self, "Copiado", f"Altura {altura_principal} mm copiada a todas las alturas adicionales")

    def _copiar_anchura_principal(self):
        """Copia la anchura principal a todas las anchuras adicionales"""
        anchura_principal = self.campo_anchura.value()

        for campo in self.campos_anchuras_adicionales.values():
            campo.setValue(anchura_principal)

        QMessageBox.information(self, "Copiado", f"Anchura {anchura_principal} mm copiada a todas las anchuras adicionales")

    def _limpiar_medidas_adicionales(self):
        """Limpia todas las medidas adicionales"""
        for campo in self.campos_alturas_adicionales.values():
            campo.setValue(0.0)

        for campo in self.campos_anchuras_adicionales.values():
            campo.setValue(0.0)

        QMessageBox.information(self, "Limpiado", "Todas las medidas adicionales han sido limpiadas")

    def actualizar_previsualizacion(self):
        """Actualiza la previsualización del artículo"""
        if not self.articulo_actual:
            self.text_previsualizacion.setText("Seleccione un artículo para ver la previsualización")
            return

        # Recopilar datos
        datos = self._recopilar_datos_articulo()

        # Generar texto de previsualización
        preview_text = self._generar_texto_previsualizacion(datos)

        self.text_previsualizacion.setText(preview_text)

    def _recopilar_datos_articulo(self):
        """Recopila todos los datos del artículo configurado"""
        datos = {
            'articulo': self.articulo_actual,
            'medidas_principales': {
                'altura': self.campo_altura.value(),
                'anchura': self.campo_anchura.value(),
                'cantidad': self.campo_cantidad.value()
            },
            'medidas_adicionales': {
                'alturas': {codigo: campo.value() for codigo, campo in self.campos_alturas_adicionales.items() if campo.value() > 0},
                'anchuras': {codigo: campo.value() for codigo, campo in self.campos_anchuras_adicionales.items() if campo.value() > 0}
            },
            'perfiles_asignados': self.canvas.obtener_elementos_con_perfiles(),
            'medidas_canvas': self.canvas.obtener_medidas()
        }

        return datos

    def _generar_texto_previsualizacion(self, datos):
        """Genera el texto de previsualización"""
        texto = []

        # Header
        texto.append("=" * 60)
        texto.append("📋 PREVISUALIZACIÓN DEL ARTÍCULO")
        texto.append("=" * 60)
        texto.append("")

        # Información del artículo
        articulo = datos['articulo']
        texto.append("🏷️ INFORMACIÓN DEL ARTÍCULO:")
        texto.append(f"   Código: {articulo.codigo}")
        texto.append(f"   Descripción: {articulo.descripcion}")
        texto.append(f"   Serie: {articulo.serie or 'No especificada'}")
        texto.append(f"   Tipo: {articulo.tipo or 'No especificado'}")
        texto.append("")

        # Medidas principales
        medidas = datos['medidas_principales']
        texto.append("📏 MEDIDAS PRINCIPALES:")
        texto.append(f"   H - Altura: {medidas['altura']} mm")
        texto.append(f"   A - Anchura: {medidas['anchura']} mm")
        texto.append(f"   Cantidad: {medidas['cantidad']} unidades")
        texto.append("")

        # Medidas adicionales
        adicionales = datos['medidas_adicionales']
        if adicionales['alturas'] or adicionales['anchuras']:
            texto.append("📐 MEDIDAS ADICIONALES:")

            if adicionales['alturas']:
                texto.append("   Alturas adicionales:")
                for codigo, valor in adicionales['alturas'].items():
                    texto.append(f"      {codigo}: {valor} mm")

            if adicionales['anchuras']:
                texto.append("   Anchuras adicionales:")
                for codigo, valor in adicionales['anchuras'].items():
                    texto.append(f"      {codigo}: {valor} mm")

            texto.append("")

        # Perfiles asignados
        perfiles = datos['perfiles_asignados']
        if perfiles:
            texto.append("🔧 PERFILES ASIGNADOS:")
            for elemento in perfiles:
                perfil = elemento.perfil_asignado
                texto.append(f"   {elemento.nombre}: {perfil.codigo} - {perfil.descripcion}")
            texto.append("")

        # Medidas del canvas
        medidas_canvas = datos['medidas_canvas']
        if medidas_canvas:
            texto.append("📐 MEDIDAS EN EL DIBUJO:")
            for i, medida in enumerate(medidas_canvas, 1):
                tipo_texto = "Horizontal" if medida['tipo'] == 'horizontal' else "Vertical"
                texto.append(f"   {i}. {medida['texto']} ({tipo_texto})")
            texto.append("")

        # Cálculos
        texto.append("💰 CÁLCULOS:")
        area_m2 = (medidas['altura'] * medidas['anchura']) / 1000000
        texto.append(f"   Área: {area_m2:.2f} m²")
        texto.append(f"   Área total: {area_m2 * medidas['cantidad']:.2f} m²")
        texto.append("")

        # Footer
        texto.append("=" * 60)
        texto.append("✅ Previsualización generada correctamente")
        texto.append("=" * 60)

        return "\n".join(texto)

    def cargar_datos_obra_articulo(self):
        """Carga los datos si se está editando un artículo existente"""
        if not self.obra_articulo:
            print("❌ No hay obra_articulo para cargar")
            return

        try:
            print(f"📋 Cargando datos de obra_articulo ID: {self.obra_articulo.id}")
            print(f"📋 Altura: {self.obra_articulo.altura}")
            print(f"📋 Anchura: {self.obra_articulo.anchura}")
            print(f"📋 Cantidad: {self.obra_articulo.cantidad}")

            # Cargar artículo
            articulo_encontrado = False
            for i in range(self.combo_articulo.count()):
                if self.combo_articulo.itemData(i) == self.obra_articulo.articulo:
                    self.combo_articulo.setCurrentIndex(i)
                    articulo_encontrado = True
                    print(f"✅ Artículo encontrado en posición {i}")
                    break

            if not articulo_encontrado:
                print("❌ Artículo no encontrado en combo")

            # Cargar medidas principales
            altura = self.obra_articulo.altura or 1500
            anchura = self.obra_articulo.anchura or 1200
            cantidad = self.obra_articulo.cantidad or 1

            print(f"📐 Estableciendo medidas: H={altura}, A={anchura}, Q={cantidad}")

            self.campo_altura.setValue(altura)
            self.campo_anchura.setValue(anchura)
            self.campo_cantidad.setValue(cantidad)

            print(f"✅ Medidas principales cargadas: H={self.campo_altura.value()}, A={self.campo_anchura.value()}")

            # Cargar medidas adicionales desde los campos específicos
            if hasattr(self.obra_articulo, 'altura_1') and self.obra_articulo.altura_1:
                self.campos_alturas_adicionales['H1'].setValue(self.obra_articulo.altura_1)
            if hasattr(self.obra_articulo, 'altura_2') and self.obra_articulo.altura_2:
                self.campos_alturas_adicionales['H2'].setValue(self.obra_articulo.altura_2)
            if hasattr(self.obra_articulo, 'altura_3') and self.obra_articulo.altura_3:
                self.campos_alturas_adicionales['H3'].setValue(self.obra_articulo.altura_3)
            if hasattr(self.obra_articulo, 'altura_4') and self.obra_articulo.altura_4:
                self.campos_alturas_adicionales['H4'].setValue(self.obra_articulo.altura_4)

            if hasattr(self.obra_articulo, 'anchura_1') and self.obra_articulo.anchura_1:
                self.campos_anchuras_adicionales['A1'].setValue(self.obra_articulo.anchura_1)
            if hasattr(self.obra_articulo, 'anchura_2') and self.obra_articulo.anchura_2:
                self.campos_anchuras_adicionales['A2'].setValue(self.obra_articulo.anchura_2)
            if hasattr(self.obra_articulo, 'anchura_3') and self.obra_articulo.anchura_3:
                self.campos_anchuras_adicionales['A3'].setValue(self.obra_articulo.anchura_3)
            if hasattr(self.obra_articulo, 'anchura_4') and self.obra_articulo.anchura_4:
                self.campos_anchuras_adicionales['A4'].setValue(self.obra_articulo.anchura_4)

            # Cargar datos de diseño desde notas si existen
            if self.obra_articulo.notas:
                try:
                    datos_diseno = json.loads(self.obra_articulo.notas)
                    self.diseno_data = datos_diseno

                    # Cargar configuración de divisiones si existe
                    if 'divisiones' in datos_diseno:
                        divisiones = datos_diseno['divisiones']
                        if 'verticales' in divisiones:
                            self.canvas.divisiones_verticales = divisiones['verticales']
                        if 'horizontales' in divisiones:
                            self.canvas.divisiones_horizontales = divisiones['horizontales']
                        self.canvas._generar_elementos_ventana()
                        self._actualizar_labels_divisiones()

                    # Cargar perfiles asignados si existen
                    if 'perfiles_asignados' in datos_diseno:
                        self._cargar_perfiles_desde_datos(datos_diseno['perfiles_asignados'])

                except json.JSONDecodeError:
                    print("⚠️ Error decodificando datos de diseño desde notas")

            # CARGAR AUTOMÁTICAMENTE PERFILES, ACCESORIOS Y CRISTALES DEL ARTÍCULO
            self._cargar_componentes_automaticos()

            # Configurar referencia al editor en el canvas
            self.canvas.parent_editor = self

            # Actualizar previsualización
            self.actualizar_previsualizacion()

        except Exception as e:
            QMessageBox.warning(self, "Error", f"Error cargando datos del artículo: {e}")

    def _cargar_componentes_automaticos(self):
        """Carga automáticamente perfiles, accesorios y cristales del artículo seleccionado"""
        if not self.articulo_actual:
            return

        try:
            db = next(get_db())

            # 🔧 CARGAR PERFILES AUTOMÁTICAMENTE
            perfiles_articulo = db.query(ArticuloPerfil).filter(
                ArticuloPerfil.articulo_id == self.articulo_actual.id
            ).all()

            print(f"🔧 Cargando {len(perfiles_articulo)} perfiles del artículo {self.articulo_actual.codigo}")

            for ap in perfiles_articulo:
                perfil = db.query(Perfil).filter(Perfil.id == ap.perfil_id).first()
                if perfil:
                    self._asignar_perfil_automatico(perfil, ap.tipo_elemento, ap.cantidad)

            # 🔩 CARGAR ACCESORIOS AUTOMÁTICAMENTE
            accesorios_articulo = db.query(ArticuloAccesorio).filter(
                ArticuloAccesorio.articulo_id == self.articulo_actual.id
            ).all()

            print(f"🔩 Cargando {len(accesorios_articulo)} accesorios del artículo {self.articulo_actual.codigo}")

            # 🔷 CARGAR CRISTALES AUTOMÁTICAMENTE
            cristales_articulo = db.query(ArticuloCristal).filter(
                ArticuloCristal.articulo_id == self.articulo_actual.id
            ).all()

            print(f"🔷 Cargando {len(cristales_articulo)} cristales del artículo {self.articulo_actual.codigo}")

            for ac in cristales_articulo:
                cristal = db.query(Cristal).filter(Cristal.id == ac.cristal_id).first()
                if cristal:
                    self._asignar_cristal_automatico(cristal, ac.espesor)

            db.close()

            # Actualizar interfaz
            self.canvas.update()
            self.actualizar_tabla_perfiles()

            print("✅ Componentes automáticos cargados correctamente")

        except Exception as e:
            print(f"⚠️ Error cargando componentes automáticos: {e}")

    def _asignar_perfil_automatico(self, perfil, tipo_elemento, cantidad):
        """Asigna un perfil automáticamente a elementos del tipo especificado"""
        elementos_asignados = 0

        for elemento in self.canvas.elementos_interactivos:
            if isinstance(elemento, ElementoInteractivo):
                # Mapear tipos de elemento
                if (tipo_elemento == "marco" and elemento.tipo == "marco") or \
                   (tipo_elemento == "hoja" and elemento.tipo == "hoja") or \
                   (tipo_elemento == "travesano" and elemento.tipo.startswith("travesano")):

                    if elementos_asignados < cantidad:
                        elemento.perfil_asignado = perfil
                        elementos_asignados += 1
                        print(f"  ✅ Perfil {perfil.codigo} asignado a {elemento.nombre}")

    def _asignar_cristal_automatico(self, cristal, espesor):
        """Asigna cristales automáticamente a elementos de cristal"""
        for elemento in self.canvas.elementos_interactivos:
            if isinstance(elemento, ElementoInteractivo) and elemento.tipo == "cristal":
                # Crear información del cristal (podríamos expandir esto)
                elemento.cristal_asignado = {
                    'cristal': cristal,
                    'espesor': espesor
                }
                print(f"  ✅ Cristal {cristal.codigo} asignado a {elemento.nombre}")

    def _obtener_medidas_actuales(self):
        """Obtiene las medidas actuales del artículo para mostrar en el canvas"""
        try:
            medidas = {
                'altura': float(self.campo_altura.value()) if self.campo_altura.value() > 0 else 0,
                'anchura': float(self.campo_anchura.value()) if self.campo_anchura.value() > 0 else 0,
                'adicionales': {}
            }

            # Medidas adicionales
            for codigo, campo in self.campos_alturas_adicionales.items():
                if campo.value() > 0:
                    medidas['adicionales'][codigo] = float(campo.value())

            for codigo, campo in self.campos_anchuras_adicionales.items():
                if campo.value() > 0:
                    medidas['adicionales'][codigo] = float(campo.value())

            return medidas

        except Exception as e:
            print(f"⚠️ Error obteniendo medidas: {e}")
            return None

    def _cargar_perfiles_desde_datos(self, perfiles_data):
        """Carga los perfiles asignados desde los datos guardados"""
        try:
            db = next(get_db())

            for perfil_info in perfiles_data:
                elemento_nombre = perfil_info.get('elemento')
                perfil_id = perfil_info.get('perfil_id')

                if elemento_nombre and perfil_id:
                    # Buscar el elemento en el canvas
                    elemento_encontrado = None
                    for elemento in self.canvas.elementos_interactivos:
                        if elemento.nombre == elemento_nombre:
                            elemento_encontrado = elemento
                            break

                    if elemento_encontrado:
                        # Buscar el perfil en la base de datos
                        perfil = db.query(Perfil).filter(Perfil.id == perfil_id).first()
                        if perfil:
                            elemento_encontrado.perfil_asignado = perfil

            db.close()
            self.canvas.update()
            self.actualizar_tabla_perfiles()

        except Exception as e:
            print(f"⚠️ Error cargando perfiles: {e}")

    def guardar_articulo(self):
        """Guarda el artículo en la obra"""
        if not self.articulo_actual:
            QMessageBox.warning(self, "Error", "Debe seleccionar un artículo")
            return

        try:
            # Recopilar todos los datos
            datos = self._recopilar_datos_articulo()

            # Preparar datos para guardar en notas
            datos_diseno = {
                'divisiones': {
                    'verticales': self.canvas.divisiones_verticales,
                    'horizontales': self.canvas.divisiones_horizontales
                },
                'perfiles_asignados': [
                    {
                        'elemento': elem.nombre,
                        'tipo': elem.tipo,
                        'perfil_id': elem.perfil_asignado.id if elem.perfil_asignado else None,
                        'perfil_codigo': elem.perfil_asignado.codigo if elem.perfil_asignado else None
                    }
                    for elem in datos['perfiles_asignados']
                ],
                'medidas_canvas': datos['medidas_canvas'],
                'timestamp': time.time()
            }

            db = next(get_db())

            if self.obra_articulo:
                # Actualizar artículo existente
                self.obra_articulo.articulo_id = self.articulo_actual.id
                self.obra_articulo.altura = datos['medidas_principales']['altura']
                self.obra_articulo.anchura = datos['medidas_principales']['anchura']
                self.obra_articulo.cantidad = datos['medidas_principales']['cantidad']

                # Guardar medidas adicionales en campos específicos
                medidas_adic = datos['medidas_adicionales']
                self.obra_articulo.altura_1 = medidas_adic['alturas'].get('H1', 0) or None
                self.obra_articulo.altura_2 = medidas_adic['alturas'].get('H2', 0) or None
                self.obra_articulo.altura_3 = medidas_adic['alturas'].get('H3', 0) or None
                self.obra_articulo.altura_4 = medidas_adic['alturas'].get('H4', 0) or None
                self.obra_articulo.anchura_1 = medidas_adic['anchuras'].get('A1', 0) or None
                self.obra_articulo.anchura_2 = medidas_adic['anchuras'].get('A2', 0) or None
                self.obra_articulo.anchura_3 = medidas_adic['anchuras'].get('A3', 0) or None
                self.obra_articulo.anchura_4 = medidas_adic['anchuras'].get('A4', 0) or None

                # Guardar datos de diseño en notas
                self.obra_articulo.notas = json.dumps(datos_diseno)

                mensaje = "Artículo actualizado correctamente"
            else:
                # Crear nuevo artículo en la obra
                medidas_adic = datos['medidas_adicionales']
                nuevo_obra_articulo = ObraArticulo(
                    obra_id=self.obra.id,
                    articulo_id=self.articulo_actual.id,
                    altura=datos['medidas_principales']['altura'],
                    anchura=datos['medidas_principales']['anchura'],
                    cantidad=datos['medidas_principales']['cantidad'],
                    altura_1=medidas_adic['alturas'].get('H1', 0) or None,
                    altura_2=medidas_adic['alturas'].get('H2', 0) or None,
                    altura_3=medidas_adic['alturas'].get('H3', 0) or None,
                    altura_4=medidas_adic['alturas'].get('H4', 0) or None,
                    anchura_1=medidas_adic['anchuras'].get('A1', 0) or None,
                    anchura_2=medidas_adic['anchuras'].get('A2', 0) or None,
                    anchura_3=medidas_adic['anchuras'].get('A3', 0) or None,
                    anchura_4=medidas_adic['anchuras'].get('A4', 0) or None,
                    notas=json.dumps(datos_diseno)
                )

                db.add(nuevo_obra_articulo)
                mensaje = "Artículo agregado correctamente a la obra"

            db.commit()
            db.close()

            QMessageBox.information(self, "Éxito", mensaje)
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error guardando artículo: {e}")


# ==================== FUNCIÓN DE PRUEBA ====================

def main():
    """Función principal para pruebas"""
    import sys
    from PyQt6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # Crear una obra de prueba
    class ObraPrueba:
        def __init__(self):
            self.id = 1
            self.codigo = "TEST-001"
            self.nombre = "Obra de Prueba"

    obra = ObraPrueba()

    # Crear y mostrar el editor
    editor = EditorArticuloCompleto(obra=obra)
    editor.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
