"""
Script para inicializar la base de datos y crear las tablas necesarias.
"""
import sys
import os
from pathlib import Path

# Asegurarse de que el directorio raíz esté en el PYTHONPATH
root_dir = str(Path(__file__).parent.absolute())
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

from models.base import init_db, engine, Base

def main():
    print("Inicializando base de datos...")
    
    # Crear todas las tablas
    try:
        init_db()
        print("\n¡Base de datos inicializada correctamente!")
        print(f"Ubicación: {engine.url}")
        
        # Mostrar información de las tablas creadas
        print("\nTablas creadas:")
        for table in Base.metadata.tables:
            print(f"  - {table}")
            
    except Exception as e:
        print(f"\nError al inicializar la base de datos: {e}", file=sys.stderr)
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
