"""
Dashboard ejecutivo moderno para PRO-2000
Diseño profesional con métricas clave y visualización de datos
"""

import sys
import os
from pathlib import Path

# Agregar el directorio raíz al path
root_dir = str(Path(__file__).parent.parent.parent.parent.absolute())
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QFrame, QGridLayout, QPushButton, QProgressBar,
                            QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt6.QtCore import Qt, QTimer
from datetime import datetime

try:
    from models.base import get_db
    from models.obra import Obra
    from models.cliente import Cliente
    from models.articulo import Articulo
except ImportError:
    # Fallback para cuando no se pueden importar los modelos
    print("No se pudieron importar los modelos, usando datos de prueba")


class ModernMetricCard(QFrame):
    """Card moderno para mostrar métricas"""
    
    def __init__(self, title, value, subtitle="", icon="📊", color="#3b82f6"):
        super().__init__()
        self.setObjectName("card")
        self.setMinimumHeight(120)
        self.setMaximumHeight(140)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(24, 20, 24, 20)
        layout.setSpacing(8)
        
        # Header con icono y título
        header_layout = QHBoxLayout()
        
        # Icono
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                color: {color};
                background-color: {color}20;
                border-radius: 8px;
                padding: 8px;
                min-width: 40px;
                max-width: 40px;
                min-height: 40px;
                max-height: 40px;
            }}
        """)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(icon_label)
        
        header_layout.addStretch()
        
        # Título
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: #64748b;
                font-size: 14px;
                font-weight: 500;
                text-align: right;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        header_layout.addWidget(title_label)
        
        layout.addLayout(header_layout)
        
        # Valor principal
        self.value_label = QLabel(str(value))
        self.value_label.setStyleSheet(f"""
            QLabel {{
                color: {color};
                font-size: 32px;
                font-weight: 700;
                margin: 4px 0;
            }}
        """)
        layout.addWidget(self.value_label)
        
        # Subtítulo
        if subtitle:
            subtitle_label = QLabel(subtitle)
            subtitle_label.setStyleSheet("""
                QLabel {
                    color: #94a3b8;
                    font-size: 12px;
                    font-weight: 400;
                }
            """)
            layout.addWidget(subtitle_label)
    
    def update_value(self, value):
        """Actualiza el valor de la métrica"""
        self.value_label.setText(str(value))


class ModernProgressCard(QFrame):
    """Card moderno con barra de progreso"""
    
    def __init__(self, title, progress=0, subtitle=""):
        super().__init__()
        self.setObjectName("card")
        self.setMinimumHeight(140)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(24, 20, 24, 20)
        layout.setSpacing(12)
        
        # Título
        title_label = QLabel(title)
        title_label.setObjectName("subtitle")
        layout.addWidget(title_label)
        
        # Valor del progreso
        self.progress_value = QLabel(f"{progress}%")
        self.progress_value.setStyleSheet("""
            QLabel {
                color: #2563eb;
                font-size: 28px;
                font-weight: 700;
            }
        """)
        layout.addWidget(self.progress_value)
        
        # Barra de progreso
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(progress)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 6px;
                background-color: #e2e8f0;
                height: 8px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #2563eb;
                border-radius: 6px;
            }
        """)
        self.progress_bar.setTextVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Subtítulo
        if subtitle:
            subtitle_label = QLabel(subtitle)
            subtitle_label.setStyleSheet("""
                QLabel {
                    color: #94a3b8;
                    font-size: 12px;
                }
            """)
            layout.addWidget(subtitle_label)
    
    def update_progress(self, value):
        """Actualiza el progreso"""
        self.progress_bar.setValue(value)
        self.progress_value.setText(f"{value}%")


class ModernTableCard(QFrame):
    """Card moderno con tabla"""
    
    def __init__(self, title, columns):
        super().__init__()
        self.setObjectName("card")
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(24, 20, 24, 20)
        layout.setSpacing(16)
        
        # Header con título y botón
        header_layout = QHBoxLayout()
        
        title_label = QLabel(title)
        title_label.setObjectName("subtitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Botón de ver todo
        view_all_btn = QPushButton("Ver todo →")
        view_all_btn.setObjectName("secondary_button")
        view_all_btn.setMaximumWidth(100)
        header_layout.addWidget(view_all_btn)
        
        layout.addLayout(header_layout)
        
        # Tabla
        self.table = QTableWidget()
        self.table.setColumnCount(len(columns))
        self.table.setHorizontalHeaderLabels(columns)
        
        # Configurar tabla
        header = self.table.horizontalHeader()
        for i in range(len(columns)):
            if i == 1:  # Columna principal (nombre/descripción)
                header.setSectionResizeMode(i, QHeaderView.ResizeMode.Stretch)
            else:
                header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)
        
        self.table.setAlternatingRowColors(False)  # Desactivar filas alternadas
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setMaximumHeight(300)
        
        layout.addWidget(self.table)


class DashboardModerno(QWidget):
    """Dashboard ejecutivo moderno"""
    
    def __init__(self):
        super().__init__()
        self.setObjectName("dashboard_moderno")
        self.init_ui()
        self.apply_styles()
        self.load_data()

        # Timer para actualizar datos cada 5 minutos
        self.timer = QTimer()
        self.timer.timeout.connect(self.load_data)
        self.timer.start(300000)  # 5 minutos
        
    def init_ui(self):
        """Inicializa la interfaz moderna"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(32)
        
        # Header del dashboard
        self.create_dashboard_header()
        layout.addWidget(self.header_frame)
        
        # Grid de métricas principales
        self.create_metrics_grid()
        layout.addWidget(self.metrics_frame)
        
        # Sección de contenido principal
        content_layout = QHBoxLayout()
        content_layout.setSpacing(24)
        
        # Columna izquierda - Obras y actividad
        left_column = QVBoxLayout()
        left_column.setSpacing(24)
        
        self.create_recent_obras()
        left_column.addWidget(self.recent_obras_card)
        
        content_layout.addLayout(left_column, 2)
        
        # Columna derecha - Estadísticas y progreso
        right_column = QVBoxLayout()
        right_column.setSpacing(24)
        
        self.create_progress_section()
        right_column.addWidget(self.progress_card)
        
        self.create_quick_actions()
        right_column.addWidget(self.actions_card)
        
        content_layout.addLayout(right_column, 1)
        
        layout.addLayout(content_layout)

    def apply_styles(self):
        """Aplica estilos al dashboard moderno"""
        self.setStyleSheet("""
            QWidget#dashboard_moderno {
                background-color: #f8fafc;
                color: #1e293b;
            }

            QFrame#card {
                background-color: white;
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                padding: 16px;
            }

            QFrame#card:hover {
                border-color: #cbd5e1;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }

            QLabel {
                color: #334155;
            }

            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: bold;
            }

            QPushButton:hover {
                background-color: #2563eb;
            }

            QPushButton:pressed {
                background-color: #1d4ed8;
            }

            QTableWidget {
                background-color: white;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                gridline-color: #f1f5f9;
            }

            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f1f5f9;
            }

            QTableWidget::item:selected {
                background-color: #eff6ff;
                color: #1e40af;
            }

            QHeaderView::section {
                background-color: #f8fafc;
                color: #475569;
                padding: 12px;
                border: none;
                border-bottom: 2px solid #e2e8f0;
                font-weight: bold;
            }

            QProgressBar {
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                text-align: center;
                background-color: #f1f5f9;
            }

            QProgressBar::chunk {
                background-color: #10b981;
                border-radius: 6px;
            }
        """)

    def create_dashboard_header(self):
        """Crea el header del dashboard"""
        self.header_frame = QFrame()
        layout = QHBoxLayout(self.header_frame)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Información de bienvenida
        welcome_layout = QVBoxLayout()
        
        welcome_label = QLabel("¡Bienvenido de vuelta!")
        welcome_label.setStyleSheet("""
            QLabel {
                color: #64748b;
                font-size: 16px;
                font-weight: 500;
            }
        """)
        welcome_layout.addWidget(welcome_label)
        
        date_label = QLabel(datetime.now().strftime("%A, %d de %B de %Y"))
        date_label.setStyleSheet("""
            QLabel {
                color: #94a3b8;
                font-size: 14px;
            }
        """)
        welcome_layout.addWidget(date_label)
        
        layout.addLayout(welcome_layout)
        layout.addStretch()
        
        # Botón de actualizar
        refresh_btn = QPushButton("🔄 Actualizar")
        refresh_btn.setObjectName("secondary_button")
        refresh_btn.clicked.connect(self.load_data)
        layout.addWidget(refresh_btn)
        
    def create_metrics_grid(self):
        """Crea el grid de métricas principales"""
        self.metrics_frame = QFrame()
        grid_layout = QGridLayout(self.metrics_frame)
        grid_layout.setSpacing(24)
        grid_layout.setContentsMargins(0, 0, 0, 0)
        
        # Métricas principales
        self.total_obras_card = ModernMetricCard(
            "Total Obras", "0", "Todas las obras registradas", "🏗️", "#3b82f6"
        )
        self.obras_activas_card = ModernMetricCard(
            "Obras Activas", "0", "En progreso actualmente", "⚡", "#10b981"
        )
        self.total_clientes_card = ModernMetricCard(
            "Clientes", "0", "Base de clientes activa", "👥", "#8b5cf6"
        )
        self.articulos_card = ModernMetricCard(
            "Artículos", "0", "Catálogo de productos", "🪟", "#f59e0b"
        )
        
        grid_layout.addWidget(self.total_obras_card, 0, 0)
        grid_layout.addWidget(self.obras_activas_card, 0, 1)
        grid_layout.addWidget(self.total_clientes_card, 0, 2)
        grid_layout.addWidget(self.articulos_card, 0, 3)
        
    def create_recent_obras(self):
        """Crea la tabla de obras recientes"""
        self.recent_obras_card = ModernTableCard(
            "Obras Recientes", 
            ["Código", "Cliente", "Estado", "Fecha"]
        )
        
    def create_progress_section(self):
        """Crea la sección de progreso"""
        self.progress_card = ModernProgressCard(
            "Progreso General de Obras",
            0,
            "Porcentaje de obras completadas"
        )
        
    def create_quick_actions(self):
        """Crea el panel de acciones rápidas"""
        self.actions_card = QFrame()
        self.actions_card.setObjectName("card")
        
        layout = QVBoxLayout(self.actions_card)
        layout.setContentsMargins(24, 20, 24, 20)
        layout.setSpacing(16)
        
        # Título
        title_label = QLabel("Acciones Rápidas")
        title_label.setObjectName("subtitle")
        layout.addWidget(title_label)
        
        # Botones de acción
        actions = [
            ("+ Nueva Obra", "🏗️"),
            ("+ Nuevo Cliente", "👤"),
            ("+ Nuevo Artículo", "🪟"),
            ("📊 Informes", "📋")
        ]
        
        for text, icon in actions:
            btn = QPushButton(f"{icon}  {text}")
            btn.setObjectName("secondary_button")
            btn.setMinimumHeight(45)
            layout.addWidget(btn)
        
        layout.addStretch()
        
    def load_data(self):
        """Carga los datos del dashboard"""
        try:
            # Intentar cargar datos reales de la base de datos
            try:
                db = next(get_db())

                # Contar obras
                total_obras = db.query(Obra).count()
                obras_activas = db.query(Obra).filter(Obra.estado == "En Progreso").count()
                obras_completadas = db.query(Obra).filter(Obra.estado == "Completada").count()

                # Contar clientes
                total_clientes = db.query(Cliente).count()

                # Contar artículos
                total_articulos = db.query(Articulo).count()

                db.close()

            except Exception as db_error:
                print(f"Error accediendo a la base de datos: {db_error}")
                # Usar datos de ejemplo si hay problemas con la BD
                total_obras = 15
                obras_activas = 8
                obras_completadas = 7
                total_clientes = 25
                total_articulos = 120

            # Actualizar métricas
            self.total_obras_card.update_value(total_obras)
            self.obras_activas_card.update_value(obras_activas)
            self.total_clientes_card.update_value(total_clientes)
            self.articulos_card.update_value(total_articulos)

            # Actualizar progreso
            if total_obras > 0:
                progress = int((obras_completadas / total_obras) * 100)
                self.progress_card.update_progress(progress)
            else:
                self.progress_card.update_progress(0)

            # Cargar obras recientes
            self.load_recent_obras_safe()

        except Exception as e:
            print(f"Error general cargando datos del dashboard: {e}")
            # Cargar datos por defecto
            self.load_default_data()
                
    def load_recent_obras_safe(self):
        """Carga las obras recientes en la tabla de forma segura"""
        try:
            # Intentar cargar datos reales
            try:
                db = next(get_db())
                obras = db.query(Obra).order_by(Obra.fecha_inicio.desc()).limit(8).all()
                db.close()

                table = self.recent_obras_card.table
                table.setRowCount(len(obras))

                for i, obra in enumerate(obras):
                    # Código
                    table.setItem(i, 0, QTableWidgetItem(obra.codigo or f"OBR-{i+1:03d}"))

                    # Cliente
                    cliente_nombre = obra.cliente.nombre if obra.cliente else "Sin cliente"
                    table.setItem(i, 1, QTableWidgetItem(cliente_nombre))

                    # Estado
                    estado_item = QTableWidgetItem(obra.estado or "Pendiente")
                    table.setItem(i, 2, estado_item)

                    # Fecha
                    fecha = obra.fecha_inicio.strftime("%d/%m/%Y") if obra.fecha_inicio else datetime.now().strftime("%d/%m/%Y")
                    table.setItem(i, 3, QTableWidgetItem(fecha))

            except Exception as db_error:
                print(f"Error accediendo a obras: {db_error}")
                self.load_sample_obras()

        except Exception as e:
            print(f"Error cargando obras recientes: {e}")
            self.load_sample_obras()

    def load_sample_obras(self):
        """Carga obras de ejemplo"""
        table = self.recent_obras_card.table
        sample_data = [
            ["OBR-001", "Cliente Ejemplo 1", "En Progreso", "15/06/2024"],
            ["OBR-002", "Cliente Ejemplo 2", "Completada", "14/06/2024"],
            ["OBR-003", "Cliente Ejemplo 3", "En Progreso", "13/06/2024"],
            ["OBR-004", "Cliente Ejemplo 4", "Pendiente", "12/06/2024"],
        ]

        table.setRowCount(len(sample_data))
        for i, row_data in enumerate(sample_data):
            for j, cell_data in enumerate(row_data):
                table.setItem(i, j, QTableWidgetItem(cell_data))

    def load_default_data(self):
        """Carga datos por defecto en caso de error"""
        # Valores por defecto
        self.total_obras_card.update_value(15)
        self.obras_activas_card.update_value(8)
        self.total_clientes_card.update_value(25)
        self.articulos_card.update_value(120)
        self.progress_card.update_progress(47)
        self.load_sample_obras()
