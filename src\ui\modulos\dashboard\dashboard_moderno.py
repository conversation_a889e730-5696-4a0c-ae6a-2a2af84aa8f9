"""
Dashboard Moderno para PRO-2000
Diseño limpio y funcional sin scroll, ajustado perfectamente a la ventana
"""

import sys
import os
from pathlib import Path

# Agregar el directorio raíz al path
root_dir = str(Path(__file__).parent.parent.parent.parent.absolute())
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, 
    QGridLayout, QPushButton, QProgressBar, QTableWidget, 
    QTableWidgetItem, QHeaderView, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont
from datetime import datetime

try:
    from models.base import get_db
    from models.obra import Obra
    from models.cliente import Cliente
    from models.articulo import Articulo
except ImportError:
    print("⚠️ No se pudieron importar los modelos")


class MetricCard(QFrame):
    """Tarjeta de métrica compacta"""
    
    def __init__(self, title, value, icon, color):
        super().__init__()
        self.setFixedSize(200, 100)
        self.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}, stop:1 {self._darken_color(color)});
                border-radius: 10px;
                border: 2px solid rgba(255,255,255,0.3);
            }}
            QFrame:hover {{
                border: 2px solid rgba(255,255,255,0.6);
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(4)
        
        # Título
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            color: white;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # Valor
        self.value_label = QLabel(str(value))
        self.value_label.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
        """)
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.value_label)
        
        # Icono
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("""
            color: white;
            font-size: 16px;
        """)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(icon_label)
    
    def _darken_color(self, color):
        """Oscurece un color para el gradiente"""
        colors = {
            "#3b82f6": "#2563eb",
            "#10b981": "#059669",
            "#f59e0b": "#d97706",
            "#ef4444": "#dc2626"
        }
        return colors.get(color, color)
    
    def update_value(self, value):
        """Actualiza el valor de la métrica"""
        self.value_label.setText(str(value))


class CompactTable(QFrame):
    """Tabla compacta para obras recientes"""
    
    def __init__(self, title):
        super().__init__()
        self.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 10px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)
        
        # Header
        header_layout = QHBoxLayout()
        
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #1e293b;
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        refresh_btn = QPushButton("🔄")
        refresh_btn.setFixedSize(30, 30)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 15px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_data)
        header_layout.addWidget(refresh_btn)
        
        layout.addLayout(header_layout)
        
        # Tabla
        self.table = QTableWidget()
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(["Código", "Cliente", "Estado", "Fecha"])
        self.table.setMaximumHeight(180)
        self.table.verticalHeader().setVisible(False)
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        # Configurar columnas
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        
        self.table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e2e8f0;
                font-size: 11px;
                background-color: white;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #f1f5f9;
            }
            QHeaderView::section {
                background-color: #f8fafc;
                padding: 8px;
                border: none;
                font-weight: bold;
                font-size: 10px;
            }
        """)
        
        layout.addWidget(self.table)
        
        # Cargar datos iniciales
        self.refresh_data()
    
    def refresh_data(self):
        """Actualiza los datos de la tabla"""
        try:
            db = next(get_db())
            obras = db.query(Obra).order_by(Obra.fecha_inicio.desc()).limit(6).all()
            
            self.table.setRowCount(len(obras))
            
            for i, obra in enumerate(obras):
                self.table.setItem(i, 0, QTableWidgetItem(obra.codigo or ""))
                
                # Cliente
                cliente_nombre = "Sin cliente"
                if obra.cliente_id:
                    try:
                        cliente = db.query(Cliente).filter(Cliente.id == obra.cliente_id).first()
                        if cliente:
                            cliente_nombre = cliente.nombre
                    except:
                        pass
                
                self.table.setItem(i, 1, QTableWidgetItem(cliente_nombre))
                self.table.setItem(i, 2, QTableWidgetItem(obra.estado or ""))
                
                fecha_str = ""
                if obra.fecha_inicio:
                    fecha_str = obra.fecha_inicio.strftime("%d/%m/%Y")
                self.table.setItem(i, 3, QTableWidgetItem(fecha_str))
            
            db.close()
            
        except Exception as e:
            print(f"Error cargando obras: {e}")
            # Datos de ejemplo si hay error
            self.table.setRowCount(3)
            self.table.setItem(0, 0, QTableWidgetItem("PRUEB"))
            self.table.setItem(0, 1, QTableWidgetItem("Cliente Ejemplo"))
            self.table.setItem(0, 2, QTableWidgetItem("En Progreso"))
            self.table.setItem(0, 3, QTableWidgetItem("09/07/2025"))


class ActionPanel(QFrame):
    """Panel de acciones rápidas"""

    def __init__(self, parent_window=None):
        super().__init__()
        self.parent_window = parent_window
        self.setFixedHeight(200)
        self.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 10px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)

        # Título
        title = QLabel("Acciones Rápidas")
        title.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #1e293b;
        """)
        layout.addWidget(title)

        # Botones
        button_style = """
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
        """

        buttons = [
            ("🏗️  Nueva Obra", self.nueva_obra),
            ("👤  Nuevo Cliente", self.nuevo_cliente),
            ("🪟  Nuevo Artículo", self.nuevo_articulo),
            ("📊  Ver Informes", self.ver_informes)
        ]

        for text, callback in buttons:
            btn = QPushButton(text)
            btn.setStyleSheet(button_style)
            btn.setFixedHeight(30)
            btn.clicked.connect(callback)
            layout.addWidget(btn)
    
    def nueva_obra(self):
        """Abre el módulo de gestión de obras"""
        try:
            # Buscar la ventana principal
            main_window = self._find_main_window()
            if main_window and hasattr(main_window, 'abrir_obras'):
                main_window.abrir_obras()
                print("✅ Módulo de obras abierto desde dashboard")
            else:
                QMessageBox.information(None, "Nueva Obra", "🏗️ Abriendo módulo de gestión de obras...")
                print("🏗️ Acción: Nueva Obra solicitada")
        except Exception as e:
            QMessageBox.warning(None, "Error", f"Error abriendo módulo de obras: {e}")

    def nuevo_cliente(self):
        """Abre el módulo de gestión de clientes"""
        try:
            # Buscar la ventana principal
            main_window = self._find_main_window()
            if main_window and hasattr(main_window, 'abrir_clientes'):
                main_window.abrir_clientes()
                print("✅ Módulo de clientes abierto desde dashboard")
            else:
                QMessageBox.information(None, "Nuevo Cliente", "👤 Abriendo módulo de gestión de clientes...")
                print("👤 Acción: Nuevo Cliente solicitada")
        except Exception as e:
            QMessageBox.warning(None, "Error", f"Error abriendo módulo de clientes: {e}")

    def nuevo_articulo(self):
        """Abre el módulo de gestión de artículos"""
        try:
            # Buscar la ventana principal
            main_window = self._find_main_window()
            if main_window and hasattr(main_window, 'abrir_articulos'):
                main_window.abrir_articulos()
                print("✅ Módulo de artículos abierto desde dashboard")
            else:
                QMessageBox.information(None, "Nuevo Artículo", "🪟 Abriendo módulo de gestión de artículos...")
                print("🪟 Acción: Nuevo Artículo solicitada")
        except Exception as e:
            QMessageBox.warning(None, "Error", f"Error abriendo módulo de artículos: {e}")

    def ver_informes(self):
        """Abre el módulo de informes"""
        try:
            # Buscar la ventana principal
            main_window = self._find_main_window()
            if main_window and hasattr(main_window, 'abrir_informes'):
                main_window.abrir_informes()
                print("✅ Módulo de informes abierto desde dashboard")
            else:
                QMessageBox.information(None, "Informes", "📊 Abriendo módulo de informes y estadísticas...")
                print("📊 Acción: Ver Informes solicitada")
        except Exception as e:
            QMessageBox.warning(None, "Error", f"Error abriendo módulo de informes: {e}")

    def _find_main_window(self):
        """Busca la ventana principal en la jerarquía de widgets"""
        from PyQt6.QtWidgets import QApplication

        # Buscar en todas las ventanas de la aplicación
        for widget in QApplication.allWidgets():
            if hasattr(widget, 'abrir_obras') and hasattr(widget, 'abrir_clientes'):
                return widget

        # Si no se encuentra, buscar el parent más alto
        parent = self.parent()
        while parent:
            if hasattr(parent, 'abrir_obras'):
                return parent
            parent = parent.parent()

        return None


class DashboardModerno(QWidget):
    """Dashboard moderno principal"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("dashboard_moderno")
        self.parent_window = parent
        self.init_ui()
        self.apply_styles()
        self.load_data()

        # Timer para actualizar datos cada 30 segundos
        self.timer = QTimer()
        self.timer.timeout.connect(self.load_data)
        self.timer.start(30000)
    
    def init_ui(self):
        """Inicializa la interfaz sin scroll"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(8)
        
        # Header compacto
        header = QLabel("📊 Dashboard Moderno")
        header.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #1e293b;
            padding: 8px;
        """)
        header.setFixedHeight(40)
        main_layout.addWidget(header)
        
        # Grid de métricas (altura fija)
        metrics_frame = QFrame()
        metrics_frame.setFixedHeight(120)
        metrics_layout = QHBoxLayout(metrics_frame)
        metrics_layout.setContentsMargins(0, 0, 0, 0)
        metrics_layout.setSpacing(8)
        
        # Crear tarjetas de métricas
        self.obras_card = MetricCard("Total Obras", "0", "🏗️", "#3b82f6")
        self.clientes_card = MetricCard("Clientes", "0", "👥", "#10b981")
        self.articulos_card = MetricCard("Artículos", "0", "🪟", "#f59e0b")
        self.progreso_card = MetricCard("Progreso", "0%", "📈", "#ef4444")
        
        metrics_layout.addWidget(self.obras_card)
        metrics_layout.addWidget(self.clientes_card)
        metrics_layout.addWidget(self.articulos_card)
        metrics_layout.addWidget(self.progreso_card)
        
        main_layout.addWidget(metrics_frame)
        
        # Contenido principal (resto del espacio)
        content_layout = QHBoxLayout()
        content_layout.setSpacing(8)
        
        # Tabla de obras (70% del ancho)
        self.obras_table = CompactTable("Obras Recientes")
        content_layout.addWidget(self.obras_table, 7)
        
        # Panel de acciones (30% del ancho)
        self.actions_panel = ActionPanel(self.parent_window)
        content_layout.addWidget(self.actions_panel, 3)
        
        main_layout.addLayout(content_layout, 1)  # Toma el resto del espacio
    
    def apply_styles(self):
        """Aplica estilos globales"""
        self.setStyleSheet("""
            QWidget#dashboard_moderno {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8fafc, stop:1 #e2e8f0);
                font-family: 'Segoe UI', Arial, sans-serif;
            }
        """)
    
    def load_data(self):
        """Carga datos reales de la base de datos"""
        try:
            db = next(get_db())
            
            # Contar datos
            total_obras = db.query(Obra).count()
            total_clientes = db.query(Cliente).count()
            total_articulos = db.query(Articulo).count()
            obras_completadas = db.query(Obra).filter(Obra.estado == "Completada").count()
            
            # Calcular progreso
            progreso = 0
            if total_obras > 0:
                progreso = int((obras_completadas / total_obras) * 100)
            
            # Actualizar tarjetas
            self.obras_card.update_value(total_obras)
            self.clientes_card.update_value(total_clientes)
            self.articulos_card.update_value(total_articulos)
            self.progreso_card.update_value(f"{progreso}%")
            
            db.close()
            print(f"✅ Dashboard moderno actualizado: {total_obras} obras, {total_clientes} clientes")
            
        except Exception as e:
            print(f"⚠️ Error cargando datos dashboard moderno: {e}")
            # Datos de ejemplo
            self.obras_card.update_value("6")
            self.clientes_card.update_value("3")
            self.articulos_card.update_value("12")
            self.progreso_card.update_value("75%")
