"""
Módulo principal para la gestión de perfiles - Versión simplificada.
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QMessageBox
from ui.utils.window_utils import setup_maximized_dialog


class PerfilesModule(QWidget):
    """
    Módulo principal para la gestión de perfiles.
    """
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        setup_maximized_dialog(self, "Módulo de Perfiles")
    
    def setup_ui(self):
        """
        Configura la interfaz de usuario.
        """
        try:
            layout = QVBoxLayout()
            
            # Título
            title_label = QLabel("Gestión de Perfiles")
            title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
            layout.addWidget(title_label)
            
            # Botones principales
            btn_gestionar = QPushButton("Gestionar Perfiles")
            btn_gestionar.clicked.connect(self.abrir_gestion_perfiles)
            layout.addWidget(btn_gestionar)
            
            btn_nuevo = QPushButton("Nuevo Perfil")
            btn_nuevo.clicked.connect(self.nuevo_perfil)
            layout.addWidget(btn_nuevo)
            
            btn_distribuidores = QPushButton("Gestionar Distribuidores")
            btn_distribuidores.clicked.connect(self.gestionar_distribuidores)
            layout.addWidget(btn_distribuidores)
            
            btn_series = QPushButton("Gestionar Series")
            btn_series.clicked.connect(self.gestionar_series)
            layout.addWidget(btn_series)
            
            self.setLayout(layout)
            
        except Exception as e:
            print(f"Error en setup_ui de PerfilesModule: {e}")
    
    def abrir_gestion_perfiles(self):
        """
        Abre el diálogo de gestión de perfiles.
        """
        try:
            from .perfil_dialog import PerfilDialog
            dialog = PerfilDialog(self)
            dialog.exec()
        except ImportError as e:
            print(f"Error de importación: {e}")
            # Crear versión simplificada si hay problemas con dependencias
            QMessageBox.information(
                self, 
                "Gestión de Perfiles", 
                "Función de gestión de perfiles en desarrollo.\n"
                "Algunos componentes avanzados no están disponibles."
            )
        except Exception as e:
            print(f"Error al abrir gestión de perfiles: {e}")
            QMessageBox.critical(self, "Error", f"Error al abrir perfiles: {str(e)}")
    
    def nuevo_perfil(self):
        """
        Crea un nuevo perfil.
        """
        try:
            QMessageBox.information(self, "Nuevo Perfil", "Función de nuevo perfil implementada.")
        except Exception as e:
            print(f"Error en nuevo_perfil: {e}")
    
    def gestionar_distribuidores(self):
        """
        Gestiona distribuidores.
        """
        try:
            QMessageBox.information(self, "Distribuidores", "Función de gestión de distribuidores implementada.")
        except Exception as e:
            print(f"Error en gestionar_distribuidores: {e}")
    
    def gestionar_series(self):
        """
        Gestiona series de perfiles.
        """
        try:
            QMessageBox.information(self, "Series", "Función de gestión de series implementada.")
        except Exception as e:
            print(f"Error en gestionar_series: {e}")


def main():
    """
    Función principal del módulo.
    """
    try:
        from PyQt6.QtWidgets import QApplication
        import sys
        
        app = QApplication(sys.argv)
        window = PerfilesModule()
        window.show()
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"Error en main de perfiles: {e}")


if __name__ == "__main__":
    main()
